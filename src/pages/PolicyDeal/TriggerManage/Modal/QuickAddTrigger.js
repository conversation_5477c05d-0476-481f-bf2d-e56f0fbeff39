import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Select, Form, Row, Col, message, Checkbox } from "antd";
import { CommonConstants } from "@/constants";
import { policyDealAPI } from "@/services";

const Option = Select.Option;

class QuickAddTrigger extends PureComponent {

	constructor(props) {
		super(props);
		this.changeDialogDataHandle = this.changeDialogDataHandle.bind(this);
		this.saveQuickTrigger = this.saveQuickTrigger.bind(this);
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyDealStore } = this.props;
		let { quickAddTrigger } = policyDealStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		} else if (type === "check") {
			value = e.target.checked;
		}
		quickAddTrigger[field] = value;
		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				quickAddTrigger: quickAddTrigger
			}
		});
	}

	saveQuickTrigger() {
		let { dispatch, policyDealStore } = this.props;
		let { quickAddTrigger, modifyTriggerTimeData } = policyDealStore.dialogData;

		let quickType = quickAddTrigger.quickType;

		let params = {
			quickType: quickType,
			timeSlot: quickAddTrigger.timeSlot,
			dealType: modifyTriggerTimeData.dealType
		};

		if (quickType === "day") {
			params["startTime"] = quickAddTrigger.startHour;
			params["endTime"] = quickAddTrigger.endHour;
		} else if (quickType === "week") {
			params["startTime"] = quickAddTrigger.startWeek;
			params["endTime"] = quickAddTrigger.endWeek;
		} else if (quickType === "month") {
			params["startTime"] = quickAddTrigger.startDay;
			params["endTime"] = quickAddTrigger.endDay;
		}

		if (params.timeSlot) {
			if (params.startTime >= params.endTime) {
				message.error("结束时间要大于开始时间");
				return;
			}
		}

		policyDealAPI.addBatchTriggerTimeEvent(params).then(res => {
			if (res.success) {
				dispatch({
					type: "policyDeal/loadCalendarData",
					payload: {
						dealType: modifyTriggerTimeData.dealType,
						calendarValue: modifyTriggerTimeData.calendarValue
					}
				});

				modifyTriggerTimeData.quickType = quickType;
				dispatch({
					type: "policyDeal/setDialogData",
					payload: {
						modifyTriggerTimeData: modifyTriggerTimeData
					}
				});

				dispatch({
					type: "policyDeal/setDialogShow",
					payload: {
						quickAddTrigger: false
					}
				});
			} else {
				console.log(res.message);
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { policyDealStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyDealStore;
		let { quickAddTrigger } = dialogData;

		let monthOptions = [];

		for (let i = 1; i <= 31; i++) {
			monthOptions.push(<Option value={i} key={i}>{i} </Option>);
		}

		let hourOptions = [];

		for (let j = 0; j <= 24; j++) {
			hourOptions.push(<Option value={j} key={j}>{j} </Option>);
		}

		return (
			<Modal
				title="快捷触发日程设置"
				visible={dialogShow.quickAddTrigger}
				maskClosable={false}
				onOk={this.saveQuickTrigger.bind(this)}
				onCancel={() => {
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							quickAddTrigger: false
						}
					});
				}}
			>
				<Form className="modal-form">
					{
						quickAddTrigger.quickType === "month" &&
                        <Row gutter={CommonConstants.gutterSpan}>
                        	<Col span={6} className="basic-info-title">每月：</Col>
                        	<Col span={18}>
                        		<Select
                        			value={quickAddTrigger.startDay}
                        			onChange={this.changeDialogDataHandle.bind(this, "startDay", "select")}
                        			style={{ width: "100px", marginRight: "5px" }}
                        		>
                        			{monthOptions}
                        		</Select>号
                        		{quickAddTrigger.timeSlot && "~"}
                        		{
                        			quickAddTrigger.timeSlot &&
                                    <Select
                                    	value={quickAddTrigger.endDay}
                                    	onChange={this.changeDialogDataHandle.bind(this, "endDay", "select")}
                                    	style={{ width: "100px", marginLeft: "5px", marginRight: "5px" }}
                                    >
                                    	{monthOptions}
                                    </Select>
                        		}
                        		{quickAddTrigger.timeSlot && "号"}
                        	</Col>
                        </Row>
					}
					{
						quickAddTrigger.quickType === "week" &&
                        <Row gutter={CommonConstants.gutterSpan}>
                        	<Col span={6} className="basic-info-title">每周：</Col>
                        	<Col span={18}>
                        		<Select
                        			value={quickAddTrigger.startWeek}
                        			onChange={this.changeDialogDataHandle.bind(this, "startWeek", "select")}
                        			style={{ width: "100px", marginRight: "5px" }}
                        		>
                        			<Option value="1">周日</Option>
                        			<Option value="2">周一</Option>
                        			<Option value="3">周二</Option>
                        			<Option value="4">周三</Option>
                        			<Option value="5">周四</Option>
                        			<Option value="6">周五</Option>
                        			<Option value="7">周六</Option>
                        		</Select>
                        		{quickAddTrigger.timeSlot && "~"}
                        		{
                        			quickAddTrigger.timeSlot &&
                                    <Select
                                    	value={quickAddTrigger.endWeek}
                                    	onChange={this.changeDialogDataHandle.bind(this, "endWeek", "select")}
                                    	style={{ width: "100px", marginLeft: "5px", marginRight: "5px" }}
                                    >
                                    	<Option value="1">周日</Option>
                                    	<Option value="2">周一</Option>
                                    	<Option value="3">周二</Option>
                                    	<Option value="4">周三</Option>
                                    	<Option value="5">周四</Option>
                                    	<Option value="6">周五</Option>
                                    	<Option value="7">周六</Option>
                                    </Select>
                        		}
                        	</Col>
                        </Row>
					}
					{
						quickAddTrigger.quickType === "day" &&
                        <Row gutter={CommonConstants.gutterSpan}>
                        	<Col span={6} className="basic-info-title">每日：</Col>
                        	<Col span={18}>
                        		<Select
                        			value={quickAddTrigger.startHour}
                        			onChange={this.changeDialogDataHandle.bind(this, "startHour", "select")}
                        			style={{ width: "100px", marginRight: "5px" }}
                        		>
                        			{hourOptions}
                        		</Select>时 ~
                        		<Select
                        			value={quickAddTrigger.endHour}
                        			onChange={this.changeDialogDataHandle.bind(this, "endHour", "select")}
                        			style={{ width: "100px", marginLeft: "5px", marginRight: "5px" }}
                        		>
                        			{hourOptions}
                        		</Select>时
                        	</Col>
                        </Row>
					}
					{
						(quickAddTrigger.quickType === "week" || quickAddTrigger.quickType === "month") &&
                        <Row gutter={CommonConstants.gutterSpan}>
                        	<Col span={6} className="basic-info-title">时间片：</Col>
                        	<Col span={18}>
                        		<Checkbox
                        			checked={quickAddTrigger.timeSlot || undefined}
                        			onChange={this.changeDialogDataHandle.bind(this, "timeSlot", "check")}
                        			style={{ marginTop: "5px" }}
                        		/>
                        	</Col>
                        </Row>
					}
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(QuickAddTrigger);
