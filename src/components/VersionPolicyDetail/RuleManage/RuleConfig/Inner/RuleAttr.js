import React, {PureComponent, Fragment} from "react";
import {connect} from "dva";
import {Input, Icon, Select, Row, Col, Popover, Checkbox} from "antd";
import {CommonConstants} from "@/constants";
import {policyDetailLang} from "@/constants/lang";
import "./rule.less";

const InputGroup = Input.Group;
const {Option} = Select;

class RuleAttr extends PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			hasExpand: false
		}
	}


	// 展开
	handleExpand = () => {
		let a = this.state.hasExpand;
		this.setState({
			hasExpand: !a
		});
	};
	render() {
		let {policyDetailStore, globalStore, currentRule, mode} = this.props;
		let {hintRules} = policyDetailStore;
		let policyMode = mode;
		if (!policyMode) {
			policyMode = policyDetailStore.policyMode;
		}
		// let { policyMode } = policyDetailStore;
		let {allMap = {}} = globalStore;

		if (!currentRule) {
			currentRule = {};
		}
		let upLimit = !!currentRule["weightUpperLimit"];
		let downLimit = !!currentRule["weightLowerLimit"];

		let {reminder={}} = currentRule;
		// if (reminder) {
		// 	console.log(reminder, 're')
		// 	let arr = JSON.parse(reminder);
		// 	let obj = {};
		// 	arr.forEach(c => {
		// 		obj[c.key] = c.code;
		// 	});
		// 	reminder = obj;
		// }
		let ruleList = Object.keys(hintRules).sort();
		if (!this.state.hasExpand) {
			ruleList = [ruleList[0]];
		}
		console.log(policyMode);
		return (
			<div className="ml-1">
				<Row gutter={CommonConstants.gutterSpan} className="mb10">
					<Col span={4} className="basic-info-title">
						{/* 规则名称 */}
						{policyDetailLang.ruleAttr("ruleName")}
					</Col>
					<Col span={8}>
						<Input
							type="text"
							value={currentRule.name || undefined}
							placeholder={policyDetailLang.ruleAttr("inputStrat")} // 请输入策略名称
							disabled={true}
						/>
					</Col>
				</Row>
				{
					currentRule.ifClause !== "IF" &&
					policyMode === "Weighted" &&
					<Fragment>
						<Row gutter={CommonConstants.gutterSpan}>
							<Col span={4} className="basic-info-title">
								{/* 风险权重 */}
								{policyDetailLang.ruleAttr("riskWeighting")}
							</Col>
							<Col span={2}>
								<Input
									type="text"
									value={(currentRule["baseWeight"] || currentRule["baseWeight"] === 0) ? currentRule["baseWeight"] : undefined}
									disabled={true}
								/>
							</Col>
							<Col span={1}>
								<span className="ml10 mr10 inline-span">+</span>
							</Col>
							<Col span={2}>
								<Input
									type="text"
									value={(currentRule["weightRatio"] || currentRule["weightRatio"] === 0) ? currentRule["weightRatio"] : undefined}
									disabled={true}
								/>
							</Col>
							<Col span={3}>
								<Select
									value={currentRule["weightOperator"] || undefined}
									disabled={true}
								>
									<Option value="add">{policyDetailLang.ruleAttr("addition")}</Option>
									<Option value="sub">{policyDetailLang.ruleAttr("substruction")}</Option>
									<Option value="mul">{policyDetailLang.ruleAttr("multiplication")}</Option>
									<Option value="div">{policyDetailLang.ruleAttr("division")}</Option>
								</Select>
							</Col>
							<Col span={8}>
								<InputGroup compact>
									<Select
										style={{width: "40%"}}
										onChange={(value) => {
											this.changeBaseField("weightType", "select", value);
											// 当类型改变的时候，清空值
											this.changeBaseField("weightIndex", "select", "");
										}}
										value={currentRule["weightType"] || undefined}
										disabled={true}
									>
										<Option value="salaxyZb">
											{policyDetailLang.ruleAttr("indicator")}
										</Option>
										<Option value="systemField">
											{policyDetailLang.ruleAttr("systemField")}
										</Option>
									</Select>
									{
										currentRule["weightType"] === "salaxyZb" &&
										<Select
											style={{width: "60%"}}
											value={currentRule["weightIndex"] || ""}
											placeholder={policyDetailLang.common("select")}
											disabled={true}
										>
											<Option value="">
												{/* 默认选项 */}
												{policyDetailLang.ruleAttr("defaultOption")}
											</Option>
											{
												allMap["salaxyFieldList"] &&
												allMap["salaxyFieldList"].filter(item => item.type === "DOUBLE" || item.type === "INT").map((item, index) => {
													return (
														<Option value={item.name} key={index}>
															{item.dName}
														</Option>
													);
												})
											}
										</Select>
									}
									{
										currentRule["weightType"] === "systemField" &&
										<Select
											style={{width: "60%"}}
											disabled={true}
											value={currentRule["weightIndex"] || undefined}
											showSearch
											optionFilterProp="children"
											dropdownMatchSelectWidth={false}
											placeholder={policyDetailLang.common("select")}
										>
											{
												allMap["ruleFieldList"] &&
												allMap["ruleFieldList"].filter(item => item.type === "DOUBLE" || item.type === "INT").map((item, index) => {
													return (
														<Option value={item.name} key={index}>
															{item.dName}
														</Option>
													);
												})
											}
										</Select>
									}
								</InputGroup>
							</Col>
						</Row>
						<Row gutter={CommonConstants.gutterSpan} className="mt10">
							<Col span={4} className="basic-info-title">
							</Col>
							<Col span="20">
								<Row type="flex" align="middle">
									<Checkbox
										className="fl"
										checked={upLimit}
										disabled={true}
									>
										{/* 上限 */}
										{policyDetailLang.ruleAttr("upLimit")}
									</Checkbox>
									{
										upLimit &&
										<Col span={4} className="mr20">
											<Input
												disabled={true}
												value={currentRule["weightUpperLimit"] || ""}
											/>
										</Col>
									}
									<Checkbox
										className="fl"
										checked={downLimit}
										disabled={true}
									>
										{/* 下限 */}
										{policyDetailLang.ruleAttr("downLimit")}
									</Checkbox>
									{
										downLimit &&
										<Col span={4} className="mr10">
											<Input
												disabled={true}
												value={currentRule["weightLowerLimit"] || ""}
											/>
										</Col>
									}
								</Row>
							</Col>
						</Row>
					</Fragment>

				}
				{
					currentRule.ifClause !== "IF" &&
					policyMode !== "Weighted" &&
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={4} className="basic-info-title">
							{/* 风险决策 */}
							{policyDetailLang.ruleAttr("riskDecision")}
						</Col>
						<Col span={8}>
							<Select
								value={currentRule ? currentRule.fkDealTypeUuid : undefined}
								disabled={true}
							>
								{
									allMap.dealTypeList &&
									allMap.dealTypeList.map((item, index) => {
										return (
											<Option value={item.uuid} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
						<Col span={1}>
							<Popover
								popupClassName="rule-param-pop-tip"
								placement="right"
								title={policyDetailLang.ruleAttr("riskDecision")} // 风险决策
								content={policyDetailLang.ruleAttr("riskIntroduced")} // 风险决策介绍
							>
								<Icon
									type="question-circle-o"
									className="param-tip-icon"
								/>
							</Popover>
						</Col>
					</Row>
				}
				{/* 规则提示定义	*/}
				{
					ruleList && ruleList.map((item, index) => {
						let children = hintRules[item];
						return <div className="hint">
							<Row gutter={CommonConstants.gutterSpan} className="mb10">
								<Col span={4} className="basic-info-title">
									{/* 规则提示 */}
									{policyDetailLang.ruleAttr("rulesOfTheTip")}{(index + 1)}
								</Col>
								<Col span={8}>
									<Select disabled value={reminder[item]} placeholder="请选择" onChange={(val) => {
										this.changeHintRules(val, item);
									}}>
										{
											children && children.map((cItem, cIndex) => {
												return <Option value={cItem.code}>{cItem.description}</Option>;
											})
										}
									</Select>
								</Col>
								<Col span={1}>
									{
										index === (ruleList.length - 1) &&
										<div onClick={this.handleExpand} style={{height: "26px", lineHeight: "26px"}}
											 className="double-right"
										>
											<Popover
												placement="right"
												title={this.state.hasExpand ? policyDetailLang.ruleAttr("packUp") : policyDetailLang.ruleAttr("unfold")}
												content={this.state.hasExpand ? policyDetailLang.ruleAttr("packUpContent") : policyDetailLang.ruleAttr("unfoldContent")}
											>
												<Icon className={this.state.hasExpand ? "icon2" : "icon1"}
													  type="double-right"/>
											</Popover>
										</div>
									}

								</Col>
							</Row>
						</div>;
					})
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(RuleAttr);
