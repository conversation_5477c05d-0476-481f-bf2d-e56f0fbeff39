@import "../../base/variables";

:global {
	.policy-detail-header {
		& {
			margin: 20px 0 30px;
		}
		.tab-menu {
			& {
				width: 242px;
				margin: 0 auto;
				padding: 0;
				text-align: center;
				border: 1px solid #3498db;
				overflow: hidden;
				border-radius: 4px;
			}
			span {
				margin: 0;
				padding: 0;
				display: inline-block;
				width: 120px;
				height: 32px;
				line-height: 32px;
				font-size: 14px;
				color: #428bca;
				cursor: pointer;
				transition: all .5s cubic-bezier(0, 1, 0.5, 1);
				-webkit-transition: all .5s cubic-bezier(0, 1, 0.5, 1);
				-moz-transition: all .5s cubic-bezier(0, 1, 0.5, 1);
			}
			span.active {
				background-color: #3498db;
				color: #fff !important;
			}
		}
		.policy-detail-guide {
			& {
				position: absolute;
				left: 0;
				line-height: 32px;
			}
			.go-back {
				width: 32px;
				height: 32px;
				line-height: 32px;
				text-align: left;
				float: left;
				cursor: pointer;
				i {
					font-size: 18px;
					line-height: 32px;
				}
			}
			h3 {
				& {
					float: left;
					margin-bottom: 0;
					color: #333;
					cursor: pointer;
					font-size: 16px;
					font-weight: normal;
					max-width: 280px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space:nowrap;
				}
				i {
					position: relative;
					top: -2px;
				}
			}
		}
	}
}

