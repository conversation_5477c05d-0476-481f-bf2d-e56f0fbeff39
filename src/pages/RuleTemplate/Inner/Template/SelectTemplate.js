import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Switch, Menu, Dropdown } from "antd";
import ChangeRuleForOther from "./ChangeRuleForOther";
import ChangeRuleForSelf from "./ChangeRuleForSelf";
import ChangeRuleForMoreSelf from "./ChangeRuleForMoreSelf";

const InputGroup = Input.Group;
const { Option, OptGroup } = Select;

class SelectTemplate extends PureComponent {
	constructor(props) {
		super(props);
		this.changeParamValue = this.changeParamValue.bind(this);
		this.deleteChild = this.deleteChild.bind(this);
		this.addSelectOption = this.addSelectOption.bind(this);
		this.changeSelectOption = this.changeSelectOption.bind(this);
		this.removeSelectOption = this.removeSelectOption.bind(this);
		this.switchTypeMenu = this.switchTypeMenu.bind(this);
	}

	changeParamValue(type, field, actionType, e) {
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else {
			value = e && e === "dataSetNull" ? null : e;
		}
		if (actionType === "onBlur") {
			// 正则表达式去除首尾空格
			value = value.replace(/(^\s*)|(\s*$)/g, "");
		}
		let { templateStore, childIndex, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex][field] = value;
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	deleteChild(index) {
		let { templateStore, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"].splice(index, 1);
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	addSelectOption() {
		let { templateStore, childIndex, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		let optionTemplate = {
			"name": "",
			"value": ""
		};

		if (cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["selectOption"]) {
			cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["selectOption"].push(optionTemplate);
		} else {
			cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["selectOption"] = [
				{
					"name": "",
					"value": ""
				}
			];
		}
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	changeSelectOption(index, field, e) {
		let { templateStore, childIndex, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["selectOption"][index][field] = e.target.value;

		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	removeSelectOption(index) {
		let { templateStore, childIndex, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["selectOption"].splice(index, 1);

		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	switchTypeMenu({ key }) {
		let { templateStore, childIndex, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["componentType"] = key;

		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	render() {
		let { templateStore, childIndex, componentType } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		const switchTypeMenu = (
			<Menu onClick={this.switchTypeMenu.bind(this)}>
				<Menu.Item key="select">select</Menu.Item>
				<Menu.Item key="checkbox">checkbox</Menu.Item>
				<Menu.Item key="radio">radio</Menu.Item>
			</Menu>
		);

		return (
			<div className="single-param">
				<div className="single-param-header">
					<Dropdown overlay={switchTypeMenu}>
						<h2>{componentType}</h2>
					</Dropdown>
					<Icon
						type="delete"
						onClick={this.deleteChild.bind(this, childIndex)}
					/>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>唯一标识：</span>
					</div>
					<div className="param-field">
						<Input
							value={currentParam["name"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "name", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "name", "onBlur")}
							placeholder="请输入唯一标识"
						/>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>默认值：</span>
					</div>
					<div className="param-field">
						<Input
							value={currentParam["defaultValue"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "defaultValue", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "defaultValue", "onBlur")}
							placeholder="请输入默认值"
						/>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>选项范围：</span>
					</div>
					<div className="param-field">
						<Select
							defaultValue="local"
							value={currentParam["selectType"] || undefined}
							rows={4}
							placeholder="请选择数据类型"
							style={{ width: "100%" }}
							onChange={this.changeParamValue.bind(this, "select", "selectType", "onChange")}
						>
							<Option value="local">local</Option>
							<Option value="service">service</Option>
							<Option value="self">self</Option>
						</Select>
					</div>
				</div>
				{
					currentParam["selectType"] === "service" && componentType === "select" &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>map类型：</span>
                    	</div>
                    	<div className="param-field">
                    		<Select
                    			value={currentParam["mapType"] || undefined}
                    			rows={4}
                    			placeholder="请选择map类型"
                    			style={{ width: "100%" }}
                    			onChange={this.changeParamValue.bind(this, "select", "mapType", "onChange")}
                    		>
                    			<Option value="static">静态map</Option>
                    			<Option value="dynamic">动态map</Option>
                    			<Option value="attrFilter">属性过滤map</Option>
                    		</Select>
                    	</div>
                    </div>
				}
				{
					(currentParam["selectType"] === "local" || currentParam["selectType"] === "service") &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>Map名称：</span>
                    	</div>
                    	<div className="param-field">
                    		<Input
                    			value={currentParam["selectName"] || undefined}
                    			placeholder="请输入select选择项map名称"
                    			onChange={this.changeParamValue.bind(this, "input", "selectName", "onChange")}
                    			onBlur={this.changeParamValue.bind(this, "input", "selectName", "onBlur")}
                    		/>
                    	</div>
                    </div>
				}
				{
					currentParam["selectType"] === "service" &&
                    currentParam["mapType"] === "attrFilter" &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>属性过滤配置：</span>
                    	</div>
                    	<div className="param-field">
                    		<InputGroup compact>
                    			<Input
                    				style={{ width: "30%" }}
                    				placeholder="请输入属性值"
                    				value={currentParam["attrFilterName"] || undefined}
                    				onChange={this.changeParamValue.bind(this, "input", "attrFilterName", "onChange")}
                    				onBlur={this.changeParamValue.bind(this, "input", "attrFilterName", "onBlur")}
                    			/>
                    			<Select
                    				value={currentParam["attrFilterBy"] || undefined}
                    				style={{ width: "70%" }}
                    				placeholder="请选择影响项"
                    				onChange={this.changeParamValue.bind(this, "select", "attrFilterBy", "onChange")}
                    			>
                    				{
                    					params &&
                                        params.filter((fItem, fIndex) => {
                                        	return fIndex !== operatorTemplateItemIndex;
                                        }).map((paramItem, paramIndex) => {
                                        	return (
                                        		<OptGroup
                                        			label={paramItem.labelText || "暂无标题"}
                                        			key={paramIndex}
                                        		>
                                        			{
                                        				paramItem.children &&
                                                        paramItem.children.map((subItem, subIndex) => {
                                                        	if (subItem.componentType === "select") {
                                                        		return (
                                                        			<Option
                                                        				value={subItem.name || undefined}
                                                        				key={subIndex}
                                                        			>
                                                                        [{subItem.componentType}] {subItem.name}
                                                        			</Option>
                                                        		);
                                                        	}
                                                        })
                                        			}
                                        		</OptGroup>
                                        	);
                                        })
                    				}
                    			</Select>
                    		</InputGroup>
                    	</div>
                    </div>
				}
				{
					currentParam["selectType"] === "self" &&
                    <li className="single-param-item">
                    	<div className="param-title">
                    		<span>添加选项：</span>
                    	</div>
                    	<div className="param-field">
                    		<span className="add-select-option" onClick={this.addSelectOption.bind(this)}>添加选项</span>
                    	</div>
                    	<div className="param-select-option">
                    		<ul>
                    			{
                    				currentParam["selectOption"] && currentParam["selectOption"].map((item, index) => {
                    					return (
                    						<li key={index}>
                    							<InputGroup compact>
                    								<Input
                    									value={item.name}
                    									addonBefore="中文名称"
                    									onChange={this.changeSelectOption.bind(this, index, "name")}
                    									style={{ width: "35%" }}
                    								/>
                    								<Input
                    									value={item.enName}
                    									addonBefore="英文名称"
                    									onChange={this.changeSelectOption.bind(this, index, "enName")}
                    									style={{ width: "35%" }}
                    								/>
                    								<Input value={item.value}
                    									addonBefore="选项值"
                    									onChange={this.changeSelectOption.bind(this, index, "value")}
                    									style={{ width: "30%" }}
                    								/>
                    								<Tooltip placement="top" content="移除此选项">
                    									<Icon
                    										onClick={this.removeSelectOption.bind(this, index)}
                    										className="param-select-option-delete"
                    										type="minus-circle"
                    									/>
                    								</Tooltip>
                    							</InputGroup>
                    						</li>
                    					);
                    				})
                    			}
                    		</ul>
                    	</div>
                    </li>
				}
				{
					componentType === "select" &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>placeholder：</span>
                    	</div>
                    	<div className="param-field">
                    		<Input
                    			value={currentParam["placeholder"] || undefined}
                    			onChange={this.changeParamValue.bind(this, "input", "placeholder", "onChange")}
                    			onBlur={this.changeParamValue.bind(this, "input", "placeholder", "onBlur")}
                    			placeholder="中文placeholder（非必填）"
                    			addonBefore="cn"
                    		/>
                    		<Input
                    			value={currentParam["enPlaceholder"] || undefined}
                    			onChange={this.changeParamValue.bind(this, "input", "enPlaceholder", "onChange")}
                    			onBlur={this.changeParamValue.bind(this, "input", "enPlaceholder", "onBlur")}
                    			placeholder="英文placeholder（非必填）"
                    			addonBefore="en"
                    		/>
                    	</div>
                    </div>
				}
				{
					componentType === "select" &&
                    currentParam["selectType"] !== "service" &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>字段类型：</span>
                    	</div>
                    	<div className="param-field">
                    		<Select
                    			value={currentParam["type"] || undefined}
                    			rows={4}
                    			placeholder="请选择数据类型"
                    			style={{ width: "100%" }}
                    			onChange={this.changeParamValue.bind(this, "select", "type", "onChange")}
                    		>
                    			<Option value="string">string</Option>
                    			<Option value="int">int</Option>
                    		</Select>
                    	</div>
                    </div>
				}
				{
					componentType === "select" &&
                    currentParam["selectType"] === "service" &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>字段过滤类型：</span>
                    	</div>
                    	<div className="param-field">
                    		<Select
                    			mode="tags"
                    			value={currentParam["filterType"] || undefined}
                    			rows={4}
                    			placeholder="请选择过滤类型"
                    			style={{ width: "100%" }}
                    			onChange={this.changeParamValue.bind(this, "select", "filterType", "onChange")}
                    		>
                    			<Option value="dataSetNull">没有过滤</Option>
                    			<Option value="string">string</Option>
                    			<Option value="int">int</Option>
                    			<Option value="double">double</Option>
                    			<Option value="datetime">datetime</Option>
                    			<Option value="enum">enum</Option>
                    			<Option value="boolean">boolean</Option>
                    		</Select>
                    	</div>
                    </div>
				}
				<div className="single-param-item">
					<div className="param-title">
						<span>字段最终类型：</span>
					</div>
					<div className="param-field">
						<Select
							value={currentParam["typeFinal"] || undefined}
							rows={4}
							placeholder="请选择数据最终类型"
							style={{ width: "100%" }}
							onChange={this.changeParamValue.bind(this, "select", "typeFinal", "onChange")}
						>
							<Option value="string">string</Option>
							<Option value="int">int</Option>
							<Option value="context">context</Option>
						</Select>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>二次编辑：</span>
					</div>
					<div className="param-field">
						<Switch
							checkedChildren="可以"
							unCheckedChildren="不可"
							checked={currentParam["canEditSecondTimes"]}
							onChange={this.changeParamValue.bind(this, "select", "canEditSecondTimes", "onChange")}
						/>
					</div>
				</div>
				<hr style={{ borderColor: "#fff" }} />
				<div className="single-param-item">
					<div className="param-title">
						<span>根据字段过滤：</span>
					</div>
					<div className="param-field">
						<Switch
							checkedChildren="有"
							unCheckedChildren="没有"
							checked={currentParam["filterByField"]}
							onChange={this.changeParamValue.bind(this, "select", "filterByField", "onChange")}
						/>
					</div>
				</div>
				{
					componentType === "select" &&
                    currentParam["selectType"] === "service" &&
                    currentParam["filterByField"] &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>过滤字段name：</span>
                    	</div>
                    	<div className="param-field">
                    		<Input
                    			addonAfter="等于"
                    			value={currentParam["filterByFieldName"] || undefined}
                    			onChange={this.changeParamValue.bind(this, "input", "filterByFieldName", "onChange")}
                    			onBlur={this.changeParamValue.bind(this, "input", "filterByFieldName", "onBlur")}
                    			placeholder="请输入过滤字段name"
                    		/>
                    	</div>
                    </div>
				}
				{
					componentType === "select" &&
                    currentParam["selectType"] === "service" &&
                    currentParam["filterByField"] &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>过滤类型：</span>
                    	</div>
                    	<div className="param-field">
                    		<Select
                    			value={currentParam["filterByFieldType"] || undefined}
                    			rows={4}
                    			placeholder="请选择过滤类型"
                    			style={{ width: "100%" }}
                    			onChange={this.changeParamValue.bind(this, "select", "filterByFieldType", "onChange")}
                    		>
                    			<Option value="policyApp">规则所在策略的app</Option>
                    			<Option value="custom">自己输入</Option>
                    			<Option value="policyAppAndCustom">规则所在策略的app+自己输入</Option>
                    		</Select>
                    	</div>
                    </div>
				}
				{
					componentType === "select" &&
                    currentParam["selectType"] === "service" &&
                    currentParam["filterByField"] &&
                    (currentParam["filterByFieldType"] === "custom" || currentParam["filterByFieldType"] === "policyAppAndCustom") &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>fieldName：</span>
                    	</div>
                    	<div className="param-field">
                    		<Input
                    			value={currentParam["filterByFieldValue"] || undefined}
                    			onChange={this.changeParamValue.bind(this, "input", "filterByFieldValue", "onChange")}
                    			onBlur={this.changeParamValue.bind(this, "input", "filterByFieldValue", "onBlur")}
                    			placeholder="请输入placeholder" />
                    	</div>
                    </div>
				}
				<hr style={{ borderColor: "#fff" }} />
				<ChangeRuleForOther componentType={componentType} childIndex={childIndex} />
				<ChangeRuleForSelf componentType={componentType} childIndex={childIndex} />
				<ChangeRuleForMoreSelf componentType={componentType} childIndex={childIndex} />
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(SelectTemplate);
