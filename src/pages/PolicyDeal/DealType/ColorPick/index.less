.choose-color{
    position: relative;
    .color-picker-wrap{
		background-color: #fff;
		border-radius: 4px;
		border:1px solid #ededed;
		border-top:0;
        position: absolute;
        top:100%;
        left:0;
        z-index: 9999;;
    }
    .color-picker-wrap{
        width:100%;
        .compact-picker {
			width:100% !important;
			padding-top:10px !important;
            span{
                >div{
                    width:20px !important;
                    height:20px !important;
                    margin-right:9px !important;
                    margin-bottom:9px !important;
                }
            }
        }
    }
}
.show-color-detail{
    width:20px;
    height:20px;
    display: block;
}

.color-pick-tab{
	.ant-tabs-bar{
		margin-bottom:0;
	}
	.ant-tabs-nav .ant-tabs-tab{
		padding:6px 16px;
	}
}
.color-picker-content{
	margin-top:6px;
}
.color-picker-linear{
	list-style: none;
	margin: 0;
    padding: 10px 5px;
	min-height: 120px;
    overflow-y: scroll;
    max-height: 180px;
	li{
		width:20px;
		height:20px;
		float: left;
		margin-right:9px;
		margin-bottom:9px;
		cursor: pointer;
		position: relative;
		&.active{
			&::after{
				content: "";
				position: absolute;
				width:10px;
				height:10px;
				background-color: #fff;
				border-radius: 100%;
				top:5px;
				left:5px;
			}
		}
		&:nth-child(12n){
			margin-right: 0;
		}
	}
}
