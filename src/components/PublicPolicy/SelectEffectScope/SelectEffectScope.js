import { PureComponent } from "react";
import { connect } from "dva";
import { Icon, Checkbox, Button, Input, Tooltip } from "antd";
import { trim } from "lodash";
import { indexListLang } from "@/constants/lang";
import "./SelectEffectScope.less";

class SelectEffectScope extends PureComponent {
	state = {
		currentSelectAppIndex: 0,
		searchIng: false,
		searchWord: null,
		publicPolicyList: []
	}
	constructor(props) {
		super(props);
	}

	componentWillMount() {
		let { globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		const publicPolicyList = publicPolicyScene && publicPolicyScene.length > 0 && publicPolicyScene.filter((v) => v.name !== "GLOBAL_APP");
		this.setState({
			publicPolicyList
		});
	}
	changeSceneEvent = (appItem = {}, eventId, checked) => {
		const { changeField, onChange, value } = this.props;
		let scene = [...this.props.scene];
		let sceneObj = scene.find(scene => scene.appName === appItem.name);
		let sceneObjIndex = scene.findIndex(scene => scene.appName === appItem.name);

		if (checked) {
			// add
			if (sceneObj) {
				// app scene对象已经存在
				scene[sceneObjIndex]["appName"] = appItem.name;
				if (Array.isArray(scene[sceneObjIndex]["eventList"])) {
					scene[sceneObjIndex]["eventList"].push(eventId);
				} else {
					scene[sceneObjIndex]["eventList"] = [eventId];
				}

			} else {
				// add
				let obj = {
					appName: appItem.name,
					eventList: [eventId]
				};
				scene.push(obj);
			}
		} else {
			// remove
			if (sceneObj) {
				let { eventList } = sceneObj;

				if (sceneObjIndex > -1) {
					let eventIdIndex = eventList.findIndex(event => event === eventId);
					eventList.splice(eventIdIndex, 1);
					if (eventList.length > 0) {
						// 更新
						scene[sceneObjIndex]["appName"] = appItem.name;
						scene[sceneObjIndex]["eventList"] = sceneObj.eventList || [];
					} else {
						// 移除一整个
						scene.splice(sceneObjIndex, 1);
					}

				}
			}
		}
		changeField("scene", scene, "scene");
		if (onChange) {
			onChange({ ...value, scene });
		}
	}

	render() {
		let { currentSelectAppIndex, searchIng, searchWord, publicPolicyList } = this.state;
		let { scene } = this.props;
		let newAppList = [];
		searchWord = trim(searchWord);
		publicPolicyList.forEach((item) => {
			if (searchIng && searchWord) {
				if (item.dName) {
					if (item.dName.indexOf(searchWord) === -1) {
						// 如果没有匹配应用，继续检索应用下的策略集
						let policySetList = item.eventList;
						let hasMatchSearchWord = false;
						policySetList.forEach(setItem => {
							if (setItem.dName && setItem.dName.indexOf(searchWord) > -1) {
								hasMatchSearchWord = true;
							}
						});
						if (hasMatchSearchWord) {
							newAppList.push(item);
						}
					} else {
						// 匹配应用
						newAppList.push(item);
					}
				}
			} else {
				if (item.name !== "all") {
					newAppList.push(item);
				}
			}
		});
		let currentAppObj = newAppList.length > 0 && newAppList[currentSelectAppIndex] ? newAppList[currentSelectAppIndex] : null;
		let policySetMap = (currentAppObj && currentAppObj.eventList) || [];
		let hasSelectSceneObj = currentAppObj && scene ? scene.find(sceneItem => sceneItem.appName === currentAppObj.name) : null;

		if (!hasSelectSceneObj) {
			hasSelectSceneObj = {
				appName: currentAppObj && currentAppObj.name,
				eventList: []
			};
		}

		let disabled = false;
		return (
			<div className="scene-wrap">
				{
					searchIng &&
					<div className="scene-search-wrap">
						<Input
							value={searchWord || undefined}
							placeholder={indexListLang.ruleConfig("searchSceneTip")} // lang:搜索应用、策略集
							prefix={
								<Icon
									type="search"
									style={{ color: "rgba(0,0,0,.25)" }}
								/>
							}
							suffix={
								<Tooltip
									title={indexListLang.ruleConfig("exitSearch")} // lang:退出搜索
								>
									<Icon
										type="close"
										style={{ color: "rgba(0,0,0,.45)" }}
										onClick={() => {
											this.setState({
												searchIng: false,
												searchWord: null
											});
										}}
									/>
								</Tooltip>
							}
							style={{ width: "100%" }}
							onChange={(e) => {
								this.setState({
									searchWord: e.target.value
								});
							}}
						/>
					</div>
				}
				<div className="left">
					<div className="scene-content-wrap">
						<div className="scene-content-header">
							<h3>
								{/* lang:应用 */}
								{indexListLang.ruleConfig("application")}
							</h3>
						</div>
						<div className="scene-content-body">
							<ul className="scene-app-list">
								{
									newAppList && newAppList.map((item, index) => {
										let hasSelect = scene && !!scene.find(sceneItem => {
											return sceneItem && sceneItem.eventList && sceneItem.appName === item.name && sceneItem.eventList.length > 0;
										});

										return (
											<li
												className={currentSelectAppIndex === index ? "scene-app-item active" : "scene-app-item"}
												key={index}
												onClick={() => {
													this.setState({
														currentSelectAppIndex: index
													});
												}}
											>
												<Icon
													style={{ marginRight: "5px" }}
													type={hasSelect ? "check-square" : "border"}
												/>
												<span>{item.dName}</span>
											</li>
										);
									})
								}
							</ul>
						</div>
					</div>
				</div>
				<div className="right">
					<div className="scene-content-wrap">
						<div className="scene-content-header">
							<h3>
								{/* lang:策略集 */}
								{indexListLang.ruleConfig("policySet")}
							</h3>
							<div className="search-handle">
								<Tooltip
									title={indexListLang.ruleConfig("searchSceneTip")} // lang:搜索应用、策略集
								>
									<Button
										type="dashed"
										shape="circle"
										icon="search"
										size="small"
										onClick={() => {
											this.setState({
												searchIng: true
											});
										}}
									/>
								</Tooltip>
							</div>
						</div>
						<div className="scene-content-body">
							<ul className="scene-event-list">
								{
									policySetMap &&
									policySetMap.map((item, index) => {
										let eventList = hasSelectSceneObj.eventList;
										let Dom = (
											<li
												className="scene-event-item"
												key={index}
											>
												<Checkbox
													checked={!!(eventList && eventList.find(event => event === item.name))}
													onChange={(e) => {
														let checked = e.target.checked;
														this.changeSceneEvent(currentAppObj, item.name, checked);
													}}
													disabled={disabled}
												>
													{item.dName}
												</Checkbox>
											</li>
										);

										if (searchIng && searchWord) {
											if (currentAppObj.dName.indexOf(searchWord) > -1) {
												return Dom;
											} else if (item.dName && item.dName.indexOf(searchWord) > -1) {
												return Dom;
											}
										} else {
											return Dom;
										}
									})
								}
							</ul>
						</div>
					</div>
				</div>
			</div>
		);
	}
}
export default connect(state => ({
	globalStore: state.global
}))(SelectEffectScope);
