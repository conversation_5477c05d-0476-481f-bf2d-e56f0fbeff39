import { PureComponent } from "react";
import { Icon, Checkbox, Button, Input, Tooltip } from "antd";
import { trim } from "lodash";
import { indexListLang } from "@/constants/lang";
import "./AppScene.less";

export default class AppScene extends PureComponent {
	state = {
		currentSelectAppIndex: 0, // 当前选中的应用索引
		searchIng: false, // 开启搜索的状态
		searchWord: null // 搜索内容
	}
	constructor(props) {
		super(props);
	}

	componentDidMount() {
		console.log("come in");
		// sceneListSource 场景数据源
		// scene 当前已选中的场景
		// 判断已选择场景是否在数据源中
		let { sceneListSource = [], scene: sceneOld = [], changeField, name } = this.props;
		if (sceneOld && typeof sceneOld === "string") {
			sceneOld = JSON.parse(sceneOld);
		}
		if (sceneListSource && typeof sceneListSource === "string") {
			sceneListSource = JSON.parse(sceneListSource);
		}
		sceneOld = sceneOld || [];
		sceneListSource = sceneListSource || [];
		console.log(sceneListSource);

		const scene = [];
		if (sceneOld && sceneOld.length > 0) {
			sceneOld.forEach(sce=>{
				const {eventList, appName} = sce;
				const newSce = {
					appName,
					eventList: []
				};
				const {eventList: sceneListSourceEventList = [] } = sceneListSource.find(v=>v.name === appName) || {};
				eventList &&
				eventList.length > 0 &&
				eventList.forEach(event=>{
					const existEvent = sceneListSourceEventList && sceneListSourceEventList.length > 0 && sceneListSourceEventList.filter(v=>v.name === event);
					if (existEvent && existEvent.length > 0) {
						newSce.eventList.push(event);
					}
				});
				scene.push(newSce);
			});
		}
		changeField(name, scene, "select", "noModify"); // 传noModify表示指标场景初始化未发生变更无需提示未保存
	}

	changeSceneEvent = (appItem = {}, eventId, checked) => {
		const { changeField, onChange, value, name } = this.props;
		let scene = [...(this.props.scene || [])];
		let sceneObj = scene.find(scene => scene.appName === appItem.name);
		let sceneObjIndex = scene.findIndex(scene => scene.appName === appItem.name);

		if (checked) {
			// add
			if (sceneObj) {
				// app scene对象已经存在
				scene[sceneObjIndex]["appName"] = appItem.name;
				if (Array.isArray(scene[sceneObjIndex]["eventList"])) {
					scene[sceneObjIndex]["eventList"].push(eventId);
				} else {
					scene[sceneObjIndex]["eventList"] = [eventId];
				}

			} else {
				// add
				let obj = {
					appName: appItem.name,
					eventList: [eventId]
				};
				scene.push(obj);
			}
		} else {
			// remove
			if (sceneObj) {
				let { eventList } = sceneObj;
				if (sceneObjIndex > -1) {
					let eventIdIndex = eventList.findIndex(event => event === eventId);
					eventList.splice(eventIdIndex, 1);
					if (eventList.length > 0) {
						// 更新
						scene[sceneObjIndex]["appName"] = appItem.name;
						scene[sceneObjIndex]["eventList"] = sceneObj.eventList || [];
					} else {
						// 移除一整个
						scene.splice(sceneObjIndex, 1);
					}

				}
			}
		}
		changeField(name, scene, "select");
		if (onChange) {
			onChange({ ...value, scene });
		}
	}

	// 搜索内容
	filterOption = (sceneListSource) => {
		const newAppList = [];
		let { searchIng, searchWord } = this.state;
		searchWord = trim(searchWord);
		sceneListSource &&
		sceneListSource.length > 0 &&
		sceneListSource.forEach((item) => {
			const { dName, eventList } = item;
			// 如果开启搜索
			if (searchIng && searchWord) {
				if (dName) {
					if (dName.indexOf(searchWord) === -1 && eventList && eventList.length > 0) {
						// 如果没有匹配应用，继续检索应用下的策略集
						let hasMatchSearchWord = false;
						eventList.forEach(setItem => {
							if (setItem.dName && setItem.dName.indexOf(searchWord) > -1) {
								hasMatchSearchWord = true;
							}
						});
						if (hasMatchSearchWord) {
							newAppList.push(item);
						}
					} else {
						// 匹配应用
						if (eventList && eventList.length > 0) {
							newAppList.push(item);
						}
					}
				}
			} else {
				// 处理全局的case
				if (item.name !== "all" && eventList && eventList.length > 0) {
					newAppList.push(item);
				}
			}
		});
		return newAppList;
	}

	/**
	 * 全选层面的逻辑
	 * **/
	// 判断是否已经全部选中
	ifAllSelected = (sceneListSource, scene) => {
		const selectEdEventList = scene && scene.reduce((selectEdScenes, cur) => {
			selectEdScenes = selectEdScenes.concat(cur.eventList);
			return selectEdScenes;
		}, []);
		const totalEventList = sceneListSource && sceneListSource.reduce((totalEventList, cur) => {
			totalEventList = totalEventList.concat(cur.eventList);
			return totalEventList;
		}, []);
		console.log(selectEdEventList);
		if (selectEdEventList && selectEdEventList.length > 0) {
			if (totalEventList.length !== selectEdEventList.length) {
				return {
					checked: false,
					indeterminate: true
				};
			} else {
				return {
					checked: true,
					indeterminate: false
				};
			}
		} else {
			return {
				checked: false,
				indeterminate: false
			};
		}
	}
	// 全选
	toSelectAll = (sceneListSource, allChecked) =>{
		const { changeField, name } = this.props;
		let selectValue = [];
		if (allChecked) {
			selectValue = [];
		} else {
			sceneListSource.forEach(v=>{
				let obj = {
					appName: v.name,
					eventList: []
				};
				v.eventList.forEach((event)=>{
					obj.eventList.push(event.name);
				});
				selectValue.push(obj);
			});
		}
		changeField(name, selectValue, "select");
	}

	/**
	 * app应用层面选中
	 * **/
	// 判断app应用是否已经全部选中
	ifAppAllSelected = (item, scene) => {
		const appSelectedEventList = ((
			scene && scene.length && scene.find(sceneItem => {
				if (sceneItem && sceneItem.appName === item.name) {
					return sceneItem;
				};
			})
		) || {}).eventList || [];
		const { eventList = [] } = item || {};
		if (appSelectedEventList.length > 0) {
			if (appSelectedEventList.length !== eventList.length) {
				return {
					checked: false,
					indeterminate: true
				};
			} else {
				return {
					checked: true,
					indeterminate: false
				};
			}
		} else {
			return {
				checked: false,
				indeterminate: false
			};
		}
	}
	// app应用全选
	toSelectAppAll = (oldScene = [], sceneListSource, item, allAppChecked) => {
		const { changeField, name } = this.props;
		let appEventList = [];
		if (allAppChecked) {
			appEventList = [];
		} else {
			const { eventList } = sceneListSource.find(v=>{
				return v.name === item.name;
			}) || {};
			eventList.forEach(v=>{
				appEventList.push(v.name);
			});
		}
		let hasScene = false;
		const scene = oldScene && [...oldScene];
		scene.map(v=>{
			if (v.appName === item.name) {
				hasScene = true;
				v.eventList = appEventList;
			};
			return v;
		});
		if (!hasScene) {
			scene.push({
				appName: item.name,
				eventList: appEventList
			});
		}
		changeField(name, scene, "select");
	}

	/**
	 * 判断策略集下面
	 * **/
	ifPolicySetSelected = (currentAppObj, scene) => {
		const policySetSelectedEventList = ((
			scene && scene.length && scene.find(sceneItem => {
				if (sceneItem && sceneItem.appName === currentAppObj.name) {
					return sceneItem;
				};
			})
		) || {}).eventList || [];
		const { eventList = [] } = currentAppObj || {};
		const containCurrentEvent = [];
		eventList.forEach(v=>{
			if (policySetSelectedEventList.indexOf(v.name) > -1) {
				containCurrentEvent.push(v);
			}
		});
		if (containCurrentEvent.length > 0) {
			if (containCurrentEvent.length !== eventList.length) {
				return {
					checked: false,
					indeterminate: true
				};
			} else {
				return {
					checked: true,
					indeterminate: false
				};
			}
		} else {
			return {
				checked: false,
				indeterminate: false
			};
		}
	}
	toSelectPolicySetAll = (oldScene, currentAppObj, allPolicySetChecked) => {
		const { changeField, name } = this.props;
		const { eventList } = currentAppObj || {};
		const eventListIds = [];
		eventList.forEach(v=>{
			eventListIds.push(v.name);
		});

		let hasScene = false;
		// 已经选中的策略集
		const scene = [...oldScene];
		let policySetEventList = (
			scene.find(v=>{
				if (v.appName === currentAppObj.name) {
					hasScene = true;
					return v;
				};
			}) || {}
		).eventList || [];
		if (hasScene) {
			eventListIds.forEach(v=>{
				if (policySetEventList && policySetEventList.length > 0 && policySetEventList.indexOf(v) > -1) {
					policySetEventList.splice(policySetEventList.indexOf(v), 1);
				}
			});
			if (!allPolicySetChecked) {
				policySetEventList = eventListIds;
			}
			scene.map(v=>{
				if (v.appName === currentAppObj.name) {
					v.eventList = policySetEventList;
				}
			});
		} else {
			scene.push({
				appName: currentAppObj.name,
				eventList: eventListIds
			});
		}
		changeField(name, scene, "select");
	}
	render() {
		let { currentSelectAppIndex, searchIng, searchWord } = this.state;
		// sceneListSource 场景数据源
		// scene 当前已选中的场景
		let { sceneListSource = [], scene: scene = [], disabled } = this.props;
		if (scene && typeof scene === "string") {
			scene = JSON.parse(scene);
		}
		if (sceneListSource && typeof sceneListSource === "string") {
			sceneListSource = JSON.parse(sceneListSource);
		}
		scene = scene || [];
		sceneListSource = sceneListSource || [];
		console.log(scene);

		// 处理场景数据源
		const newAppList = this.filterOption(sceneListSource);
		// 获取当前应用的对象
		let currentAppObj = newAppList.length > 0 && newAppList[currentSelectAppIndex] ? newAppList[currentSelectAppIndex] : null;
		// 当前应用下的策略集或事件
		let policySetMap = (currentAppObj && currentAppObj.eventList) || [];
		// 展示当前应用的选中项
		let hasSelectSceneObj = currentAppObj && scene ? scene.find(sceneItem => sceneItem.appName === currentAppObj.name) : null;

		if (!hasSelectSceneObj) {
			hasSelectSceneObj = {
				appName: currentAppObj && currentAppObj.name,
				eventList: []
			};
		}

		// 判断所有复选框状态
		const {checked: allChecked, indeterminate: allIndeterminate} = this.ifAllSelected(sceneListSource, scene);
		// 判断策略集复选状态
		// const {checked: allPolicySetChecked, indeterminate: allPolicySetIndeterminate} = this.ifPolicySetSelected(currentAppObj, scene);

		return (
			<div className="select-scene-common">
				 <div className="scene-wrap">
					{/* 搜索模块 */}
					{
						searchIng &&
						<div className="scene-search-wrap">
							<Input
								value={searchWord || undefined}
								placeholder={indexListLang.ruleConfig("searchSceneTip")} // lang:搜索应用、策略集
								prefix={
									<Icon
										type="search"
										style={{ color: "rgba(0,0,0,.25)" }}
									/>
								}
								suffix={
									<Tooltip
										title={indexListLang.ruleConfig("exitSearch")} // lang:退出搜索
									>
										<Icon
											type="close"
											style={{ color: "rgba(0,0,0,.45)" }}
											onClick={() => {
												this.setState({
													searchIng: false,
													searchWord: null
												});
											}}
										/>
									</Tooltip>
								}
								style={{ width: "100%" }}
								onChange={(e) => {
									this.setState({
										searchWord: e.target.value
									});
								}}
							/>
						</div>
					}
					<div className="left">
						<div className="scene-content-wrap">
							<div className="scene-content-header">
								<h3>
									{/* lang:应用 */}
									<Checkbox
										disabled={disabled}
										checked={allChecked}
										indeterminate={allIndeterminate}
										onChange={this.toSelectAll.bind(this, sceneListSource, allChecked)}
									>
										{indexListLang.ruleConfig("application")}
									</Checkbox>
								</h3>
							</div>
							<div className="scene-content-body">
								<ul className="scene-app-list">
									{
										newAppList && newAppList.map((item, index) => {
											// 判断应用复选框状态
											const {checked: allAppChecked, indeterminate: allAppIndeterminate} = this.ifAppAllSelected(item, scene);
											return (
												<li
													className={currentSelectAppIndex === index ? "scene-app-item active" : "scene-app-item"}
													key={index}
													onClick={() => {
														this.setState({
															currentSelectAppIndex: index
														});
													}}
												>
													<Checkbox
														disabled={disabled}
														onChange={this.toSelectAppAll.bind(this, scene, sceneListSource, item, allAppChecked)}
														checked={allAppChecked}
														indeterminate={allAppIndeterminate}
														style={{ marginRight: "8px" }}
													/>
													<span>{item.dName}</span>
												</li>
											);
										})
									}
								</ul>
							</div>
						</div>
					</div>
					<div className="right">
						<div className="scene-content-wrap">
							<div className="scene-content-header">
								<h3>
									{/* lang:策略集 */}
									{/* <Checkbox
										onChange={this.toSelectPolicySetAll.bind(this, scene, currentAppObj, allPolicySetChecked)}
										checked={allPolicySetChecked}
										indeterminate={allPolicySetIndeterminate}
									>
										{indexListLang.ruleConfig("policySet")}
									</Checkbox> */}
									{indexListLang.ruleConfig("policySet")}
								</h3>
								<div className="search-handle">
									<Tooltip
										title={indexListLang.ruleConfig("searchSceneTip")} // lang:搜索应用、策略集
									>
										<Button
											type="dashed"
											shape="circle"
											icon="search"
											size="small"
											onClick={() => {
												this.setState({
													searchIng: true
												});
											}}
										/>
									</Tooltip>
								</div>
							</div>
							<div className="scene-content-body">
								<ul className="scene-event-list">
									{
										policySetMap &&
										policySetMap.map((item, index) => {
											let eventList = hasSelectSceneObj.eventList;
											let Dom = (
												<li
													className="scene-event-item"
													key={index}
												>
													<Checkbox
														checked={!!(eventList && eventList.find(event => event === item.name))}
														onChange={(e) => {
															let checked = e.target.checked;
															this.changeSceneEvent(currentAppObj, item.name, checked);
														}}
														disabled={disabled}
													>
														{item.dName}
													</Checkbox>
												</li>
											);

											if (searchIng && searchWord) {
												if (currentAppObj.dName.indexOf(searchWord) > -1) {
													return Dom;
												} else if (item.dName && item.dName.indexOf(searchWord) > -1) {
													return Dom;
												}
											} else {
												return Dom;
											}
										})
									}
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}
