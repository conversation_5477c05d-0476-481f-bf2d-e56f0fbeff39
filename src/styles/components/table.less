:global {
	// 覆盖antd table原有的middle padding
	.ant-table-middle > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
	.ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
	.ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
	.ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
	.ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th,
	.ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
	.ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
	.ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
	.ant-table-middle > .ant-table-content > .ant-table-header > table > .ant-table-tbody > tr > td,
	.ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td,
	.ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-tbody > tr > td,
	.ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td,
	.ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-tbody > tr > td,
	.ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-tbody > tr > td,
	.ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-tbody > tr > td,
	.ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-tbody > tr > td {
		padding: 12px 16px !important;
	}

	.ant-table-small {
		table {
			.table-row {
				margin: -8px -8px -8px -58px;
			}
		}
	}
	table {
		.oper-list {
			a {
				margin-right: 12px;
			}
			.none-info {
				color: #999;
			}
		}
		.table-action {
			a {
				& {
					margin-right: 8px;
				}
				&:hover i {
					color: @blue;
				}
				&:hover i.delete {
					color: @red;
				}
				&.blue i {
					color: @blue;
				}
				i {
					font-size: 18px;
					color: #999;
				}
			}
		}
		.table-row {
			& {
				margin: -16px -8px -16px -58px;
			}
			.row {
				& {
					border-bottom: 1px solid #ebeef5;
					display: flex;
					padding: 9px 0;
					padding-left: 58px;
				}
				&:last-child {
					border-bottom: 0;
				}
				.row-item {
					padding-left: 8px;
				}
			}
			.none-data {
				& {
					text-align: center;
					padding: 20px 0;
				}
				i {
					font-size: 24px;
				}
				p {
					margin-bottom: 0;
				}
			}
		}
	}
	.table-out-border {
		border: 1px solid #e8e8e8;
		border-width: 1px 1px 0 1px;
	}
	.table-page-position {
		& {
			padding-top: 20px;
			padding-bottom: 20px;
			background-color: white;
		}
		.ant-pagination {
			text-align: center;
		}
	}
}
