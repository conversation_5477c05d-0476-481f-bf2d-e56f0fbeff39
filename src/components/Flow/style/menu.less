:global {
    .flow-editor-menu-wrap {
        & {
            display: none;
        }

        .menu {
            & {
                left: 30px;
                width: 200px;
                font-size: 13px;
                background: #fff;
                padding: 10px 0;
                box-shadow: 0 8px 10px 1px rgba(0, 0, 0, .14), 0 3px 14px 2px rgba(0, 0, 0, .12), 0 5px 5px -3px rgba(0, 0, 0, .2);
            }

            .command {
                & {
                    height: 36px;
                    line-height: 36px;
                    cursor: pointer;
                    color: rgba(0, 0, 0, .85);
                    padding: 0 20px;
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;
                }

                &.disable {
                    color: #999;
                    cursor: not-allowed;
                }

                &:hover {
                    background: #eee;
                }
            }

            hr {
                height: 1px;
                border: none;
                background: #dcdcdc;
                margin: 10px 0;
            }
        }
    }
}
