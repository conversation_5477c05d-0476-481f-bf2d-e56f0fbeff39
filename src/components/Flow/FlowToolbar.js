import { PureComponent, Fragment } from "react";
import { Too<PERSON><PERSON>, Divider, Icon, Alert } from "antd";
import { Toolbar, Command } from "gg-editor";
import "./style/toolbar.less";
import { workflowLang } from "@/constants/lang";

class FlowToolbar extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { disabled, status, isApprovalPage } = this.props;
		return (
			<Fragment>
				{
					status === "wait_review" &&
                    !isApprovalPage &&
                    <div className="toolbar-left">
                    	<Alert
                    		message={workflowLang.flowToolTip("alterMessage")}// lang:"执行流程处于待审批状态，无法修改。"
                    		type="error"
                    		showIcon
                    	/>
                    </div>
				}
				{/* 如果当前状态不是审批中，或者是在审批任务中查看的即显示 */}
				{
					(
						status !== "wait_review" ||
						isApprovalPage
					) &&
                    <Toolbar className="flow-toolbar">
                    	{
                    		!disabled &&
                            <Fragment>
                            	<Command name="undo">
                            		<Tooltip
                            			title={workflowLang.flowToolTip("revoke")}// lang:"撤销"
                            			placement="top"
                            			overlayClassName="tooltip"
                            		>
                            			<Icon type="undo" theme="outlined" />
                            		</Tooltip>
                            	</Command>
                            	<Command name="redo">
                            		<Tooltip
                            			title={workflowLang.flowToolTip("redo")}// lang:"重做"
                            			placement="top"
                            			overlayClassName="tooltip"
                            		>
                            			<Icon type="redo" theme="outlined" />
                            		</Tooltip>
                            	</Command>
                            	<Divider type="vertical" />
                            	<Command name="delete">
                            		<Tooltip
                            			title={workflowLang.flowToolTip("delete")}// lang:"删除"
                            			placement="top"
                            			overlayClassName="tooltip"
                            		>
                            			<Icon type="delete" theme="outlined" />
                            		</Tooltip>
                            	</Command>
                            	<Divider type="vertical" />
                            </Fragment>
                    	}
                    	<Command name="zoomIn">
                    		<Tooltip
                    			title={workflowLang.flowToolTip("enlarge")}// lang:"放大"
                    			placement="top"
                    			overlayClassName="tooltip"
                    		>
                    			<Icon type="zoom-in" theme="outlined" />
                    		</Tooltip>
                    	</Command>
                    	<Command name="zoomOut">
                    		<Tooltip
                    			title={workflowLang.flowToolTip("narrow")}// lang:"缩小"
                    			placement="top"
                    			overlayClassName="tooltip"
                    		>
                    			<Icon type="zoom-out" theme="outlined" />
                    		</Tooltip>
                    	</Command>
                    	<Command name="autoZoom">
                    		<Tooltip
                    			title={workflowLang.flowToolTip("adaptToCanvas")}// lang:"适应画布"
                    			placement="top"
                    			overlayClassName="tooltip"
                    		>
                    			<Icon type="fullscreen" theme="outlined" />
                    		</Tooltip>
                    	</Command>
                    	<Command name="resetZoom">
                    		<Tooltip
                    			title={workflowLang.flowToolTip("actualSize")}// lang:"实际尺寸"
                    			placement="top"
                    			overlayClassName="tooltip"
                    		>
                    			<Icon type="fullscreen-exit" theme="outlined" />
                    		</Tooltip>
                    	</Command>
                    	<Command name="selectAll">
                    		<Tooltip
                    			title={workflowLang.flowToolTip("allSelect")}// lang:"全选"
                    			placement="top"
                    			overlayClassName="tooltip"
                    		>
                    			<Icon type="select" theme="outlined" />
                    		</Tooltip>
                    	</Command>
                    </Toolbar>
				}
			</Fragment>
		);
	}
}

export default FlowToolbar;
