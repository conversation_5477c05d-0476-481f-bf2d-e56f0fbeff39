import { PureComponent } from "react";
import { connect } from "dva";
import ImportCommon from "./ImportCommon";
import { imExportModeLang } from "@/constants/lang";
import { policyEditorAPI } from "@/services";

class PolicySetImportModal extends PureComponent {
	constructor(props) {
		super(props);
	}
	// 监听导入字段输入
	changeImport = (e, fileName, type = "input") => {
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		let { policyEditorStore, dispatch } = this.props;
		let { dialogData } = policyEditorStore;
		let { policySetImportData } = dialogData;
		const newPolicySetImportData = { ...policySetImportData };

		newPolicySetImportData[fileName] = value;
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				policySetImportData: newPolicySetImportData
			}
		});
	}

	closeModal = () => {
		let { dispatch } = this.props;
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policySetImport: false
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				policySetImportData: {
					uuid: null,
					importMode: null,
					file: null,
					replaceScene: "",
					zbMode: "",
					policyMode: ""
				}
			}
		});
	}
	importCallback = () => {
		let { policyEditorStore, globalStore, dispatch } = this.props;
		let { curPage, pageSize } = policyEditorStore;
		// 导入成功后
		// 1. 重新刷新编辑区策略列表
		let { currentApp } = globalStore;
		if (currentApp.name) {
			dispatch({
				type: "policyEditor/getPolicySets",
				payload: {
					appName: currentApp.name ? currentApp.name : null,
					curPage: curPage,
					pageSize: pageSize
				}
			});
		}
	}
	render() {
		const { policyEditorStore } = this.props;
		const { dialogShow, dialogData } = policyEditorStore;
		const { policySetImportData } = dialogData;
		return (
			<ImportCommon
				title={imExportModeLang.importPolicyModal("policyImport")} // 策略导入
				modalData={policySetImportData}
				importType="policySet"
				visible={dialogShow.policySetImport}
				changeField={this.changeImport.bind(this)}
				onCancel={this.closeModal.bind(this)}
				importCallback={this.importCallback.bind(this)}
				fetchMethod={policyEditorAPI.importPolicySets}
			/>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(PolicySetImportModal);
