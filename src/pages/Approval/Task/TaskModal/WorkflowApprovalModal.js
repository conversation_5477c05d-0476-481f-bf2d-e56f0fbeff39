import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, message } from "antd";
import { policyDetailAPI } from "@/services";
import { approvalTaskLang } from "@/constants/lang";
import WorkflowApprovalBase from "./Inner/WorkflowApprovalBase";
import WorkflowApprovalDiff from "./Inner/WorkflowApprovalDiff";
import "./Less/ApprovalModal.less";

class WorkflowApprovalModal extends PureComponent {

    state = {
    	desc: null
    };

    constructor(props) {
    	super(props);
    	this.workflowCommit = this.workflowCommit.bind(this);
    	this.changeLeftNav = this.changeLeftNav.bind(this);
    }

    workflowCommit() {
    	let { policyDetailStore, globalStore, dispatch } = this.props;
    	let { policyDetail } = policyDetailStore;
    	let { userInfoMode } = globalStore;
    	let desc = this.state.desc;
    	if (!desc) {
    		// lang:请输入审批描述
    		message.warning(approvalTaskLang.modal("enterDescriptionPlaceholder"));
    		return;
    	}
    	let params = {
    		account: userInfoMode.account,
    		policyUuid: policyDetail.uuid,
    		desc: desc
    	};

    	policyDetailAPI.policyCommit(params).then(res => {
    		if (res.success) {
    			// lang:策略版本提交成功
    			message.success(approvalTaskLang.modal("policyVersionSubmitOk"));
    			dispatch({
    				type: "policyDetail/setDialogShow",
    				payload: {
    					policyCommit: false
    				}
    			});
    			this.setState({
    				desc: null
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    changeLeftNav(index) {
    	let { approvalTaskStore, dispatch } = this.props;
    	let { dialogData } = approvalTaskStore;
    	let { workflowApprovalData } = dialogData;

    	workflowApprovalData.leftNavIndex = index;
    	dispatch({
    		type: "approvalTask/setDialogData",
    		payload: {
    			workflowApprovalData: workflowApprovalData
    		}
    	});
    }

    render() {
    	let { approvalTaskStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = approvalTaskStore;
    	let { workflowApproval } = dialogShow;
    	let { workflowApprovalData } = dialogData;
    	let { leftNavIndex } = workflowApprovalData;

    	return (
    		<Modal
    			title={approvalTaskLang.modal("workflowTitle")} // lang:策略版本审核
    			visible={workflowApproval}
    			className="approval-modal-wrap"
    			maskClosable={true}
    			width={1180}
    			onOk={this.workflowCommit.bind(this)}
    			onCancel={() => {
    				dispatch({
    					type: "approvalTask/setDialogShow",
    					payload: {
    						workflowApproval: false
    					}
    				});
    				setTimeout(() => {
    					dispatch({
    						type: "approvalTask/setDialogData",
    						payload: {
    							workflowApprovalData: {
    								uuid: null,
    								status: "APPROVED",
    								desc: null,
    								leftNavIndex: 0,
    								tabIndex: 0,
    								diffTime: "online",
    								diffDetail: {
    									onlineVersion: null,
    									pendVersion: null
    								}
    							}
    						}
    					});
    				}, 300);
    			}}
    			footer={null}
    		>
    			<div className="approval-wrap">
    				<div className="left-nav">
    					<ul>
    						<li
    							className={leftNavIndex === 0 ? "active" : ""}
    							onClick={this.changeLeftNav.bind(this, 0)}
    						>
    							{/* lang:规则流审批 */}
    							{approvalTaskLang.modal("workflowApproval")}
    						</li>
    						<li
    							className={leftNavIndex === 1 ? "active" : ""}
    							onClick={this.changeLeftNav.bind(this, 1)}
    						>
    							{/* lang:修改详情 */}
    							{approvalTaskLang.modal("changeDetail")}
    						</li>
    					</ul>
    				</div>
    				<div className="right-content">
    					{
    						leftNavIndex === 0 && <WorkflowApprovalBase />
    					}
    					{
    						leftNavIndex === 1 && <WorkflowApprovalDiff />
    					}
    				</div>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(WorkflowApprovalModal);
