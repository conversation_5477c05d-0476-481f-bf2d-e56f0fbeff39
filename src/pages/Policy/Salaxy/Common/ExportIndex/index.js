import { PureComponent } from "react";
import { message, Button } from "antd";
import { connect } from "dva";
import { indexEditorAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";
import { indexListLang, commonLang } from "@/constants/lang";

class ExportIndex extends PureComponent {
    state = {
    	exportLoading: false
    }
    // 导出指标列表
    exportIndexList = async() => {
    	const { checkedList, isEditorArea } = this.props;
    	const idList = [];
    	checkedList.map((item) => {
    		idList.push(item.id);
    	});
    	const ids = idList.join(",");
    	const timestamp = Date.parse(new Date());
    	const params = {
    		ids: ids,
    		fileName: indexListLang.operator("exportPrefixText") + timestamp,
    		fileType: "zb",
    		isEditorArea
    	};
    	this.setState({
    		exportLoading: true
    	});
    	const testResult = await indexEditorAPI.exportIndexListTest(params);
    	this.setState({
    		exportLoading: false
    	});
    	if (testResult && testResult.hasOwnProperty("success") && !testResult.success) {
    		message.warning(testResult.message);
    	} else {
    		indexEditorAPI.exportIndexList(params).then(res => {
    			if (res.success) {
    				message.success(indexListLang.operator("exportIndexSuccess")); // lang:导出所选指标列表成功
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	}
    }
    render() {
    	const { disabled } = this.props;
    	const { exportLoading } = this.state;
    	return (
    		<Button
    			disabled={disabled || exportLoading}
    			loading={exportLoading}
    			onClick={() => {
    				if (checkFunctionHasPermission("ZB0102", "editorExport")) {
    					// 已经有权限了
    					this.exportIndexList();
    				} else {
    					// lang:无权限操作
    					message.warning(commonLang.messageInfo("noPermission"));
    				}
    			}}
    		>
    			{indexListLang.common("export")}
    		</Button>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global
}))(ExportIndex);
