import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getDealTypes = async(params) => {
	return request(getUrl("/admin/dealTypes", params), {
		method: "GET",
		headers: getHeader()
	});
};

const addDealType = async(params) => {
	return request("/admin/dealTypes", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyDealType = async(params) => {
	return request("/admin/dealTypes/detail", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const deleteDealType = async(params) => {
	return request(getUrl("/admin/dealTypes/single", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

const getTriggers = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/dealTypeTriggers", params), {
		method: "GET",
		headers: getHeader()
	});
};

const verifyTriggerExist = async(params) => {
	return request(getUrl("/admin/dealTypeTriggers/existVerify", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getCalendarDetail = async(params) => {
	return request(getUrl("/admin/dealTypeTriggers/times/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getTriggerFieldDetail = async(params) => {
	return request(getUrl("/admin/dealTypeTriggers/fields/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};

const addTriggerTimeEvent = async(params) => {
	return request("/admin/dealTypeTriggers/times/single", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const addBatchTriggerTimeEvent = async(params) => {
	return request("/admin/dealTypeTriggers/times/batch", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyTriggerTimeEvent = async(params) => {
	return request("/admin/dealTypeTriggers/times/detail", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const addTriggerField = async(params) => {
	return request("/admin/dealTypeTriggers/fields", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyTriggerField = async(params) => {
	return request("/admin/dealTypeTriggers/fields/detail", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const getTimeQuickType = async(params) => {
	return request(getUrl("/admin/dealTypeTriggers/quickType", params), {
		method: "GET",
		headers: getHeader()
	});
};

const deleteTriggerTimeEvent = async(params) => {
	return request(getUrl("/admin/dealTypeTriggers/times/single", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

const getImmunos = async(params) => {
	return request(getUrl("/admin/dealTypeImmunos", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getImmunoDetail = async(params) => {
	return request(getUrl("/admin/dealTypeImmunos/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};

const addImmuno = async(params) => {
	return request("/admin/dealTypeImmunos", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyImmuno = async(params) => {
	return request("/admin/dealTypeImmunos/detail", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const deleteImmuno = async(params) => {
	return request(getUrl("/admin/dealTypeImmunos/single", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

const deleteTrigger = async(params) => {
	return request(getUrl("/admin/dealTypeTriggers/single", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

// 获取app应用下的策略集
const policySetsByApp = async(params) => {
	return request(getUrl("/admin/policySets/byApp", params), {
		method: "GET",
		headers: getHeader()
	});
};

export default {
	getTriggers,
	getDealTypes,
	addDealType,
	modifyDealType,
	deleteDealType,
	getImmunos,
	getImmunoDetail,
	addImmuno,
	modifyImmuno,
	deleteImmuno,
	verifyTriggerExist,
	getTriggerFieldDetail,
	getTimeQuickType,
	addTriggerTimeEvent,
	addBatchTriggerTimeEvent,
	modifyTriggerTimeEvent,
	deleteTriggerTimeEvent,
	addTriggerField,
	modifyTriggerField,
	getCalendarDetail,
	deleteTrigger,
	policySetsByApp
};
