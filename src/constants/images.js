import store from "../app";

// 首页图片
const mainImages = {
	"logo": require("../sources/images/common/zhibiaoLogo.svg")
};

// 规则流功能图片
const workflowImages = {
	"empty": require("../sources/images/workflow/workflow-empty.svg")
};

// 报告功能图片
const policyImages = {
	"noneRuleTip": require("../sources/images/policy/noneRuleTip.png")
};

// 工作流图片
const flowImages = (field)=>{
	const globalStore = store.getState().global;
	const { personalMode } = globalStore;
	const lang = personalMode.lang === "cn" ? "cn" : "en";
	const params = {
		"exclusivity": {
			cn: require("../sources/images/flow/flow-exclusivity.svg"),
			en: require("../sources/images/flow/flow-exclusivity-en.svg")
		},
		"parallel": {
			cn: require("../sources/images/flow/flow-parallel.svg"),
			en: require("../sources/images/flow/flow-parallel-en.svg")
		}
	};
	return params[field][lang];
};

// 主题图片
export const themeImages = {
	"themeSymbol1": require("../sources/images/theme/symbol/theme1.svg"),
	"themeSymbol2": require("../sources/images/theme/symbol/theme2.svg")
};

export default {
	mainImages,
	workflowImages,
	policyImages,
	flowImages
};
