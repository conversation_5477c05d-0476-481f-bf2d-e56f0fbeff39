import React from "react";
import { connect } from "dva";
import { Drawer, Row, Col, Input, Icon, message, Alert, Tag } from "antd";
import "./PolicyTestDrawer.less";
import { CommonConstants, PolicyConstants } from "@/constants";
import { debugLang } from "@/constants/lang";

const TextArea = Input.TextArea;

class PolicyTestDrawerChild extends React.PureComponent {

	constructor(props) {
		super(props);
		this.closeDrawerHandle = this.closeDrawerHandle.bind(this);
		this.deleteResult = this.deleteResult.bind(this);
	}

	closeDrawerHandle() {
		let { dispatch } = this.props;
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policyDrawer: false
			}
		});
	}

	deleteResult(index) {
		let { policyEditorStore, dispatch } = this.props;
		let { dialogData } = policyEditorStore;
		let { resultList } = dialogData.policyTestDrawer;

		resultList.splice(index, 1);
		// lang:移除结果成功
		message.success(debugLang.message("removeResultSuccess"));
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				resultList: resultList
			}
		});
	}

	render() {
		let { policyEditorStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyEditorStore;
		let { policyTestDrawer } = dialogData;
		let { resultList } = policyTestDrawer;

		let totalTimes = 0;
		resultList.map((item) => {
			totalTimes += item.spendTime;
		});

		return (
			<Drawer
				title="Two"
				width={650}
				closable={false}
				className="policy-test-wrap"
				onClose={() => {
					dispatch({
						type: "policyEditor/setDialogShow",
						payload: {
							policyTestDrawerChild: false
						}
					});
					policyTestDrawer.resultList = [];
					dispatch({
						type: "policyEditor/setDialogData",
						payload: {
							policyTestDrawer: policyTestDrawer
						}
					});
				}}
				visible={dialogShow.policyTestDrawerChild}
			>
				<div className="policy-test-main">
					<div className="policy-test-header">
						<i className="iconfont icon-spider title-spider"></i>
						<span className="policy-detail-title-text">
							{/* lang:测试结果 */}
							{debugLang.result("debugResult")}
						</span>
					</div>
					<div className="policy-test-body child-drawer-body">
						<div className="basic-form">
							{
								resultList.length
								// lang:接口执行共1次，引擎执行耗时：5毫秒
									? <Alert
										message={debugLang.result("resultMsg1") + resultList.length + debugLang.result("resultMsg2") + totalTimes + debugLang.result("resultMsg3")}
										type="success"
										showIcon
									/> : ""
							}
							{
								resultList.map((item, index) => {
									let hitLen = 0;
									item.policySet && item.policySet.map(psItem => {
										if (psItem.hitRules && psItem.hitRules.length > 0) {
											hitLen += 1;
										}
									});

									return (
										<div className="result-item-wrap">
											<div
												className="result-delete"
												onClick={this.deleteResult.bind(this, index)}>
												<Icon type="delete" />
											</div>
											<Row gutter={CommonConstants.gutterSpan}>
												<Col span={6} className="basic-info-title">SEQID：</Col>
												<Col span={18}>
													<Input
														type="text"
														value={item.seqId || undefined}
														disabled={true}
													/>
												</Col>
											</Row>
											<Row gutter={CommonConstants.gutterSpan}>
												<Col span={6} className="basic-info-title">
													{/* lang:调用成功*/}
													{debugLang.result("callSuccess")}：
												</Col>
												<Col span={18}>
													<Input
														type="text"
														value={item.success || undefined}
														disabled={true}
													/>
												</Col>
											</Row>
											<Row gutter={CommonConstants.gutterSpan}>
												<Col span={6} className="basic-info-title">
													{/* lang:风险决策结果 */}
													{debugLang.result("riskDecisionResult")}：
												</Col>
												<Col span={18}>
													<Input
														type="text"
														value={item.finalDealTypeName || undefined}
														disabled={true}
													/>
												</Col>
											</Row>
											<Row gutter={CommonConstants.gutterSpan}>
												<Col span={6} className="basic-info-title">
													{/* lang:风险分数*/}
													{debugLang.result("riskScore")}：
												</Col>
												<Col span={18}>
													<Input
														type="text"
														value={(item.finalScore || item.finalScore === 0) ? item.finalScore.toString() : undefined}
														disabled={true}
													/>
												</Col>
											</Row>
											<Row gutter={CommonConstants.gutterSpan}>
												<Col span={6} className="basic-info-title">
													{/* lang:请求用时 */}
													{debugLang.result("requestTime")}：</Col>
												<Col span={18}>
													<Input
														type="text"
														value={(item.spendTime || item.spendTime === 0) ? item.spendTime.toString() : undefined}
														disabled={true}
														addonAfter="ms"
													/>
												</Col>
											</Row>
											{/* <Row gutter={CommonConstants.gutterSpan}>*/}
											{/* <Col span={6} className="basic-info-title">*/}
											{/* /!* lang:错误代码 *!/*/}
											{/* {debugLang.result("errorCode")}*/}
											{/* </Col>*/}
											{/* <Col span={18}>*/}
											{/* <Input*/}
											{/* type="text"*/}
											{/* disabled={true}*/}
											{/* />*/}
											{/* </Col>*/}
											{/* </Row>*/}
											{
												item.flowExecutePath &&
                                                <Row
                                                	gutter={CommonConstants.gutterSpan}
                                                	style={{
                                                		marginBottom: "10px",
                                                		height: "auto",
                                                		overflow: "hidden"
                                                	}}
                                                >
                                                	<Col span={6} className="basic-info-title">
                                                		{/* lang:流程执行顺序 */}
                                                		{debugLang.result("flowExecutePath")}
                                                	</Col>
                                                	<Col span={18}>
                                                		<TextArea
                                                			rows={2}
                                                			disabled={true}
                                                			value={item.flowExecutePath}
                                                		/>
                                                	</Col>
                                                </Row>
											}
											<Row gutter={CommonConstants.gutterSpan}>
												<Col span={6} className="basic-info-title">
													{/* lang:命中列表 */}
													{debugLang.result("hitList")}：
												</Col>
												<Col span={18}>
													{
														hitLen === 0 &&
                                                        <div className="hit-policy-list">暂无命中结果</div>
													}
													{
														item.policySet &&
                                                        hitLen !== 0 &&
                                                        item.policySet.map((pItem, pIndex) => {
                                                        	if (pItem["hitRules"] && pItem["hitRules"].length) {
                                                        		return (
                                                        			<div
                                                        				className="hit-policy-list"
                                                        				key={pIndex}
                                                        			>
                                                        				<div className="hit-policy-header">
                                                        					<h3>
                                                        						<span
                                                        							className="text-overflow"
                                                        							style={{
                                                        								maxWidth: "180px",
                                                        								display: "inline-block",
                                                        								verticalAlign: "bottom"
                                                        							}}
                                                        						>
                                                        							{pItem.policyName}
                                                        						</span>
                                                        						<Tag color="orange">
                                                        							{pItem["policyScore"]}
                                                        						</Tag>
                                                        						<Tag color="blue">
                                                        							{
                                                        								PolicyConstants.policyMode[pItem.policyMode]
                                                        							}
                                                        						</Tag>
                                                        					</h3>
                                                        				</div>
                                                        				<div className="hit-policy-body">
                                                        					<ul className="hit-rule-list">
                                                        						{
                                                        							pItem["hitRules"] && pItem["hitRules"].map((rItem, rIndex) => {
                                                        								return (
                                                        									<li
                                                        										className="hit-rule-item"
                                                        										key={rIndex}
                                                        									>
                                                        										{/* lang:规则 */}
                                                        										<span className="is-rule-tip">
                                                                                                    [{debugLang.result("rule")}]
                                                        										</span>
                                                        										<span
                                                        											className="rule-name text-overflow"
                                                        											style={{
                                                        												maxWidth: "180px",
                                                        												display: "inline-block",
                                                        												verticalAlign: "bottom"
                                                        											}}
                                                        										>
                                                        											{rItem.name}
                                                        										</span>
                                                        										<div
                                                        											className="rule-attr">
                                                        											<Tag color="purple">
                                                        												{rItem.score}
                                                        											</Tag>
                                                        										</div>
                                                        									</li>
                                                        								);
                                                        							})
                                                        						}
                                                        					</ul>
                                                        				</div>
                                                        			</div>
                                                        		);
                                                        	}
                                                        })
													}
												</Col>
											</Row>

											<Row gutter={CommonConstants.gutterSpan}>
												<Col span={6} className="basic-info-title">
													{/* lang:原始内容 */}
													{debugLang.result("originalContent")}：
												</Col>
												<Col span={18}>
													<TextArea
														rows={10}
														disabled={true}
														value={item ? JSON.stringify(item, null, 4) : "{}"}
													/>
												</Col>
											</Row>
										</div>
									);
								})
							}
						</div>
					</div>

				</div>
			</Drawer>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(PolicyTestDrawerChild);
