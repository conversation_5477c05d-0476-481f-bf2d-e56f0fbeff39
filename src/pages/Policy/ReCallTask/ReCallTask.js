import React, { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Table, Pagination, Form, message, Select, Button, Row, Col, Input, Tag, Popover, Progress, Modal, Tooltip, Icon } from "antd";
import { reCallTaskAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";
import { commonLang, reCallTaskLang } from "@/constants/lang";
import NoPermission from "@/components/NoPermission";
import { searchToObject } from "@/utils/utils";
import { ReCallTaskConstants } from "@/constants";
import "./ReCallTask.less";

const SearchResultModal = React.lazy(() => import("./Modal/SearchResultModal"));
const DealResultModal = React.lazy(() => import("./Modal/DealResultModal"));
const { confirm } = Modal;
class ReCallTask extends PureComponent {
	constructor(props) {
		super(props);
		this.reCallInterval = null;
	}
	// 初始化
	componentDidMount() {
    	this.timer = setInterval(() => {
    		const { globalStore, reCallTaskStore, location, dispatch } = this.props;
    		const { menuTreeReady } = globalStore;
    		const { search } = location;
    		const { zbUuid } = searchToObject(search);
    		if (menuTreeReady) {
    			clearInterval(this.timer);
    			if (checkFunctionHasPermission("ZB0107", "ListZBFillBack")) {
    				const { searchParams } = reCallTaskStore;
    				dispatch({
    					type: "reCallTask/setAttrValue",
    					payload: {
    						searchParams: {
    							...searchParams,
    							zbUuid: zbUuid || ""
    						}
    					}
    				});
    				this.initSearch();
    			}
    		}
    	}, 100);
	}

	// 切换应用重新发起请求
	async componentWillReceiveProps(nextProps) {
    	let { reCallTaskStore, dispatch, globalStore } = this.props;
    	const { menuTreeReady } = globalStore;
    	if (menuTreeReady && checkFunctionHasPermission("ZB0107", "ListZBFillBack")) {
    		let preCurrentApp = this.props.globalStore.currentApp;
    		let nextCurrentApp = nextProps.globalStore.currentApp;
    		if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
    			// 检测到切换应用，开始刷新列表
    			// 首先清空搜索项目
    			let { searchParams } = reCallTaskStore;
    			await dispatch({
    				type: "reCallTask/setAttrValue",
    				payload: searchParams
				});
    			await this.initSearch();
    		}
    	}
	}

	// 当前页为1的查询请求
	initSearch() {
    	const { reCallTaskStore, globalStore, dispatch } = this.props;
    	const { searchParams } = reCallTaskStore;
		const { currentApp } = globalStore;
		this.reCallInterval && clearInterval(this.reCallInterval);
    	dispatch({
    		type: "reCallTask/getReCallList",
    		payload: {
    			...searchParams,
    			curPage: 1,
    			appName: currentApp && currentApp.name ? currentApp.name : null
    		}
    	});
    	this.startSearch();
	}

	// 开始轮询的请求
	startSearch() {
		clearInterval(this.reCallInterval);
    	this.reCallInterval = setInterval(() => {
			const { reCallTaskStore, globalStore, dispatch } = this.props;
			const { searchParams } = reCallTaskStore;
			const { currentApp } = globalStore;
    			dispatch({
    				type: "reCallTask/getReCallList",
    				payload: {
    					...searchParams,
    					appName: currentApp && currentApp.name ? currentApp.name : null
    				}
    			});
    		}, 5000);
	}

	componentWillUnmount() {
    	// 销毁页面做的事情
    	let { reCallInterval } = this;
    	clearInterval(reCallInterval);
    	reCallInterval = null;
	}

	// 翻页
	async changePagination(current, pageSize) {
    	const { reCallTaskStore, globalStore, dispatch } = this.props;
    	const { searchParams } = reCallTaskStore;
    	const { currentApp } = globalStore;

    	searchParams["curPage"] = current;
    	searchParams["pageSize"] = pageSize;
    	await dispatch({
    		type: "reCallTask/setAttrValue",
    		payload: searchParams
    	});
    	await dispatch({
    		type: "reCallTask/getReCallList",
    		payload: {
    			...searchParams,
    			appName: currentApp && currentApp.name ? currentApp.name : null
    		}
    	});
	}

	// 清空
	resetSearch() {
    	const { reCallTaskStore, dispatch, history } = this.props;
		const { searchParams } = reCallTaskStore;
		dispatch({
    		type: "reCallTask/setAttrValue",
    		payload: {
				searchParams: {
					...searchParams,
					zbUuid: null,
					zbName: null,
					status: null,
					curPage: 1
				},
				filterOption: {
					zbName: null,
					status: null
				}
    		}
		});
    	// 如果有指标id 清空的时候则删除url中的参数 恢复全选查询
    	if (searchParams["zbUuid"]) {
    		const { location } = history;
    		history.replace(null, null, location.pathname);
		}
		this.initSearch();
	}

	// 点击查询
	btnSearch = () => {
		const { reCallTaskStore, dispatch, location } = this.props;
		const { searchParams, filterOption } = reCallTaskStore;
		const { search } = location;
		const { zbUuid } = searchToObject(search);
		dispatch({
    		type: "reCallTask/setAttrValue",
    		payload: {
    			searchParams: {
					...searchParams,
					...filterOption,
					zbUuid,
					curPage: 1
				}
    		}
    	});
		this.initSearch();
	}

	// 修改查询条件
	changeParamValue(field, type, e) {
		const { reCallTaskStore, dispatch, history } = this.props;

		// 如果有指标id且修改了指标名
		const { searchParams } = reCallTaskStore;
		if (searchParams["zbUuid"] && field === "zbName") {
    		const { location } = history;
			history.replace(null, null, location.pathname);
		}

    	let value;
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}

		const { filterOption } = reCallTaskStore;
		dispatch({
			type: "reCallTask/setAttrValue",
			payload: {
				filterOption: {
					...filterOption,
					[field]: value
				}
			}
		});
	}

	// 终止任务
	terminateReCall(item) {
    	let params = {
    		zbFillBackUuid: item.uuid
    	};
    	confirm({
    		title: reCallTaskLang.table("terminateTaskTip"), // lang: 终止回溯任务提醒
    		content: reCallTaskLang.table("confirmTerminateTaskTip"), // lang: 确认终止回溯任务
    		onOk: ()=> {
    			return reCallTaskAPI.terminateReCall(params).then(res => {
    				if (res.success) {
    					message.success(res.message || reCallTaskLang.table("terminateTaskSuc"));
    					this.initSearch();
    				} else {
    					message.error(res.message);
    				}
    			}).catch(err => {
    				console.log(err);
    			});
    		},
    		onCancel() {}
    	});
	}

	// 生效/丢弃
	dealResult = (item, resultStatus)=>{
		const { dispatch } = this.props;
		dispatch({
			type: "reCallTask/setDialogData",
			payload: {
				dealResultData: {
					resultStatus,
					currentReCall: item
				}
			}
		});
		dispatch({
			type: "reCallTask/setDialogShow",
			payload: {
				dealResultModal: true
			}
		});
	}

	// 查询结果
	searchResult = (item) => {
		const {zbUuid, uuid, zbName} = item;
		const {dispatch} = this.props;
		dispatch({
			type: "reCallTask/viewIndexItemDetail",
			payload: {
				zbUuid,
				zbFillBackUuid: uuid,
				zbName
			}
		});
	}

	render() {
    	const { reCallTaskStore, globalStore } = this.props;
    	const { personalMode, menuTreeReady } = globalStore;
    	const { searchParams, total, reCallList, filterOption } = reCallTaskStore;
		const { zbName, status } = filterOption;
    	const lang = personalMode.lang === "cn" ? "cn" : "en";

    	const columns = [
    		{
    			title: reCallTaskLang.table("zbName"), // lang: 指标名称
    			dataIndex: "zbName",
    			ellipsis: true,
    			width: 130,
    			render: (zbName)=>{
					return (
						<Tooltip title={zbName}>
							<span>
								{zbName}
							</span>
						</Tooltip>
					);
    			}
    		},
    		{
    			title: reCallTaskLang.table("status"), // lang: 回溯状态
    			dataIndex: "status",
    			width: 100,
    			render: (text)=>{
    				const { reCallStatusMap } = ReCallTaskConstants;
    				if (reCallStatusMap.hasOwnProperty(text)) {
    					const statusObj = reCallStatusMap[text] || {};
    					return <Tag color={statusObj.color}>{lang === "cn" ? statusObj.dName : statusObj.enDName}</Tag>;
    				}
    			}
    		},
    		{
    			title: reCallTaskLang.table("progress"), // lang: 回溯进度
    			dataIndex: "completePercent",
    			width: 150,
    			render: (completePercent)=>{
    				return (
    					<Popover
    						content={
    							<div>
    								{/* lang:已完成 */}
									{reCallTaskLang.table("completed")}
									{completePercent || 0}%
    							</div>
    						}
    						title={ reCallTaskLang.table("progress") } // lang: 回溯进度
    					>
    						<Progress
    							strokeColor={{
    								"0%": "#108ee9",
    								"100%": "#87d068"
    							}}
    							percent={completePercent}
    							status="active"
    						/>
    					</Popover>
    				);
    			}
    		},
    		{
    			title: reCallTaskLang.table("taskCreateTime"), // lang: 任务创建时间
    			dataIndex: "gmtCreate",
    			width: 140
    		},
    		{
    			title: reCallTaskLang.table("taskStartTime"), // lang: 执行开始时间
    			dataIndex: "taskStartAt",
    			width: 140
    		},
    		{
    			title: reCallTaskLang.table("taskEndTime"), // lang: 执行结束时间
    			dataIndex: "taskEndAt",
    			width: 140
    		},
    		{
    			title: reCallTaskLang.table("effectStatus"), // lang: 生效状态
    			dataIndex: "resultStatus",
    			width: 100,
    			render: (text)=>{
    				const { resMap } = ReCallTaskConstants;
    				if (resMap.hasOwnProperty(text)) {
    					const resultStatusObj = resMap[text] || {};
    					return <Tag color={resultStatusObj.color}>{lang === "cn" ? resultStatusObj.dName : resultStatusObj.enDName}</Tag>;
    				}
    			}
    		},
    		{
    			title: reCallTaskLang.table("operation"), // lang: 操作
    			width: 120,
    			fixed: "right",
    			render: (record) => {
    				const { status, resultStatus } = record;
    				return (
    					<div className="table-action">
    						{/* 终止 */}
    						{/* 待运行，运行中 可进行终止操作 */}
    						{
    							(
									["PEND_RUN", "RUNNING"].indexOf(status) > -1 &&
									checkFunctionHasPermission("ZB0107", "TerminateZBFillBack")
								) &&
								<Tooltip title={reCallTaskLang.table("terminateTask")}>
									<a onClick={this.terminateReCall.bind(this, record)}>
										<Icon type="stop" />
									</a>
								</Tooltip>
    						}
    						{/* 生效 */}
    						{/* 执行成功 可进行生效操作 */}
    						{
    							["COMPLETED"].indexOf(status) > -1 &&
								resultStatus === "PEND" &&
                                checkFunctionHasPermission("ZB0107", "ProcessZBFillBackResult") &&
                                <Tooltip title={reCallTaskLang.table("takeEffect")}>
                                	<a onClick={this.dealResult.bind(this, record, "APPLIED")}>
                                		<Icon type="alert" />
                                	</a>
                                </Tooltip>
    						}
    						{/* 废弃 */}
    						{/* 执行成功 可进行废弃操作 */}
    						{
    							["COMPLETED"].indexOf(status) > -1 &&
								resultStatus === "PEND" &&
								checkFunctionHasPermission("ZB0107", "ProcessZBFillBackResult") &&
								<Tooltip title={reCallTaskLang.table("discard")}>
									<a onClick={this.dealResult.bind(this, record, "DISCARD")}>
										<Icon type="disconnect" />
									</a>
								</Tooltip>
    						}
							{/* 运行完成生效状态为非运用和丢弃状态 可查询指标处理结果 */}
    						{
								["COMPLETED"].indexOf(status) > -1 &&
								!(["APPLIED", "DISCARD"].indexOf(resultStatus) > -1) &&
								checkFunctionHasPermission("ZB0107", "QueryZBFillBackResult") &&
								<Tooltip title={reCallTaskLang.table("search")}>
									<a onClick={this.searchResult.bind(this, record)}>
										<Icon type="eye" />
									</a>
								</Tooltip>
    						}
    					</div>
    				);
    			}
    		}
    	];

    	return (
    		<Fragment>
				<div className="page-global-header">
					<div className="left-info">
    					<h2>
    						{/* lang:回溯任务列表 */}
    						{reCallTaskLang.common("title")}
    					</h2>
    				</div>
				</div>
    			{
    				menuTreeReady &&
                    <div className="page-global-body">
                    	{
                    		checkFunctionHasPermission("ZB0107", "ListZBFillBack") &&
							<Form>
								<Row gutter={10} className="mb10">
									<Col span={5}>
										<Input
											placeholder={reCallTaskLang.searchParams("zbName")} // lang:请输入指标名称
											className="wid-100-percent"
											onChange={this.changeParamValue.bind(this, "zbName", "input")}
											value={zbName || undefined}
											onPressEnter={this.btnSearch}
										/>
									</Col>
									<Col span={5}>
										<Select
											placeholder={reCallTaskLang.searchParams("backtrackStatus")} // lang:请选择回溯状态
											className="wid-100-percent"
											value={status || undefined}
											onChange={this.changeParamValue.bind(this, "status", "select")}
											allowClear
										>
											{
												Object.values(ReCallTaskConstants.reCallStatusMap).map(v=>{
													return (
														<Option value={v.value} key={v.value}>
															{lang === "cn" ? v.dName : v.enDName}
														</Option>
													);
												})
											}
										</Select>
									</Col>
									<Col span={5} className="fr">
										<Button
											className="mr10"
											onClick={this.resetSearch.bind(this)}
										>
											{/* 清空 */}
											{reCallTaskLang.searchParams("clear")}
										</Button>
										<Button
											type="primary"
											onClick={this.btnSearch}
										>
											{/* 搜索 */}
											{reCallTaskLang.searchParams("search")}
										</Button>
									</Col>
								</Row>
							</Form>
                    	}
                    	{
                    		checkFunctionHasPermission("ZB0107", "ListZBFillBack")
                    			? <div className="page-global-body-main has-table-border">
                    				<Table
                    					rowClassName="recall-table"
                    					columns={columns}
                    					pagination={false}
                    					dataSource={reCallList}
                    					rowKey="id"
                    				/>
                    				<div className="page-global-body-pagination">
                    					<span className="count">
                    						{commonLang.getRecords(total)}
                    					</span>
                    					<Pagination
                    						showSizeChanger
                    						onChange={this.changePagination.bind(this)}
                    						onShowSizeChange={this.changePagination.bind(this)}
                    						defaultCurrent={1}
                    						total={total}
                    						current={searchParams.curPage || 1}
                    					/>
                    				</div>
                    			</div>
                    			: <NoPermission />
                    	}
                    </div>
    			}
				<React.Suspense fallback={null}>
					<SearchResultModal />
				</React.Suspense>
				<React.Suspense fallback={null}>
					<DealResultModal initSearch={this.initSearch}/>
				</React.Suspense>
    		</Fragment>
    	);
	}
}
export default connect(state => ({
	globalStore: state.global,
	reCallTaskStore: state.reCallTask
}))(ReCallTask);
