import { PureComponent } from "react";
import { Input } from "antd";
import { connect } from "dva";
// import FormulaEdit from "formula-edit-react";
import FormulaEdit from "@/components/FormulaEdit";
import { commonLang } from "@/constants/lang";
import "./FormulaScriptEditor.less";
import { filterAvailableFieldList } from "@/utils/filterFieldList";

const { TextArea } = Input;

class FormulaEditor extends PureComponent {

	state = {
		defaultCnCode: "",
		originCode: ""
	};

	constructor(props) {
		super(props);
	}

	componentWillMount() {
		let { value } = this.props;
		let formulaCode = "";
		if (value) {
			formulaCode = value;
		}
		let cnCode = this.enCodeToCn(value || "");
		this.setState({
			defaultCnCode: cnCode,
			originCode: formulaCode
		});
	}

	getCode = (code) => {
		code = code.replace(/\[.*?\]/g, "");
		let { globalStore, onChange } = this.props;
		let { allMap } = globalStore;
		let { ruleAndIndexFieldList = [] } = allMap;
		let newFieldList = [];
		let keywords = [];
		ruleAndIndexFieldList &&
			ruleAndIndexFieldList.length > 0 &&
			ruleAndIndexFieldList.forEach(item => {
				if (item.name && item.dName) {
					keywords.push(`@${item.dName}`);
					newFieldList.push({
						name: `@${item.dName}`,
						value: item.name,
						type: item.type
					});
				}
			});

		const invalid = /[°"\(\)\[\]{}=\\?´`'<>,;.:\+]{1,1}/g; // 对字符串中的特殊字符进行转义
		keywords = keywords.sort((a, b) => b.length - a.length);

		// 正则替换关键词
		let newCode = code.replace(
			new RegExp(`(${keywords.join("|")})`.replace(invalid, (v) => { return "\\" + v; }), "g"),
			(match) => {
				let turnStr = match;
				newFieldList.forEach(item => {
					if (item.name === match) {
						turnStr = `@${item.value}`;
					}
				});
				return turnStr;
			}
		);
		onChange(newCode);
	};

	enCodeToCn = (code) => {
		let { globalStore } = this.props;
		let { allMap } = globalStore;
		let { ruleAndIndexFieldList = [] } = allMap;
		let newFieldList = [];
		let keywords = [];
		ruleAndIndexFieldList &&
			ruleAndIndexFieldList.length > 0 &&
			ruleAndIndexFieldList.forEach(item => {
				if (item.name && item.dName) {
					keywords.push(`@${item.name}`);
					newFieldList.push({
						name: `@[${commonLang.sourceName(item.sourceName)}]${item.dName}`,
						value: `@${item.name}`
					});
				}
			});

		// 正则替换关键词
		let newCode = code.replace(
			new RegExp(`(${keywords.join("|")})`, "g"),
			(match) => {
				let turnStr = match;
				newFieldList.forEach(item => {
					if (item.value === match) {
						turnStr = item.name;
					}
				});
				return turnStr;
			}
		);
		return newCode;
	};
	render() {
		let { defaultCnCode } = this.state;
		let { globalStore, disabled, ruleErrMsg, appName } = this.props;
		let { allMap } = globalStore;
		// let { ruleAndIndexFieldList = [] } = allMap;
		const ruleAndIndexFieldList = filterAvailableFieldList({allMap, appName});

		let newFieldList = [];
		ruleAndIndexFieldList &&
			ruleAndIndexFieldList.length > 0 &&
			ruleAndIndexFieldList.forEach(item => {
				if (item.name && item.dName) {
					newFieldList.push({
						name: `[${commonLang.sourceName(item.sourceName)}]${item.dName}`,
						value: item.name
					});
				}
			});
		return (
			<div className="ml-1 formulaScriptEditor">
				{
					disabled
						? <TextArea
							disabled
							value={defaultCnCode || undefined}
							autoSize={true}
						/>
						: <FormulaEdit
							theme="night"
							height={200}
							defaultValue={defaultCnCode}
							fieldList={newFieldList}
							readOnly={false}
							id="formulaScriptEditor"
							lineNumber={false}
							onChange={(code, obj) => this.getCode(code, obj)}
						/>
				}
				<div className="red">{ruleErrMsg}</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(FormulaEditor);

