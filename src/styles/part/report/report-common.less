:global {
	.report-modal {
		.ant-modal-header {
			display: none;
		}
		.ant-modal-body {
			background: linear-gradient(to bottom, #4594fa, #54affc);
			padding: 0;
		}
		.ant-modal-close {
			& {
				right: -56px;
				color: #fff;
				display: none;
			}
			.ant-modal-close-x {
				font-size: 28px;
			}
		}
		.ant-modal-content {
			& {
				border-radius: 0;
			}
			.ant-modal-body {
				padding: 0;
			}
		}

		/* 报告右侧菜单 */
		.report-menu {
			& {
				position: absolute;
				right: -160px;
				padding: 0;
			}
			.report-menu-list {
				& {
					list-style: none;
				}
				.report-menu-item {
					& {
						position: relative;
						text-align: left;
						background: #eef5ff;
						margin-bottom: 10px;
						width: 150px;
						height: 40px;
						line-height: 40px;
						padding: 0 12px;
						color: #000;
						cursor: pointer;
						border-radius: 4px;
						text-align: center;
					}
					&.active {
						background: #4594fa;
						color: #fff;
					}
					&:hover {
						border-color: #4594fa;
					}
				}
			}
		}

		/* 报告头部 */
		.report-header {
			& {
				position: relative;
				height: auto;
				overflow: hidden;
				padding: 25px;
			}
			.report-base-info {
				img {
					width: 60px;
					float: left;
					margin-right: 20px;
				}
				.info {
					& {
						float: left;
						height: 100px;
						color: #fff;
					}
					.report-title {
						display: block;
						color: #fff;
						font-size: 22px;
						padding-top: 3px;
						font-weight: normal;
					}
					.show-content {
						font-size: 12px;
						display: block;
						.report-id {
							display: block;
						}

						.report-time {
							display: block;
						}
					}
				}
			}
			.download-report {
				& {
					position: absolute;
					right: 30px;
					top: 61px;
				}
				button {
					& {
						font-size: 14px;
						background: none;
						border: 1px solid #fff;
						padding: 6px 20px;
						display: inline-block;
						color: #fff;
						margin-left: 12px;
						cursor: pointer;
						border-radius: 4px;
					}
					&:hover {
						background: rgba(255, 255, 255, 0.35);
					}
					&:focus,
					&:active {
						outline: none;
					}
				}
			}
			.is-close-btn-wrapper {
				& {
					position: absolute;
					top: 20px;
					right: 20px;
				}
				.is-close-btn {
					position: relative;
					font-size: 22px;
					color: rgba(255, 255, 255, 0.45);
				}
				.is-close-btn:hover {
					cursor: pointer;
					color: #fff;
				}
			}
		}

		/* 报告主体内容 */
		.report-body {
			& {
				overflow: hidden;
				padding: 25px;
				margin-top: -50px;
			}

			.report-panel {
				position: relative;
				width: 100%;
				// height: auto;
				background: #fff;

				.report-panel-box {
					position: relative;
					top: 20px;
					height: 36px;
					border-left: 4px solid #ffd76d;
					margin-bottom: 20px;

					.report-panel-title {
						position: relative;
						display: inline-block;
						left: 10px;
						top: 2px;
						font-size: 18px;
						font-family: "Microsoft YaHei";
					}
				}
				.person-item-array {
					position: relative;
					top: 28px;
				}
				/* 报告个人基本信息 */
				.report-panel-content-personal {
					position: relative;
					padding: 20px;
					width: 100%;

					.personal-info-menu {
						& {
							display: flex;
							display: -webkit-inline-flex;
							display: -moz-inline-flex;
							display: -ms-inline-flex;
							justify-content: center;
							//align-items: center;
							width: 100%;
							padding: 10px;
							margin-bottom: 0;
						}
						.first-li {
							width: 33.33%;
							.monitor-name-report-img {
								position: relative;
								left: -40%;
							}
							.monitor-idNum-report-img {
								position: relative;
								left: -20%;
							}
							.monitor-mobil-report-img {
								position: relative;
								left: -30%;
							}
							.monitor-report-img {
								float: left;
							}
						}
						////.secound-li {
						////	position: relative;
						////	left: 8%;
						////}
						//.third-li {
						//	position: relative;
						//	left: 8%;
						//}
						.person-item {
							position: relative;
							top: -56px;
							left: 22%;
							.item-title {
								text-align: left;
							}
						}
						.person-mobile-item {
							position: relative;
							top: -52%;
							left: -31%;
							float: right;
							.item-title {
								text-align: left;
							}
						}
						.person-idNum-item {
							position: relative;
							top: -56%;
							float: right;
							.item-title {
								text-align: left;
							}
						}
						li {
							& {
								list-style: none;
								padding: 0;
								float: left;
								display: block;
								text-align: center;
								width: 25%;
							}
							img {
								display: block;
								margin: 0 auto 8px;
								width: 56px;
								height: 56px;
							}
							p {
								margin-bottom: 0;
							}
						}
					}
				}

				.report-secound-title {
					display: block;
					position: relative;
					color: #4798FA;
					font-size: 17px;
					left: 10px;
				}

				.report-danger-detail {
					position: relative;
					top: 15px;
				}

				.report-panel-content-advice {
					.report-progress-box {
						position: relative;
						height: 200px;

						.antifuraud-process {
							position: relative;
							top: -20%;
							width: 180px;
							height: 180px;
						}

						.report-mark {
							font-size: 41px;
							display: block;
							margin-top: 35px;
						}

						.report-mark-smart {
							font-size: 48px;
							display: block;
							margin-top: 54px;
						}

						.show-score {
							font-size: 24px;
						}

						.antifuraud-advice-position {
							position: relative;
							left: 22%;
							width: 160px;
							height: 160px;
							text-align: center;
						}

						.antifuraud-info {
							z-index: 999999;
							top: -165px;

							.report-result {
								display: block;
								font-size: 30px;
								margin-top: -11px;
							}
						}

						.antifuraud-desc {
							position: relative;
							top: -320px;
							left: 44%;
							width: 50%;

							.report-advice {
								font-size: 34px;
								display: block;
								//margin-bottom: 10px;
							}

							.report-desc {
								font-size: 20px;
								display: block;
								color: #666;
							}
						}
					}

					.heigh-color {
						color: #fb4d46;
					}

					.people-color {
						color: #ffc157;
					}

					.safe-color {
						color: #33e5a8;
					}
				}

				.report-panel-content {
					width: 100%;
					position: relative;
					top: 10px;
					padding: 4px 20px 30px;

					.table-position {
						margin-top: 10px;
					}

					.report-table-box {
						.danger-type-desc {
							list-style: none;
							.red-point {
								border-radius: 50%;
								background: red;
								width: 5px;
								height: 5px;
							}

							.base-icon {
								position: relative;
								display: inline-block;
								margin-right: 5px;
								top: -2px;
							}
							.danger-type-ul {
								list-style: none;
								display: block;
								margin-left: -40px;
								li {
									list-style: none;
								}
							}
						}
					}
				}
			}

			.info-to-top {
				margin-top: 40px;
			}

			.dashed-style {
				border-bottom: 1px dashed #c8c8c8;
			}
		}
	}
}
