import React, { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { policyEditorAPI, policyRunningAPI, workflowAPI } from "@/services";
import { Table, Pagination, Tag, message, Input, Select, Tooltip, Spin, Modal, Popover } from "antd";
import { checkFunctionHasPermission } from "@/utils/permission";
import NoPermission from "@/components/NoPermission";
import { PolicyConstants, StandFieldConstants } from "@/constants";
import { policyListLang, commonLang, replayTaskLang } from "@/constants/lang";
import { isJSON } from "@/utils/isJSON";
import { searchToObject } from "@/utils/utils";
import moment from "moment";
import ExportRules from "../Common/Export/ExportRules";
import ExportPolicyList from "../Common/Export/ExportPolicyList";
import ViewPublicBtn from "../Common/ViewPublicBtn/ViewPublicBtn";
import CircleProcess from "../../Salaxy/EditorArea/Inner/CircleProcess";
import "./RunningArea.less";

const S_DT_VS_PARTNERCODE = StandFieldConstants("S_DT_VS_PARTNERCODE");
const S_DT_VS_APPNAME = StandFieldConstants("S_DT_VS_APPNAME");
const S_DT_VS_EVENTID = StandFieldConstants("S_DT_VS_EVENTID");
const D_T_VB_EVENTOCCURTIME = StandFieldConstants("D_T_VB_EVENTOCCURTIME");

const PolicyDrawer = React.lazy(() => import("@/components/PolicyList/PolicyDrawer"));
const PolicyTestDrawer = React.lazy(() => import("@/components/PolicyList/PolicyTestDrawer"));
const PolicyReplayModal = React.lazy(() => import("./Modal/PolicyReplayModal"));
const PublicPolicyModal = React.lazy(() => import("@/components/PublicPolicy/PublicPolicyModal"));
const ViewOutputParams = React.lazy(() => import("@/components/PolicyList/OutputParamsModal/ViewOutputParams"));

const InputGroup = Input.Group;
const Search = Input.Search;
const Option = Select.Option;
const confirm = Modal.confirm;

class RuleTemplate extends PureComponent {
	constructor(props) {
		super(props);

		let { location: { search }, policyRunningStore: { searchData }, dispatch } = props;
		let searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		let name = searchObj && searchObj.name ? searchObj.name : null;
		let eventId = searchObj && searchObj.eventId ? searchObj.eventId : null;

		if (name) {
			searchData["searchField"] = "name";
			searchData["searchValue"] = name;
		}
		if (eventId) {
			searchData["searchField"] = "eventId";
			searchData["searchValue"] = eventId;
		}

		dispatch({
			type: "policyRunning/setSearchData",
			payload: searchData
		});

		this.state = {
			circleProcessVisible: false, // 回测进度圆
			replayTaskProcessVisible: false, // 回测进度查看
			replayTaskProcessTaskId: "" // 当前查看回测进度的策略集id
		};
	}

	componentWillMount() {
		// 页面初始化数据
		const { dispatch, location } = this.props;
		const { search } = location;
		const searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		const { policySetName, policyUuid } = searchObj || {};
		dispatch({
			type: "policyRunning/setSearchData",
			payload: {
				"searchValue": policySetName
			}
		});
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policyDrawer: false
			}
		});
		dispatch({
			type: "policyRunning/setAttrValue",
			payload: {
				expandedRowKeys: policyUuid ? [policyUuid] : []
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				policyDrawer: {
					policyDetail: {},
					uuid: null
				}
			}
		});
	}

	componentDidMount() {
		this.timer = setInterval(() => {
			const { globalStore } = this.props;
			const { menuTreeReady } = globalStore;
			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkFunctionHasPermission("ZB0101", "runningSearch")) {
					this.search();
				}
			}
		}, 100);
	}

	search() {
		const { policyRunningStore, globalStore, dispatch } = this.props;
		const { curPage, pageSize, searchData } = policyRunningStore;
		const { currentApp } = globalStore;

		let extraParams = {};
		extraParams[searchData["searchField"]] = searchData["searchValue"];
		extraParams["tag"] = searchData["tag"];

		if (currentApp && currentApp.name) {
			dispatch({
				type: "policyRunning/getPolicySets",
				payload: {
					appName: currentApp.name ? currentApp.name : null,
					curPage: curPage,
					pageSize: pageSize,
					...extraParams
				}
			});
		}
	}

	componentWillReceiveProps(nextProps) {
		let { policyRunningStore, dispatch, globalStore } = this.props;
		let { menuTreeReady } = globalStore;
		let { curPage, pageSize, searchData } = policyRunningStore;

		if (menuTreeReady && checkFunctionHasPermission("ZB0101", "runningSearch")) {
			const preCurrentApp = this.props.globalStore.currentApp;
			const nextCurrentApp = nextProps.globalStore.currentApp;

			if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
				curPage = 1;
				// 检测到切换应用，开始刷新列表
				// 首先清空搜索项目
				dispatch({
					type: "policyRunning/setAttrValue",
					payload: {
						searchData: {
							searchField: "name",
							searchValue: null
						}
					}
				});
				dispatch({
					type: "policyRunning/getPolicySets",
					payload: {
						appName: nextCurrentApp.name ? nextCurrentApp.name : null,
						curPage: curPage,
						pageSize: pageSize,
						tag: searchData.tag
					}
				});
			}

			const preSearch = this.props.location.search;
			const nextSearch = nextProps.location.search;
			const preSearchObj = preSearch && searchToObject(preSearch) ? searchToObject(preSearch) : null;
			const nextSearchObj = nextSearch && searchToObject(nextSearch) ? searchToObject(nextSearch) : null;
			const preTab = (preSearchObj||{}).currentTab;
			const nextTab = (nextSearchObj||{}).currentTab;
			if(preTab !== nextTab && preTab && nextTab === "1"){
				this.search();
			}
		}
	}

	paginationOnChange = (current, pageSize) => {
		const { policyRunningStore, globalStore, dispatch } = this.props;
		const { currentApp } = globalStore;
		const { searchData } = policyRunningStore;

		dispatch({
			type: "policyRunning/setAttrValue",
			payload: {
				curPage: current,
				pageSize: pageSize
			}
		});

		let payload = {
			appName: currentApp.name ? currentApp.name : null,
			curPage: current,
			pageSize: pageSize,
			tag: searchData.tag
		};

		payload[searchData.searchField] = searchData.searchValue;

		dispatch({
			type: "policyRunning/getPolicySets",
			payload: payload
		});
	}

	viewPolicyDetailHandle = (item, e) => {
		const { policyEditorStore, dispatch } = this.props;
		const { dialogData } = policyEditorStore;
		e.stopPropagation();

		if (dialogData.policyDrawer && dialogData.policyDrawer.uuid && dialogData.policyDrawer.uuid === item.uuid) {
			dispatch({
				type: "policyEditor/setDialogShow",
				payload: {
					policyDrawer: false
				}
			});
			dispatch({
				type: "policyEditor/setDialogData",
				payload: {
					policyDrawer: {
						policyDetail: {},
						uuid: null
					}
				}
			});
			return;
		}
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policyDrawer: true
			}
		});

		// 请求策略详情
		const params = {
			policyUuid: item.uuid
		};
		policyEditorAPI.getVersionPolicyDetail(params).then(res => {
			const { dispatch } = this.props;
			if (res.success) {
				dispatch({
					type: "policyEditor/setDialogData",
					payload: {
						policyDrawer: {
							policyDetail: res.data || {},
							uuid: item.uuid
						}
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	changeSearchValue = (searchField, type, e) => {
		let { policyRunningStore, dispatch } = this.props;
		let { searchData } = policyRunningStore;
		let value = "";

		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		searchData[searchField] = value;

		dispatch({
			type: "policyRunning/setSearchData",
			payload: searchData
		});
	}

	// 运行区策略下线
	offlinePolicy = (item) => {
		const { uuid, name } = item;
		const { policyEditorStore, globalStore, dispatch } = this.props;
		confirm({
			title: policyListLang.table("policyOffLineTitle"),
			content: policyListLang.table("policyOffLineContent")(name),
			onOk: ()=> {
				policyRunningAPI.policyOffLine({
					policyUuid: uuid
				}).then(res => {
					if (res.success) {
						// lang:策略《${name}》下线成功
						message.success(policyListLang.table("policyOffLineSuccess")(name));

						// 刷新列表数据
						// 运行区
						this.search();
						// 编辑区
						const { curPage, pageSize, searchData } = policyEditorStore;
						const { currentApp } = globalStore;
						if (currentApp && currentApp.name) {
							dispatch({
								type: "policyEditor/getPolicySets",
								payload: {
									appName: currentApp.name ? currentApp.name : null,
									curPage: curPage,
									pageSize: pageSize,
									tag: searchData.tag,
									[searchData.searchField]: searchData.searchValue
								}
							});
						}

						// 刷新global
						dispatch({
							type: "global/getAllMap",
							payload: {}
						});
					} else {
						message.error(res.message);
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel() {}
		});
	}

	renderSubTable = (policyList, rowRecord) => {
		let tableRows = policyList.map((item, index) => {
			if (!item) {
				return;
			}
			return (
				<div
					className="row"
					key={index}
					onClick={(e) => {
						this.viewPolicyDetailHandle(item, e);
					}}
				>
					<div style={{ flex: "1 1 0%", lineHeight: "30px" }}>
						<a onClick={() => {
							if (checkFunctionHasPermission("ZB0101", "getPolicy")) {
								let { dispatch, location } = this.props;
								let pathname = location.pathname;
								let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
								dispatch(routerRedux.push(prefix + "/policy/versionPolicyDetail/" + item.uuid + "?tabIndex=1&policyVersion=" + item.policyVersion));
							} else {
								// lang:无权限操作
								message.warning(commonLang.messageInfo("noPermission"));
							}
						}}>
							<span className="a">
								{item.name ? item.name : "name"}
								<Tag color="purple" style={{ marginLeft: "10px" }}>
									{policyListLang.table("version")}：{item.policyVersion}
								</Tag>
							</span>
						</a>
					</div>
					<div className="row-item" style={{ width: "180px", lineHeight: "30px" }}>
						{rowRecord.appDisplayName}
					</div>
					<div className="row-item" style={{ width: "212px", lineHeight: "30px" }}>
						<Tooltip title={item.description && item.description.length > 15 ? item.description : undefined}>
							<div
								className="text-overflow"
								style={{ width: "180px" }}
							>
								{item.description}
							</div>
						</Tooltip>
					</div>
					{/* 不展示回测操作 */}
					{
						(
							checkFunctionHasPermission("ZB0101", "AddPsReplayTask") ||
							(checkFunctionHasPermission("ZB0101", "QueryPsReplayTaskDetail") &&
							checkFunctionHasPermission("ZB0101", "ModifyPsReplayTask"))
						) &&
						<div className="row-item" style={{ width: "120px", lineHeight: "30px" }}></div>
					}
					<div className="row-item" style={{ width: "155px" }}>
						<div className="table-action">
							{/* 指标导出 */}
							<ExportRules record={item} isEditorArea={false} />
							{/* 运行区策略下线 */}
							<Tooltip title={policyListLang.tooltip("offLinePolicy")} placement="top">
								<a onClick={(e) => {
									e.stopPropagation();
									if (checkFunctionHasPermission("ZB0101", "offRunPolicy")) {
										this.offlinePolicy(item);
									} else {
									// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}}>
									<i className={`salaxy-iconfont salaxy-offline ${checkFunctionHasPermission("ZB0101", "offRunPolicy") ? "" : "icon-disabled"}`}></i>
								</a>
							</Tooltip>
						</div>
					</div>
				</div>
			);
		});
		let noneData = (
			<div className="none-data">
				<i className="iconfont icon-empty"></i>
				<p>
					{/* lang:当前策略集暂无策略 */}
					{policyListLang.table("hasNonePolicy")}
				</p>
			</div>
		);
		return <div className="table-row">
			{
				policyList && policyList.length > 0 ? tableRows : noneData
			}
		</div>;
	};

	policyReplayHandle = async(item, e) => {
		e.stopPropagation();
		let { dispatch, policyRunningStore } = this.props;
		let { policyReplayData: oldPolicyReplayData } = policyRunningStore.dialogData;
		const policyReplayData = {...oldPolicyReplayData};
		let params = {
			policySetUuid: item.uuid,
			taskUuid: item.taskUuid
		};
		let isAdd = false;

		await dispatch({
			type: "policyRunning/getVersionPolicys",
			payload: { policySetUuid: item.uuid }
		});
		if (item.taskUuid) {
			// 如果task存在
			if (item.replayTaskStatus === 10 || item.replayTaskStatus === 15 || item.replayTaskStatus === 20) {
				isAdd = true;
			}
		} else {
			isAdd = true;
		}
		if (isAdd) {
			// 如果是新增
			policyReplayData["taskTarget"] = item.eventId;
			policyReplayData["policySetUuid"] = item.uuid;
			policyReplayData["replayTaskStatus"] = item.replayTaskStatus;
			policyReplayData["testTarget"] = {
				"policySetUuid": item.uuid,
				"policySetName": item.name,
				"policySetId": item.id,
				"policyList": []
			};
			if (item.eventId) {
				// 默认带入事件标识
				policyReplayData["fieldFilters"] = [{
					"fieldKey": S_DT_VS_EVENTID,
					"compareSign": "==",
					"fieldValue": item.eventId,
					"fieldType": "STRING"
				}];
			}

			dispatch({
				type: "policyRunning/setDialogShow",
				payload: {
					addPolicyReplay: true,
					modifyPolicyReplay: false
				}
			});
			dispatch({
				type: "policyRunning/setDialogData",
				payload: {
					policyReplayData: policyReplayData
				}
			});
		} else {
			// 如果是修改
			policyRunningAPI.getReplayTaskDetail(params).then(res => {
				if (res.success) {
					let taskDetail = res.data && res.data.taskUuid ? res.data : null;
					// 如果data不存在，那说明是新增的回测任务
					if (!res.data) {
						return;
					}

					dispatch({
						type: "policyRunning/setDialogData",
						payload: {
							policyReplayData: {
								policySetUuid: item.uuid,
								taskName: taskDetail ? taskDetail.taskName : null,
								taskStartAt: taskDetail ? taskDetail.taskStartAt : null,
								taskFrom: taskDetail ? taskDetail.taskFrom : null,
								taskTo: taskDetail ? taskDetail.taskTo : null,
								taskTarget: taskDetail ? taskDetail.taskTarget : null,
								taskExecuteType: taskDetail ? taskDetail.taskExecuteType : "RIGHTNOW",
								taskUuid: taskDetail ? taskDetail.taskUuid : null,
								replayTaskStatus: item.replayTaskStatus,
								testTarget: taskDetail ? JSON.parse(taskDetail.testTarget) : {},
								fieldFilters: (taskDetail && taskDetail.fieldFilters) ? JSON.parse(taskDetail.fieldFilters) : null,
								dataSourceType: taskDetail ? taskDetail.dataSourceType : null
							}
						}
					});
					dispatch({
						type: "policyRunning/setDialogShow",
						payload: {
							addPolicyReplay: !taskDetail.taskUuid,
							modifyPolicyReplay: !!taskDetail.taskUuid
						}
					});
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		}

	}

	openWorkflow = (item, e) => {
		e.stopPropagation();
		let { dispatch } = this.props;

		let params = {
			policySetUuid: item.uuid,
			flowVersion: item.decisionFlowVersion
		};
		workflowAPI.getWorkflowByVersion(params).then(res => {
			if (res.success && res.data) {
				let data = res.data;
				let processContent = data["processContent"];
				let flowData = {
					nodes: [],
					edges: []
				};
				let workflowContent = processContent && isJSON(processContent) ? JSON.parse(processContent) : flowData;

				dispatch({
					type: "workflow/setAttrValue",
					payload: {
						policySetItem: item,
						exportFlowVersion: item.decisionFlowVersion,		// 请求成功后将当前version信息放到model里面，后面导出的时候用到。
						pageName: "policyRunning",
						operationType: "view",
						status: data.status,
						flowData: workflowContent
					}
				});
				dispatch({
					type: "workflow/setDialogShow",
					payload: {
						workflow: true
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	openPolicyTestDrawer = (item, e) => {
		e.stopPropagation();
		let { policyEditorStore, dispatch } = this.props;
		let { dialogData } = policyEditorStore;
		let { policyTestDrawer } = dialogData;
		let { policyDetail } = policyTestDrawer;
		let params = {
			appName: item.appName,
			eventId: item.eventId
		};

		policyEditorAPI.getTestFields(params).then(res => {
			if (res.success) {
				if (res.data) {
					let selectFieldList = [];
					res.data.forEach(item => {
						selectFieldList.push(
							{
								key: item,
								value: null
							}
						);
					});
					policyDetail["selectFieldList"] = selectFieldList;

					dispatch({
						type: "policyEditor/setDialogData",
						payload: {
							policyTestDrawer: policyTestDrawer
						}
					});
				}
			} else {
				message.warning(res.message);
			}
		}).catch(err => {
			console.log(err);
		});

		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policyTestDrawer: true
			}
		});

		policyTestDrawer["policyDetail"][S_DT_VS_PARTNERCODE] = item.partnerCode;
		policyTestDrawer["policyDetail"][S_DT_VS_APPNAME] = item.appName;
		policyTestDrawer["policyDetail"][S_DT_VS_EVENTID] = item.eventId;
		policyTestDrawer["policyDetail"]["S_DT_VS_EVENTTYPE"] = item.eventType;
		policyTestDrawer["policyDetail"][D_T_VB_EVENTOCCURTIME] = moment().format("YYYY-MM-DD HH:mm:ss");

		dispatch({
			type: "policyEditor/setAttrValue",
			payload: {
				policyTestDrawer: policyTestDrawer
			}
		});
	}

	/*
		查看公共策略列表模块
	*/
	// 查看公共策略列表
	searchPublicPolicy = ({ curPage = 1, pageSize = 10, cb }) => {
		const { dispatch, policyRunningStore } = this.props;
		const { curUuid } = policyRunningStore;
		policyRunningAPI.refPublicPolicy({
			publicSetUuid: curUuid,
			isEditorArea: false,
			curPage,
			pageSize
		}).then(res => {
			if (res.success) {
				dispatch({
					type: "policyRunning/setDialogData",
					payload: {
						publicPolicyData: res.data || {}
					}
				});
				cb && cb();
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}
	viewPublicPolicy = (record) => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyRunning/setAttrValue",
			payload: {
				curUuid: record.uuid
			}
		});
		this.searchPublicPolicy({
			curPage: 1,
			pageSize: 10,
			cb: () => {
				this.controlPublicPolicy(true);
			}
		});
	}
	// 控制应用的公共策略弹窗
	controlPublicPolicy = (visible) => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyRunning/setDialogShow",
			payload: {
				publicPolicy: visible
			}
		});
		// 如果是关闭
		if (!visible) {
			dispatch({
				type: "policyRunning/setAttrValue",
				payload: {
					curUuid: ""
				}
			});
			dispatch({
				type: "policyRunning/setDialogData",
				payload: {
					publicPolicyData: {}
				}
			});
		}
	}

	// 回测查看
	handleReCallProcess = async(item) => {
		const { dispatch } = this.props;
		const { replayTaskProcessVisible } = this.state;
		const { taskUuid } = item;
		if (!checkFunctionHasPermission("ZB0103", "QueryPsReplayTaskReport")) {
			message.warning(commonLang.messageInfo("noPermission"));
			return;
		}
		if (!replayTaskProcessVisible) {
			await dispatch({
				type: "policyRunning/changePolicySetStatus",
				payload: {
					taskUuid
				}
			});
			this.setState({
				replayTaskProcessVisible: true,
				replayTaskProcessTaskId: taskUuid
			}, ()=>{
				this.setState({
					circleProcessVisible: true
				});
			});
		} else {
			this.setState({
				replayTaskProcessVisible: false,
				replayTaskProcessTaskId: ""
			}, ()=>{
				this.setState({
					circleProcessVisible: false
				});
			});
		}
	}

	// 展示回测操作按钮
	policyRepayTask = (record) => {
		let [isAdd, isView] = [false, false];
		if (record.taskUuid) {
			// 如果task存在
			if (record.replayTaskStatus === 10 || record.replayTaskStatus === 15 || record.replayTaskStatus === 20) {
				isAdd = true;
			} else if (record.replayTaskStatus === 3 || record.replayTaskStatus === 5) {
				isView = true;
			}
		} else {
			isAdd = true;
		}
		// 新增/编辑
		const hasPolicyList = record.policyList && record.policyList.length;
		const canAddPermission = checkFunctionHasPermission("ZB0101", "AddPsReplayTask");
		const canEditPermission = checkFunctionHasPermission("ZB0101", "QueryPsReplayTaskDetail") && checkFunctionHasPermission("ZB0101", "ModifyPsReplayTask");
		const canAdd = hasPolicyList && canAddPermission;
		const canEdit = hasPolicyList && canEditPermission;
		let addTitle = replayTaskLang.operatorModal("addReplay"); // 添加回测
		if (!canAddPermission) {
			addTitle = commonLang.messageInfo("noPermission"); // 暂无权限
		} else if (!hasPolicyList) {
			addTitle = replayTaskLang.operatorModal("notSupportRepayTask"); // 暂不支持回测
		}
		let editTitle = replayTaskLang.operatorModal("modifyReplay"); // 修改回测
		if (!canEditPermission) {
			editTitle = commonLang.messageInfo("noPermission"); // 暂无权限
		} else if (!hasPolicyList) {
			editTitle = replayTaskLang.operatorModal("notSupportRepayTask"); // 暂不支持回测
		}

		// 查看
		const { replayTaskProcessTaskId, circleProcessVisible, replayTaskProcessVisible } = this.state;
		const { globalStore } = this.props;
		const { personalMode} = globalStore;
		const lang = personalMode.lang === "cn" ? "cn" : "en";
		const showPopover = replayTaskProcessVisible && record.taskUuid === replayTaskProcessTaskId;
		let percent = 0;
		if (record.totalCount === 0) {
			percent = 100;
		} else if (record.completeCount === 0) {
			percent = 0;
		} else {
			percent = (record.completeCount / record.totalCount).toFixed(2) * 100;
		}
		if (!(canAddPermission || canEditPermission)) {
			return null;
		}
		return (
			<Fragment>
				{/* lang:添加回测*/}
				{
					isAdd &&
					!isView &&
					<Tooltip title={addTitle} placement="left">
						<a
							className={canAdd ? "blue" : "a-disabled"}
							onClick={(e) => {
								e.stopPropagation();
								if (canAddPermission) {
									if (hasPolicyList) {
										this.policyReplayHandle(record, e);
									} else {
										// lang:暂不支持回测
										message.warning(replayTaskLang.operatorModal("notSupportRepayTask"));
									}
								} else {
									// lang:无权限操作
									message.warning(commonLang.messageInfo("noPermission"));
								}
							}}
						>
							<i className="salaxy-iconfont salaxy-repayAdd"></i>
						</a>
					</Tooltip>
				}
				{/* 修改回测 */}
				{
					!isAdd &&
					!isView &&
					<Tooltip title={editTitle} placement="left">
						<a
							className={canEdit ? "blue" : "a-disabled"}
							onClick={(e) => {
								e.stopPropagation();
								if (canEditPermission) {
									if (hasPolicyList) {
										this.policyReplayHandle(record, e);
									} else {
										// lang:暂不支持回测
										message.warning(replayTaskLang.operatorModal("notSupportRepayTask"));
									}
								} else {
									// lang:无权限操作
									message.warning(commonLang.messageInfo("noPermission"));
								}
							}}
						>
							<i class="salaxy-iconfont salaxy-repayEdit"></i>
						</a>
					</Tooltip>
				}
				{/* 查看回测 */}
				{
					!isAdd &&
					isView &&
					<Popover
						placement="left"
						title={replayTaskLang.operatorModal("replayProcessView")} // 查看回测进度
						content={
							circleProcessVisible
								? <CircleProcess
									uuid={record.uuid}
									percent={percent}
									lang={lang}
									location={location}
								/>
								: null
						}
						onVisibleChange={this.handleReCallProcess.bind(this, record)}
						visible={showPopover}
					>
						<i
							className={"salaxy-iconfont salaxy-view-recall"}
							onClick={(e) => {
								e.stopPropagation();
							}}
						></i>
					</Popover>
				}
			</Fragment>
		);
	};

	// 新增出参
	openOutputParams = async(record, e) => {
		e.stopPropagation();
		const { uuid } = record;
		const {dispatch} = this.props;
		await dispatch({
			type: "policyRunning/getCustomReturnFields",
			payload: {
				publicSetUuid: uuid
			}
		});
		dispatch({
			type: "policyRunning/setDialogShow",
			payload: {
				outputParams: true
			}
		});
	}
	render() {
		let { policyRunningStore, globalStore, pagePosition, dispatch, location } = this.props;
		let { personalMode, menuTreeReady, allMap: { policyTags = [] } } = globalStore;
		let { policySetsList, curPage, total, expandedRowKeys, searchData, policySetsListLoad, dialogShow, dialogData } = policyRunningStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		const { publicPolicy } = dialogShow;
		const { publicPolicyData } = dialogData;
		let columns = [
			{
				title: policyListLang.table("policySet"),
				dataIndex: "name",
				render: (text, record) => {
					return (
						<div>
							<span className="mr10">{text}</span>
							{/* 公共策略 */}
							<ViewPublicBtn record={record} viewPublicPolicy={this.viewPublicPolicy} />
						</div>
					);
				}
			},
			{
				title: policyListLang.table("app"),
				width: 180,
				dataIndex: "appDisplayName"
			},
			{
				title: policyListLang.table("description"),
				width: 180,
				dataIndex: "description",
				key: "description",
				render: (text) => {
					return (
						<Tooltip title={text && text.length > 15 ? text : undefined}>
							<div
								className="text-overflow"
								style={{ width: "180px" }}
							>
								{text}
							</div>
						</Tooltip>
					);
				}
			},
			{
				title: policyListLang.table("operation"),
				width: 170,
				dataIndex: "",
				render: (record) => {
					let workflowText = policyListLang.table("viewRuleStream"); // lang:"查看规则流"
					if (!record.hasDecisionFlow) {
						workflowText = policyListLang.table("noConfigRuleStream"); // lang:"当前策略集还没有配置规则流";
					}
					return (
						<div className="table-action">
							{/* lang:导出策略集 */}
							<ExportPolicyList record={record} isEditorArea={false} />
							{
								checkFunctionHasPermission("ZB0101", "editorTestPolicySet")
									// lang:测试策略集
									? <Tooltip title={policyListLang.tooltip("testPolicySet")}>
										<a onClick={(e) => {
											this.openPolicyTestDrawer(record, e);
										}}>
											<i className="iconfont icon-spider"></i>
										</a>
									</Tooltip>
									: null
							}
							{
								checkFunctionHasPermission("ZB0101", "decisionFlowHistoryDetail")
									? <Tooltip title={workflowText} placement="left">
										<a
											className={record.hasDecisionFlow ? "blue" : ""}
											onClick={(e) => {
												e.stopPropagation();
												if (checkFunctionHasPermission("ZB0101", "decisionFlowHistoryDetail")) {
													if (record.hasDecisionFlow) {
														this.openWorkflow(record, e);
													} else {
														message.warning(workflowText);
													}
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}}
										>
											<i className="iconfont icon-flow"></i>
										</a>
									</Tooltip> : null
							}
							{
								checkFunctionHasPermission("ZB0101", "queryResponseConfig")
									// lang:查询出参
									? <Tooltip title={policyListLang.tooltip("searchOutput")}>
										<a onClick={(e) => {
											this.openOutputParams(record, e);
										}}>
											<i className="salaxy-iconfont salaxy-chucan"></i>
										</a>
									</Tooltip>
									: null
							}
							{ this.policyRepayTask(record) }
						</div>
					);
				}
			}
		];

		// 回测状态
		const policyColumn = {
			title: policyListLang.table("replayStatus"), // 回测状态
			dataIndex: "replayTaskStatus",
			width: 120,
			render: (text, record) => {
				let statusObj = {
					dName2: "待执行",
					enDName2: "To be executed",
					bgColor: "#B2BECD"
				};
				if (record && record.hasOwnProperty("replayTaskStatus")) {
					statusObj = PolicyConstants.replayTaskStatusMap[record.replayTaskStatus.toString()] || {};
				}
				return (
					<Tooltip title={lang === "cn" ? statusObj.dName2 : statusObj.enDName2}>
						<div className="repay-status" style={{"background": statusObj.bgColor}}></div>
					</Tooltip>
				);
			}
		};
		if (
			(checkFunctionHasPermission("ZB0101", "AddPsReplayTask") ||
			(
				checkFunctionHasPermission("ZB0101", "QueryPsReplayTaskDetail") &&
				checkFunctionHasPermission("ZB0101", "ModifyPsReplayTask"))
			) &&
			!columns.find(ele => ele.key === "policyReplay")
		) {
			columns.splice(3, 0, policyColumn);
		}
		return (
			<div className="page-global-body">
				<div className="page-global-body-search">
					<div className="left-info">
						<h2>{policyListLang.common(pagePosition === "editorArea" ? "titleForEditor" : "titleForRunning")}</h2>
					</div>
					{
						menuTreeReady && checkFunctionHasPermission("ZB0101", "runningSearch") &&
						<div className="right-info">
							{/* lang:全部标签 */}
							{/* <div className="right-info-item">
                        		<Select
                        			style={{ width: "150px" }}
                        			value={searchData["tag"] || ""}
                        			onChange={async(e) => {
                        				await this.changeSearchValue("tag", "select", e);
                        				await this.paginationOnChange(1, 10);
                        			}}
                        		>
                        			<Option value="">
                        				{policyListLang.searchParams("allTags")}
                        			</Option>
                        			{
                        				policyTags.map((item, index) => {
                        					return (
                        						<Option value={item.name} key={index}>
                        							{item.dName}
                        						</Option>
                        					);
                        				})
                        			}
                        		</Select>
                        	</div> */}
							<div className="right-info-item">
								<InputGroup compact>
									<Select
										value={searchData["searchField"] || undefined}
										onChange={(e) => {
											this.changeSearchValue("searchField", "select", e);
										}}
									>
										<Option value="name">
											{policyListLang.searchParams("policySetName")}
										</Option>
										<Option value="policyName">
											{policyListLang.searchParams("policyName")}
										</Option>
										<Option value="eventId">
											{policyListLang.searchParams("event")}
										</Option>
										<Option value="ruleName">
											{policyListLang.searchParams("ruleName")}
										</Option>
										<Option value="ruleCustomId">
											{policyListLang.searchParams("ruleCustomId")}
										</Option>
									</Select>
									<Search
										placeholder={policyListLang.searchParams("searchPlaceholder")}
										onSearch={val => {
											this.paginationOnChange(1, 10);
										}}
										value={searchData["searchValue"] || undefined}
										onChange={(e) => {
											this.changeSearchValue("searchValue", "input", e);
										}}
										style={{ width: 200 }}
										enterButton={policyListLang.searchParams("search")} // lang:搜索
									/>
								</InputGroup>
							</div>
						</div>
					}
				</div>
				{
					menuTreeReady &&
					checkFunctionHasPermission("ZB0101", "runningSearch") &&
					<Spin spinning={policySetsListLoad}>
						<div className="page-global-body-main has-table-border">
							<Table
								className="table-out-border"
								columns={columns}
								expandedRowRender={record => {
									let policyList = record.policyList ? record.policyList : [];
									return this.renderSubTable(policyList, record);
								}}
								pagination={false}
								dataSource={policySetsList}
								rowKey="uuid"
								expandedRowKeys={expandedRowKeys}
								size={personalMode.layout === "default" ? "middle" : "small"}
								onExpand={(expanded, record) => {
									let uuidArray = [];
									if (expandedRowKeys[0] !== record.uuid) {
										uuidArray = record.uuid ? [record.uuid] : [];
									}

									dispatch({
										type: "policyRunning/setAttrValue",
										payload: {
											expandedRowKeys: uuidArray
										}
									});

									dispatch({
										type: "policyEditor/setDialogShow",
										payload: {
											policyDrawer: false
										}
									});
								}}
								onRow={(record) => {
									return {
										onClick: () => {
											let uuidArray = [];
											if (expandedRowKeys[0] !== record.uuid) {
												uuidArray = record.uuid ? [record.uuid] : [];
											}

											dispatch({
												type: "policyRunning/setAttrValue",
												payload: {
													expandedRowKeys: uuidArray
												}
											});

											dispatch({
												type: "policyEditor/setDialogShow",
												payload: {
													policyDrawer: false
												}
											});
										}
									};
								}}
							/>
							<div className="page-global-body-pagination">
								{/* lang:共x条记录 */}
								<span className="count">{commonLang.getRecords(total)}</span>
								<Pagination
									showSizeChanger
									onChange={this.paginationOnChange}
									onShowSizeChange={this.paginationOnChange}
									defaultCurrent={1}
									total={total}
									current={curPage}
								/>
							</div>
						</div>
					</Spin>
				}
				{
					menuTreeReady &&
					!checkFunctionHasPermission("ZB0101", "runningSearch") &&
					<NoPermission />
				}
				<React.Suspense fallback={null}>
					<PolicyDrawer
						location={location}
						isRunning={true}
					/>
				</React.Suspense>
				<React.Suspense fallback={null}>
					<PolicyReplayModal />
				</React.Suspense>
				<React.Suspense fallback={null}>
					<PolicyTestDrawer />
				</React.Suspense>
				<React.Suspense fallback={null}>
					<PublicPolicyModal
						location={location}
						visible={publicPolicy}
						onCancel={this.controlPublicPolicy}
						publicPolicyData={publicPolicyData}
						paginationOnChange={this.searchPublicPolicy}
					/>
				</React.Suspense>
				<React.Suspense fallback={null}>
					<ViewOutputParams />
				</React.Suspense>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor,
	policyRunningStore: state.policyRunning
}))(RuleTemplate);

