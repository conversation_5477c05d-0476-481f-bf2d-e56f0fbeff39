import { PureComponent } from "react";
import { Card } from "antd";
import { Minimap } from "gg-editor";
import { workflowLang } from "@/constants/lang";

class FlowMinimap extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		return (
			<Card
				type="inner"
				title={workflowLang.flowMinimap("title")} // lang:"缩略图"
				bordered="false"
			>
				<Minimap height={120} />
			</Card>
		);
	}
}

export default FlowMinimap;
