import {message} from "antd";
import key from "keymaster";
import {policyDetailAPI, policyEditorAPI, publicPolicyEditorAPI} from "@/services";
import {isJSON} from "@/utils/isJSON";
import cloneDeep from "lodash.clonedeep";
import {isPublicPolicy} from "@/utils/utils";
import {formatPolicyRules} from "@/utils/formatPolicyRules";
import {uniqBy} from "lodash";

export default {
	namespace: "policyDetail",
	state: {
		policyOriginalAllInfo: {
			policyInfo: null,
			rules: null
		},
		ruleOperatorLoading: false,		// 规则操作loading，新增规则或修改规则的时候
		policyDetail: {},
		policyMode: null,
		policyRules: [],
		policyRulesReady: false,
		policyUuid: null,
		conditions: {},
		currentRuleUuid: null,
		tabIndex: 0,
		ruleActiveKey: [],
		addIFChildRuleIndex: null,
		ruleIndexArr: [],
		expandTemplate: {},
		policyVersionInfo: {
			onlineVersion: null,
			currentVersion: null
		},
		ruleSearch: {
			showSearch: false,
			searchWord: null
		},
		dialogShow: {
			addPolicy: false,
			templateDrawer: false,
			addRuleTemplate: false,
			addIFChildRule: false,
			addToCustomList: false,
			PolicyCiteIndex: false,
			policyCommit: false,
			policyVersionDrawer: false,
			addRuleImmuno: false,
			modifyRuleImmuno: false,
			ruleSort: false,
			viewImmune: false
		},
		dialogData: {
			addPolicyData: {
				partner: null
			},
			policyVersionDrawerData: {
				versionList: null
			},
			addToCustomListData: {
				triggers: [],
				ruleIndexArr: [],
				ruleUuid: null
			},
			ruleImmunoData: {
				ruleUuid: null,
				ruleName: null,
				expireAt: null,
				logicOperator: "&&",
				description: null,
				conditions: [
					{
						operator: null,
						leftVarType: "string",
						leftVar: null,
						rightVarType: "input",
						rightVar: null
					}
				]
			},
			ruleSortData: {
				ruleList: []
			},
			viewImmuneData: {
				ruleUuid: ""
			}
		},
		upLimit: false, // 用以判断用户是否勾选了上下限
		downLimit: false,
		ruleErrMsg: "", // 规则配置错误信息
		hintRules: []
	},
	effects: {
		*getPolicyRules({payload}, {call, put}) {
			let response = yield call(policyDetailAPI.getPolicyRules, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const {policyInfo} = response.data || {};

			// 如果有中断条件 进行初始化
			if (policyInfo.hasOwnProperty("terminate")) {
				policyInfo.terminate = policyInfo.terminate ? "1" : "0";
				const riskEventId = policyInfo.riskEventId.split(",");
				// 如果有全局则显示全局 且场景置空
				policyInfo.effectScope = riskEventId.indexOf("GLOBAL_APP") > -1 ? "GLOBAL_APP" : "set";
				if (policyInfo.effectScope === "GLOBAL_APP") {
					policyInfo.scene = "[]";
				}
			}

			yield put({
				type: "setAttrValue",
				payload: {
					policyOriginalAllInfo: {
						policyInfo: response["data"]["policyInfo"],
						rules: response["data"]["rules"]
					}
				}
			});
			yield put({
				type: "initPolicyDetail",
				payload: response["data"]["policyInfo"]
			});
			yield put({
				type: "initPolicyRules",
				payload: response["data"]["rules"]
			});
		},
		// 获取规则提示
		*getHintRules({payload}, {call, put}) {
			let response = yield call(policyDetailAPI.getReminders, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			// const {rules} = response.data || {};
			yield put({
				type: "initHintRules",
				payload: response.data
			});
		},
		*getPolicyVersionRules({payload}, {call, put, select}) {
			let {policyVersion, isFirst} = payload;
			let policyVersionInfo = yield select(state => state.policyDetail.policyVersionInfo);
			console.log(isPublicPolicy());
			const getRulesAPI = isPublicPolicy() ? policyDetailAPI.getPublicPolicyVersionRules : policyDetailAPI.getPolicyVersionRules;
			let response = yield call(getRulesAPI, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			// 如果有中断条件 进行初始化
			if (response.data && response.data.policyInfo && response.data.policyInfo.hasOwnProperty("terminate")) {
				response.data.policyInfo.terminate = response.data.policyInfo.terminate ? "1" : "0";
				const riskEventId = response.data.policyInfo.riskEventId.split(",");
				response.data.policyInfo.effectScope = riskEventId.indexOf("GLOBAL_APP") > -1 ? "GLOBAL_APP" : "set";
			}
			if (isFirst) {
				let backupResponse = cloneDeep(response);
				yield put({
					type: "backupPolicyInfo",
					payload: backupResponse
				});
			}
			policyVersionInfo["currentVersion"] = policyVersion;
			yield put({
				type: "setAttrValue",
				payload: {
					policyVersionInfo: policyVersionInfo
				}
			});
			yield put({
				type: "initPolicyDetail",
				payload: response["data"]["policyInfo"]
			});
			yield put({
				type: "initPolicyRules",
				payload: response["data"]["rules"]
			});
		},
		*getRuleConditionById({payload}, {call, put}) {
			let response = yield call(policyDetailAPI.getRuleConditionById, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initRuleConditionById",
				payload: response
			});
		},
		*changePolicyStatus({payload}, {call, put, select}) {
			let policyDetail = yield select(state => state.policyDetail.policyDetail);
			let response = {};
			// 公共策略
			console.log("isPublicPolicy:" + isPublicPolicy());
			if (isPublicPolicy()) {
				response = yield call(publicPolicyEditorAPI.publicPolicyDetail, payload);
			} else {
				response = yield call(policyEditorAPI.getPolicyDetail, payload);
			}

			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			policyDetail["status"] = response && response.data && response.data.status ? response.data.status : "wait_commit";
			yield put({
				type: "setAttrValue",
				payload: {
					policyDetail: policyDetail
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let {payload} = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setPolicyDetail(state, action) {
			let {policyDetail} = state;
			let {payload} = action;

			for (let key in payload) {
				policyDetail[key] = payload[key];
			}

			return {
				...state,
				policyDetail: policyDetail
			};
		},
		initHintRules(state, action) {
			let {payload} = action;

			return {
				...state,
				hintRules: payload
			};
		},
		setDialogShow(state, action) {
			let {dialogShow} = state;
			let {payload} = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let {dialogData} = state;
			let {payload} = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		initPolicyDetail(state, {payload}) {
			let {policyDetail, policyMode, policyUuid} = state;
			policyDetail = payload || {};
			policyMode = policyDetail && policyDetail.mode ? policyDetail.mode : "FirstMatch";
			let dealTypeMappingsTemp = [
				{
					"score": null,
					"dealType": null
				},
				{
					"score": null,
					"dealType": null
				},
				{
					"score": null,
					"dealType": null
				}
			];
			policyDetail["dealTypeMappings"] = policyDetail["dealTypeMapping"] && isJSON(policyDetail["dealTypeMapping"]) ? JSON.parse(policyDetail["dealTypeMapping"]) : [];
			if (policyDetail["dealTypeMappings"].length === 0) {
				policyDetail["dealTypeMappings"] = dealTypeMappingsTemp;
			}
			policyUuid = policyDetail.uuid;

			return {
				...state,
				policyDetail: policyDetail,
				policyMode: policyMode,
				policyUuid: policyUuid
			};
		},
		initPolicyRules(state, {payload}) {
			let {policyRules, policyRulesReady} = state;
			policyRules = payload || [];
			policyRulesReady = true;
			// 处理基本信息
			// 处理规则信息
			policyRules = formatPolicyRules(policyRules);

			return {
				...state,
				policyRules: policyRules,
				policyRulesReady: policyRulesReady
			};
		},
		backupPolicyInfo(state, {payload}) {
			let {policyOriginalAllInfo} = state;
			let data = payload.data || null;
			let policyInfo = data && data.policyInfo ? data.policyInfo : null;
			let policyRules = data && data.rules ? data.rules : null;
			policyOriginalAllInfo["policyInfo"] = policyInfo;
			console.log(policyRules);

			policyRules = formatPolicyRules(policyRules);
			policyOriginalAllInfo["rules"] = policyRules;

			return {
				...state,
				policyOriginalAllInfo: policyOriginalAllInfo
			};
		},
		initRuleConditionById(state, action) {
			let {conditions} = state;
			let data = action.payload.data || null;
			conditions[data["fkRuleUuid"]] = data || {};
			return {
				...state,
				conditions: conditions
			};
		},
		setPolicyRule(state, action) {
			let {policyRules} = state;
			// policyRules = formatPolicyRules(policyRules);
			policyRules = action.payload.policyRules.length ? action.payload.policyRules : policyRules;
			/*
				* 这段循环处理是为了处理params重复的问题，原因是每次修改模板文件，都会push一个name,type,value构成的数组
				* 这段代码5月31日进行了紧急处理，后续还是需要精简的
				* */
			policyRules && policyRules.length > 0 && policyRules.map(item => {
				if (item.conditions && item.conditions.children) {
					item.conditions.children.map(subItem => {
						if (subItem.params && subItem.params.length > 0) {
							let params = subItem.params;
							subItem.params = uniqBy(params, "name");
						}
					});
				}

				// 如果是if规则，判断if下面的子规则
				if (item.ifClause) {
					item.children && item.children.length > 0 && item.children.map(ifItem => {
						ifItem.conditions.children.map(subItem => {
							if (subItem.params && subItem.params.length > 0) {
								let params = subItem.params;
								subItem.params = uniqBy(params, "name");
							}
						});
					});
				}
			});

			return {
				...state,
				policyRules: policyRules
			};
		},
		switchTemplateDrawer(state, action) {
			let {dialogShow, tabIndex} = state;
			let {templateDrawer} = dialogShow;

			if (tabIndex === 1) {
				dialogShow.templateDrawer = true;
			}
			return {
				...state,
				dialogShow: dialogShow
			};
		},
		switchRuleSearch(state, {payload}) {
			let {ruleSearch, tabIndex} = state;
			let {status} = payload;

			if (tabIndex === 1) {
				ruleSearch.showSearch = status;
				ruleSearch.searchWord = null;
			}
			return {
				...state,
				ruleSearch: ruleSearch
			};
		}
	},
	subscriptions: {
		// 监听地址，如果地址含有app则跳转到登陆页
		setup({dispatch, history}) {
			history.listen(location => {
				if (location.pathname.includes("/policy/policyDetail/") || location.pathname.includes("/policy/publicPolicyDetail/")) {
					key("c", () => {
						dispatch({
							type: "switchTemplateDrawer"
						});
					});
					key("s", () => {
						dispatch({
							type: "switchRuleSearch",
							payload: {
								status: true
							}
						});
					});
					key("esc", () => {
						dispatch({
							type: "switchRuleSearch",
							payload: {
								status: false
							}
						});
					});
				}
			});
		}
	}
};
