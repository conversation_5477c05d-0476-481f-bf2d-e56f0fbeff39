import { message } from "antd";
import moment from "moment";
import { immuneAPI } from "@/services";

export default {
	namespace: "immuneConfigList",
	state: {
		immuneConfigListLoad: false,
		immuneConfigList: [],
		indexListReady: false,
		curPage: 1,
		pageSize: 10,
		total: 0,
		pages: 0,
		searchData: {
			ruleUuid: "",
			appName: "",
			ruleName: "",
			createType: "",
			effectFrom: null,
			effectTo: null
		}
	},
	effects: {
		// 获取免疫列表
		*immuneList({}, {call, put, select}) {
			yield put({
				type: "setAttrValue",
				payload: {
					immuneConfigListLoad: true
				}
			});
			const { searchData, curPage, pageSize } = yield select(state=>state.immuneConfigList);
			const { effectFrom, effectTo, appName, ruleName, createType, ruleUuid } = searchData;
			let [effectFromStartTs, effectFromEndTs, effectToEndTs, effectToStartTs] = [null, null, null, null];
			if (effectFrom && effectFrom.length > 1) {
				effectFromStartTs = moment(effectFrom[0]).valueOf();
				effectFromEndTs = moment(effectFrom[1]).valueOf();
			}
			if (effectTo && effectTo.length > 1) {
				effectToStartTs = moment(effectTo[0]).valueOf();
				effectToEndTs = moment(effectTo[1]).valueOf();
			}
			const params = {
				ruleUuid,
				appName,
				ruleName,
				createType,
				curPage,
				pageSize,
				effectFromStartTs,
				effectFromEndTs,
				effectToStartTs,
				effectToEndTs
			};
			let response = yield call(immuneAPI.immuneList, params);
			yield put({
				type: "setAttrValue",
				payload: {
					immuneConfigListLoad: false
				}
			});
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initImmuneList",
				payload: response
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}
			return {
				...state
			};
		},
		setSearchData(state, action) {
			let { payload } = action;
			let { searchData } = state;
			for (let key in payload) {
				if (payload[key] !== undefined) {
					searchData[key] = payload[key];
				}
			}
			return {
				...state,
				searchData
			};
		},
		initImmuneList(state, action) {
			let { immuneConfigList, curPage, pageSize, total, pages } = state;
			let data = action.payload.data || null;
			immuneConfigList = data["dataList"] || [];
			curPage = data.curPage;
			pageSize = data.pageSize;
			total = data.total;
			pages = data.pages;
			return {
				...state,
				immuneConfigList,
				curPage: curPage,
				pageSize: pageSize,
				total: total,
				pages: pages
			};
		}
	}
};
