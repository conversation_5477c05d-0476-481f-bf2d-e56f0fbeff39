import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Form, Row, Col, message } from "antd";
import { policyDetailAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { policyDetailLang } from "@/constants/lang";
import { isPublicPolicy } from "@/utils/utils";

const TextArea = Input.TextArea;

class PolicyCommitModal extends PureComponent {

	state = {
		desc: null,
		submiting: false
	};

	constructor(props) {
		super(props);
		this.policyCommit = this.policyCommit.bind(this);
	}

	policyCommit() {
		let { policyDetailStore, dispatch, refreshFun } = this.props;
		let { policyDetail } = policyDetailStore;
		let desc = this.state.desc;
		if (!desc) {
			message.warning(policyDetailLang.policyCommitModal("inputReleaseDes")); // 请输入发版描述
			return;
		}
		let params = {
			policyUuid: policyDetail.uuid,
			desc: desc
		};
		let commitMethod = "policyCommit";
		if (isPublicPolicy()) {
			commitMethod = "publicPolicyCommit";
		}
		this.setState({submiting: true}, ()=>{
			policyDetailAPI[commitMethod](params).then(res => {
				// 成功 或者 逻辑无变化，和发布版本一致，自动修改状态已发布
				if (res.success || res.code === "200005") {
					message.success(res.message);
					dispatch({
						type: "policyDetail/setDialogShow",
						payload: {
							policyCommit: false
						}
					});
					dispatch({
						type: "policyDetail/changePolicyStatus",
						payload: {
							uuid: policyDetail.uuid
						}
					});
					this.setState({
						desc: null
					});

					// 发版本成功重新调用规则历史接口更新跳转运行区的版本
					refreshFun && refreshFun();

					dispatch({
						type: "global/getAllMap"
					});
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			}).finally(()=>{
				this.setState({submiting: false});
			});
		});
	}

	render() {
		let { policyDetailStore, dispatch } = this.props;
		let { dialogShow, policyDetail } = policyDetailStore;
		let { policyCommit } = dialogShow;

		return (
			<Modal
				title={policyDetailLang.policyCommitModal("title")} // 策略版本提交
				visible={policyCommit}
				maskClosable={true}
				confirmLoading={this.state.submiting}
				onOk={this.policyCommit.bind(this)}
				onCancel={() => {
					dispatch({
						type: "policyDetail/setDialogShow",
						payload: {
							policyCommit: false
						}
					});
					this.setState({
						desc: null
					});
				}}
			>
				<div className="basic-form">
					<Form>
						<Row gutter={CommonConstants.gutterSpan}>
							<Col span={8} className="basic-info-title">
								{/* 策略名称： */}
								{policyDetailLang.policyCommitModal("policyName")}：
    						</Col>
							<Col span={16}>
								<Input
									type="text"
									placeholder={policyDetailLang.policyCommitModal("inputPolicyName")} // 请输入策略名称
									value={policyDetail.name}
									disabled={true}
								/>
							</Col>
						</Row>
						<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
							<Col span={8} className="basic-info-title">
								{/* 发版描述： */}
								{policyDetailLang.policyCommitModal("releaseDes")}：
    						</Col>
							<Col span={16}>
								<TextArea
									placeholder={policyDetailLang.policyCommitModal("inputReleaseDes")} // 请输入发版描述
									value={this.state.desc || undefined}
									onChange={(e) => {
										this.setState({
											desc: e.target.value
										});
									}}
									rows={4}
								/>
							</Col>
						</Row>
					</Form>
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(PolicyCommitModal);
