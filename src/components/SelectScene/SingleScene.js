import { PureComponent } from "react";
import { Icon, Checkbox, Button, Input, Tooltip, Empty } from "antd";
import { indexListLang } from "@/constants/lang";
import { trim } from "lodash";
import "./AppScene.less";

export default class SingleScene extends PureComponent {
	state = {
		searchIng: false, // 开启搜索的状态
		searchWord: null // 搜索内容
	}

	constructor(props) {
		super(props);
	}

	componentDidMount() {
		console.log("come in");
		// sceneListSource 场景数据源
		// scene 当前已选中的场景
		// 判断已选择场景是否在数据源中
		let { sceneListSource = [], scene: sceneOld, changeField, name } = this.props;
		if (sceneOld && typeof sceneOld === "string") {
			sceneOld = JSON.parse(sceneOld);
		}
		if (sceneListSource && typeof sceneListSource === "string") {
			sceneListSource = JSON.parse(sceneListSource);
		}

		sceneOld = sceneOld || [];
		sceneListSource = sceneListSource || [];
		const scene = [];
		sceneOld &&
		sceneOld.length > 0 &&
		sceneOld.forEach(v=>{
			const existScene = sceneListSource.filter(sceneList=>(sceneList.name === v));
			if (existScene && existScene.length > 0) {
				scene.push(v);
			}
		});
		changeField(name, scene, "select", "noModify"); // 传noModify表示指标场景初始化未发生变更无需提示未保存
	}

	changeSceneEvent = (sceneOld = [], nameVal, checked) => {
		const { changeField, name } = this.props;
		const scene = sceneOld && [...sceneOld];
		if (checked) {
			scene.push(nameVal);
		} else {
			scene.splice(scene.indexOf(nameVal), 1);
		}
		changeField(name, scene, "select");
	}
	/**
	 * 全选层面的逻辑
	 * **/
	// 判断是否已经全部选中
	ifAllSelected = (sceneListSource, scene) => {
		if (scene && scene.length > 0) {
			if (sceneListSource.length !== scene.length) {
				return {
					checked: false,
					indeterminate: true
				};
			} else {
				return {
					checked: true,
					indeterminate: false
				};
			}
		} else {
			return {
				checked: false,
				indeterminate: false
			};
		}
	}
	// 全选
	toSelectAll = (sceneListSource, allChecked) =>{
		const { changeField, name } = this.props;
		let selectValue = [];
		if (allChecked) {
			selectValue = [];
		} else {
			sceneListSource.forEach(v=>{
				selectValue.push(v.name);
			});
		}
		changeField(name, selectValue, "select");
	}
	// 搜索内容
	filterOption = (sceneListSource) => {
		const newAppList = [];
		let { searchIng, searchWord } = this.state;
		searchWord = trim(searchWord);
		sceneListSource &&
		sceneListSource.length > 0 &&
		sceneListSource.forEach((item) => {
			const { dName } = item;
			// 如果开启搜索
			if (searchIng && searchWord) {
				if (dName) {
					if (dName.indexOf(searchWord) > -1) {
						newAppList.push(item);
					}
				}
			} else {
				newAppList.push(item);
			}
		});
		return newAppList;
	}
	render() {
		let { searchIng, searchWord } = this.state;
		// sceneListSource 场景数据源
		// scene 当前已选中的场景
		let { sceneListSource = [], scene, disabled } = this.props;
		if (scene && typeof scene === "string") {
			scene = JSON.parse(scene);
		}
		if (sceneListSource && typeof sceneListSource === "string") {
			sceneListSource = JSON.parse(sceneListSource);
		}

		scene = scene || [];
		sceneListSource = sceneListSource || [];

		const newAppList = this.filterOption(sceneListSource);
		// 判断所有复选框状态
		const {checked: allChecked, indeterminate: allIndeterminate} = this.ifAllSelected(sceneListSource, scene);
		return (
			<div className="select-scene-common">
				 <div className="scene-wrap">
					{/* 搜索模块 */}
					{
						searchIng &&
						<div className="scene-search-wrap">
							<Input
								value={searchWord || undefined}
								placeholder={indexListLang.ruleConfig("searchSceneTip")} // lang:搜索应用、策略集
								prefix={
									<Icon
										type="search"
										style={{ color: "rgba(0,0,0,.25)" }}
									/>
								}
								suffix={
									<Tooltip
										title={indexListLang.ruleConfig("exitSearch")} // lang:退出搜索
									>
										<Icon
											type="close"
											style={{ color: "rgba(0,0,0,.45)" }}
											onClick={() => {
												this.setState({
													searchIng: false,
													searchWord: null
												});
											}}
										/>
									</Tooltip>
								}
								style={{ width: "100%" }}
								onChange={(e) => {
									this.setState({
										searchWord: e.target.value
									});
								}}
							/>
						</div>
					}
					<div className="right">
						<div className="scene-content-wrap">
							<div className="scene-content-header">
								<h3>
									{/* lang:具体场景 */}
									 <Checkbox
										disabled={disabled || !(newAppList && newAppList.length > 0) }
										checked={allChecked}
										indeterminate={allIndeterminate}
										onChange={this.toSelectAll.bind(this, sceneListSource, allChecked)}
									>
										{indexListLang.ruleConfig("sceneDetail")}
									</Checkbox>
								</h3>
								<div className="search-handle">
									<Tooltip
										title={indexListLang.ruleConfig("searchSceneTip1")} // lang:搜索场景
									>
										<Button
											type="dashed"
											shape="circle"
											icon="search"
											size="small"
											onClick={() => {
												this.setState({
													searchIng: true
												});
											}}
										/>
									</Tooltip>
								</div>
							</div>
							<div className="scene-content-body">
								<ul className="scene-event-list">
									{
										newAppList &&
										newAppList.length > 0 &&
										newAppList.map((item, index) => {
											return (
												<li
													className="scene-event-item"
													key={index}
												>
													<Checkbox
														checked={(scene || []).indexOf(item.name) > -1}
														onChange={(e) => {
															let checked = e.target.checked;
															this.changeSceneEvent(scene, item.name, checked);
														}}
														disabled={disabled}
													>
														<Tooltip title={item.dName}>
															{item.dName}
														</Tooltip>
													</Checkbox>
												</li>
											);
										})
									}
									{
										!(
											newAppList &&
											newAppList.length > 0
										) && (
											<li>
												<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
											</li>
										)
									}
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}
