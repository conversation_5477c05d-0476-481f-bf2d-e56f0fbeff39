import React, { PureComponent, Fragment, Suspense } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Table, Spin, Pagination, Tag, Tooltip, Button, message, Modal } from "antd";
import { commonLang, publicPolicyListLang } from "@/constants/lang";
import { PolicyConstants } from "@/constants";
import checkPermissionPublicPolicy from "@/constants/permission/checkPermissionPublicPolicy";
import { publicPolicyEditorAPI } from "@/services";
import EffectScope from "@/components/PublicPolicy/EffectScope/EffectScope";
import Search from "../Common/Search";
import AddModifyPublicPolicy from "../Common/Modal/AddModifyPublicPolicy";
import ExportPublicPolicy from "../Common/ExportPublicPolicy";
import "./index.less";

const PublicPolicyImportModal = React.lazy(() => import("@/components/ImportModal/PublicPolicyImportModal"));
const PolicyDrawer = React.lazy(() => import("@/components/PublicPolicy/PublicPolicyDrawer"));
const { confirm } = Modal;

class EditorArea extends PureComponent {
	componentWillMount() {
		// 初始化
		this.closePolicyDetail(); // 关闭公共策略详情
	}
	componentDidMount() {
		this.timer = setInterval(() => {
			const { globalStore } = this.props;
			const { menuTreeReady } = globalStore;
			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkPermissionPublicPolicy.PubPolicyList()) {
					this.search();
				}
			}
		}, 100);
	};
	// 初始化搜索查看
	search = () => {
		let { dispatch } = this.props;
		dispatch({
			type: "publicPolicyEditor/getPublicPolicy"
		});
	};
	// 操作之后刷新操作
	operaRefresh = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyEditor/setAttrValue",
			payload: {
				curPage: 1
			}
		});
		this.search();
		dispatch({
			type: "global/getAllMap",
			payload: {}
		});
	};
	// 搜索折叠
	toggleSearch = () => {
		const { dispatch, publicPolicyEditorStore } = this.props;
		const { searchExpand } = publicPolicyEditorStore;
		dispatch({
			type: "publicPolicyEditor/setAttrValue",
			payload: {
				searchExpand: !searchExpand
			}
		});
	};

	// 删除公共策略
	deletePolicyHandle = (record) => {
		const { dispatch } = this.props;
		const params = {
			uuid: record.uuid
		};
		confirm({
			title: publicPolicyListLang.deleteModal("deletePolicyTip"),	// lang:确认删除公共策略
			content: publicPolicyListLang.deleteModal("deletePolicyDesc1") + record.name + publicPolicyListLang.deleteModal("deletePolicyDesc2"),		// lang:您真的要删除《发版策略》吗？
			onOk: () => {
				publicPolicyEditorAPI.deletePublicPolicy(params).then(res => {
					if (res.success) {
						this.operaRefresh();
						// 更新运行区
						dispatch({
							type: "publicPolicyRunning/getPublicRunningPolicy"
						});
					} else {
						message.error(res.message);
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel() {
				console.log("Cancel");
			}
		});
	};

	// 打开新增公共策略弹窗
	controlAddModifyModal = (visible) => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyEditor/setDialogShow",
			payload: {
				addModifyModal: visible
			}
		});
	};

	// 关闭公共策略弹窗后刷新
	refreshAddModifyModal = () => {
		this.operaRefresh();
	}

	// 查看公共策略详情
	publicPolicyDetail = (record) => {
		const { dispatch, publicPolicyEditorStore } = this.props;
		const { dialogData } = publicPolicyEditorStore || {};
		const { policyDrawer } = dialogData || {};
		if (policyDrawer.uuid && policyDrawer.uuid === record.uuid) {
			dispatch({
				type: "publicPolicyEditor/setDialogShow",
				payload: {
					policyDrawer: false
				}
			});
			dispatch({
				type: "publicPolicyEditor/setDialogData",
				payload: {
					policyDrawer: {
						policyDetail: {},
						uuid: null
					}
				}
			});
			return;
		}
		publicPolicyEditorAPI.publicPolicyDetail({ uuid: record.uuid }).then(res => {
			if (res.success) {
				dispatch({
					type: "publicPolicyEditor/setDialogShow",
					payload: {
						policyDrawer: true
					}
				});
				dispatch({
					type: "publicPolicyEditor/setDialogData",
					payload: {
						policyDrawer: {
							policyDetail: res.data || {},
							uuid: record.uuid
						}
					}
				});
			} else {
				message.error(res.message || publicPolicyListLang.fetchError("policyDetail")); // 查看公共策略详情失败
			}
		}).catch(e => {
			message.error(e.message || publicPolicyListLang.fetchError("policyDetail")); // 查看公共策略详情失败
		});
	};

	// 关闭公共策略详情抽屉
	closePolicyDetail = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyEditor/setDialogShow",
			payload: {
				policyDrawer: false
			}
		});
		dispatch({
			type: "publicPolicyEditor/setDialogData",
			payload: {
				policyDrawer: {
					policyDetail: {},
					uuid: null
				}
			}
		});
	};

	// 修改查看参数
	changeSearchField = (name, e, type) => {
		const { dispatch } = this.props;
		let value = e;
		if (type === "input") {
			value = e.target.value;
		}
		dispatch({
			type: "publicPolicyEditor/setSearchData",
			payload: {
				[name]: value
			}
		});
	};
	conditionSearch = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyEditor/setAttrValue",
			payload: {
				curPage: 1
			}
		});
		this.search();
	}
	// 重置
	reset = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyEditor/setSearchData",
			payload: {
				policyName: "",
				riskType: "",
				eventIds: "",
				ruleName: "",
				ruleCustomId: ""
			}
		});
		dispatch({
			type: "publicPolicyEditor/setAttrValue",
			payload: {
				curPage: 1
			}
		});
	};

	// 翻页
	paginationOnChange = (current, pageSize) => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyEditor/setAttrValue",
			payload: {
				curPage: current,
				pageSize: pageSize
			}
		});
		this.search();
	};

	// 点击公共策略名跳转链接
	goPublicPolicy = (item, e) => {
		e.stopPropagation();
		let { dispatch, location } = this.props;
		let pathname = location.pathname;
		let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
		let baseUrl = `/policy/publicPolicyDetail/${item.uuid}?tabIndex=1`;
		dispatch(routerRedux.push(prefix + baseUrl));
	};

	// 导入公共策略
	importPublicPolicy = (item) => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyEditor/setDialogData",
			payload: {
				publicPolicyImportData: {
					uuid: item.uuid,
					importMode: null,
					file: null,
					replaceScene: "",
					zbMode: "",
					ruleMode: ""
				}
			}
		});
		dispatch({
			type: "publicPolicyEditor/setDialogShow",
			payload: {
				publicPolicyImport: true
			}
		});
	}
	// 表单内容
	columns = () => {
		return (
			[
				{
					title: publicPolicyListLang.table("publicPolicyName"), // 公共策略名称
					dataIndex: "name",
					key: "name",
					width: 280,
					ellipsis: true,
					render: (text, record) => {
						let statusObj = "";
						if (record && record.hasOwnProperty("status")) {
							statusObj = PolicyConstants.policyStatusMap[record.status.toString()];
						}
						return (
							<Fragment>
								{
									statusObj &&
									<Tag
										color={statusObj.color}
									>
										{statusObj.text}
									</Tag>
								}
								<Tooltip placement="left" title={text}>
									<a onClick={(e) => {
										this.goPublicPolicy(record, e);
									}}>{text}</a>
								</Tooltip>
							</Fragment>
						);
					}
				},
				{
					title: publicPolicyListLang.table("effectScope"), // 生效范围
					dataIndex: "scene",
					key: "scene",
					width: 300,
					render: (text, record) => {
						return <EffectScope scene={record.scene && JSON.parse(record.scene)} />;
					}
				},
				{
					title: publicPolicyListLang.table("description"), // 描述
					dataIndex: "description",
					key: "description",
					render: (text)=>{
						return (
							<Tooltip placement="left" title={text}>
								<div className="line-num-2">{text}</div>
							</Tooltip>
						);
					}
				},
				{
					title: publicPolicyListLang.table("modifyTime"), // 修改时间
					width: 120,
					key: "gmtModified",
					dataIndex: "gmtModified"
				},
				{
					title: publicPolicyListLang.table("modifier"), // 修改人
					key: "updatedBy",
					dataIndex: "updatedBy"
				},
				{
					title: publicPolicyListLang.table("operation"), // 操作
					key: "action",
					width: 140,
					render: (text, record) => (
						<div className="table-action">
							<ExportPublicPolicy
								record={record}
								isEditorArea={true}
							/>
							{/* lang:导入规则 */}
							<Tooltip title={publicPolicyListLang.tooltip("importPublicPolicy")}>
								<a onClick={(e) => {
									e.stopPropagation();
									if (checkPermissionPublicPolicy.PublicPolicyImport()) {
										this.importPublicPolicy(record, e);
									} else {
										// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}}>
									<i className={`iconfont icon-download ${!checkPermissionPublicPolicy.PublicPolicyImport() ? "icon-disabled" : ""}`}></i>
								</a>
							</Tooltip>
							{/* 编辑公共策略 */}
							<Tooltip title={publicPolicyListLang.tooltip("editPublicPolicy")} placement="top">
								<a
									onClick={(e) => {
										e.stopPropagation();
										if (checkPermissionPublicPolicy.UpdatePubPolicy()) {
											let { dispatch, location } = this.props;
											let pathname = location.pathname;
											let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
											dispatch(routerRedux.push(prefix + "/policy/publicPolicyDetail/" + record.uuid + "?tabIndex=1"));
										} else {
											// lang:无权限操作
											message.warning(commonLang.messageInfo("noPermission"));
										}
									}}
								>
									<i className={`iconfont icon-edit ${!checkPermissionPublicPolicy.UpdatePubPolicy() ? "icon-disabled" : ""}`}></i>
								</a>
							</Tooltip>
							{/* 删除公共策略 */}
							<Tooltip title={publicPolicyListLang.tooltip("delPublicPolicy")}>
								<a onClick={(e) => {
									e.stopPropagation();
									if (checkPermissionPublicPolicy.DelPubPolicy()) {
										this.deletePolicyHandle(record);
									} else {
										// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}}>
									<i className={`iconfont icon-delete  ${!checkPermissionPublicPolicy.DelPubPolicy() ? "icon-disabled" : ""}`}></i>
								</a>
							</Tooltip>
						</div >
					)
				}
			]
		);
	};

	render() {
		let { publicPolicyEditorStore, globalStore, location } = this.props;
		let { publicPolicyListLoad, publicPolicyList = [], curPage, total, dialogShow, dialogData, searchData, searchExpand } = publicPolicyEditorStore;
		const { policyDrawer } = dialogData || {};
		let { menuTreeReady, allMap } = globalStore;
		return (
			<div className="page-global-body" >
				{
					menuTreeReady &&
					allMap &&
					Object.keys(allMap).length > 0 &&
					checkPermissionPublicPolicy.PubPolicyList() &&
					<Fragment>
						<Search
							changeSearchField={this.changeSearchField}
							searchData={searchData}
							search={this.conditionSearch}
							reset={this.reset}
							toggle={this.toggleSearch}
							searchExpand={searchExpand}
						/>
						{
							checkPermissionPublicPolicy.AddPublicPolicy() &&
							<div>
								{/* 新建公共策略 */}
								<Button type="primary" onClick={this.controlAddModifyModal}>
									{publicPolicyListLang.common("addPublicPolicy")}
								</Button>
							</div>
						}
						<Spin spinning={publicPolicyListLoad}>
							<div className="page-global-body-main mt10">
								<Table
									columns={this.columns()}
									dataSource={publicPolicyList}
									pagination={false}
									rowKey={(e, ind) => ind}
									onRow={(record) => {
										return {
											onClick: () => {
												if (checkPermissionPublicPolicy.PubPolicyDetail()) {
													this.publicPolicyDetail(record);
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}
										};
									}}
								/>

								<div className="page-global-body-pagination">
									{/* lang:共x条记录 */}
									<span className="count">{commonLang.getRecords(total)}</span>
									<Pagination
										showSizeChanger
										onChange={this.paginationOnChange}
										onShowSizeChange={this.paginationOnChange}
										defaultCurrent={1}
										total={total}
										current={curPage}
									/>
								</div>
							</div>
						</Spin>
					</Fragment>
				}

				{
					dialogShow.addModifyModal &&
					<AddModifyPublicPolicy
						onCancel={this.controlAddModifyModal}
						refresh={this.refreshAddModifyModal}
					/>
				}

				<Suspense fallback={null}>
					<PolicyDrawer
						isRunning={false}
						location={location}
						visible={dialogShow.policyDrawer}
						policyDetail={policyDrawer.policyDetail}
						onClose={this.closePolicyDetail}
					/>
				</Suspense>
				<React.Suspense fallback={null}>
					<PublicPolicyImportModal />
				</React.Suspense>
			</div>
		);
	}
};
export default connect(state => ({
	globalStore: state.global,
	publicPolicyRunningStore: state.publicPolicyRunning,
	publicPolicyEditorStore: state.publicPolicyEditor
}))(EditorArea);
