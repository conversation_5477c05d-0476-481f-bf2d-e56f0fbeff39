@import "../../base/variables";

:global {
	.ant-modal.modify-template-editor {
		& {
			top: 0;
			height: 100vh;
			padding: 0;
		}
		.ant-modal-content {
			height: 100%;
			border-radius: 0;
		}
		.steps-content {
			margin-top: 30px;
			margin-bottom: 30px;
			min-height: 250px;
			overflow: hidden;
			.template-preview {
				width: 750px;
				margin-left: auto;
				margin-right: auto;
				.basic-info-title {
					text-align: right;
				}
				.param-tip {
					font-size: 18px;
					color: #999;
					cursor: pointer;
					line-height: 32px;
				}
			}
		}
		.steps-action {
			& {
				position: absolute;
				bottom: 0;
				left: 0;
				text-align: center;
				clear: both;
				margin-top: 20px;
				width: 100%;
				height: 56px;
				line-height: 56px;
				border-top: 1px solid #dcdcdc;
			}
		}
	}
}
