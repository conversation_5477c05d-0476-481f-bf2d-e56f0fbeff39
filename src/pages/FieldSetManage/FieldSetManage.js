/**
 * @Description:字段集管理
 * <AUTHOR>
 * @date 2019/5/8
 */
import React, { PureComponent } from "react";
import { connect } from "dva";
import { Table, Button, message, Select, Popconfirm, Pagination, Input } from "antd";
import NoPermission from "@/components/NoPermission";
import { isJSON } from "@/utils/isJSON";
import { fieldSetAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";
import { fieldSetManagementLang, commonLang } from "@/constants/lang";

const OperateFieldSetModal = React.lazy(() => import("./Modal/OperateFieldSetModal"));
const Option = Select.Option;

class FieldSetManage extends PureComponent {

	constructor(props) {
		super(props);
		this.deleteFieldSet = this.deleteFieldSet.bind(this);
	}

	componentDidMount() {
		this.timer = setInterval(async() => {
			let { globalStore } = this.props;
			let { menuTreeReady } = globalStore;
			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkFunctionHasPermission("ZB0104", "fieldSet")) {
					this.initSearch();
				}
			}
		}, 100);
	}

	async initSearch() {
		let { dispatch } = this.props;
		await dispatch({
			type: "fieldSet/setAttrValue",
			payload: {
				loading: true,
				searchParams: {
					curPage: 1
				}
			}
		});
		await dispatch({
			type: "fieldSet/getFieldSetList"
		});
	}

	async changeFieldValue(field, type, e) {
		let { dispatch, fieldSetStore } = this.props;
		let { searchParams } = fieldSetStore;
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		searchParams[field] = value;
		await dispatch({
			type: "fieldSet/setAttrValue",
			payload: {
				searchParams
			}
		});
		if (field === "type") {
			await this.initSearch();
		}
	}

    paginationOnChange = async(curPage, pageSize) => {
    	const { dispatch } = this.props;
    	await dispatch({
    		type: "fieldSet/setAttrValue",
    		payload: {
    			loading: true,
    			searchParams: {
    				curPage,
    				pageSize
    			}
    		}
    	});
    	await dispatch({
    		type: "fieldSet/getFieldSetList"
    	});
    };

    /**
	 * 删除字段集
	 * @param name
	 */
    deleteFieldSet(id) {
    	const { dispatch, fieldSetStore } = this.props;
    	const { fieldSetList, searchParams } = fieldSetStore;
    	const { curPage } = searchParams;
    	let params = { id };

    	fieldSetAPI.deleteFieldSet(params).then(async res => {
    		if (res.success) {
    			message.success(fieldSetManagementLang.message("deleteSuccess")); // lang: "删除成功"
    			if (fieldSetList.length === 1) {
    				await dispatch({
    					type: "fieldSet/setAttrValue",
    					payload: {
    						loading: true,
    						searchParams: {
    							curPage: curPage - 1 > 0 ? curPage - 1 : 1
    						}
    					}
    				});
    			} else {
    				await dispatch({
    					type: "fieldSet/setAttrValue",
    					payload: {
    						loading: true
    					}
    				});
    			}
    			await dispatch({
    				type: "fieldSet/getFieldSetList"
    			});
    			// 刷新allMap
    			dispatch({
    				type: "global/getAllMap"
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    		message.error("请求异常");
    	});
    }

    render() {
    	const { fieldSetStore, dispatch, globalStore } = this.props;
    	const { menuTreeReady, allMap = {}, personalMode } = globalStore;
    	const lang = personalMode.lang === "cn" ? "cn" : "en";
    	const { fieldSetTypeSelect = [] } = allMap;
    	const { fieldSetList, loading, searchParams, dialogShow } = fieldSetStore;
    	const { addFieldSet, modifyFieldSet } = dialogShow;
    	const { curPage, pageSize, total, type, name, displayName } = searchParams;
    	const cols = [
    		{
    			title: fieldSetManagementLang.table("id"), // lang:"标识",
    			dataIndex: "name",
    			key: "name"
    		},
    		{
    			title: fieldSetManagementLang.table("name"), // lang:"显示名",
    			dataIndex: "displayName",
    			key: "displayName"
    		},
    		{
    			title: fieldSetManagementLang.table("type"), // lang:"类型",
    			dataIndex: "type",
    			key: "type",
    			render: (type) => {
    				const obj = fieldSetTypeSelect && fieldSetTypeSelect.length > 0 ? fieldSetTypeSelect.find(ele => ele && ele.name === type) : null;

    				if (!obj) return "-";
    				return (
    					<span>{lang === "cn" ? obj.dName || obj.enDName : obj.enDName || obj.dName}</span>
    				);
    			}

    		},
    		{
    			title: fieldSetManagementLang.table("operation"), // lang: "操作",
    			key: "operation",
    			width: 150,
    			render: (record, index) => {
    				return (
    					<div className="operation-box" key={index}>
    						<a
    							className={checkFunctionHasPermission("ZB0104", "fieldSetUpdate") ? "mr10" : "mr10 a-disabled"}
    							onClick={() => {
    								if (checkFunctionHasPermission("ZB0104", "fieldSetUpdate")) {
    									dispatch({
    										type: "fieldSet/setAttrValue",
    										payload: {
    											dialogShow: {
    												modifyFieldSet: true
    											},
    											dialogData: {
    												operateFieldSetData: {
    													id: record.id,
    													name: record.name,
    													displayName: record.displayName,
    													type: record.type,
    													fields: isJSON(record.fields) ? JSON.parse(record.fields) : []
    												}
    											}
    										}
    									});
    								}
    							}}>
    							{/* lang:编辑 */}
    							{fieldSetManagementLang.table("modify")}
    						</a>
    						{
    							checkFunctionHasPermission("ZB0104", "fieldSetDelete")
    								? <Popconfirm
    									title={fieldSetManagementLang.table("deleteMsg")} // lang:"确认要删除此字段集吗？"
    									onConfirm={this.deleteFieldSet.bind(this, record.id)}
    									okText={fieldSetManagementLang.table("delete")}
    									cancelText={fieldSetManagementLang.table("cancel")}
    								>
    									<a>
    										{/* lang:删除 */}
    										{fieldSetManagementLang.table("delete")}
    									</a>
    								</Popconfirm>
    								: <a className="a-disabled">
    									{/* lang:删除 */}
    									{fieldSetManagementLang.table("delete")}
    								</a>
    						}
    					</div>
    				);
    			}
    		}
    	];

    	return (
    		<div>
    			<div className="page-global-header">
    				<div className="left-info">
    					<h2>
    						{/* lang:字段集管理 */}
    						{fieldSetManagementLang.common("title")}
    					</h2>
    				</div>
    				{
    					menuTreeReady &&
                        checkFunctionHasPermission("ZB0104", "fieldSet") &&
                        <div className="right-info">
                        	<div className="right-info-item">
                        		<Input
                        			value={name}
                        			placeholder={fieldSetManagementLang.searchParams("fieldSetNamePleaseholder")} // lang:"请输入字段集标识"
                        			onChange={(e) => {
                        				this.changeFieldValue("name", "input", e);
                        			}}
                        		/>
                        	</div>
                        	<div className="right-info-item">
                        		<Input
                        			value={displayName}
                        			placeholder={fieldSetManagementLang.searchParams("fieldSetDisPlayNamePleaseholder")} // lang:"请输入字段集显示名"
                        			onChange={(e) => {
                        				this.changeFieldValue("displayName", "input", e);
                        			}}
                        		/>
                        	</div>
                        	<div className="right-info-item">
                        		<Select
                        			value={type || undefined}
                        			style={{ width: 160 }}
                        			placeholder={fieldSetManagementLang.searchParams("fieldSetTypePlaceholder")} // lang:"请选择字段集类型"
                        			onChange={(e) => {
                        				this.changeFieldValue("type", "select", e);
                        			}}
                        		>
                        			<Option value="all">
                        				{/* lang:全部字段集类型 */}
                        				{fieldSetManagementLang.searchParams("allFieldType")}
                        			</Option>
                        			{
                        				fieldSetTypeSelect &&
                                        Array.isArray(fieldSetTypeSelect) &&
                                        fieldSetTypeSelect.map((item, index) =>
                                        	<Option value={item.name} key={index}>
                                        		{lang === "cn" ? item.dName || item.enDName : item.enDName || item.dName}
                                        	</Option>
                                        )
                        			}
                        		</Select>
                        	</div>
                        	<div className="right-info-item">
                        		<Button
                        			type="primary"
                        			onClick={() => {
                        				this.initSearch();
                        			}}>
                        			{/* lang:搜索 */}
                        			{fieldSetManagementLang.searchParams("search")}
                        		</Button>
                        	</div>
                        	<div className="right-info-item">
                        		<Button
                        			type="primary"
                        			disabled={!checkFunctionHasPermission("ZB0104", "fieldSetAdd")}
                        			onClick={() => {
                        				dispatch({
                        					type: "fieldSet/setAttrValue",
                        					payload: {
                        						dialogShow: {
                        							addFieldSet: true
                        						}
                        					}
                        				});
                        			}}>
                        			{/* lang:新增 */}
                        			{fieldSetManagementLang.searchParams("add")}
                        		</Button>
                        	</div>
                        </div>
    				}
    			</div>
    			{
    				menuTreeReady &&
                    <div className="page-global-body">
                    	{
                    		checkFunctionHasPermission("ZB0104", "fieldSet")
                    			? <div className="page-global-body-main">
                    				<Table
                    					columns={cols}
                    					dataSource={fieldSetList}
                    					pagination={false}
                    					loading={loading}
                    					rowKey={record => record.id}
                    				/>
                    				<div className="page-global-body-pagination">
                    					<span className="count">{commonLang.getRecords(total ? total : 0)}</span>
                    					<Pagination
                    						showQuickJumper
                    						showSizeChanger
                    						onChange={this.paginationOnChange}
                    						onShowSizeChange={this.paginationOnChange}
                    						current={curPage}
                    						pageSize={pageSize}
                    						total={total}
                    					/>
                    				</div>
                    			</div>
                    			: <NoPermission />
                    	}
                    </div>
    			}
    			{
    				(addFieldSet || modifyFieldSet) &&
                    <React.Suspense fallback={null}>
                    	<OperateFieldSetModal initSearch={this.initSearch} />
                    </React.Suspense>
    			}
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	fieldSetStore: state.fieldSet
}))(FieldSetManage);
