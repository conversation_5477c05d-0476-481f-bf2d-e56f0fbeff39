import React, { PureComponent } from "react";
import Sortable from "react-sortablejs";
import "./drag.less";

class RuleList extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let sortable = null;
		let { items, onChange } = this.props;
		const listItems = items.map((item, index) => {
			return (
				<li
					key={index}
					data-id={item}
					className="rule-item"
				>
                    List Item: {item}
				</li>
			);
		});

		return (
			<Sortable
				options={{
					animation: 150,
					ghostClass: "blue-background-class"
				}}
				ref={(c) => {
					if (c) {
						sortable = c.sortable;
					}
				}}
				tag="div"
				onChange={(order, sortable, evt) => {
					onChange(order);
				}}
			>
				{listItems}
			</Sortable>
		);
	}
}

export default RuleList;
