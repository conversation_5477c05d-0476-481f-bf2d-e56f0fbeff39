import { Fragment } from "react";
import { Input, Tag, Select, Row, Col, Progress } from "antd";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";

export default (props) => {
	const { indexData = {} } = props;

	const offlineStateMap = {
		WAIT_RUNNING: {
			cnName: "等待运行",
			enName: "Wait running",
			status: "normal",
			color: "blue"
		},
		WAIT_RESOURCE: {
			cnName: "等待资源",
			enName: "Wait Resource",
			status: "active",
			color: "green"
		},
		SUBMITTING: {
			cnName: "提交中",
			enName: "Submitting",
			status: "active",
			color: "geekblue"
		},
		RUNNING: {
			cnName: "运行中",
			enName: "Running",
			status: "active",
			color: "geekblue"
		},
		RUN_FAIL: {
			cnName: "运行失败",
			enName: "Running Fail",
			status: "exception",
			color: "geekblue"
		},
		RUN_SUSS: {
			cnName: "运行成功",
			enName: "Running Success",
			status: "success",
			color: "green"
		},
		PAUSE: {
			cnName: "暂停",
			enName: "Pause",
			status: "exception",
			color: "red"
		},
		STOP: {
			cnName: "运行终止",
			enName: "Stop",
			status: "exception",
			color: "red"
		}
	};

	let stateObj;
	if (indexData.offlineState && offlineStateMap[indexData.offlineState]) {
		stateObj = offlineStateMap[indexData.offlineState];
	}

	console.log(stateObj);

	return (
		<Fragment>
			<Row className="mb10" gutter={CommonConstants.gutterSpan}>
				<Col span={4} className="basic-info-title">
					{/* lang:计算进度 */}
					{indexListLang.ruleAttr("progress")}
				</Col>
				<Col span={8}>
					<Progress
						percent={indexData.offlinePercent || 0}
						status={stateObj && stateObj["status"]}
						style={{ marginTop: "5px" }}
					/>
				</Col>
			</Row>
			{
				indexData.offlineStartTime &&
                <Row className="mb10" gutter={CommonConstants.gutterSpan}>
                	<Col span={4} className="basic-info-title">
                		{/* lang:计算状态 */}
                		{indexListLang.ruleAttr("status")}
                	</Col>
                	<Col
                		span={8}
                		style={{ lineHeight: "32px" }}
                	>
                		<Tag color={stateObj && stateObj.color}>
                			{stateObj && stateObj.cnName}
                		</Tag>
                	</Col>
                </Row>
			}
			{
				indexData.offlineStartTime &&
                <Row className="mb10" gutter={CommonConstants.gutterSpan}>
                	<Col span={4} className="basic-info-title">
                		{/* lang:计算开始时间 */}
                		{indexListLang.ruleAttr("offlineStartTime")}
                	</Col>
                	<Col span={8}>
                		<Input
                			value={indexData.offlineStartTime}
                			disabled
                		/>
                	</Col>
                </Row>
			}
			{
				indexData.offlineUpdateTime &&
                <Row className="mb10" gutter={CommonConstants.gutterSpan}>
                	<Col span={4} className="basic-info-title">
                		{/* lang:计算更新时间 */}
                		{indexListLang.ruleAttr("offlineUpdateTime")}
                	</Col>
                	<Col span={8}>
                		<Input
                			value={indexData.offlineUpdateTime}
                			disabled
                		/>
                	</Col>
                </Row>
			}
		</Fragment>
	);
};
