import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { TreeSelect } from "antd";
import { cloneDeep } from "lodash";
import { replayTaskLang } from "@/constants/lang";
class HistoryPolicyRule extends PureComponent {
	constructor(props) {
		super(props);
	}
	componentWillMount() {
		const { value, changeFieldValue } = this.props;
		const newVal = cloneDeep(value);
		const { policyList = [] } = newVal || {};
		const newPolicyList = [];
		if (policyList && policyList.length > 0 && policyList[0].hasOwnProperty("policyUuid")) {
			policyList.forEach((v) => {
				const { ruleList } = v;
				ruleList.forEach((v) => {
					newPolicyList.push(v.ruleUuid);
				});
			});
			newVal.policyList = newPolicyList;
			changeFieldValue(newVal);
		}
	}
	getTreeData = () => {
		const { policyRulesSet = [] } = this.props;
		const treeData = [];
		policyRulesSet && policyRulesSet.length > 0 && policyRulesSet.map((v) => {
			const { rules, policyInfo } = v || {};
			const rulesData = rules && rules.map((v) => {
				return {
					title: v.name,
					value: v.uuid,
					key: v.uuid
				};
			});
			treeData.push({
				title: policyInfo.name,
				value: policyInfo.uuid,
				key: policyInfo.uuid,
				children: rulesData || []
			});
		});
		return treeData;
	}
	render() {
		const { changeFieldValue, value = {} } = this.props;
		const { policyList = [] } = value || {};
		const tProps = {
			value: policyList || [],
			treeData: this.getTreeData(),
			searchPlaceholder: replayTaskLang.operatorModal("historyReplaySetPlaceHolder"),
			treeCheckable: true,
			maxTagCount: 10,
			dropdownStyle: { "maxHeight": "400px" },
			maxTagPlaceholder: "...",
			onChange: (e) => {
				const newVal = cloneDeep(value);
				newVal.policyList = e;
				changeFieldValue(newVal);
			},
			style: {
				width: "100%"
			}
		};
		return (
			<Fragment>
				{
					(
						!policyList || policyList.length === 0 ||
						(policyList && policyList.length > 0 && !policyList[0].hasOwnProperty("policyUuid"))
					) &&
					<TreeSelect
						{...tProps}
						dropdownStyle={{ "maxHeight": "400px" }}
						allowClear
					/>
				}
			</Fragment>
		);
	}
}

export default connect(state => ({
	policyDetail: state.policyDetail
}))(HistoryPolicyRule);
