import { PureComponent } from "react";
import { connect } from "dva";
import { Input, InputNumber, Form, message, Modal } from "antd";
import { templateAPI } from "@/services";

const FormItem = Form.Item;

class ModifyGroup extends PureComponent {
	constructor(props) {
		super(props);
		this.changeHandle = this.changeHandle.bind(this);
		this.modifyGroup = this.modifyGroup.bind(this);
	}

	changeHandle(field, type, e) {
		let { dispatch, templateStore } = this.props;
		let { modifyGroupData } = templateStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "inputNumber") {
			value = e;
		}
		modifyGroupData[field] = value;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyGroupData: modifyGroupData
			}
		});
	}

	modifyGroup() {
		let { templateStore, dispatch } = this.props;
		let { currentType, dialogData } = templateStore;
		let { modifyGroupData } = dialogData;
		templateAPI.modifyTemplateGroup(modifyGroupData).then((res) => {
			if (res.success) {
				dispatch({
					type: "template/getTemplateList",
					payload: {
						type: currentType
					}
				});
				message.success("修改分组成功");
				dispatch({
					type: "template/setDialogShow",
					payload: {
						modifyGroup: false
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { templateStore, dispatch } = this.props;
		let { dialogShow, dialogData } = templateStore;
		let { modifyGroupData } = dialogData;
		let formItemLayout = {
			labelCol: { span: 6 },
			wrapperCol: { span: 18 }
		};

		return (
			<Modal
				title="修改分组"
				visible={dialogShow.modifyGroup}
				onOk={this.modifyGroup.bind(this)}
				maskClosable={false}
				onCancel={() => {
					dispatch({
						type: "template/setDialogShow",
						payload: {
							modifyGroup: false
						}
					});
				}}
			>
				<Form>
					<FormItem label="显示名称" {...formItemLayout}>
						<Input
							type="text"
							addonBefore="中文名称"
							placeholder="请输入中文显示名称"
							value={modifyGroupData.displayName || undefined}
							onChange={this.changeHandle.bind(this, "displayName", "input")}
						/>
						<Input
							type="text"
							addonBefore="英文名称"
							placeholder="请输入英文显示名称"
							value={modifyGroupData.enDisplayName || undefined}
							onChange={this.changeHandle.bind(this, "enDisplayName", "input")}
						/>
					</FormItem>
					<FormItem label="唯一标识" {...formItemLayout}>
						<Input
							type="text"
							placeholder="唯一标识请参考以前的"
							value={modifyGroupData.name || undefined}
							onChange={this.changeHandle.bind(this, "name", "input")}
						/>
					</FormItem>
					<FormItem label="排序" {...formItemLayout}>
						<InputNumber
							min={1}
							max={100}
							placeholder="输入排序字段"
							value={modifyGroupData.sort || undefined}
							onChange={this.changeHandle.bind(this, "sort", "inputNumber")}
						/>
					</FormItem>
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(ModifyGroup);
