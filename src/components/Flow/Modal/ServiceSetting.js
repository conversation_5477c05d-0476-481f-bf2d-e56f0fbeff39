import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Select, Table, Icon, message } from "antd";
import "../style/model-setting-modal.less";
import { workflowAPI } from "@/services";
import { commonLang, workflowLang } from "@/constants/lang";
import { filterAvailableFieldList } from "@/utils/filterFieldList";

const Option = Select.Option;

class ServiceSetting extends PureComponent {
    state = {
    	serviceName: null, // 三数据标识
    	invokeType: null,
    	interfaceName: null,
    	interfaceType: null,
    	invokeAccording: null,
    	dataInput: [],
    	dataOutput: []
    };

    constructor(props) {
    	super(props);
    	this.initPage = this.initPage.bind(this);
    	this.changeSystemFieldValue = this.changeSystemFieldValue.bind(this);
    }

    componentDidMount() {
    	let { nodeConfig } = this.props;
    	let serviceName = nodeConfig && nodeConfig.serviceName ? nodeConfig.serviceName : null;
    	let invokeType = nodeConfig && nodeConfig.invokeType ? nodeConfig.invokeType : null;
    	let interfaceName = nodeConfig && nodeConfig.interfaceName ? nodeConfig.interfaceName : null;
    	let interfaceType = nodeConfig && nodeConfig.interfaceType ? nodeConfig.interfaceType : null;
    	let invokeAccording = nodeConfig && nodeConfig.invokeAccording ? nodeConfig.invokeAccording : null;
    	let dataInput = nodeConfig && nodeConfig.dataInput ? nodeConfig.dataInput : [];
    	let dataOutput = nodeConfig && nodeConfig.dataOutput ? nodeConfig.dataOutput : [];

    	this.setState({
    		serviceName: serviceName,
    		invokeType: invokeType,
    		interfaceName: interfaceName,
    		interfaceType: interfaceType,
    		invokeAccording: invokeAccording,
    		dataInput: dataInput,
    		dataOutput: dataOutput
    	});

    	if (serviceName) {
    		this.initPage(serviceName);
    	}
    }

    initPage(serviceName) {
    	let params = {
    		serviceName: serviceName
    	};
    	workflowAPI.getServiceParamsByName(params).then(res => {
    		if (res.success) {
    			let { dataInput, dataOutput } = this.state;
    			let serviceModelInput = res.data && res.data["dataInput"] ? res.data["dataInput"] : [];
    			let serviceModelOutput = res.data && res.data["dataOutput"] ? res.data["dataOutput"] : [];

    			serviceModelInput.map(item => {
    				let inStateObj = dataInput.find(fItem => fItem.dataVar === item.name);
    				if (inStateObj) {
    					item["systemField"] = inStateObj["systemField"];
    				}
    				item["dataVar"] = item.name;
    				delete item.name;
    			});
    			serviceModelOutput.map(item => {
    				let inStateObj = dataOutput.find(fItem => fItem.dataVar === item.name);
    				if (inStateObj) {
    					item["systemField"] = inStateObj["systemField"];
    				}
    				item["dataVar"] = item.name;
    				delete item.name;
    			});
    			this.setState({
    				dataInput: serviceModelInput,
    				dataOutput: serviceModelOutput
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    getServiceParams(serviceName) {
    	let params = {
    		serviceName: serviceName
    	};

    	workflowAPI.getServiceParamsByName(params).then(res => {
    		if (res.success) {
    			let dataInput = res.data && res.data["dataInput"] ? res.data["dataInput"] : [];
    			let dataOutput = res.data && res.data["dataOutput"] ? res.data["dataOutput"] : [];

    			dataInput.map(item => {
    				item["systemField"] = null;
    				item["dataVar"] = item.name;
    				delete item.name;
    			});
    			dataOutput.map(item => {
    				item["systemField"] = null;
    				item["dataVar"] = item.name;
    				delete item.name;
    			});

    			this.setState({
    				dataInput: dataInput,
    				dataOutput: dataOutput
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    changeSystemFieldValue(type, index, value) {
    	let { dataInput, dataOutput } = this.state;
    	let { globalStore } = this.props;
    	let { allMap } = globalStore;
    	let { ruleAndIndexFieldList } = allMap;

    	let itemObj = ruleAndIndexFieldList && ruleAndIndexFieldList.find(item => item.name === value);
    	if (type === "input") {
    		dataInput[index]["systemField"] = value;
    		dataInput[index]["systemFieldDName"] = itemObj.dName;
    		this.setState({
    			dataInput: JSON.parse(JSON.stringify(dataInput))
    		});
    	} else if (type === "output") {
    		dataOutput[index]["systemField"] = value;
    		dataOutput[index]["systemFieldDName"] = itemObj.dName;
    		this.setState({
    			dataOutput: JSON.parse(JSON.stringify(dataOutput))
    		});
    	}
    }

    submitModal = () => {
    	let { serviceName, invokeType, interfaceName, interfaceType, invokeAccording, dataInput, dataOutput } = this.state;
    	let { onChange, dispatch } = this.props;

    	if (!serviceName) {
    		message.warning(workflowLang.serviceSettingModal("selectService")); // lang:请选择模型
    		return;
    	}
    	// 判断input，output参数是否有空值
    	let inputHasEmptyField = false;
    	let outputHasEmptyField = false;

    	dataInput.forEach(item => {
    		if (!item.systemField) {
    			inputHasEmptyField = true;
    		}
    	});
    	dataOutput.forEach(item => {
    		if (!item.systemField) {
    			outputHasEmptyField = true;
    		}
    	});

    	if (inputHasEmptyField) {
    		message.warning(workflowLang.serviceSettingModal("inputFieldEmptyMsg")); // lang:"模型入参选择字段存在空值，请补充完整。");
    		return;
    	}
    	if (outputHasEmptyField) {
    		message.warning(workflowLang.serviceSettingModal("outputFieldEmptyMsg")); // lang:"模型出参选择字段存在空值，请补充完整。");
    		return;
    	}

    	let nodeConfig = {
    		serviceName,
    		invokeType,
    		interfaceName: serviceName,
    		interfaceType,
    		invokeAccording,
    		dataInput,
    		dataOutput
    	};

    	onChange(nodeConfig);

    	dispatch({
    		type: "workflow/setDialogShow",
    		payload: {
    			serviceSetting: false
    		}
    	});

    	// 延迟300ms初始化state
    	setTimeout(() => {
    		this.setState({
    			serviceName: null,
    			dataInput: [],
    			dataOutput: []
    		});
    	}, 300);
    }

    render() {
    	let { workflowStore, globalStore, dispatch, disabled } = this.props;
    	let { serviceName, dataInput, dataOutput } = this.state;
    	let { dialogShow, policySetItem } = workflowStore;
    	let { allMap } = globalStore;
    	let { ruleFieldList = [] } = allMap;

    	const { appName } = policySetItem || {};
    	const ruleAndIndexFieldList = filterAvailableFieldList({allMap, appName});

    	let inputColumns = [
    		{
    			title: workflowLang.serviceSettingModal("serviceInput"), // lang:"服务入参",
    			dataIndex: "systemField",
    			width: 200,
    			render: (text, record, index) => {
    				return (
    					<Select
    						placeholder={workflowLang.serviceSettingModal("serviceInvolvementPlaceholder")} // lang:"请选择入参字段"
    						value={text || undefined}
    						onChange={this.changeSystemFieldValue.bind(this, "input", index)}
    						showSearch
    						optionFilterProp="children"
    						style={{ width: "200px" }}
    						disabled={disabled}
    					>
    						{
    							ruleAndIndexFieldList.map((ruleItem, ruleIndex) => {
    								return (
    									<Option
    										value={ruleItem.name}
    										key={ruleIndex}
    									>
                                            [{ruleItem.sourceName ? commonLang.sourceName(ruleItem.sourceName) : commonLang.sourceName("realtime")}]&nbsp;
    										{ruleItem.dName}
    									</Option>
    								);
    							})
    						}
    					</Select>
    				);
    			}
    		},
    		{
    			title: workflowLang.serviceSettingModal("assignmentDirection"), // lang:"赋值方向",
    			width: 100,
    			render: (text, record, index) => {
    				return (
    					<Icon type="arrow-right" />
    				);
    			}
    		},
    		{
    			title: workflowLang.serviceSettingModal("elementName"), // lang:"要素名",
    			dataIndex: "dName"
    		},
    		{
    			title: workflowLang.serviceSettingModal("elementId"), // lang:"要素标识",
    			dataIndex: "dataVar",
    			width: 200
    		}
    	];
    	let outputColumns = [
    		{
    			title: workflowLang.serviceSettingModal("serviceOutput"), // lang"服务出参",
    			dataIndex: "systemField",
    			width: 200,
    			render: (text, record, index) => {
    				return (
    					<Select
    						placeholder={workflowLang.serviceSettingModal("serviceOutputPlaceholder")} // lang:"请选择出参字段"
    						value={text || undefined}
    						onChange={this.changeSystemFieldValue.bind(this, "output", index)}
    						showSearch
    						optionFilterProp="children"
    						style={{ width: "200px" }}
    						disabled={disabled}
    					>
    						{
    							ruleFieldList.map((ruleItem, ruleIndex) => {
    								return (
    									<Option
    										value={ruleItem.name}
    										key={ruleIndex}
    									>
    										{ruleItem.dName}
    									</Option>
    								);
    							})
    						}
    					</Select>
    				);
    			}
    		},
    		{
    			title: workflowLang.serviceSettingModal("assignmentDirection"), // lang:"赋值方向",
    			width: 100,
    			render: (text, record, index) => {
    				return (
    					<Icon type="arrow-left" />
    				);
    			}
    		},
    		{
    			title: workflowLang.serviceSettingModal("elementName"), // lang:"要素名",
    			dataIndex: "dName"
    		},
    		{
    			title: workflowLang.serviceSettingModal("elementId"), // lang:"要素标识",
    			dataIndex: "dataVar",
    			width: 200
    		}
    	];
    	return (
    		<Modal
    			title={workflowLang.serviceSettingModal("title")} // lang:"模型设置"
    			width={850}
    			className="model-setting-modal"
    			visible={dialogShow.serviceSetting}
    			maskClosable={false}
    			onOk={this.submitModal}
    			onCancel={() => {
    				dispatch({
    					type: "workflow/setDialogShow",
    					payload: {
    						serviceSetting: false
    					}
    				});
    			}}
    		>
    			<div className="model-select">
    				<Select
    					placeholder={workflowLang.serviceSettingModal("selectService")} // lang:"请选择模型"
    					value={serviceName || undefined}
    					onChange={(serviceName) => {
    						this.setState({
    							serviceName: serviceName
    						});
    						this.getServiceParams(serviceName);
    					}}
    					showSearch
    					optionFilterProp="children"
    					disabled={disabled}
    				>
    					{
    						allMap &&
                            allMap["thirdDataSelect"] &&
                            allMap["thirdDataSelect"].map((item, index) => {
                            	return (
                            		<Option
                            			value={item.name}
                            			key={index}
                            		>
                            			{item.dName}
                            		</Option>
                            	);
                            })
    					}
    				</Select>
    			</div>
    			<div className="model-section-wrap">
    				<div className="model-section-header">
    					<h3>
    						{/* lang:入参配置*/}
    						{workflowLang.serviceSettingModal("inputConfiguration")}
    					</h3>
    				</div>
    				<div className="model-section-body">
    					<Table
    						className="table-card-body"
    						columns={inputColumns}
    						dataSource={dataInput}
    						pagination={false}
    						rowKey="name"
    						size="middle"
    					/>
    				</div>
    			</div>
    			<div className="model-section-wrap">
    				<div className="model-section-header">
    					<h3>
    						{/* lang:出参配置*/}
    						{workflowLang.serviceSettingModal("outputConfiguration")}
    					</h3>
    				</div>
    				<div className="model-section-body">
    					<Table
    						className="table-card-body"
    						columns={outputColumns}
    						dataSource={dataOutput}
    						pagination={false}
    						rowKey="name"
    						size="middle"
    					/>
    				</div>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	workflowStore: state.workflow
}))(ServiceSetting);
