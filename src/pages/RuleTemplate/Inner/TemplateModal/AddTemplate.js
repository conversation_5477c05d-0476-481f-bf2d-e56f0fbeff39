import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Input, Form, message, Modal, Switch } from "antd";
import { templateAPI } from "@/services";
import cloneDeep from "lodash.clonedeep";

const FormItem = Form.Item;

class AddTemplate extends PureComponent {
	constructor(props) {
		super(props);
		this.changeHandle = this.changeHandle.bind(this);
		this.addTemplate = this.addTemplate.bind(this);
	}

	changeHandle(field, type, e) {
		let { dispatch, templateStore } = this.props;
		let { addTemplateData } = templateStore.dialogData;
		let value = "";
		if (type === "input" || type === "textarea") {
			value = e.target.value;
		}
		if (type === "select") {
			value = e;
		}
		addTemplateData[field] = value;
		dispatch({
			type: "template/setDialogData",
			payload: {
				addTemplateData: addTemplateData
			}
		});
	}

	addTemplate() {
		let { templateStore, dispatch } = this.props;
		let { currentType, dialogData } = templateStore;
		let { addTemplateData } = dialogData;
		addTemplateData["advanceConfig"] = JSON.stringify(addTemplateData["advanceConfig"]);

		templateAPI.addTemplate(addTemplateData).then((res) => {
			if (res.success) {
				dispatch({
					type: "template/getTemplateList",
					payload: {
						type: currentType
					}
				});
				message.success("新增模板成功");
				dispatch({
					type: "template/setDialogShow",
					payload: {
						addTemplate: false
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { templateStore, dispatch } = this.props;
		let { currentType, dialogShow, dialogData } = templateStore;
		let { addTemplateData } = dialogData;
		let { advanceConfig = {} } = addTemplateData;

		let formItemLayout = {
			labelCol: { span: 4 },
			wrapperCol: { span: 20 }
		};

		return (
			<Modal
				title="添加模板"
				visible={dialogShow.addTemplate}
				width={750}
				onOk={this.addTemplate.bind(this)}
				maskClosable={false}
				onCancel={() => {
					dispatch({
						type: "template/setDialogShow",
						payload: {
							addTemplate: false
						}
					});
				}}
			>
				<Form>
					<FormItem label="显示名称" {...formItemLayout}>
						<Input
							type="text"
							addonBefore="中文名称"
							placeholder="请输入显示名称"
							value={addTemplateData.displayName || undefined}
							onChange={this.changeHandle.bind(this, "displayName", "input")}
						/>
						<Input
							type="text"
							addonBefore="英文名称"
							placeholder="请输入显示名称"
							value={addTemplateData.enDisplayName || undefined}
							onChange={this.changeHandle.bind(this, "enDisplayName", "input")}
						/>
					</FormItem>
					<FormItem label="唯一标识" {...formItemLayout}>
						<Input
							type="text"
							placeholder="唯一标识请参考以前的"
							value={addTemplateData.name || undefined}
							onChange={this.changeHandle.bind(this, "name", "input")}
						/>
					</FormItem>
					<FormItem label="中文描述" {...formItemLayout}>
						<Input
							type="text"
							addonBefore="中文描述"
							placeholder="请输入模板中文描述"
							value={addTemplateData.description || undefined}
							onChange={this.changeHandle.bind(this, "description", "input")}
						/>
						<Input
							type="text"
							addonBefore="英文描述"
							placeholder="请输入模板英文描述"
							value={addTemplateData.enDescription || undefined}
							onChange={this.changeHandle.bind(this, "enDescription", "input")}
						/>
					</FormItem>
					<FormItem label="排序" {...formItemLayout}>
						<Input
							type="text"
							placeholder="请输入排序"
							value={addTemplateData.sort || undefined}
							onChange={this.changeHandle.bind(this, "sort", "input")}
						/>
					</FormItem>
					{
						currentType === 2 &&
                        <Fragment>
                        	<FormItem label="没有场景选择" {...formItemLayout}>
                        		<Switch
                        			checkedChildren="没有场景"
                        			unCheckedChildren="有场景"
                        			checked={!!advanceConfig.noScene}
                        			onChange={(value) => {
                        				let newAdvanceConfig = cloneDeep(advanceConfig);
                        				newAdvanceConfig["noScene"] = value;
                        				this.changeHandle("advanceConfig", "select", newAdvanceConfig);
                        			}}
                        		/>
                        	</FormItem>
                        	<FormItem label="没有过滤条件" {...formItemLayout}>
                        		<Switch
                        			checkedChildren="没有过滤"
                        			unCheckedChildren="有过滤"
                        			checked={!!advanceConfig.noFilterFunction}
                        			onChange={(value) => {
                        				let newAdvanceConfig = cloneDeep(advanceConfig);
                        				newAdvanceConfig["noFilterFunction"] = value;
                        				this.changeHandle("advanceConfig", "select", newAdvanceConfig);
                        			}}
                        		/>
                        	</FormItem>
                        	<FormItem label="支持回溯计算" {...formItemLayout}>
                        		<Switch
                        			checkedChildren="支持"
                        			unCheckedChildren="不支持"
                        			checked={!!advanceConfig.supportOffline}
                        			onChange={(value) => {
                        				let newAdvanceConfig = cloneDeep(advanceConfig);
                        				newAdvanceConfig["supportOffline"] = value;
                        				this.changeHandle("advanceConfig", "select", newAdvanceConfig);
                        			}}
                        		/>
                        	</FormItem>
                        </Fragment>
					}
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(AddTemplate);
