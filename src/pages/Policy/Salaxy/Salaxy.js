import React, { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { searchToObject } from "@/utils/utils";
import { checkFunctionHasPermission } from "@/utils/permission";
import NoPermission from "@/components/NoPermission";
import { Tabs } from "antd";
import { indexListLang } from "@/constants/lang";

const TabPane = Tabs.TabPane;
import EditorArea from "./EditorArea";
const RunningArea = React.lazy(() => import("./RunningArea"));

class Salaxy extends PureComponent {
	constructor(props) {
		super(props);
	}

    closeIndexCiteDrawer = () => {
    	const { indexEditorStore, dispatch } = this.props;
    	const { dialogShow } = indexEditorStore;

    	dialogShow["indexCiteDrawer"] = false;

    	dispatch({
    		type: "indexEditor/setDialogData",
    		payload: {
    			dialogShow: dialogShow
    		}
    	});
    	dispatch({
    		type: "indexEditor/setDialogData",
    		payload: {
    			indexCiteDrawerData: {
    				indexId: null,
    				item: null,
    				dataList: []
    			}
    		}
    	});
    }

    switchTab = (key) => {
    	const { location, dispatch } = this.props;
    	const { pathname } = location;
    	const search = "?currentTab=" + key;

    	dispatch(routerRedux.push(pathname + search));
    	// 在切换tab的时候，移除引用
    	this.closeIndexCiteDrawer();
    }

    render() {
    	const { globalStore, location } = this.props;
    	const { menuTreeReady } = globalStore;
    	const { search } = location;
    	const searchObj = search && searchToObject(search) ? searchToObject(search) : null;
    	const currentTab = searchObj && searchObj.currentTab ? parseInt(searchObj.currentTab, 10) : 1;

    	return (
    		<div>
    			<div className="main-area-wrap">
    				<div className="page-global-tab">
    					<Tabs
    						activeKey={currentTab.toString()}
    						onChange={this.switchTab}
    						animated={false}
    					>
    						<TabPane tab={indexListLang.common("tabForRunning")} key="1">
    							{
    								menuTreeReady &&
                                    checkFunctionHasPermission("ZB0102", "runningSearch") &&
                                    <React.Suspense fallback={null}>
                                    	<RunningArea pagePosition="runningArea" location={location} />
                                    </React.Suspense>
    							}
    							{
    								menuTreeReady && !checkFunctionHasPermission("ZB0102", "runningSearch") &&
                                    <NoPermission />
    							}
    						</TabPane>
    						<TabPane tab={indexListLang.common("tabForEditor")} key="2">
    							{
    								menuTreeReady && checkFunctionHasPermission("ZB0102", "editorSearch") &&
                                    <EditorArea pagePosition="editorArea" location={location} />
    							}
    							{
    								menuTreeReady && !checkFunctionHasPermission("ZB0102", "editorSearch") &&
                                    <NoPermission />
    							}
    						</TabPane>
    					</Tabs>
    				</div>
    			</div>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor
}))(Salaxy);
