import store from "../../app";
import moment from "moment";

const timePicker = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		dataError: {
			"cn": "开始日期必须小于或者等于结束日期",
			"en": "The start date must be less than or equal to the end date"
		},
		timeRangeError: {
			"cn": "时间范围不能超过90天",
			"en": "The time range cannot exceed 90 days"
		},
		dateMap: {
			"cn": {
				"今天": [moment(`${moment().format("YYYY-MM-DD")} 00:00:00`), moment()],
				"昨天": [moment(`${moment().subtract(1, "days").format("YYYY-MM-DD")} 00:00:00`), moment(`${moment().format("YYYY-MM-DD")} 00:00:00`)],
				"最近七天": [moment(`${moment().subtract(6, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"最近三十天": [moment(`${moment().subtract(29, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"本月": [moment(`${moment().startOf("month").format("YYYY-MM-DD")} 00:00:00`), moment()]
			},
			"en": {
				"Today": [moment(`${moment().format("YYYY-MM-DD")} 00:00:00`), moment()],
				"Yesterday": [moment(`${moment().subtract(1, "days").format("YYYY-MM-DD")} 00:00:00`), moment(`${moment().format("YYYY-MM-DD")} 00:00:00`)],
				"Recent 7 days": [moment(`${moment().subtract(6, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"Recent 30 days": [moment(`${moment().subtract(29, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"This month": [moment(`${moment().startOf("month").format("YYYY-MM-DD")} 00:00:00`), moment()]
			}
		}
	};
	return params[field][lang];
};

export default {
	timePicker
};
