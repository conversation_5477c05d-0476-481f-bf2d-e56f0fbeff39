import React, {Fragment} from "react";
import {connect} from "dva";
import {Input, Icon, Select, Row, Col, Popover, Tooltip, Checkbox} from "antd";
import {CommonConstants} from "@/constants";
import {policyDetailLang} from "@/constants/lang";
import {judgeExistVal} from "@/utils/utils";
import "./rule.less";

const InputGroup = Input.Group;
const {Option} = Select;

class RuleAttr extends React.PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			hasExpand: false
		};
		this.changeOperateCode = this.changeOperateCode.bind(this);
		this.modifyRuleName = this.modifyRuleName.bind(this);
		this.changeBaseField = this.changeBaseField.bind(this);
	}

	componentWillMount() {
		let {policyDetailStore, globalStore, ruleIndexArr, dispatch} = this.props;
		let {policyMode, policyRules} = policyDetailStore;
		let {allMap = {}} = globalStore;
		let currentRule = null;
		if (ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : {};
		} else if (ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : {};
		}
		if (currentRule) {
			let weightIndex = currentRule ? currentRule["weightIndex"] : undefined;
			let fkDealTypeUuid = currentRule ? currentRule.fkDealTypeUuid : undefined;
			let upLimit = !!currentRule["weightUpperLimit"];
			let downLimit = !!currentRule["weightLowerLimit"];

			if (currentRule["weightType"] === "salaxyZb") {
				weightIndex = judgeExistVal(weightIndex,
					allMap["salaxyFieldList"] &&
					allMap["salaxyFieldList"].filter(item => item.type === "DOUBLE" || item.type === "INT")
				);
			}
			if (currentRule["weightType"] === "systemField") {
				weightIndex = judgeExistVal(weightIndex,
					allMap["ruleFieldList"] &&
					allMap["ruleFieldList"].filter(item => item.type === "DOUBLE" || item.type === "INT")
				);
			}
			if (currentRule.ifClause !== "IF" && policyMode !== "Weighted") {
				fkDealTypeUuid = judgeExistVal(fkDealTypeUuid, allMap.dealTypeList);
			}

			currentRule["weightIndex"] = weightIndex;
			currentRule.fkDealTypeUuid = fkDealTypeUuid;
			dispatch({
				type: "policyDetail/setPolicyRule",
				payload: {
					policyRules: policyRules
				}
			});
			dispatch({
				type: "policyDetail/setAttrValue",
				payload: {
					upLimit,
					downLimit
				}
			});

		}
	}

	changeOperateCode(value) {
		let {policyDetailStore, dispatch, ruleIndexArr} = this.props;
		let {policyRules} = policyDetailStore;

		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]].fkDealTypeUuid = value;
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]].fkDealTypeUuid = value;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	modifyRuleName(e) {
		let {policyDetailStore, dispatch, ruleIndexArr} = this.props;
		let {policyRules} = policyDetailStore;
		let value = e.target.value;

		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["name"] = value;
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["name"] = value;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	changeHintRules = (val, field) => {
		console.log(val, field);
		let {policyDetailStore, dispatch, ruleIndexArr} = this.props;
		let {policyRules} = policyDetailStore;
		let value = val;

		if (ruleIndexArr.length === 1) {
			let rules = policyRules[ruleIndexArr[0]]["reminder"] ? policyRules[ruleIndexArr[0]]["reminder"] : {};
			let obj = {[field]: val};
			rules = {...rules, ...obj};
			policyRules[ruleIndexArr[0]]["reminder"] = rules;
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			let rules = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["reminder"];
			let obj = {[field]: val};
			rules = {...rules, ...obj};
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["reminder"] = rules;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	};

	changeBaseField(field, type, e) {
		let {policyDetailStore, dispatch, ruleIndexArr} = this.props;
		let {policyRules} = policyDetailStore;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]][field] = value;
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]][field] = value;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}
		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	// 控制上下限
	changeLimit = (data) => {
		const {dispatch} = this.props;
		dispatch({
			type: "policyDetail/setAttrValue",
			payload: data
		});
	};
	// 展开
	handleExpand = () => {
		let a = this.state.hasExpand;
		this.setState({
			hasExpand: !a
		});
	};

	render() {
		let {policyDetailStore, globalStore, ruleIndexArr} = this.props;
		let {policyMode, policyRules, upLimit, downLimit, hintRules} = policyDetailStore;
		let {allMap = {}} = globalStore;
		let currentRule = null;
		if (ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : {};
		} else if (ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : {};
		}
		let {reminder = {}} = currentRule;
		let ruleList = Object.keys(hintRules).sort();
		if (!this.state.hasExpand) {
			ruleList = [ruleList[0]];
		}
		return (
			<div className="ml-1">
				<Row gutter={CommonConstants.gutterSpan} className="mb10">
					<Col span={4} className="basic-info-title">
						{/* 规则名称 */}
						{policyDetailLang.ruleAttr("ruleName")}
					</Col>
					<Col span={8}>
						<Input
							type="text"
							value={currentRule.name || undefined}
							placeholder={policyDetailLang.ruleAttr("inputStrat")} // 请输入策略名称
							onChange={this.modifyRuleName.bind(this)}
						/>
					</Col>
				</Row>
				{
					currentRule.ifClause !== "IF" && policyMode === "Weighted" &&
					<Fragment>
						<Row gutter={CommonConstants.gutterSpan}>
							<Col span={4} className="basic-info-title">
								{/* 风险权重 */}
								{policyDetailLang.ruleAttr("riskWeighting")}
							</Col>
							<Col span={2}>
								<Input
									type="text"
									value={(currentRule["baseWeight"] || currentRule["baseWeight"] === 0) ? currentRule["baseWeight"] : undefined}
									onChange={this.changeBaseField.bind(this, "baseWeight", "input")}
								/>
							</Col>
							<Col span={1}>
								<span className="ml10 mr10 inline-span">+</span>
							</Col>
							<Col span={2}>
								<Input
									type="text"
									value={(currentRule["weightRatio"] || currentRule["weightRatio"] === 0) ? currentRule["weightRatio"] : undefined}
									onChange={this.changeBaseField.bind(this, "weightRatio", "input")}
								/>
							</Col>
							<Col span={3}>
								<Select
									onChange={this.changeBaseField.bind(this, "weightOperator", "select")}
									value={currentRule["weightOperator"] || undefined}
									showSearch
									optionFilterProp="children"
									dropdownMatchSelectWidth={false}
								>
									<Option value="add">{policyDetailLang.ruleAttr("addition")}</Option>
									<Option value="sub">{policyDetailLang.ruleAttr("substruction")}</Option>
									<Option value="mul">{policyDetailLang.ruleAttr("multiplication")}</Option>
									<Option value="div">{policyDetailLang.ruleAttr("division")}</Option>
								</Select>
							</Col>
							<Col span={8}>
								<InputGroup compact>
									<Select
										style={{width: "40%"}}
										onChange={(value) => {
											this.changeBaseField("weightType", "select", value);
											// 当类型改变的时候，清空值
											this.changeBaseField("weightIndex", "select", "");
										}}
										value={currentRule["weightType"] || undefined}
										showSearch
										optionFilterProp="children"
										dropdownMatchSelectWidth={false}
									>
										<Option value="salaxyZb">
											{policyDetailLang.ruleAttr("indicator")}
										</Option>
										<Option value="systemField">
											{policyDetailLang.ruleAttr("systemField")}
										</Option>
									</Select>
									{
										currentRule["weightType"] === "salaxyZb" &&
										<Select
											style={{width: "60%"}}
											onChange={this.changeBaseField.bind(this, "weightIndex", "select")}
											value={currentRule["weightIndex"] || ""}
											showSearch
											optionFilterProp="children"
											dropdownMatchSelectWidth={false}
											placeholder={policyDetailLang.common("select")}
										>
											<Option value="">
												{/* 默认选项 */}
												{policyDetailLang.ruleAttr("defaultOption")}
											</Option>
											{
												allMap["salaxyFieldList"] &&
												allMap["salaxyFieldList"].filter(item => item.type === "DOUBLE" || item.type === "INT").map((item, index) => {
													return (
														<Option value={item.name} key={index}>
															{item.dName}
														</Option>
													);
												})
											}
										</Select>
									}
									{
										currentRule["weightType"] === "systemField" &&
										<Select
											style={{width: "60%"}}
											onChange={this.changeBaseField.bind(this, "weightIndex", "select")}
											value={currentRule["weightIndex"] || undefined}
											showSearch
											optionFilterProp="children"
											dropdownMatchSelectWidth={false}
											placeholder={policyDetailLang.common("select")}
										>
											{
												allMap["ruleFieldList"] &&
												allMap["ruleFieldList"].filter(item => item.type === "DOUBLE" || item.type === "INT").map((item, index) => {
													return (
														<Option value={item.name} key={index}>
															{item.dName}
														</Option>
													);
												})
											}
										</Select>
									}
								</InputGroup>
							</Col>
						</Row>
						<Row gutter={CommonConstants.gutterSpan} className="mt10">
							<Col span={4} className="basic-info-title">
							</Col>
							<Col span="20">
								<Row type="flex" align="middle">
									<Checkbox
										className="fl"
										checked={upLimit}
										onChange={(e) => {
											this.changeLimit({upLimit: e.target.checked});
											this.changeBaseField("weightUpperLimit", "select", "");
										}}
									>
										{/* 上限 */}
										{policyDetailLang.ruleAttr("upLimit")}
									</Checkbox>
									{
										upLimit &&
										<Col span={4} className="mr20">
											<Input
												value={currentRule["weightUpperLimit"] || ""}
												onChange={this.changeBaseField.bind(this, "weightUpperLimit", "input")}
											/>
										</Col>
									}
									<Checkbox
										className="fl"
										checked={downLimit}
										onChange={(e) => {
											this.changeLimit({downLimit: e.target.checked});
											this.changeBaseField("weightLowerLimit", "select", "");
										}}
									>
										{/* 下限 */}
										{policyDetailLang.ruleAttr("downLimit")}
									</Checkbox>
									{
										downLimit &&
										<Col span={4} className="mr10">
											<Input
												value={currentRule["weightLowerLimit"] || ""}
												onChange={this.changeBaseField.bind(this, "weightLowerLimit", "input")}
											/>
										</Col>
									}
								</Row>
							</Col>
						</Row>
					</Fragment>
				}
				{
					currentRule.ifClause !== "IF" &&
					policyMode !== "Weighted" &&
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={4} className="basic-info-title">
							{/* 风险决策 */}
							{policyDetailLang.ruleAttr("riskDecision")}
						</Col>
						<Col span={8}>
							<Select
								value={currentRule ? currentRule.fkDealTypeUuid : undefined}
								onChange={this.changeOperateCode.bind(this)}
								dropdownMatchSelectWidth={false}
								placeholder={policyDetailLang.common("select")}
							>
								{
									allMap.dealTypeList &&
									allMap.dealTypeList.map((item, index) => {
										return (
											<Option value={item.uuid} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
						<Col span={1}>
							<Popover
								popupClassName="rule-param-pop-tip"
								placement="right"
								title={policyDetailLang.ruleAttr("riskDecision")} // 风险决策
								content={policyDetailLang.ruleAttr("riskIntroduced")} // 风险决策介绍
							>
								<Icon
									type="question-circle-o"
									className="param-tip-icon"
								/>
							</Popover>
						</Col>
					</Row>
				}
				{/* 规则提示定义	*/}
				{
					ruleList && ruleList.map((item, index) => {
						let children = hintRules[item];
						return <div className="hint">
							<Row gutter={CommonConstants.gutterSpan} className="mb10">
								<Col span={4} className="basic-info-title">
									{/* 规则提示 */}
									{policyDetailLang.ruleAttr("rulesOfTheTip")}{(index + 1)}
								</Col>
								<Col span={8}>
									<Select allowClear value={reminder[item]} placeholder="请选择" onChange={(val) => {
										this.changeHintRules(val, item);
									}}>
										{
											children && children.map((cItem, cIndex) => {
												return <Option value={cItem.code}>{cItem.description}</Option>;
											})
										}
									</Select>
								</Col>
								<Col span={1}>
									{
										index === (ruleList.length - 1) &&
										<div onClick={this.handleExpand} style={{height: "26px", lineHeight: "26px"}}
											 className="double-right"
										>
											<Popover
												placement="right"
												title={this.state.hasExpand ? policyDetailLang.ruleAttr("packUp") : policyDetailLang.ruleAttr("unfold")}
												content={this.state.hasExpand ? policyDetailLang.ruleAttr("packUpContent") : policyDetailLang.ruleAttr("unfoldContent")}
											>
												<Icon className={this.state.hasExpand ? "icon2" : "icon1"}
													  type="double-right"/>
											</Popover>
										</div>
									}

								</Col>
							</Row>
						</div>;
					})
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(RuleAttr);
