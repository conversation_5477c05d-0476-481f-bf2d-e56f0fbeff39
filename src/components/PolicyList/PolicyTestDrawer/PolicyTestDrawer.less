@header-height: 60px;

:global {
    .policy-test-wrap {
        & {}

        .ant-drawer-content-wrapper {
            -webkit-box-shadow: 0 2px 16px rgba(0, 0, 0, .2);
            box-shadow: 0 2px 16px rgba(0, 0, 0, .2);
            background-clip: padding-box;
        }

        .ant-drawer-header {
            display: none;
        }

        .ant-drawer-body {
            padding: 0;
        }

        .ant-drawer-close {
            display: none;
        }

        .policy-test-main {
            .policy-test-header {
                & {
                    position: absolute;
                    top: 0;
                    left: 0;
                    height: @header-height;
                    line-height: @header-height;
                    background: #fff;
                    width: 100%;
                    font-size: 20px;
                    border-bottom: 1px solid #e8e8e8;
                    z-index: 99;
                    padding: 0 20px;
                }

                .title-spider {
                    font-size: 26px;
                    color: #A0A0A0;
                    margin-right: 10px;
                }

                .policy-test-select {
                    width: 200px;
                }

                .title-close {
                    float: right;
                    position: relative;
                    top: 0;
                    height: @header-height;
                    line-height: @header-height;
                    font-size: 14px;
                    cursor: pointer;
                    -webkit-transition: all 0.2s ease-out;
                    -moz-transition: all 0.2s ease-out;
                    -ms-transition: all 0.2s ease-out;
                    -o-transition: all 0.2s ease-out;
                }

                .title-close:hover {
                    font-size: 15px;
                    color: #c90000;
                    font-weight: bold;
                }
            }

            .policy-test-body {
                & {
                    margin-top: 56px;
                    padding: 20px;
                    height: ~"calc(100vh - 120px)";
                    overflow-y: auto;
                }

                &.child-drawer-body {
                    height: ~"calc(100vh - 72px)";
                }

                .basic-form {
                    .ant-alert {
                        margin-bottom: 10px;
                    }

                    .ant-row {
                        & {
                            margin-bottom: 12px;
                        }

                        .basic-info-title {
                            text-align: right;
                            line-height: 32px;

                            em {
                                font-size: 12px;
                                color: #ff0000;
                            }
                        }

                        .basic-info-btns {
                            button {
                                margin-right: 12px;
                            }
                        }

                        .tip-text {
                            color: #ff712e;
                            padding-top: 5px;
                            display: inline-block;
                        }

                        .ant-checkbox-wrapper {
                            line-height: 32px;
                        }

                        .random-field {
                            & {
                                line-height: 32px;
                                cursor: pointer;
                            }

                            a {
                                color: inherit;
                            }

                            &:hover {
                                color: #2f80f7;
                            }

                            i {
                                font-size: 18px;
                            }

                            span {
                                margin-left: 6px;
                            }
                        }
                    }

                    .result-item-wrap {
                        & {
                            position: relative;
                            border: 1px dashed #dcdcdc;
                            padding: 20px;
                            margin-bottom: 20px;
                            overflow: hidden;
                        }

                        &:hover {
                            background: #fafafa;

                            .result-delete {
                                display: block;
                            }
                        }

                        .result-delete {
                            & {
                                position: absolute;
                                left: 0;
                                top: 0;
                                width: 56px;
                                height: 56px;
                                text-align: center;
                                line-height: 56px;
                                cursor: pointer;
                                z-index: 9;
                                display: none;
                            }

                            i {
                                font-size: 20px;
                            }

                            &:hover {
                                background: #f0f0f0;

                                i {
                                    color: red;
                                }
                            }
                        }
                    }
                }

                // 命中规则
                .hit-policy-list {
                    & {
                        border: 1px dashed #dcdcdc;
                        padding: 20px;
                        margin-bottom: 10px;
                    }

                    .hit-policy-header {
                        & {
                            height: 40px;
                            padding-bottom: 20px;
                            border-bottom: 1px solid #dcdcdc;
                        }

                        h3 {
                            font-size: 16px;
                            font-weight: normal;

                            span {
                                margin-right: 12px;
                            }

                            .ant-tag {
                                position: relative;
                                top: -2px;
                            }
                        }
                    }

                    .hit-policy-body {
                        & {}

                        .hit-rule-list {
                            & {
                                padding-left: 0;
                            }

                            .hit-rule-item {
                                & {
                                    position: relative;
                                    line-height: 40px;
                                    border-bottom: 1px dashed #dcdcdc;
                                    list-style: square;
                                }

                                .is-rule-tip {
                                    color: #999;
                                }

                                .rule-attr {
                                    position: absolute;
                                    right: 0;
                                    top: 0;
                                }
                            }
                        }
                    }
                }
            }

            .policy-test-footer {
                & {
                    position: absolute;
                    bottom: 0;
                    width: 100%;
                    height: 48px;
                    line-height: 48px;
                    border-top: 1px solid #e8e8e8;
                    text-align: right;
                    left: 0;
                    background: #fff;
                }

                button {
                    font-size: 16px;
                    width: 100%;
                    border-radius: 0;
                    height: 100%;
                }
            }
        }
    }
}
