import { PureComponent } from "react";
import { indexListLang } from "@/constants/lang";
import "./ShowScene.less";

export default class ShowAppScene extends PureComponent {
	constructor(props) {
		super(props);
	}
	render() {
		let { sceneListSource, scene, className } = this.props;
		if (scene && typeof scene === "string") {
			scene = JSON.parse(scene);
		}
		if (sceneListSource && typeof sceneListSource === "string") {
			sceneListSource = JSON.parse(sceneListSource);
		}
		scene = scene || [];
		sceneListSource = sceneListSource || [];
		return (
			<div className={`show-scene-table-wrap ${className}`}>
				<table>
					<thead>
						<tr>
							<th>
								{/* lang:应用 */}
								{indexListLang.ruleConfig("application")}
							</th>
							<th>
								{/* lang:策略集 */}
								{indexListLang.ruleConfig("policySet")}
							</th>
						</tr>
					</thead>
					<tbody>
						{
							scene &&
							scene.map((sceneItem, sceneIndex) => {
								const { eventList = [], appName } = sceneItem;
								let appObj = sceneListSource.find(app => app.name === appName) || {};
								let eventDNameList = [];
								eventList.forEach(eventId => {
									let policySetObj = (appObj.eventList || []).find(setItem => setItem.name === eventId);
									if (policySetObj) {
										if (eventId) {
											eventDNameList.push(policySetObj.dName);
										}
									}
								});

								if (appObj && appObj.dName && eventDNameList && eventDNameList.length > 0) {
									return (
										<tr key={sceneIndex}>
											<td
												width={150}
											>
												{appObj && appObj.dName}
											</td>
											<td>
												{eventDNameList && eventDNameList.join(" | ")}
											</td>
										</tr>
									);
								}
							})
						}
					</tbody>
				</table>
			</div>
		);
	}
}
