import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getPolicyApprovalTaskList = async(params) => {
	return request(getUrl("/admin/approval/task/listPolicy", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getSalaxyApprovalTaskList = async(params) => {
	return request(getUrl("/admin/approval/task/listZb", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getWorkflowApprovalTaskList = async(params) => {
	return request(getUrl("/admin/approval/task/listDecisionFlow", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getPolicyApprovalTaskDiff = async(params) => {
	return request(getUrl("/admin/approval/task/diffPolicy", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getSalaxyApprovalTaskDiff = async(params) => {
	return request(getUrl("/admin/approval/task/diffZb", params), {
		method: "GET",
		headers: getHeader()
	});
};
const getWorkflowApprovalTaskDiff = async(params) => {
	return request(getUrl("/admin/approval/task/diffDecisionFlow", params), {
		method: "GET",
		headers: getHeader()
	});
};

const doPolicyApproval = async(params) => {
	return request("/admin/approval/task/doPolicy", {
		method: "PUT",
		headers: getHeader(),
		body: params
	});
};

const doSalaxyApproval = async(params) => {
	return request("/admin/approval/task/doZb", {
		method: "PUT",
		headers: getHeader(),
		body: params
	});
};
const doWorkflowApproval = async(params) => {
	return request("/admin/approval/task/doDecisionFlow", {
		method: "PUT",
		headers: getHeader(),
		body: params
	});
};

export default {
	getPolicyApprovalTaskList,
	getSalaxyApprovalTaskList,
	getWorkflowApprovalTaskList,
	getPolicyApprovalTaskDiff,
	getSalaxyApprovalTaskDiff,
	getWorkflowApprovalTaskDiff,
	doPolicyApproval,
	doSalaxyApproval,
	doWorkflowApproval
};
