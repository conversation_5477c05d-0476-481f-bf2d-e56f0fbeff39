import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request, { PostForm } from "../utils/request";

const addPublicPolicy = async (params) => {
	return request("/admin/publicPolicy", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const publicList = async (params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/publicPolicy/list", params), {
		method: "GET",
		headers: getHeader()
	});
};

const deletePublicPolicy = async (params) => {
	return request("/admin/publicPolicy/delete", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const publicPolicyDetail = async (params) => {
	return request(getUrl("/admin/publicPolicy/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};
const modifyPublicPolicy = async (params) => {
	return request("/admin/publicPolicy/modify", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

// 导入规则列表
const importPublicRules = async (params) => {
	const res = await PostForm("/admin/publicPolicy/import", "post", { ...params });
	return res;
};

export default {
	addPublicPolicy,
	publicList,
	deletePublicPolicy,
	publicPolicyDetail,
	modifyPublicPolicy,
	importPublicRules
};
