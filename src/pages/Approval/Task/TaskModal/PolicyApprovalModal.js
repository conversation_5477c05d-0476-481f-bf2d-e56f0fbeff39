import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, message } from "antd";
import { policyDetailAPI } from "@/services";
import { approvalTaskLang } from "@/constants/lang";
import PolicyApprovalBase from "./Inner/PolicyApprovalBase";
import PolicyApprovalDiff from "./Inner/PolicyApprovalDiff";
import "./Less/ApprovalModal.less";

class PolicyApprovalModal extends PureComponent {

    state = {
    	desc: null
    };

    constructor(props) {
    	super(props);
    	this.policyCommit = this.policyCommit.bind(this);
    	this.changeLeftNav = this.changeLeftNav.bind(this);
    }

    policyCommit() {
    	let { policyDetailStore, globalStore, dispatch } = this.props;
    	let { policyDetail } = policyDetailStore;
    	let { userInfoMode } = globalStore;
    	let desc = this.state.desc;
    	if (!desc) {
    		// lang:请输入审批描述
    		message.warning(approvalTaskLang.modal("enterDescriptionPlaceholder"));
    		return;
    	}
    	let params = {
    		account: userInfoMode.account,
    		policyUuid: policyDetail.uuid,
    		desc: desc
    	};

    	policyDetailAPI.policyCommit(params).then(res => {
    		if (res.success) {
    			// lang:策略版本提交成功
    			message.success(approvalTaskLang.modal("policyVersionSubmitOk"));
    			dispatch({
    				type: "policyDetail/setDialogShow",
    				payload: {
    					policyCommit: false
    				}
    			});
    			this.setState({
    				desc: null
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    changeLeftNav(index) {
    	let { approvalTaskStore, dispatch } = this.props;
    	let { dialogData } = approvalTaskStore;
    	let { policyApprovalData } = dialogData;

    	policyApprovalData.leftNavIndex = index;
    	dispatch({
    		type: "approvalTask/setDialogData",
    		payload: {
    			policyApprovalData: policyApprovalData
    		}
    	});
    }

    render() {
    	let { approvalTaskStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = approvalTaskStore;
    	let { policyApproval } = dialogShow;
    	let { policyApprovalData } = dialogData;
    	let { leftNavIndex } = policyApprovalData;

    	return (
    		<Modal
    			title={approvalTaskLang.modal("policyTitle")} // lang:策略版本审核
    			visible={policyApproval}
    			className="approval-modal-wrap"
    			maskClosable={true}
    			width={1180}
    			onOk={this.policyCommit.bind(this)}
    			onCancel={() => {
    				dispatch({
    					type: "approvalTask/setDialogShow",
    					payload: {
    						policyApproval: false
    					}
    				});
    				setTimeout(() => {
    					dispatch({
    						type: "approvalTask/setDialogData",
    						payload: {
    							policyApprovalData: {
    								uuid: null,
    								status: "APPROVED",
    								desc: null,
    								leftNavIndex: 0,
    								tabIndex: 0,
    								diffTime: "online",
    								diffDetail: {
    									onlineVersion: null,
    									pendVersion: null
    								}
    							}
    						}
    					});
    				}, 300);
    			}}
    			footer={null}
    		>
    			<div className="approval-wrap">
    				<div className="left-nav">
    					<ul>
    						<li
    							className={leftNavIndex === 0 ? "active" : ""}
    							onClick={this.changeLeftNav.bind(this, 0)}
    						>
    							{/* lang:策略审批 */}
    							{approvalTaskLang.modal("policyApproval")}
    						</li>
    						<li
    							className={leftNavIndex === 1 ? "active" : ""}
    							onClick={this.changeLeftNav.bind(this, 1)}
    						>
    							{/* lang:修改详情 */}
    							{approvalTaskLang.modal("changeDetail")}
    						</li>
    					</ul>
    				</div>
    				<div className="right-content">
    					{
    						leftNavIndex === 0 && <PolicyApprovalBase />
    					}
    					{
    						leftNavIndex === 1 && <PolicyApprovalDiff />
    					}
    				</div>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(PolicyApprovalModal);
