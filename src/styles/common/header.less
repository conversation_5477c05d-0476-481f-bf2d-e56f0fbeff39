@import "../base/variables";

:global {
	.basic-header.ant-layout-header {
		& {
			position: relative;
			top: 0;
			left: 0;
			height: @header-height;
			line-height: @header-height;
			color: #fff;
			z-index: 101;
			padding: 0;
			box-shadow: 0 1px 8px rgba(0, 0, 0, .3);
			transition: box-shadow 200ms cubic-bezier(0.4, 0, 0.2, 1);
			background-color: @headerColor1;
			background: @headerColor1;
			background: -webkit-linear-gradient(108deg, @headerColor2, @headerColor1 90%);
		}
		.sidebar-collapsed-controller {
			& {
				position: absolute;
				left: 0;
				top: 0;
				text-align: center;
				width: @header-height;
				line-height: @header-height;
				height: @header-height;
				cursor: pointer;
			}
			&:hover {
				& {
					background-color: rgba(0, 0, 0, 0.08);
				}
				.anticon.trigger {
					color: #f0f0f0;
				}
			}
			.anticon.trigger {
				& {
					color: #fff;
					font-size: 20px;
					cursor: pointer;
				}
			}
		}
	}
	.development-login-modal {
		& {
			color: #fff !important;
			opacity: .75 !important;
			font-size: 15px !important;
		}
	}
	// right
	.navbar-content {
		& {
			position: absolute;
			top: 0;
			right: 20px;
		}
		.navbar-top-links {
			& {
				float: right;
				margin-bottom: 0;
			}
			li {
				& {
					float: left;
					list-style: none;
					margin-left: 20px;
				}
				&.system-info, &.notice-info {
					a i {
						font-size: 22px;
					}
				}
				a i {
					opacity: 0.75;
				}
				&:hover a i {
					opacity: 1;
				}
				> a {
					& {
						display: table-cell;
						font-size: 18px;
						vertical-align: middle;
						height: @header-height;
						color: #fff;
						-webkit-transition: all 0.4s;
						transition: all 0.4s;
						text-decoration: none;
					}
					i {
						font-size: 30px;
						vertical-align: middle;
					}
					.anticon-github {
						font-size: 40px !important;
					}
					&:focus {
						background-color: transparent;
					}
					&:hover {
						-webkit-transition: all 0.4s;
						transition: all 0.4s;
					}
					.user-avatar-wrap {
						& {
							position: relative;
							width: 40px;
							height: 40px;
							-webkit-border-radius: 50%;
							-moz-border-radius: 50%;
							border-radius: 50%;
							margin-left: 0;
						}
						img {
							width: 40px;
							height: 40px;
							-webkit-border-radius: 50%;
							-moz-border-radius: 50%;
							border-radius: 50%;
							background: #7594c3;
						}
					}
					span {
						margin-left: 8px;
					}
				}
			}
			& > .open > a,
			& > .open > a:focus {
				background-color: #f2f2f2;
				color: #515151;
			}
			.dropdown-menu-md {
				display: none;
			}
		}
	}

	// 下拉菜单
	.ant-dropdown {
		& {
			border-radius: 0;
		}
		.ant-dropdown-menu {
			& {
				width: 150px;
				padding: 10px 0;
			}
			.ant-dropdown-menu-item {
				& {
					height: 40px;
					line-height: 40px;
					cursor: pointer;
					font-size: 14px;
				}
				&:hover {
					background: #eee;
				}
				a {
					color: #222;
					padding: 0 12px;
				}
				i {
					font-size: 16px;
					color: #666;
					margin-right: 12px;
					margin-left: 6px;
				}
			}
		}
	}
	.user-info-setting-wrap {
		& {
			min-width: 235px;
			z-index: 1;
			background-color: #fff;
			border-radius: 4px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, .1);
			filter: drop-shadow(0 0 8px rgba(0, 0, 0, .1));
			-webkit-filter: drop-shadow(0 0 8px rgba(0, 0, 0, .1));
		}
		.user-info-body {
			& {
				padding: 30px 20px 20px 24px;
				line-height: 16px;
			}
			.user-info-body-username {
				margin-top: 0px;
				font-size: 16px;
				color: #333;
				font-weight: normal;
				margin-bottom: 10px;
				line-height: 16px;
			}
			.user-info-body-account {
				margin-top: 0px;
				color: #666;
				margin-bottom: 14px;
				line-height: 16px;
			}
			a {
				margin-right: 16px;
			}
		}
		.user-info-footer {
			& {
				border-top: 1px solid #ccc;
				background-color: #f5f5f5;
				padding: 8px 20px;
				text-align: right;
			}
			.user-info-footer-signout:hover {
				box-shadow: 0 2px 8px rgba(0, 0, 0, .1);
				border-color: #d9d9d9;
				color: rgba(0, 0, 0, 0.65);
			}
		}
	}
}
