import { PureComponent } from "react";
import { connect } from "dva";
import { Table } from "antd";
import { replayTaskLang } from "@/constants/lang";

class RuleHitDetail extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { ruleList } = this.props;

		let columns = [
			{
				title: replayTaskLang.report("ruleName"),		// lang: 规则名称
				dataIndex: "ruleName"
			},
			{
				title: "触发报警交易量",		// lang: 触发报警交易量
				dataIndex: "hitRuleTransCount"
			},
			{
				title: "触发报警交易额",		// lang: 触发报警交易额
				dataIndex: "hitRuleTransAmountCount"
			},
			{
				title: "总报警率",		// lang: 总报警率
				dataIndex: "totalAlarmRate"
			},
			{
				title: "日均报警量",		// lang: 日均报警量
				dataIndex: "dailyAverageAlarm"
			},
			{
				title: "命中欺诈量",		// lang: 命中欺诈量
				dataIndex: "blackTransHitCount"
			},
			{
				title: "命中欺诈金额",		// lang: 命中欺诈金额
				dataIndex: "blackTransHitAmountCount"
			},
			{
				title: "命中率",		// lang: 命中率
				dataIndex: "hitRate"
			},
			{
				title: "欺诈覆盖率",		// lang: 欺诈覆盖率
				dataIndex: "fraudCoverageRate"
			}
			// {
			// 	title: "欺诈类型",		// lang: 欺诈类型
			// 	dataIndex: "totalCount"
			// },
			// {
			// 	title: replayTaskLang.report("rulePercent"),		// lang:命中比例
			// 	render: (record) => {
			// 		let percent;
			// 		if (record.count === 0 || record.totalCount === 0) {
			// 			percent = 0;
			// 		} else {
			// 			percent = record.count / record.totalCount * 100;
			// 		}
			// 		percent = percent.toFixed(2);
			// 		return (
			// 			<div>
			// 				{percent + "%"}
			// 			</div>
			// 		);
			// 	}
			// }
		];

		return (
			<div className="report-panel">
				<div className="report-panel-box">
					<span className="report-panel-title">
						{/* lang:规则命中明细 */}
						{replayTaskLang.report("ruleHitDetail")}
					</span>
				</div>
				<div className="report-panel-content-personal">
					<Table
						className="table-out-border"
						columns={columns}
						pagination={false}
						dataSource={ruleList}
						rowKey="id"
						size="middle"
						scroll={{ x: 1200 }}
					/>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	replayTaskStore: state.replayTask
}))(RuleHitDetail);
