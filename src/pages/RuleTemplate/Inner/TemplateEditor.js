import { PureComponent } from "react";
import { connect } from "dva";
import { Table, But<PERSON>, Popconfirm, message } from "antd";
import { templateAPI } from "@/services";

const ButtonGroup = Button.Group;

class TemplateEditor extends PureComponent {

	constructor(props) {
		super(props);
		this.resetTemplate = this.resetTemplate.bind(this);
		this.modifyTemplate = this.modifyTemplate.bind(this);
		this.deleteTemplate = this.deleteTemplate.bind(this);
	}

	resetTemplate(item) {
		let { templateStore, dispatch } = this.props;
		let { currentType } = templateStore;

		let params = {
			cfgJson: "{}",
			id: item.id,
			name: item.name,
			displayName: item.displayName,
			enDisplayName: item.enDisplayName,
			description: item.description,
			enDescription: item.enDescription,
			type: item.type,
			parentId: item.parentId,
			sort: item.sort,
			tag: null
		};

		templateAPI.modifyTemplate(params).then((res) => {
			if (res.success) {
				dispatch({
					type: "template/getTemplateList",
					payload: {
						type: currentType
					}
				});
				message.success("更新模板成功");
				dispatch({
					type: "template/setDialogShow",
					payload: {
						modifyTemplate: false
					}
				});
				dispatch({
					type: "template/setAttrValue",
					payload: {
						currentStep: 0
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});

	}

	modifyTemplate(item) {
		let { templateStore, dispatch } = this.props;
		let { dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgTemplate = {
			"description": "",
			"type": "alias",
			"params": []
		};
		modifyTemplateData["id"] = item.id;
		modifyTemplateData["name"] = item.name;
		modifyTemplateData["displayName"] = item.displayName;
		modifyTemplateData["enDisplayName"] = item.enDisplayName;
		modifyTemplateData["type"] = item.type;
		modifyTemplateData["cfgJson"] = item.cfgJson && item.cfgJson !== "{}" ? JSON.parse(item.cfgJson) : cfgTemplate;
		modifyTemplateData["parentId"] = item.parentId;
		modifyTemplateData["sort"] = item.sort;
		modifyTemplateData["description"] = item.description;
		modifyTemplateData["enDescription"] = item.enDescription;
		modifyTemplateData["advanceConfig"] = item.advanceConfig;
		modifyTemplateData["tag"] = item.tag ? JSON.parse(item.tag) : null;

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
		dispatch({
			type: "template/setDialogShow",
			payload: {
				modifyTemplate: true
			}
		});
	}

	deleteTemplate(item) {
		let { templateStore, dispatch } = this.props;
		let { currentType } = templateStore;
		let params = {
			id: item.id
		};

		templateAPI.deleteTemplate(params).then((res) => {
			if (res.success) {
				dispatch({
					type: "template/getTemplateList",
					payload: {
						type: currentType
					}
				});
				message.success("删除模板成功");
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { templateStore, dispatch } = this.props;
		let { currentType, activeGroupIndex, ruleTemplateList, indexTemplateList, dialogData } = templateStore;
		let templateList = currentType === 1 ? ruleTemplateList : indexTemplateList;
		let groupChildren = templateList && templateList[activeGroupIndex] && templateList[activeGroupIndex]["templateDataList"] ? templateList[activeGroupIndex]["templateDataList"] : [];

		let columns = [
			{
				title: "规则名称",
				dataIndex: "displayName"
			},
			{
				title: "规则标识",
				dataIndex: "name"
			},
			{
				title: "描述",
				width: 300,
				dataIndex: "description",
				render: (description) => {
					return (
						<div>
							{description}
						</div>
					);
				}
			},
			{
				title: "操作",
				width: 200,
				dataIndex: "",
				render: (record, records) => {
					return (
						<div className="oper-list">
							<a onClick={this.modifyTemplate.bind(this, records)}>编辑</a>
							<Popconfirm
								title="确认要删除当前模板吗？"
								onConfirm={this.deleteTemplate.bind(this, records)} okText="删除"
								cancelText="取消"
							>
								<a>删除</a>
							</Popconfirm>
							{/* <Popconfirm*/}
							{/* title="确认要初始化当前模板JSON吗？"*/}
							{/* onConfirm={this.resetTemplate.bind(this, records)} okText="确定"*/}
							{/* cancelText="取消"*/}
							{/* >*/}
							{/* <a>初始化</a>*/}
							{/* </Popconfirm>*/}
						</div>
					);
				}
			}
		];

		return (
			<div style={{ position: "relative", height: "100%" }}>
				<div className="box-item-header">
					<h3>
						{currentType === 1 ? "规则模板列表" : "指标模板列表"}
					</h3>
					<ButtonGroup className="btns">
						<Button
							size="small"
							onClick={() => {
								let { addTemplateData } = dialogData;
								let groupId = templateList[activeGroupIndex]["groupId"];
								dispatch({
									type: "template/setDialogShow",
									payload: {
										addTemplate: true
									}
								});
								addTemplateData["type"] = currentType;
								addTemplateData["parentId"] = groupId;
								dispatch({
									type: "template/setDialogData",
									payload: {
										addTemplateData: addTemplateData
									}
								});
							}}
						>
                            新增模板
						</Button>
					</ButtonGroup>
				</div>
				<div className="box-item-body p20">
					<Table
						columns={columns}
						dataSource={groupChildren}
						pagination={false}
						size="middle"
						bordered={true}
						rowKey={(e, index) => index}
					/>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(TemplateEditor);
