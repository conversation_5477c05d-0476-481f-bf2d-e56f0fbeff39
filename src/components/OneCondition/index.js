import { PureComponent, Fragment, useCallback } from "react";
import { connect } from "dva";
import OneCondition from "./OneCondition";
import { policyDetailLang } from "@/constants/lang";
import { Select, Radio, Row, Col, Form } from "antd";
import "./index.less";

const RadioGroup = Radio.Group;

class ConditionSetting extends PureComponent {
	// 添加
	addCondition(type) {
		const { conditionJson = {}, changeCondition } = this.props;
		let conditionTemp = {};
		if (type === "single") {
			conditionTemp = {
				operator: "==",
				leftValueType: "STRING",
				leftVarType: "context",
				leftVar: null,
				rightVarType: "input",
				rightVar: null
			};
		} else if (type === "group") {
			conditionTemp = {
				logicOperator: "&&",
				children: [
					{
						operator: "==",
						leftValueType: "STRING",
						leftVarType: "context",
						property: null,
						rightVarType: "input",
						rightVar: null
					}
				]
			};
		}
		conditionJson["children"].push(conditionTemp);
		changeCondition(conditionJson);
	};

    // 删除
    deleteCondition = (i) => {
    	const { conditionJson, changeCondition } = this.props;
    	console.log(conditionJson);
    	conditionJson["children"].splice(i, 1);
    	changeCondition(conditionJson);
    };

    // 修改组的条件
    changeGroupConditionType = (index, value) => {
    	const { conditionJson, changeCondition } = this.props;
    	conditionJson["children"][index]["logicOperator"] = value;
    	changeCondition(conditionJson);
    }

    // 修改前提条件
    changeAllConditionType(e) {
    	const { conditionJson, changeCondition } = this.props;
    	let value = e.target.value;
    	conditionJson["logicOperator"] = value;
    	changeCondition(conditionJson);
    }

    renderRuleTemplate = (item, index) => {
    	const { conditionJson, changeCondition, disabled } = this.props;
    	let ConditionDom = [];
    	let ruleType = item.hasOwnProperty("children") ? "group" : "single";
    	if (ruleType === "group") {
    		ConditionDom = item["children"].map((obj, index2) => {
    			let key = item.ruleType + "_" + index[0] + "_" + index2;
    			return (
    				<OneCondition
    					disabled={disabled}
    					conditionJson={conditionJson}
    					changeCondition={changeCondition}
    					key={key}
    					item={obj}
    					type="group"
    					indexArr={[index, index2]}
    				/>
    			);
    		});
    	} else {
    		let key = item.ruleType + "_" + index;
    		ConditionDom = (
    			<OneCondition
    				disabled={disabled}
    				conditionJson={conditionJson}
    				changeCondition={changeCondition}
    				key={key}
    				item={item}
    				type="single"
    				indexArr={[index]}
    			/>
    		);
    	}
    	return (
    		<div
    			className={ruleType === "single" ? "one-condition custom-item" : "group-condition custom-item"}
    			key={index}
    		>
    			<Row gutter={8}>
    				<Col span={2} push={1}>
    					{
    						ruleType === "group" &&
                            <Select
                            	disabled={disabled}
                            	value={item.logicOperator}
                            	onChange={(e) => { this.changeGroupConditionType(index, e); }}
                            >
                            	<Option value="&&">
                            		{/* 与 */}
                            		{policyDetailLang.oneCondition("and")}
                            	</Option>
                            	<Option value="||">
                            		{/* 或 */}
                            		{policyDetailLang.oneCondition("or")}
                            	</Option>
                            </Select>
    					}
    				</Col>
    				<Col span={20} push={1}>{ConditionDom}</Col>
    			</Row>
    		</div>
    	);
    }

	renderRadioGroup = () => {
		const { conditionJson = {}, disabled } = this.props;
		return (
			<RadioGroup
				onChange={this.changeAllConditionType.bind(this)}
				value={conditionJson.logicOperator}
				disabled={disabled}
			>
				<Radio value="&&">
					{/* lang:满足以下所有条件 */}
					{policyDetailLang.ruleCondition("mztj1")}
				</Radio>
				<Radio value="||">
					{/* lang:满足以下任意条件 */}
					{policyDetailLang.ruleCondition("mztj2")}
				</Radio>
			</RadioGroup>
		);
	}
	renderRuleConditionTitle = () => {
		const { conditionJson = {}, type, required, form, title } = this.props;
		const { getFieldDecorator } = form;
		if (type === "formItem") {
			return (
				<Form.Item
					label={title}
				>
					{
						getFieldDecorator("conditionJson", {
							initialValue: conditionJson.logicOperator || "undefined",
							rules: [
								{
									required
								}
							]
						})(
							this.renderRadioGroup()
						)
					}
				</Form.Item>
			);
		}
		return (
			<Row gutter={8} className="rule-content-common">
				<Col className="gutter-row" span={4}>
					<div
						className="gutter-box"
						style={{ textAlign: "right" }}
					>
						{title}
					</div>
				</Col>
				<Col className="gutter-row" span={20}>
					<div className="gutter-box">
						{this.renderRadioGroup()}
					</div>
				</Col>
			</Row>
		);
	}
	render() {
    	const { conditionJson = {}, disabled } = this.props;
    	return (
    		<Fragment>
				{this.renderRuleConditionTitle()}
    			<div className={`rule-content-common ${disabled ? "last-no-line" : ""}`}>
    				{
    					conditionJson.children &&
                        conditionJson.children.map((item, index) => {
                        	return this.renderRuleTemplate(item, index);
                        })
    				}
					{
						!disabled &&
						<Row gutter={8} className="rule-ctrl">
							<Col className="gutter-row" span={21} push={3}>
								<span onClick={this.addCondition.bind(this, "single")}>
									{/* lang:添加单条条件 */}
									{policyDetailLang.ruleCondition("add2")}
								</span>
								<span onClick={this.addCondition.bind(this, "group")}>
									{/* lang:添加条件组 */}
									{policyDetailLang.ruleCondition("add3")}
								</span>
							</Col>
						</Row>
					}
    			</div>
    		</Fragment>
    	);
	}
}
export default connect(state => ({
	globalStore: state.global
}))(ConditionSetting);
