import store from "../app";
const getCreateType = () => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	return ([
		{
			"value": "manual",
			"name": personalMode.lang === "cn" ? "手动" : "Manual"
		}, {
			"value": "system",
			"name": personalMode.lang === "cn" ? "系统" : "System"
		}
	]);
};

const getPolicyType = () => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	return ([
		{
			"value": "publicPolicy",
			"name": personalMode.lang === "cn" ? "公共策略" : "Public policy"
		}, {
			"value": "normalPolicy",
			"name": personalMode.lang === "cn" ? "普通策略" : "Normal policy"
		}
	]);
};

const getUpdateType = () => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	return ([
		{
			"value": "manual",
			"name": personalMode.lang === "cn" ? "手动" : "Manual"
		}, {
			"value": "system",
			"name": personalMode.lang === "cn" ? "系统" : "System"
		}
	]);
};

const getOperaType = () => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	return ([
		{
			"value": "create",
			"name": personalMode.lang === "cn" ? "新增" : "Add"
		}, {
			"value": "delete",
			"name": personalMode.lang === "cn" ? "删除" : "Delete"
		}, {
			"value": "update",
			"name": personalMode.lang === "cn" ? "修改" : "Update"
		}
	]);
};
export default {
	getCreateType,
	getPolicyType,
	getUpdateType,
	getOperaType
};
