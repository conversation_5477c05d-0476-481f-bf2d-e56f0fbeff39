import { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { searchToObject } from "@/utils/utils";
import { checkFunctionHasPermission } from "@/utils/permission";
import NoPermission from "@/components/NoPermission";
import { approvalTaskLang } from "@/constants/lang";
import { Tabs } from "antd";

const TabPane = Tabs.TabPane;
import PolicyTask from "./PolicyTask";
import SalaxyTask from "./SalaxyTask";
import WorkflowTask from "./WorkflowTask";

class Task extends PureComponent {

	constructor(props) {
		super(props);
		this.switchTab = this.switchTab.bind(this);
	}

	switchTab(key) {
		let { location, dispatch } = this.props;
		let { pathname } = location;
		let search = "?currentTab=" + key;
		dispatch(routerRedux.push(pathname + search));
	}

	render() {
		let { globalStore, location } = this.props;
		let { menuTreeReady } = globalStore;
		let { search } = location;
		let searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		let currentTab = searchObj && searchObj.currentTab ? parseInt(searchObj.currentTab, 10) : 1;

		return (
			<div>
				<div className="main-area-wrap">
					<div className="page-global-tab">
						<Tabs
							activeKey={currentTab.toString()}
							onChange={this.switchTab.bind(this)}
							animated={false}
						>
							<TabPane
								tab={approvalTaskLang.common("policyTitle1")} // lang:策略审批任务
								key="1"
							>
								{
									menuTreeReady && checkFunctionHasPermission("ZB0401", "policyApprovalSearch") &&
                                    <PolicyTask />
								}
								{
									menuTreeReady && !checkFunctionHasPermission("ZB0401", "policyApprovalSearch") &&
                                    <NoPermission />
								}
							</TabPane>
							<TabPane
								tab={approvalTaskLang.common("indicatorTitle1")} // lang:指标审批任务
								key="2"
							>
								{
									menuTreeReady && checkFunctionHasPermission("ZB0401", "indexApprovalSearch") &&
                                    <SalaxyTask />
								}
								{
									menuTreeReady && !checkFunctionHasPermission("ZB0401", "indexApprovalSearch") &&
                                    <NoPermission />
								}
							</TabPane>
							<TabPane
								tab={approvalTaskLang.common("workflowTitle1")} // lang:规则流审批任务
								key="3"
							>
								{
									menuTreeReady && checkFunctionHasPermission("ZB0401", "workflowApprovalSearch") &&
                                    <WorkflowTask />
								}
								{
									menuTreeReady && !checkFunctionHasPermission("ZB0401", "workflowApprovalSearch") &&
                                    <NoPermission />
								}
							</TabPane>
						</Tabs>
					</div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(Task);
