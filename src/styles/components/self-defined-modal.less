@import "../base/variables";

:global {
	.algo-modal {
		.algo-config-wrap {
			& {
				position: fixed;
				z-index: 199;
				width: 100%;
				height: 100%;
				top: 0;
				left: 0;
			}
			.algo-config-mask {
				& {
					height: 100%;
					overflow: hidden;
					background: rgba(0, 0, 0, .35);
				}
				.algo-config-content {
					& {
						position: relative;
						width: 90%;
						background: #fff;
						display: flex;
						flex-direction: column;
						height: 100%;
					}
					.algo-config-close {
						& {
							position: absolute;
							right: -56px;
							top: 0;
							width: 56px;
							height: 56px;
							line-height: 56px;
							text-align: center;
							background: rgba(0, 0, 0, 0.45);
							cursor: pointer;
						}
						&:hover i {
							opacity: 1;
						}
						i {
							color: #fff;
							font-size: 20px;
							vertical-align: super;
							opacity: .65;
							transition: opacity .3s;
						}
					}
					.algo-config-header {
						& {
							position: relative;
							flex: 0 0 auto;
							//padding: 20px 50px;
							-moz-box-shadow: -10px 0px 15px 6px #dcdcdc;
							-webkit-box-shadow: -10px 0px 15px 6px #dcdcdc;
							box-shadow: -10px 0px 15px 6px #dcdcdc;
							z-index: 10;
						}
						.td-steps-horizontal .td-steps-step-line {
							top: 15px;
						}
						.td-steps-step-progress .td-steps-step-icon {
							background: #2ea5ff;
							border-color: #2ea5ff;
							color: #fff;
						}
						.td-steps-step-icon {
							& {
								width: 32px;
								height: 32px;
								line-height: 32px;
								font-size: 16px;
								font-weight: normal;
								background: #fff;
							}
							.tdicon {
								font-size: 24px;
							}
						}
						.td-steps-step-title {
							font-size: 14px;
						}

						.algo-config-title {
							& {
								background: #fff;
								padding-left: 50px;
								border-bottom: 1px dashed #dcdcdc;
								line-height: 56px;
								text-align: center;
							}
							.ant-tag {
								position: absolute;
								left: 30px;
								top: 12px;
								width: 100px;
								height: 32px;
								line-height: 32px;
								font-size: 14px;
							}
							h2 {
								font-size: 18px;
								margin-bottom: 0;
							}
						}
						.ant-steps {
							padding: 20px 50px;
						}
					}
					.algo-config-body {
						& {
							padding: 30px;
							flex: 1 0 auto;
							//-moz-box-shadow: inset 0px 0px 15px 6px #dcdcdc;
							//-webkit-box-shadow: inset 0px 0px 15px 6px #dcdcdc;
							//box-shadow: inset 0px 0px 15px 6px #dcdcdc;
							background: #f2f3f4;
							overflow-y: scroll;
						}
						&.no-padding {
							padding: 0;
						}
						.select-keys {
							background-color: none;
							padding: 5px 0;

							.two-model-form {
								& {
									width: 850px;
									margin: 10px auto;
								}

								.form-lable {
									min-width: 0 !important;
								}

								.special-row {
									display: inline-block;

									.form-item {
										width: 200px;
										margin-right: 20px;
									}
								}
							}

							//transfer less
							.transfer {
								& {
									width: 850px;
									margin: 20px auto 0;
								}

								.select-item-box {
									display: inline-block;
									width: 400px;
									height: 370px;
									border: 1px solid #dcdcdc;
									border-radius: 5px;
									padding: 4px 10px;
									overflow-y: scroll;

									.item-box-header {
										padding: 5px 0;
										margin-bottom: 10px;
										border-bottom: 1px dashed #EEEEEE;

										.ant-btn {
											position: relative;
											top: -5px;
											float: right;
											font-size: 12px;
										}
									}

									.items {
										margin: 3px 0;
										input {
											margin-right: 5px;
										}
									}
								}

								.btn-item {
									& {
										display: inline-block;
										position: relative;
										top: -145px;
										margin: 0 20px;
									}

									p {
										margin-top: 16px;

										span {
											display: block;
											text-align: center;
											width: 50px;
											height: 30px;
											cursor: pointer;
											font-size: 14px;
											border: 1px solid #EEEEEE;
											border-radius: 5px;
											line-height: 30px;
										}

										span:hover {
											background-color: #419BF9;
											color: white;
										}

										.disabled, .disabled:hover {
											background-color: #F5F5F5;
											color: #BBB;
											cursor: not-allowed;
										}
									}
								}
							}
						}

						//table css
						.ant-table-wrapper {
							.ant-table-content .ant-table-body {
								.operation-icon {
									font-size: 16px;
								}

								.operation-icon:first-child {
									margin-right: 5px;
								}
							}
						}

						.label-require {
							display: inline-block;

							.require {
								margin-right: 3px;
								color: red;
							}
						}

						.modal-input-width {
							width: 100%;
						}

						.modal-form {
							margin: 0 auto;
							padding: 20px 60px;
							width: 98%;
							background: none;
						}

						.ant-form-item-label {
							text-align: left;
						}

						.algo-base-info {
							& {
								position: relative;
								border-bottom: 1px solid #e6e6e6;
								padding-bottom: 10px;
								margin-bottom: 20px;
							}
							span {
								font-size: 14px;
								margin-right: 20px;
								color: #777;
							}
							.temp-preview-switch {
								position: absolute;
								right: 0;
								top: 0;
							}
						}
					}
					.algo-config-footer {
						& {
							position: relative;
							flex: 0 0 auto;
							border-top: 1px solid #dcdcdc;
							padding: 20px 0;
							-moz-box-shadow: -10px 0px 15px 6px #dcdcdc;
							-webkit-box-shadow: -10px 0px 15px 6px #dcdcdc;
							box-shadow: -10px 0px 15px 6px #dcdcdc;
						}
						.footer-btns {
							& {
								text-align: center;
							}
							button {
								& {
									font-size: 14px;
									width: 100px;
									height: 40px;
									line-height: 40px;
									border: 1px solid #dcdcdc;
									background: #fff;
									border-radius: 5px;
									outline: none;
									cursor: pointer;
									margin-right: 10px;
								}
								&:last-child {
									margin-right: 0;
								}
								&:disabled {
									background: #e6e6e6;
									cursor: not-allowed;
									color: #b4b4b4;
								}
								&:hover:not(:disabled) {
									color: #48a0e3;
									background: #f3f4f5;
								}
							}
						}
					}
				}
			}
		}
	}
}
