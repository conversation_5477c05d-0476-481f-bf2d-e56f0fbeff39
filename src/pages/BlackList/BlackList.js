import { PureComponent } from "react";
import { connect } from "dva";
import { blackListAPI } from "@/services";
import { Button, Select, Input, Table, Pagination, Modal, message, Radio } from "antd";
import TableHeadSettingModal from "./Modal/TableHeadSettingModal";
import AddBlackListModal from "./Modal/AddBlackListModal";
import BlackListImportModal from "./Modal/BlackListImportModal";

const confirm = Modal.confirm;
const Option = Select.Option;
const InputGroup = Input.Group;

class BlackList extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.startSearch = this.startSearch.bind(this);
    	this.changePagination = this.changePagination.bind(this);
    	this.changeSearchParamValue = this.changeSearchParamValue.bind(this);
    	this.tableHeadSettingHandle = this.tableHeadSettingHandle.bind(this);
    	this.addBlackListHandle = this.addBlackListHandle.bind(this);
    	this.deleteBlackListHandle = this.deleteBlackListHandle.bind(this);
    	this.blackListImportHandle = this.blackListImportHandle.bind(this);
    	this.modifyBlackListHandle = this.modifyBlackListHandle.bind(this);
    }

    componentDidMount() {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "blackList/getBlackListHeader"
    	});

    	this.startSearch();
    }

    startSearch() {
    	let { dispatch, blackListStore } = this.props;
    	let { searchParams } = blackListStore;
    	// 页面初始化数据
    	searchParams["curPage"] = 1;

    	dispatch({
    		type: "blackList/getBlackListData",
    		payload: {
    			...searchParams
    		}
    	});
    	dispatch({
    		type: "blackList/setAttrValue",
    		payload: {
    			searchParams: searchParams
    		}
    	});
    }

    changePagination(curPage, pageSize) {
    	let { blackListStore, dispatch } = this.props;
    	let { searchParams } = blackListStore;

    	searchParams["curPage"] = curPage;
    	searchParams["pageSize"] = pageSize;

    	dispatch({
    		type: "blackList/getBlackListData",
    		payload: {
    			...searchParams
    		}
    	});
    }

    async changeSearchParamValue(field, type, e) {
    	let { blackListStore, dispatch } = this.props;
    	let { searchParams } = blackListStore;

    	let value;
    	if (type === "select") {
    		value = e;
    	} else if (type === "input") {
    		value = e.target.value;
    	} else if (type === "radio") {
    		value = e.target.value;
    	}
    	searchParams[field] = value;
    	await dispatch({
    		type: "blackList/setAttrValue",
    		payload: {
    			searchParams: searchParams
    		}
    	});

    	if (type === "radio") {
    		this.startSearch();
    	}
    }

    tableHeadSettingHandle() {
    	let { blackListStore, dispatch } = this.props;
    	let { tableHeaderList, dialogData } = blackListStore;
    	let { tableHeadData } = dialogData;

    	tableHeadData["tableHeaderList"] = tableHeaderList;
    	dispatch({
    		type: "blackList/setDialogShow",
    		payload: {
    			tableHeadModal: true
    		}
    	});
    	dispatch({
    		type: "blackList/setDialogData",
    		payload: {
    			tableHeadData: tableHeadData
    		}
    	});
    }

    addBlackListHandle() {
    	let { blackListStore, dispatch } = this.props;
    	let { tableHeaderList, dialogData } = blackListStore;
    	let { addBlackListData } = dialogData;

    	let newTableHeaderList = [];
    	tableHeaderList.forEach((item) => {
    		newTableHeaderList.push({
    			headerField: item.headerField,
    			headerFieldName: item.headerFieldName,
    			systemField: item.systemField,
    			type: item.type,
    			value: null
    		});
    	});

    	addBlackListData["tableHeaderList"] = newTableHeaderList;
    	dispatch({
    		type: "blackList/setDialogShow",
    		payload: {
    			addBlackListModal: true
    		}
    	});
    	dispatch({
    		type: "blackList/setDialogData",
    		payload: {
    			addBlackListData: addBlackListData
    		}
    	});
    }

    deleteBlackListHandle(item) {
    	let _this = this;
    	let params = {
    		id: item.black_list_number
    	};
    	confirm({
    		title: "删除名单提醒",
    		content: "您确认要删除当前名单项吗？",
    		onOk() {
    			blackListAPI.deleteBlackListData(params).then(res => {
    				if (res.success) {
    					message.success("删除名单项成功");
    					setTimeout(() => {
    						_this.startSearch();
    					}, 1000);
    					_this.closeModal();
    				} else {
    					message.error(res.message);
    				}
    			}).catch(err => {
    				console.log(err);
    			});
    		},
    		onCancel() {
    			console.log("Cancel");
    		}
    	});
    }

    modifyBlackListHandle(obj) {
    	let { blackListStore, dispatch } = this.props;
    	let { tableHeaderList, dialogData } = blackListStore;
    	let { addBlackListData } = dialogData;

    	if (!obj) {
    		return;
    	}

    	let newTableHeaderList = [];
    	tableHeaderList.forEach((item) => {
    		let value = obj[item["headerField"]];
    		newTableHeaderList.push({
    			headerField: item.headerField,
    			headerFieldName: item.headerFieldName,
    			systemField: item.systemField,
    			type: item.type,
    			value: value
    		});
    	});

    	addBlackListData["tableHeaderList"] = newTableHeaderList;
    	addBlackListData["modifyObj"] = obj;
    	dispatch({
    		type: "blackList/setDialogShow",
    		payload: {
    			addBlackListModal: false,
    			modifyBlackListModal: true
    		}
    	});
    	dispatch({
    		type: "blackList/setDialogData",
    		payload: {
    			addBlackListData: addBlackListData
    		}
    	});
    };

    blackListImportHandle() {
    	let { blackListStore, dispatch } = this.props;
    	let { dialogData } = blackListStore;
    	let { blackListImportData } = dialogData;

    	dispatch({
    		type: "blackList/setDialogShow",
    		payload: {
    			blackListImportModal: true
    		}
    	});
    	dispatch({
    		type: "blackList/setDialogData",
    		payload: {
    			blackListImportData: blackListImportData
    		}
    	});
    }

    render() {
    	let { globalStore, blackListStore } = this.props;
    	let { allMap } = globalStore;
    	let { tableHeaderList, dataList, searchParams } = blackListStore;
    	let { curPage, total } = searchParams;
    	let { blackListSelect } = allMap;
    	let columns = [];

    	tableHeaderList &&
            tableHeaderList.map((item, index, arr) => {
            	if (item && item["headerField"]) {
            		let itemObj = {
            			title: item["headerFieldName"],
            			dataIndex: item.headerField,
            			key: item.headerField,
            			width: 200
            		};
            		if (index === 0) {
            			itemObj.fixed = "left";
            		}
            		columns.push(itemObj);
            		if (index === arr.length - 1) {
            			columns.push({
            				title: "操作",
            				fixed: "right",
            				width: 200,
            				render: (record) => {
            					return (
            						<div className="table-action" key={index}>
            							<a
            								onClick={this.modifyBlackListHandle.bind(this, record)}
            							>
                                            编辑
            							</a>
            							<a
            								onClick={this.deleteBlackListHandle.bind(this, record)}
            							>
                                            删除
            							</a>
            						</div>
            					);
            				}
            			});
            		}
            	}
            });

    	return (
    		<div>
    			<div className="page-global-header">
    				<div className="left-info">
    					<h2>黑名单管理</h2>
    				</div>
    				<div className="right-info">
    					<div className="right-info-item">
    						<Radio.Group
    							value={searchParams.listName}
    							buttonStyle="solid"
    							onChange={this.changeSearchParamValue.bind(this, "listName", "radio")}
    						>
    							{
    								blackListSelect &&
                                    blackListSelect.map((item, index) => {
                                    	return (
                                    		<Radio.Button
                                    			value={item.name}
                                    			key={index}
                                    		>
                                    			{item.dName}
                                    		</Radio.Button>
                                    	);
                                    })
    							}
    						</Radio.Group>
    					</div>
    					<div className="right-info-item">
    						<div className="quick-search">
    							<InputGroup compact>
    								<Select
    									value={searchParams.columnName || ""}
    									style={{ width: 200 }}
    									onChange={this.changeSearchParamValue.bind(this, "columnName", "select")}
    									showSearch
    									optionFilterProp="children"
    									dropdownMatchSelectWidth={false}
    								>
    									<Option value="">全部字段</Option>
    									{
    										tableHeaderList &&
                                            tableHeaderList.map((item, index) => {
                                            	return (
                                            		<Option
                                            			value={item.headerField}
                                            			key={index}
                                            		>
                                            			{item.headerFieldName}
                                            		</Option>
                                            	);
                                            })
    									}
    								</Select>
    								<Input
    									placeholder="请输入搜索内容"
    									onChange={this.changeSearchParamValue.bind(this, "columnValue", "input")}
    									onPressEnter={this.startSearch.bind(this)}
    									style={{ width: 200 }}
    									value={searchParams.columnValue || undefined}
    								/>
    							</InputGroup>
    						</div>
    					</div>
    					<div className="right-info-item">
    						<Button
    							type="primary"
    							className="search-button"
    							onClick={this.startSearch.bind(this)}
    						>
                                搜索
    						</Button>
    					</div>
    				</div>
    			</div>
    			<div className="page-global-body">
    				<div className="page-global-body-search">
    					<div className="left-info">
    						<div className="left-info-item">
    							<Button
    								type="primary"
    								onClick={this.tableHeadSettingHandle.bind(this)}
    								icon="setting"
    							>
                                    设置表头映射
    							</Button>
    						</div>
    						<div className="left-info-item">
    							<Button
    								type="primary"
    								onClick={this.addBlackListHandle.bind(this)}
    								icon="plus-square"
    							>
                                    添加名单
    							</Button>
    						</div>
    						<div className="left-info-item">
    							<Button
    								type="primary"
    								onClick={this.blackListImportHandle.bind(this)}
    								icon="download"
    							>
                                    导入
    							</Button>
    						</div>
    					</div>
    				</div>
    				<div className="page-global-body-main">
    					<Table
    						className="table-card-body"
    						columns={columns}
    						dataSource={dataList}
    						pagination={false}
    						rowKey="black_list_number"
    						scroll={{ x: tableHeaderList.length * 200 }}
    					/>
    					<div className="page-global-body-pagination">
    						<span className="count">共{total ? total : 0}条记录</span>
    						<Pagination
    							showQuickJumper
    							showSizeChanger
    							onChange={this.changePagination.bind(this)}
    							onShowSizeChange={this.changePagination.bind(this)}
    							current={curPage}
    							total={total}
    						/>
    					</div>
    				</div>
    			</div>
    			<TableHeadSettingModal />
    			<AddBlackListModal />
    			<BlackListImportModal />
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	blackListStore: state.blackList
}))(BlackList);
