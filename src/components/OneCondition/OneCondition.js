/*
 * @Author: zefei.zhou
 * @Date: 2019-12-04 19:44:51
 * @Describe:
 * leftVar 左侧系统字段
 * leftValueType 左侧系统字段数据类型
 * operator 操作符
 * rightVarType 常量／变量类型
 * rightVar 右侧值
 */
import { PureComponent } from "react";
import { connect } from "dva";
import { Select, Input, InputNumber, Icon, Row, Col, DatePicker } from "antd";
import { PolicyConstants } from "@/constants";
import { commonLang, policyDetailLang } from "@/constants/lang";
import moment from "moment";

const Option = Select.Option;
const InputGroup = Input.Group;
const format = "YYYY-MM-DD HH:mm:ss";

class OneCondition extends PureComponent {
	constructor(props) {
		super(props);
	}

    // 删除执行条件
    deleteCondition = (indexArr) => {
    	const { conditionJson = {}, changeCondition } = this.props;

    	if (indexArr.length === 1) {
    		// 单条件
    		conditionJson["children"].splice(indexArr[0], 1);
    	} else if (indexArr.length === 2) {
    		// 多条件组
    		if (conditionJson["children"][indexArr[0]]["children"].length === 1) {
    			// 如果多条件组中长度为1
    			conditionJson["children"].splice(indexArr[0], 1);
    		} else {
    			// 如果多条件组中长度不为1，则按照index删除
    			conditionJson["children"][indexArr[0]]["children"].splice(indexArr[1], 1);
    		}
    	}
    	changeCondition(conditionJson);
    }

    // 增加条件组中的条件
    addCondition = (indexArr) => {
    	const { conditionJson = {}, changeCondition } = this.props;
    	let baseTemp = {
    		operator: "==",
    		leftValueType: "STRING",
    		leftVarType: "context",
    		leftVar: null,
    		rightVarType: "input",
    		rightVar: null
    	};
    	conditionJson["children"][indexArr[0]]["children"].splice(indexArr[1] + 1, 0, baseTemp);
    	changeCondition(conditionJson);
    }

    /**
	 * select 选择事件
	 * @param {标示出要修改的位置，他是一个数组，最大两位数} indexArr
	 * @param {判断是select还是input} type
	 * @param {field 修改的阈} field
	 * @param {select value:input value} e
	 */
    changeFieldValue = (indexArr, type, field, e, date) => {
    	const { globalStore, item } = this.props;
    	const { conditionJson = {}, changeCondition } = this.props;

    	let { allMap } = globalStore;
    	let { ruleAndIndexFieldList } = allMap;

    	let ruleObj = {};
    	let conditionObj;
    	if (indexArr.length === 1) {
    		conditionObj = conditionJson["children"][indexArr[0]];
    	} else if (indexArr.length === 2) {
    		conditionObj = conditionJson["children"][indexArr[0]]["children"][indexArr[1]];
    	}

    	// 获取下拉选择到的数据对象，以便获取该对象的数据类型
    	if (field === "leftVar" || field === "rightVar" && type === "select") {
    		ruleObj = ruleAndIndexFieldList.filter(item => item.name === e)[0];
    	}
    	let value = null;
    	if (type === "select") {
    		value = e;
    	} else if (type === "input") {
    		value = e.target.value;
    	}

    	conditionObj[field] = value;
    	if (field === "rightVarType") {
    		conditionObj["rightVar"] = null;
    	}
    	if (field === "leftVar" && (field !== "rightVar" || type !== "inputNumber")) {
    		item.operator = "";
    	}

    	// 变更左变量 清空右侧变量
    	if (field === "leftVar") {
    		conditionObj["rightVar"] = null;
    		conditionObj["leftValueType"] = ruleObj ? ruleObj.type : "STRING";
    	}
    	/**
		 * @description 右值输入时间转换为毫秒数
		 */
    	if (field === "rightVar" && type === "date") {
    		let dates = new Date(date.replace(/-/g, "/")); // - 转换 /
    		conditionObj["rightVar"] = dates.getTime();
    	}

    	if (field === "rightVar" && type === "inputNumber") {
    		conditionObj["rightVar"] = e;
    	}

    	changeCondition(conditionJson);
    }

    render() {
    	let { globalStore, item, type, indexArr, disabled } = this.props; // indexArr 为层级数组
    	let { allMap } = globalStore;
    	let { ruleAndIndexFieldList } = allMap;
    	return (
    		<Row gutter={8} className="one-condition custom-item">
    			<Col className="gutter-row" span={7}>
    				<Select
    					style={{ width: "100%" }}
    					placeholder={policyDetailLang.oneCondition("select")} // lang:"请选择"
    					value={item.leftVar || undefined}
    					onChange={this.changeFieldValue.bind(this, indexArr, "select", "leftVar")}
    					showSearch
    					optionFilterProp="children"
    					disabled={disabled}
    					dropdownMatchSelectWidth={false}
    				>
    					{
    						ruleAndIndexFieldList &&
                            ruleAndIndexFieldList.map((item, index) => {
                            	return (
                            		<Option key={index} value={item.name}>
                                        [{commonLang.sourceName(item.sourceName)}]&nbsp;
                                    			{item.dName}
                            		</Option>
                            	);
                            })
    					}
    				</Select>
    			</Col>
    			<Col className="gutter-row" span={4}>
    				<Select
    					placeholder={policyDetailLang.oneCondition("select")} // lang:"请选择"
    					value={item.operator || undefined}
    					onChange={this.changeFieldValue.bind(this, indexArr, "select", "operator")}
    					disabled={disabled}
    				>
    					{
    						PolicyConstants.conditionOperator[item.leftValueType] &&
                            PolicyConstants.conditionOperator[item.leftValueType].map((item, index) => {
                            	return (
                            		<Option key={index} value={item.name}>
                            			{item.dName}
                            		</Option>
                            	);
                            })
    					}
    				</Select>
    			</Col>
    			{
    				item &&
                    item["operator"] &&
                    item["operator"] !== "isnull" &&
                    item["operator"] !== "notnull" &&
                    <Col className="gutter-row" span={10}>
                    	{
                    		item.leftValueType !== "ENUM" &&
                            <InputGroup compact>
                            	{
                            		item.leftVarType === "context" &&
                                    <Select
                                    	disabled={disabled}
                                    	style={{ width: "30%" }}
                                    	placeholder={policyDetailLang.oneCondition("select")} // lang:"请选择"
                                    	value={item.rightVarType || undefined}
                                    	onChange={this.changeFieldValue.bind(this, indexArr, "select", "rightVarType")}
                                    >

                                    	<Option value="input">
                                    		{/* lang:常量 */}
                                    		{policyDetailLang.oneCondition("constant")}
                                    	</Option>
                                    	<Option value="context">
                                    		{/* lang:变量 */}
                                    		{policyDetailLang.oneCondition("variable")}
                                    	</Option>
                                    </Select>
                            	}
                            	{
                            		item.rightVarType === "input" &&
                                    item.leftValueType === "STRING" &&
                                    <Input
                                    	style={{ width: "70%" }}
                                    	value={item.rightVar || undefined}
                                    	placeholder={policyDetailLang.oneCondition("inputConstant")} // lang:"请输入常量值"
                                    	onChange={this.changeFieldValue.bind(this, indexArr, "input", "rightVar")}
                                    	disabled={disabled}
                                    />
                            	}
                            	{
                            		item.rightVarType === "input" &&
                                    item.leftValueType === "BOOLEAN" &&
                                    <Input
                                    	style={{ width: "70%" }}
                                    	value={item.rightVar || undefined}
                                    	placeholder={policyDetailLang.oneCondition("inputConstant")} // lang:"请输入常量值"
                                    	onChange={this.changeFieldValue.bind(this, indexArr, "input", "rightVar")}
                                    	disabled={disabled}
                                    />
                            	}
                            	{
                            		item.rightVarType === "input" &&
                                    item.leftValueType === "DATETIME" &&
                                    <DatePicker
                                    	style={{ width: "70%" }}
                                    	showTime
                                    	format={format}
                                    	onChange={this.changeFieldValue.bind(this, indexArr, "date", "rightVar")}
                                    	disabled={disabled}
                                    	value={item.rightVar ? moment(item.rightVar) : undefined}
                                    />
                            	}
                            	{
                            		item.rightVarType === "input" &&
                                    (item.leftValueType === "INT" || item.leftValueType === "DOUBLE") &&
                                    <InputNumber
                                    	style={{ width: "70%" }}
                                    	defaultValue={1}
                                    	step={0}
                                    	value={item.rightVar}
                                    	onChange={this.changeFieldValue.bind(this, indexArr, "inputNumber", "rightVar")}
                                    	disabled={disabled}
                                    />
                            	}
                            	{
                            		item.rightVarType === "context" &&
                                    <Select
                                    	placeholder={policyDetailLang.oneCondition("select")} // lang:"请选择"
                                    	value={item.rightVar || undefined}
                                    	style={{ width: "70%" }}
                                    	onChange={this.changeFieldValue.bind(this, indexArr, "select", "rightVar")}
                                    	showSearch
                                    	optionFilterProp="children"
                                    	disabled={disabled}
                                    	dropdownMatchSelectWidth={false}
                                    >
                                    	{
                                    		ruleAndIndexFieldList &&
											ruleAndIndexFieldList.length > 0 &&
                                            ruleAndIndexFieldList.filter(fItem => fItem.type === item.leftValueType).map((item, index) => {
                                            	return (
                                            		<Option key={index} value={item.name}>
                                                        [{commonLang.sourceName(item.sourceName)}]&nbsp;
                                        			    {item.dName}
                                            		</Option>
                                            	);
                                            })
                                    	}
                                    </Select>
                            	}
                            </InputGroup>
                    	}

                    	{
                    		item.leftValueType === "ENUM" &&
                            <Select
                            	value={item.rightVar || undefined}
                            	placeholder={policyDetailLang.oneCondition("select")} // lang:"请选择"
                            	onChange={(value) => {
                            		this.changeFieldValue(indexArr, "select", "rightVarType", "input");
                            		this.changeFieldValue(indexArr, "select", "rightVar", value);
                            	}}
                            	showSearch
                            	optionFilterProp="children"
                            	dropdownMatchSelectWidth={false}
                            	disabled={disabled}
                            >
                            	{
                            		allMap &&
                                    allMap["fieldEnumObj"] &&
                                    allMap["fieldEnumObj"][item.leftVar] &&
                                    allMap["fieldEnumObj"][item.leftVar].map((item, index) => {
                                    	return (
                                    		<Option
                                    			value={item.value}
                                    			key={index}
                                    			title={item.value}
                                    		>
                                    			{item.description}  [{item.value}]
                                    		</Option>
                                    	);
                                    })
                            	}
                            </Select>
                    	}
                    </Col>
    			}
    			{
    				!disabled &&
                    <Col className="gutter-row" span={3}>
                    	<div className="oper-icon">
                    		{
                    			type === "group" &&
                                <Icon
                                	className="add"
                                	type="plus-circle-o"
                                	onClick={this.addCondition.bind(this, indexArr)}
                                />
                    		}
                    		<Icon
                    			className="delete"
                    			type="delete"
                    			onClick={this.deleteCondition.bind(this, indexArr)}
                    		/>
                    	</div>
                    </Col>
    			}
    		</Row>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global
}))(OneCondition);
