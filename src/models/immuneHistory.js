import { message } from "antd";
import moment from "moment";
import { immuneAPI } from "@/services";

export default {
	namespace: "immuneHistory",
	state: {
		immuneHistoryListLoad: false,
		immuneHistoryList: [],
		indexListReady: false,
		curPage: 1,
		pageSize: 10,
		total: 0,
		pages: 0,
		searchData: {
			appName: "",
			ruleName: "",
			updateType: "",
			operationType: "",
			operationTime: []
		}
	},
	effects: {
		*immuneHistoryList({}, {call, put, select}) {
			yield put({
				type: "setAttrValue",
				payload: {
					immuneHistoryListLoad: true
				}
			});
			const { searchData, curPage, pageSize } = yield select(state=>state.immuneHistory);
			const { appName, ruleName, updateType, operationType, operationTime } = searchData;
			let [operationStartTs, operationEndTs] = [null, null, null, null];
			if (operationTime && operationTime.length > 1) {
				operationStartTs = moment(operationTime[0]).valueOf();
				operationEndTs = moment(operationTime[1]).valueOf();
			}
			const params = {
				appName,
				ruleName,
				updateType,
				operationType,
				curPage,
				pageSize,
				operationStartTs,
				operationEndTs
			};
			let response = yield call(immuneAPI.immuneHistoryList, params);
			yield put({
				type: "setAttrValue",
				payload: {
					immuneHistoryListLoad: false
				}
			});
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initImmuneHistoryList",
				payload: response
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}
			return {
				...state
			};
		},
		setSearchData(state, action) {
			let { payload } = action;
			let { searchData } = state;
			for (let key in payload) {
				if (payload[key] !== undefined) {
					searchData[key] = payload[key];
				}
			}
			return {
				...state,
				searchData
			};
		},
		initImmuneHistoryList(state, action) {
			let { immuneHistoryList, curPage, pageSize, total, pages } = state;
			let data = action.payload.data || null;
			immuneHistoryList = data["dataList"] || [];
			curPage = data.curPage;
			pageSize = data.pageSize;
			total = data.total;
			pages = data.pages;
			return {
				...state,
				immuneHistoryList,
				curPage: curPage,
				pageSize: pageSize,
				total: total,
				pages: pages
			};
		}
	}
};
