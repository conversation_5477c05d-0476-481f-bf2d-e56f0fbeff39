import { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { searchToObject } from "@/utils/utils";
import { checkFunctionHasPermission } from "@/utils/permission";
import NoPermission from "@/components/NoPermission";
import { Tabs } from "antd";
import { approvalLogLang } from "@/constants/lang";

const TabPane = Tabs.TabPane;
import PolicyLog from "./PolicyLog";
import SalaxyLog from "./SalaxyLog";
import WorkflowLog from "./WorkflowLog";

class Task extends PureComponent {

	constructor(props) {
		super(props);
		this.switchTab = this.switchTab.bind(this);
	}

	switchTab(key) {
		let { location, dispatch } = this.props;
		let { pathname } = location;
		let search = "?currentTab=" + key;
		dispatch(routerRedux.push(pathname + search));
	}

	render() {
		let { globalStore, location } = this.props;
		let { menuTreeReady } = globalStore;
		let { search } = location;
		let searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		let currentTab = searchObj && searchObj.currentTab ? parseInt(searchObj.currentTab, 10) : 1;

		return (
			<div>
				<div className="main-area-wrap">
					<div className="page-global-tab">
						<Tabs
							activeKey={currentTab.toString()}
							onChange={this.switchTab.bind(this)}
							animated={false}
						>
							<TabPane
								tab={approvalLogLang.common("policyTitle1")} // lang:策略审批日志
								key="1"
							>
								{
									menuTreeReady && checkFunctionHasPermission("ZB0402", "policyApprovalLogSearch") &&
                                    <PolicyLog />
								}
								{
									menuTreeReady && !checkFunctionHasPermission("ZB0402", "policyApprovalLogSearch") &&
                                    <NoPermission />
								}
							</TabPane>
							<TabPane
								tab={approvalLogLang.common("indicatorTitle1")} // lang:指标审批日志
								key="2"
							>
								{
									menuTreeReady && checkFunctionHasPermission("ZB0402", "indexApprovalLogSearch") &&
                                    <SalaxyLog />
								}
								{
									menuTreeReady && !checkFunctionHasPermission("ZB0402", "indexApprovalLogSearch") &&
                                    <NoPermission />
								}
							</TabPane>
							<TabPane
								tab={approvalLogLang.common("workflowTitle1")} // lang:规则流审批日志
								key="3"
							>
								{
									menuTreeReady && checkFunctionHasPermission("ZB0402", "workflowApprovalLogSearch") &&
                                    <WorkflowLog />
								}
								{
									menuTreeReady && !checkFunctionHasPermission("ZB0402", "workflowApprovalLogSearch") &&
                                    <NoPermission />
								}
							</TabPane>
						</Tabs>
					</div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(Task);
