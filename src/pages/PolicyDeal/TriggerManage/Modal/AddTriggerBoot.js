import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Select, Form, Row, Col, message } from "antd";
import { CommonConstants } from "@/constants";
import { policyDealAPI } from "@/services";
import moment from "moment";
import { checkFunctionHasPermission } from "@/utils/permission";

const Option = Select.Option;

class AddTriggerBoot extends PureComponent {

	constructor(props) {
		super(props);
		this.changeDialogDataHandle = this.changeDialogDataHandle.bind(this);
		this.openRealConfigModel = this.openRealConfigModel.bind(this);
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyDealStore } = this.props;
		let { addTriggerBoot } = policyDealStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		addTriggerBoot[field] = value;
		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				addTriggerBoot: addTriggerBoot
			}
		});
	}

	openRealConfigModel() {
		let { dispatch, policyDealStore } = this.props;
		let { addTriggerBoot } = policyDealStore.dialogData;

		let manageType = addTriggerBoot.manageType;

		console.log(manageType);
		if (!manageType) {
			message.warning("请选择管理类型");
			return;
		}
		if (manageType === "untrigger") {

			if (addTriggerBoot.controlType === "byTime" && !checkFunctionHasPermission("ZB0301", "addUnTriggerByTime")) {
				message.warning("无权限操作");
				return;
			} else if (addTriggerBoot.controlType === "byField" && !checkFunctionHasPermission("ZB0301", "addUnTriggerByField")) {
				message.warning("无权限操作");
				return;
			}

			let params = {
				dealType: addTriggerBoot.dealType,
				controlType: addTriggerBoot.controlType
			};

			policyDealAPI.verifyTriggerExist(params).then(res => {
				if (res.success) {
					let isConfigNotExist = res.data;

					if (!isConfigNotExist) {
						message.error("该风险决策的不触发配置已存在");
					} else {
						if (addTriggerBoot.controlType === "byTime") {

							let modifyTriggerTimeData = {
								isUpdate: false,
								dealType: addTriggerBoot.dealType,
								quickType: null,
								calendarValue: moment(new Date()),
								calendarDataList: []
							};

							dispatch({
								type: "policyDeal/setDialogData",
								payload: {
									modifyTriggerTimeData: modifyTriggerTimeData
								}
							});

							dispatch({
								type: "policyDeal/setDialogShow",
								payload: {
									addTriggerBoot: false,
									modifyTriggerByTime: true
								}
							});
						} else {
							dispatch({
								type: "policyDeal/setDialogShow",
								payload: {
									addTriggerBoot: false,
									modifyTriggerByField: true
								}
							});

							let modifyTriggerFieldData = {
								matchField: null,
								matchValue: null,
								dealType: addTriggerBoot.dealType,
								isUpdate: false
							};

							dispatch({
								type: "policyDeal/setDialogData",
								payload: {
									modifyTriggerFieldData: modifyTriggerFieldData
								}
							});
						}
					}
				} else {
					console.log(res.message);
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		} else if (manageType === "immuno") {

			if (!checkFunctionHasPermission("ZB0301", "addDealTypeImmuno")) {
				message.warning("无权限操作");
				return;
			}

			dispatch({
				type: "policyDeal/setDialogShow",
				payload: {
					addTriggerBoot: false,
					modifyImmuno: true
				}
			});

			let modifyImmuno = {
				eventId: null,
				immunoField: null,
				immunoTime: null,
				app: "",
				description: "",
				immunoConditions: {
					logicOperator: "&&",
					priority: "1",
					defaultNode: false,
					children: []
				},
				isUpdate: false
			};

			dispatch({
				type: "policyDeal/setDialogData",
				payload: {
					modifyImmuno: modifyImmuno
				}
			});
		}

	}

	render() {
		let { policyDealStore, globalStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyDealStore;
		let { allMap } = globalStore;
		let { addTriggerBoot } = dialogData;

		return (
			<Modal
				title="新增决策触发方式"
				visible={dialogShow.addTriggerBoot}
				maskClosable={false}
				onOk={this.openRealConfigModel.bind(this)}
				onCancel={() => {
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							addTriggerBoot: false
						}
					});
				}}
			>
				<Form className="modal-form">
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">管理类型：</Col>
						<Col span={18}>
							<Select
								placeholder="请选择管理类型"
								value={addTriggerBoot.manageType || undefined}
								onChange={this.changeDialogDataHandle.bind(this, "manageType", "select")}
							>
								<Option value="untrigger" index="untrigger">不触发配置</Option>
								<Option value="immuno" index="immuno">免打扰配置</Option>
							</Select>
						</Col>
					</Row>
					{
						addTriggerBoot.manageType !== "immuno" && <Row gutter={CommonConstants.gutterSpan}>
							<Col span={6} className="basic-info-title">风险决策：</Col>
							<Col span={18}>
								<Select
									placeholder="请选择风险决策"
									value={addTriggerBoot.dealType || undefined}
									onChange={this.changeDialogDataHandle.bind(this, "dealType", "select")}
									showSearch
									optionFilterProp="children"
									dropdownMatchSelectWidth={false}
								>
									{
										allMap &&
                                        allMap["dealTypeList"] &&
                                        allMap["dealTypeList"].map((item, index) => {
                                        	return (<Option value={item.name} index={index}>{item.dName}</Option>);
                                        })
									}
								</Select>
							</Col>
						</Row>
					}
					{
						addTriggerBoot.manageType !== "immuno" && <Row gutter={CommonConstants.gutterSpan}>
							<Col span={6} className="basic-info-title">控制方式：</Col>
							<Col span={18}>
								<Select
									placeholder="请选择控制方式"
									value={addTriggerBoot.controlType || undefined}
									onChange={this.changeDialogDataHandle.bind(this, "controlType", "select")}
								>
									<Option value="byTime" index="byTime">按时间</Option>
									<Option value="byField" index="byField">按字段</Option>
								</Select>
							</Col>
						</Row>
					}
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(AddTriggerBoot);
