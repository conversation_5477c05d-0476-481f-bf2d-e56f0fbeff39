export function getSimpleCfgList(cfg<PERSON><PERSON>, currentIndexObj) {
	// console.log("=======getSimpleCfgList======>>>>>>>>>>>>");
	let list = [];
	let attachFieldsObj = currentIndexObj && currentIndexObj.attachFieldsObj ? currentIndexObj.attachFieldsObj : null;
	cfgJson &&
        cfgJson["params"] &&
        cfgJson["params"].map((item) => {
        	item.children && item.children.map((subItem) => {
        		let value;
        		if (attachFieldsObj && subItem.name && subItem.name.indexOf("attachFields.") > -1) {
        			let attName = subItem["name"].split("attachFields.")[1];
        			value = attachFieldsObj[attName] ? attachFieldsObj[attName] : undefined;
        		} else {
        			value = currentIndexObj && currentIndexObj[subItem.name] ? currentIndexObj[subItem.name] : undefined;
        		}
        		let obj = {
        			name: subItem.name,
        			componentType: subItem.componentType,
        			type: subItem.type,
        			value: value,
        			mapType: subItem.mapType ? subItem.mapType : null,
        			selectName: subItem.selectName ? subItem.selectName : null,
        			selectType: subItem.selectType ? subItem.selectType : null,
        			selectOption: subItem.selectOption ? subItem.selectOption : null,
        			willChangeOther: subItem["willChangeOther"] ? subItem["willChangeOther"] : [],
        			willChangeSelf: subItem["willChangeSelf"] ? subItem["willChangeSelf"] : null,
        			willChangeMoreSelf: subItem["willChangeMoreSelf"] ? subItem["willChangeMoreSelf"] : [],
        			willChangeParent: subItem["willChangeParent"] ? subItem["willChangeParent"] : []
        		};
        		list.push(obj);
        	});
        });
	return list;
}

export function getRuleCfgList(cfgJson, params) {
	let list = [];
	cfgJson &&
        cfgJson["params"] &&
        cfgJson["params"].map((item, index) => {
        	item.children && item.children.map((subItem, subIndex) => {
        		let param = params.find(pItem => pItem.name === subItem.name);
        		let obj = {
        			name: subItem.name,
        			componentType: subItem.componentType,
        			type: subItem.type ? subItem.type : "string",
        			value: param && param.value ? param.value : subItem.defaultValue,
        			mapType: subItem.mapType ? subItem.mapType : null,
        			selectName: subItem.selectName ? subItem.selectName : null,
        			selectType: subItem.selectType ? subItem.selectType : null,
        			selectOption: subItem.selectOption ? subItem.selectOption : null,
        			willChangeOther: subItem["willChangeOther"] ? subItem["willChangeOther"] : [],
        			willChangeSelf: subItem["willChangeSelf"] ? subItem["willChangeSelf"] : null,
        			willChangeParent: subItem["willChangeParent"] ? subItem["willChangeParent"] : []
        		};
        		list.push(obj);
        	});
        });
	return list;
}

export function getHandleType(handleObj, allMap) {
	let type = "string";
	if (handleObj && handleObj.selectType) {
		if (handleObj.selectType === "service") {
			if (handleObj.value && handleObj.selectName) {
				let mapItem = allMap && allMap[handleObj.selectName] && handleObj.value && allMap[handleObj.selectName].filter(item => item.name === handleObj.value)[0];
				type = mapItem && mapItem["type"] ? mapItem["type"].toLowerCase() : "string";
			}
		}
	}
	return type;
}
