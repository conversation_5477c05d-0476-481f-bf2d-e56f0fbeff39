@import "../base/variables";

:global {
	.ant-layout {
		background: @bgColor;
		overflow-x: inherit !important;
	}
	.ant-menu-dark {
		.ant-menu-sub {
			margin-left: 0;
		}
	}

	.ant-layout-sider-children {
		overflow-y: scroll;
		overflow-x: hidden;
	}

	.ant-dropdown {
		.anticon {
			margin-right: 5px;
		}
	}
	.basic-layout {
		.ant-layout-sider-children {
			&::-webkit-scrollbar {
				display: none;
			}
		}
		.basic-content {
			width: 100%;
			height: ~"calc(100vh - 60px)";
			overflow: hidden;
			&::-webkit-scrollbar-thumb {
				background-color: transparent;
			}

			&::-webkit-scrollbar {
				width: 8px;
				height: 8px;
			}
		}
	}
	.fit-body {
		min-height: ~"calc(100vh - 112px)";
	}
}
