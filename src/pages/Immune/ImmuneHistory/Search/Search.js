import { Row, Col, Input, <PERSON><PERSON>, <PERSON>, DatePicker } from "antd";
import { immuneHistoryLang, timePickerLang } from "@/constants/lang";
import {ImmuneConstants} from "@/constants";

const { RangePicker } = DatePicker;

export default (props) => {
	const { reset, search, changeSearchField, searchData = {} } = props;
	return (
		<Row gutter={10}>
			<Col span={5}>
				<Input
					placeholder={immuneHistoryLang.searchParams("ruleName")} // 规则名称
					value={searchData.ruleName || undefined}
					onChange={(e) => {
						changeSearchField("ruleName", e, "input");
					}}
					allowClear
					onPressEnter={search}
				/>
			</Col>
			<Col span={3}>
				<Select
					className="wid-100-percent"
					placeholder={immuneHistoryLang.searchParams("updateType")} // 更新类型
					value={searchData.updateType || undefined}
					showSearch
					allowClear
					dropdownMatchSelectWidth={false}
					optionFilterProp="children"
					onChange={(e) => {
						changeSearchField("updateType", e);
						search();
					}}
				>
					{
						ImmuneConstants.getUpdateType().map((item, index) => {
							return (
								<Option value={item.value} key={index}>
									{item.name}
								</Option>
							);
						})
					}
				</Select>
			</Col>
			<Col span={8}>
				<RangePicker
					placeholder={[immuneHistoryLang.searchParams("operaStartTime"), immuneHistoryLang.searchParams("operaEndTime")]} // 操作时间
					value={searchData.operationTime || []}
					onChange={(e) => {
						changeSearchField("operationTime", e);
						search();
					}}
					className="wid-100-percent"
					format="YYYY-MM-DD HH:mm:ss"
					allowClear
					showTime
					ranges={timePickerLang.timePicker("dateMap")}
				/>
			</Col>
			<Col span={4}>
				<Select
					className="wid-100-percent"
					placeholder={immuneHistoryLang.searchParams("operaType")} // 操作类型
					value={searchData.operationType || undefined}
					allowClear
					dropdownMatchSelectWidth={false}
					optionFilterProp="children"
					onChange={(e) => {
						changeSearchField("operationType", e);
						search();
					}}
				>
					{
						ImmuneConstants.getOperaType().map((item, index) => {
							return (
								<Option value={item.value} key={index}>
									{item.name}
								</Option>
							);
						})
					}
				</Select>
			</Col>
			<Col span={4}>
				<Button onClick={reset}>
					{/* 重置 */}
					{immuneHistoryLang.common("reset")}
				</Button>
				<Button onClick={search} type="primary" className="ml10">
					{/* 查询 */}
					{immuneHistoryLang.common("search")}
				</Button>
			</Col>
		</Row>
	);
};
