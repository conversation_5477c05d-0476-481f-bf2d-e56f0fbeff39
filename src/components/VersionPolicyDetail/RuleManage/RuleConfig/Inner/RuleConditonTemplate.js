import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Row, Col, Checkbox, Popover } from "antd";
import { isJSON } from "@/utils/isJSON";
import { CommonConstants, PolicyConstants } from "@/constants";
import { commonLang, policyDetailLang } from "@/constants/lang";
import { getRuleCfgList, getHandleType } from "@/utils/salaxy";
import CustomRuleConfig from "@/components/CustomRuleConfig";
import TooltipWrapper from "@/components/TooltipWrapper";

const { excludeRuleTemplate } = PolicyConstants;

const Option = Select.Option;
const InputGroup = Input.Group;

class RuleConditonTemplate extends PureComponent {
	constructor(props) {
		super(props);
		this.expandTemplate = this.expandTemplate.bind(this);
	}

	expandTemplate() {
		let { policyDetailStore, dispatch, ruleIndexArr, conditionIndex, currentRule: diffCurrentRule } = this.props;
		let { expandTemplate, policyRules } = policyDetailStore;

		let currentRule = null;
		if (ruleIndexArr && ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
		} else if (ruleIndexArr && ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
		}
		if (!currentRule) {
			currentRule = diffCurrentRule;
		}

		if (expandTemplate[currentRule.uuid]) {
			expandTemplate[currentRule.uuid];
			if (expandTemplate[currentRule.uuid].indexOf(conditionIndex) > -1) {
				let index = expandTemplate[currentRule.uuid].indexOf(conditionIndex);
				expandTemplate[currentRule.uuid].splice(index, 1);
			} else {
				expandTemplate[currentRule.uuid].push(conditionIndex);
			}
		} else {
			expandTemplate[currentRule.uuid] = [conditionIndex];
		}

		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				expandTemplate: expandTemplate
			}
		});
	}

	render() {
		let { globalStore, policyDetailStore, templateStore, currentRule, conditionIndex, property } = this.props;
		let { policyDetail, expandTemplate } = policyDetailStore;
		let { ruleTemplateListObj } = templateStore;
		let { allMap, personalMode } = globalStore;
		let { lang } = personalMode;

		console.log(this.props);
		let currentCondition = currentRule && currentRule["conditions"] ? currentRule["conditions"] : null;
		let templateName = property;
		let currentTemplate = ruleTemplateListObj && templateName ? ruleTemplateListObj[templateName] : null;
		let cfgJson = currentTemplate && currentTemplate.cfgJson && isJSON(currentTemplate.cfgJson) ? JSON.parse(currentTemplate.cfgJson) : null;
		let currentParamInfo = currentCondition && currentCondition.children && currentCondition.children[conditionIndex] ? currentCondition.children[conditionIndex] : {};
		let params = currentParamInfo ? currentParamInfo["params"] : [];
		let expandIndexList = expandTemplate[currentRule.uuid] ? expandTemplate[currentRule.uuid] : [];
		// 这里处理规则
		let simpleCfgList = getRuleCfgList(cfgJson, params);

		return (
			<div className="custom-condition-item template-condition">
				{
					currentTemplate &&
                    currentTemplate.description &&
                    <Row gutter={CommonConstants.gutterSpan} className="template-condition-row">
                    	<Col span={4} className="basic-info-title">
                    		{/* 规则描述 */}
                    		{policyDetailLang.ruleConditionTemplate("ruleDescription")}：
                    	</Col>
                    	<Col span={8}>
                    		<p className="basic-info-description">
                    			{lang === "en" ? currentTemplate.enDescription : currentTemplate.description}
                    		</p>
                    	</Col>
                    	<Col span={6}>

                    	</Col>
                    	{
                    		currentRule && currentRule["template"] === "common/custom" &&
                            <Col span={3} className="basic-info-oper">
                            	{/* 收起/展开条件 */}
                            	<Tooltip title={policyDetailLang.ruleConditionTemplate("upDown")} placement="top">
                            		<Icon
                            			className={expandIndexList.indexOf(conditionIndex) > -1 ? "show-more" : "show-more hide"}
                            			type="double-right"
                            			onClick={this.expandTemplate.bind(this)}
                            		/>
                            	</Tooltip>
                            </Col>
                    	}
                    </Row>
				}
				{
					// 如果是逻辑是添加规则排除，有些规则模板无法配置出来，这就需要
					currentParamInfo &&
                    currentParamInfo.property &&
                    excludeRuleTemplate.indexOf(currentParamInfo.property) > -1 &&
                    <CustomRuleConfig
                    	currentParamInfo={currentParamInfo}
                    	simpleCfgList={simpleCfgList}
                    	disabled={true}
                    />
				}
				{
					// 判断当前指标模板不在排除列表
					currentParamInfo &&
                    currentParamInfo.property &&
                    excludeRuleTemplate.indexOf(currentParamInfo.property) === -1 &&
                    currentRule &&
                    (currentRule["template"] !== "common/custom" ? true : expandIndexList.indexOf(conditionIndex) > -1) &&
                    cfgJson.params &&
                    cfgJson.params.map((item, index) => {
                    	let tipTitle = lang === "cn" ? item["tipTitle"] : item["enTipTitle"];
                    	let tipContent = lang === "cn" ? item["tipContent"] : item["enTipContent"];
                    	let tipContentArr = [];
                    	if (tipContent) {
                    		tipContentArr = tipContent.split(/\n/);
                    	}

                    	let RowDom = (
                    		<Row gutter={CommonConstants.gutterSpan} className="template-condition-row" key={index}>
                    			<Col span={4} className="basic-info-title">
                    				{lang === "en" ? item.enLabelText : item.labelText}
                    			</Col>
                    			{
                    				item.children && item.children.map((subItem, subIndex) => {
                    					let param = params.filter(item => item.name === subItem.name)[0] ? params.filter(item => item.name === subItem.name)[0] : undefined;
                    					let type = param ? param["type"] : undefined;
                    					let value = param ? param["value"] : undefined;
                    					let serviceMapName = null;
                    					if (subItem.componentType === "select" && subItem.selectName) {
                    						if (subItem.mapType === "static") {
                    							serviceMapName = subItem.selectName;
                    						} else if (subItem.mapType === "dynamic") {
                    							if (subItem.selectName === "${policySetUuid}_policys") {
                    								serviceMapName = policyDetail.fkPolicySetUuid + "_policys";
                    							}
                    						}
                    					}
                    					let filterType = subItem.filterType && subItem.filterType.length ? subItem.filterType : null;

                    					/*
										* 从这里处理changeRuleForOther规则
										* handle：指的是改变当前参数的那个值
										* */
                    					let currentSimpleObj = simpleCfgList.find(fItem => fItem.name === subItem.name);
                    					// 得到willChangeSelf
                    					let willChangeSelf = currentSimpleObj && currentSimpleObj.willChangeSelf ? currentSimpleObj.willChangeSelf : null;

                    					// 获取handle 名称
                    					let changeHandleName = willChangeSelf && willChangeSelf.name ? willChangeSelf.name : null;
                    					// 获取handle 实体
                    					let changeHandleObj = changeHandleName ? simpleCfgList.find(fItem => fItem.name === changeHandleName) : null;
                    					// 获取handle value
                    					let changeHandleValue = changeHandleObj ? changeHandleObj.value : null;
                    					/*
										* 预先设置如下几个变量
										* ruleDisabled
										* ruleHidden
										* ruleSelectMap
										* ruleMapLocation
										* */
                    					let ruleDisabled = false;
                    					let ruleHidden = false;
                    					let ruleMapName = null;
                    					let ruleMapLocation = null;

                    					if (willChangeSelf) {
                    						// console.log(changeHandleValue);
                    						if (willChangeSelf.changeMode && willChangeSelf.changeMode === "whenSomeValue") {
                    							// 当为具体值的时候
                    							willChangeSelf["caseList"] && willChangeSelf["caseList"].map((caseItem, caseIndex) => {
                    								if (caseItem["modeValueList"] && caseItem["modeValueList"].find(mvItem => mvItem === changeHandleValue)) {
                    									// 如果modeValueList列表中确实有handle value，则进行如下操作
                    									if (caseItem.changeType && caseItem.changeType === "disabled") {
                    										ruleDisabled = true;
                    									} else if (caseItem.changeType && caseItem.changeType === "hidden") {
                    										ruleHidden = true;
                    									} else if (caseItem.changeType && caseItem.changeType === "selectMap") {
                    										// 如果是改变select map
                    										ruleMapName = caseItem.mapName;
                    										ruleMapLocation = caseItem.mapLocation;
                    									}
                    								}
                    							});
                    						} else if (willChangeSelf.changeMode && willChangeSelf.changeMode === "whenSomeType") {
                    							// 当为具体类型的时候
                    							let type = getHandleType(changeHandleObj, allMap);
                    							willChangeSelf["caseList"] && willChangeSelf["caseList"].map((caseItem, caseIndex) => {
                    								if (caseItem["modeValueList"] && caseItem["modeValueList"].find(mvItem => mvItem.toLowerCase() === type.toLowerCase())) {
                    									// 如果modeValueList列表中确实有handle value，则进行如下操作
                    									if (caseItem.changeType && caseItem.changeType === "disabled") {
                    										ruleDisabled = true;
                    									} else if (caseItem.changeType && caseItem.changeType === "hidden") {
                    										ruleHidden = true;
                    									} else if (caseItem.changeType && caseItem.changeType === "selectMap") {
                    										// 如果是改变select map
                    										ruleMapName = caseItem.mapName;
                    										ruleMapLocation = caseItem.mapLocation;
                    									}
                    								}
                    							});
                    						}
                    					}

                    					// if (willChangeSelf) {
                    					// 	console.log("=========================willChangeSelf");
                    					// 	console.log(willChangeSelf);
                    					// 	console.log("ruleDisabled：" + ruleDisabled);
                    					// 	console.log("ruleHidden：" + ruleHidden);
                    					// 	console.log("ruleMapName：" + ruleMapName);
                    					// 	console.log("ruleMapLocation：" + ruleMapLocation);
                    					// }

                    					if (ruleHidden) {
                    						return;
                    					}

                    					return (
                    						<Col span={8} key={subIndex}>
                    							{
                    								subItem.componentType === "input" &&
													<TooltipWrapper>
														<Input
															value={(subItem.name === "value" || subItem.name === "operator") ? currentCondition["children"][conditionIndex][subItem.name] : value}
															disabled={true}
															placeholder={subItem.placeholder}
															addonAfter={subItem.addonAfter}
														/>
													</TooltipWrapper>
                    							}
                    							{
                    								subItem.componentType === "select" &&
													<TooltipWrapper>
														<Select
                                                    	value={(subItem.name === "value" || subItem.name === "operator") ? currentCondition["children"][conditionIndex][subItem.name] : value}
                                                    	placeholder={policyDetailLang.ruleConditionTemplate("select")} // 请选择
                                                    	disabled={true}
														>
                                                    	{
                                                    		subItem.selectType === "self" && subItem.selectOption &&
                                                            subItem.selectOption.map((optionItem, optionIndex) => {
                                                            	return (
                                                            		<Option
                                                            			value={optionItem.value}
                                                            			key={optionIndex}
                                                            		>
                                                            			{lang === "en" ? optionItem.enName : optionItem.name}
                                                            		</Option>
                                                            	);
                                                            })
                                                    	}
                                                    	{
                                                    		subItem.selectType === "service" &&
                                                            subItem.selectName &&
                                                            allMap[serviceMapName] &&
                                                            allMap[serviceMapName].filter(item => {
                                                            	return item.type && filterType ? filterType.indexOf(item.type.toLowerCase()) > -1 : true;
                                                            }).filter(fItem => {
                                                            	let appName = policyDetail && policyDetail.appName ? policyDetail.appName : null;
                                                            	let filterByFieldType = subItem["filterByField"] && subItem["filterByFieldName"] && subItem["filterByFieldType"] ? subItem["filterByFieldType"] : null;
                                                            	let result = true;
                                                            	if (filterByFieldType) {
                                                            		if (filterByFieldType === "policyApp") {
                                                            			result = fItem[subItem["filterByFieldName"]] === appName;
                                                            		} else if (filterByFieldType === "policyAppAndCustom") {
                                                            			result = fItem[subItem["filterByFieldName"]] === appName || subItem["filterByFieldValue"] === fItem[subItem["filterByFieldName"]];
                                                            		} else {
                                                            			result = subItem["filterByFieldValue"] === fItem[subItem["filterByFieldName"]];
                                                            		}
                                                            	}
                                                            	return result;
                                                            }).map((optionItem, optionIndex) => {
                                                            	return (
                                                            		<Option
                                                            			value={optionItem.name}
                                                            			key={optionIndex}
                                                            		>
                                                            			{
                                                            				serviceMapName === "ruleAndIndexFieldList"
                                                            					? `[${commonLang.sourceName(optionItem.sourceName)}] ` + optionItem.dName
                                                            					: optionItem.dName
                                                            			}
                                                            		</Option>
                                                            	);
                                                            })
                                                    	}
														</Select>
													</TooltipWrapper>
                    							}
                    							{
                    								subItem.componentType === "checkbox" &&
                                                    <Checkbox.Group
                                                    	value={value ? value.toString().split(",") : undefined}
                                                    	disabled={true}
                                                    >
                                                    	{
                                                    		subItem.selectType === "self" && subItem.selectOption &&
                                                            subItem.selectOption.map((optionItem, optionIndex) => {
                                                            	return (
                                                            		<Checkbox
                                                            			value={optionItem.value}
                                                            			key={optionIndex}
                                                            		>
                                                            			{lang === "en" ? optionItem.enName : optionItem.name}
                                                            		</Checkbox>
                                                            	);
                                                            })
                                                    	}
                                                    	{
                                                    		subItem.selectType === "service" && subItem.selectName &&
                                                            allMap[subItem.selectName] && allMap[subItem.selectName].map((optionItem, optionIndex) => {
                                                    			return (
                                                    				<Checkbox
                                                    					value={optionItem.name}
                                                    					key={optionIndex}
                                                    				>
                                                    					{lang === "en" ? optionItem.enName : optionItem.name}
                                                    				</Checkbox>
                                                    			);
                                                    		})
                                                    	}
                                                    </Checkbox.Group>
                    							}
                    							{
                    								subItem.componentType === "variable" &&
                                                    <InputGroup compact>
                                                    	<TooltipWrapper>
                                                    		<Select
                                                    			value={type}
                                                    			style={{ width: "30%" }}
                                                    			disabled={true}
                                                    		>
                                                    			{
                                                    				(subItem.type === "all" || subItem.type === "input") &&
																	<Option value="input">
																		{/* 常量 */}
																		{policyDetailLang.ruleConditionTemplate("constant")}
																	</Option>
                                                    			}
                                                    			{
                                                    				(subItem.type === "all" || subItem.type === "context") &&
																	<Option value="context">
																		{/* 变量 */}
																		{policyDetailLang.ruleConditionTemplate("variable")}
																	</Option>
                                                    			}
                                                    		</Select>
                                                    	</TooltipWrapper>
                                                    	{
                                                    		type && type === "input" &&
                                                            <Input
                                                            	style={{ width: "70%" }}
                                                            	value={value}
                                                            	disabled={true}
                                                            	placeholder={policyDetailLang.ruleConditionTemplate("enter")} // 请输入
                                                            />
                                                    	}
                                                    	{
                                                    		type && type === "context" &&
															<TooltipWrapper>
																<Select
                                                            	style={{ width: "70%" }}
                                                            	value={value}
                                                            	placeholder={policyDetailLang.ruleConditionTemplate("select")} // 请选择
                                                            	disabled={true}
																>
                                                            	{
                                                            		allMap &&
                                                                    allMap["ruleFieldList"] &&
                                                                    !subItem["includeIndex"] &&
                                                                    allMap["ruleFieldList"].filter(item => item.type && filterType ? filterType.indexOf(item.type.toLowerCase()) > -1 : true).map((item, index) => {
                                                                    	return (
                                                                    		<Option value={item.name} key={index}>
                                                                    			{
                                                                    				item.dName
                                                                    			}
                                                                    		</Option>
                                                                    	);
                                                                    })
                                                            	}
                                                            	{
                                                            		allMap &&
                                                                    allMap["ruleFieldList"] &&
                                                                    subItem["includeIndex"] &&
                                                                    allMap["ruleAndIndexFieldList"].filter(item => item.type && filterType ? filterType.indexOf(item.type.toLowerCase()) > -1 : true).map((item, index) => {
                                                                    	return (
                                                                    		<Option value={item.name} key={index}>
                                                                                [{commonLang.sourceName(item.sourceName)}]&nbsp;
                                                                    			{item.dName}
                                                                    		</Option>
                                                                    	);
                                                                    })
                                                            	}
																</Select>
															</TooltipWrapper>
                                                    	}
                                                    </InputGroup>
                    							}
                    						</Col>
                    					);
                    				})
                    			}
                    			{
                    				tipTitle &&
                                    tipContent &&
                                    <Col span={1}>
                                    	<Popover
                                    		popupClassName="rule-param-pop-tip"
                                    		placement="right"
                                    		title={tipTitle}
                                    		content={
                                    			tipContentArr.map((tip, tipIndex) => {
                                    				return (
                                    					<div key={tipIndex}>{tip}</div>
                                    				);
                                    			})
                                    		}
                                    	>
                                    		<Icon
                                    			type="question-circle-o"
                                    			className="param-tip-icon"
                                    		/>
                                    	</Popover>
                                    </Col>
                    			}
                    		</Row>
                    	);
                    	// 在这里处理行规则willChangeLine
                    	let showThisLine = false;
                    	let willChangeLine = JSON.stringify(item["willChangeLine"]) !== "{}" ? item["willChangeLine"] : undefined;

                    	if (willChangeLine) {
                    		let currentSimpleObj = simpleCfgList.find(fItem => fItem.name === willChangeLine.name);
                    		// 是否隐藏当前行
                    		showThisLine = currentSimpleObj && currentSimpleObj.value === willChangeLine.changeValue;
                    	}

                    	// 如果没有行控制规则
                    	if (!willChangeLine) {
                    		return RowDom;
                    	}

                    	// 如果有行控制规则，那么判断showThisLine是否为true
                    	if (willChangeLine && showThisLine) {
                    		return RowDom;
                    	}
                    })
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	templateStore: state.template,
	policyDetailStore: state.policyDetail
}))(RuleConditonTemplate);
