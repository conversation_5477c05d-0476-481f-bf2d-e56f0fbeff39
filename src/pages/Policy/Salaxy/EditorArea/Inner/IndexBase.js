import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Row, Col, Icon } from "antd";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import PreviewDemo from "../../Common/PreviewDemo";

class IndexBase extends PureComponent {
	state = {};

	constructor(props) {
		super(props);
		this.changeBaseField = this.changeBaseField.bind(this);
	}

	changeBaseField(field, type, e) {
		let { indexEditorStore, dispatch } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;

		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		currentIndexObj[field] = value;
		currentIndexObj["hasModify"] = true;

		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				editIndexMap: editIndexMap
			}
		});
	}

	render() {
		let { indexEditorStore, templateStore, globalStore, templateName } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let { indexTemplateListObj } = templateStore;
		let { personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";

		let currentTemplate = indexTemplateListObj && templateName ? indexTemplateListObj[templateName] : null;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		const { advanceConfig = {} } = currentTemplate || {};
		return (
			<div className="ml-1">
				<Row className="mb10" gutter={CommonConstants.gutterSpan}>
					<Col span={4} className="basic-info-title">
						{/* lang:指标名称 */}
						{indexListLang.ruleAttr("indexName")}
					</Col>
					<Col span={8}>
						<Input
							value={currentIndexObj ? currentIndexObj["name"] : undefined}
							onChange={this.changeBaseField.bind(this, "name", "input")}
						/>
					</Col>
					<Col span={1}>
						<PreviewDemo
							advanceConfig={advanceConfig}
						/>
					</Col>
				</Row>
				<Row className="mb10" gutter={CommonConstants.gutterSpan}>
					<Col span={4} className="basic-info-title">
						{/* lang:指标描述 */}
						{indexListLang.ruleAttr("indexDescription")}
					</Col>
					<Col span={16}>
						<p className="index-description">
							{
								currentTemplate ? lang === "cn" ? currentTemplate["description"] : currentTemplate["enDescription"] : undefined
							}
						</p>
					</Col>
				</Row>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(IndexBase);

