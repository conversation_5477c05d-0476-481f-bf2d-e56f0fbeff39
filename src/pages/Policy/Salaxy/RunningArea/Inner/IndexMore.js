import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { isJSON } from "@/utils/isJSON";
import { Input, Icon, Select, Row, Col, Progress } from "antd";
import { CommonConstants, PolicyConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import FilterList from "./FieldSet/FilterList";
// import Scene from "./Scene";
import FilterScene from "../../Common/FilterScene";
import CommonFilter from "../../Common/CommonFilter";
import OfflineInfo from "./OfflineInfo";
import CrossMatchFilterList from './FieldSet/CrossMatchFilterList';

const InputGroup = Input.Group;
const Option = Select.Option;
const TextArea = Input.TextArea;

class IndexMore extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { templateStore, globalStore, templateName, indexData = {}, indexRunningStore } = this.props;
		let { allMap } = globalStore;
		let filterObj = indexData && indexData.filterList ? indexData.filterList : {};
		console.log(indexData);
		let { sceneList = [] } = indexData;
		let { editIndexMap, currentIndexId } = indexRunningStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		let eventSelectOption = [];
		let policySetMapName = indexData && indexData.app ? indexData.app + "_policySets" : null;

		let { indexTemplateListObj } = templateStore;
		let currentTemplate = indexTemplateListObj && templateName ? indexTemplateListObj[templateName] : null;
		let cfgJson = currentTemplate && currentTemplate.cfgJson && isJSON(currentTemplate.cfgJson) ? JSON.parse(currentTemplate.cfgJson) : null;
		let { advanceConfig = {} } = currentTemplate || {};

		let noFilterFunction = false;
		let noScene = false;
		let supportOffline = false;
		if (advanceConfig.hasOwnProperty("noFilterFunction") && advanceConfig.noFilterFunction) {
			noFilterFunction = true;
		}
		if (advanceConfig.hasOwnProperty("noScene") && advanceConfig.noScene) {
			noScene = true;
		}
		if (advanceConfig.hasOwnProperty("supportOffline") && advanceConfig.supportOffline) {
			supportOffline = true;
		}

		if (allMap && allMap["eventIdSelect"]) {
			for (let i in allMap["eventIdSelect"]) {
				eventSelectOption.push(
					<Option value={i} key={i}>
						{
							allMap["eventIdSelect"][i]
						}
					</Option>
				);
			}
		}

		let hasFieldSet = false;
		cfgJson && cfgJson.params.length > 0 && cfgJson.params.forEach((item) => {
			if (item && item.children) {
				item.children.forEach((cItem) => {
					if (cItem.componentType === "fieldSet") {
						hasFieldSet = true;
					}
				});
			}
		});

		return (
			<div className="ml-1">
				{
					sceneList.length > 0 &&
					!noScene &&
					<FilterScene
						width="150px"
						disabled={true}
						indexData={indexData}
					/>
					// <Scene
					// 	disabled={true}
					// 	indexData={indexData}
					// />
				}
				{
					!noFilterFunction &&
					!hasFieldSet &&
					<CommonFilter
						disabled={true}
						filterObj={filterObj}
					/>
				}
				{
					hasFieldSet &&
					templateName &&
					templateName !== "crossMatch" && templateName !== "newCrossMatch" &&
					<FilterList
						indexData={indexData}
						disabled={true}
					/>
				}
				{/* 交叉匹配和自定义模版专属 */}
				{
					templateName &&
					(templateName == "crossMatch" || templateName == "newCrossMatch") &&
					<CrossMatchFilterList
						disabled={true}
						filterObj={filterObj}
						templateName={templateName}
						onChange={this.changeCommonFilter}
					/>
				}
				{
					supportOffline &&
					indexData.offlineId &&
					<OfflineInfo
						indexData={indexData}
					/>
				}
				<Row className="mb10" gutter={CommonConstants.gutterSpan}>
					<Col span={4} className="basic-info-title">
						{/* lang:指标说明 */}
						{indexListLang.ruleAttr("indexDescription")}
					</Col>
					<Col span={8}>
						<TextArea
							rows={4}
							placeholder={indexListLang.ruleAttr("indexDescriptionPlaceholder")} // lang:请输入指标说明
							value={indexData && indexData["remark"] ? indexData["remark"] : undefined}
							disabled={true}
						/>
					</Col>
				</Row>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexRunningStore: state.indexRunning,
	templateStore: state.template
}))(IndexMore);

