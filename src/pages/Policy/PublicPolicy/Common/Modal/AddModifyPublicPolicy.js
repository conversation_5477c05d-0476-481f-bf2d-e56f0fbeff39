import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Select, Form, Row, Col, Radio, message } from "antd";
import { PolicyConstants } from "@/constants";
import { policyListLang } from "@/constants/lang";
import { publicPolicyListLang } from "@/constants/lang";
import WeightModeEditor from "@/components/WeightModeEditor";
import SelectEffectScope from "@/components/PublicPolicy/SelectEffectScope/SelectEffectScope";
import { publicPolicyEditorAPI } from "@/services";
import "./AddModifyPublicPolicy.less";

const { countOperatorIntMap } = PolicyConstants;

const InputGroup = Input.Group;
const TextArea = Input.TextArea;
const Option = Select.Option;

const formItemLayout = {
	labelCol: {
		xs: { span: 24 },
		sm: { span: 5 }
	},
	wrapperCol: {
		xs: { span: 24 },
		sm: { span: 18 }
	}
};
class AddModifyPublicPolicy extends PureComponent {
	state = {
		partner: "kratos", // 不知道是干嘛的，就按照以前的方式传
		effectScope: "", // 生效范围
		scene: null, // 自定义生效范围
		name: "", // 策略集名称
		riskType: "", // 风险类型
		mode: "", // 策略模式
		dealTypeMappings: [
			{
				"score": null,
				"dealType": null
			},
			{
				"score": null,
				"dealType": null
			},
			{
				"score": null,
				"dealType": null
			}
		], // 权重模式
		terminate: "0", // 是否中断
		terminateOperator: "", // 中断操作符
		terminateThreshold: [], // 中断条件
		description: "", // 描述
		global_app: null // 全局应用
	}

	componentWillMount() {
		let { globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		const global_app = publicPolicyScene && publicPolicyScene.length > 0 && publicPolicyScene.find((v) => v.name === "GLOBAL_APP") || {}; // 全局应用
		this.setState({
			global_app
		});
	}

	changeField = (name, e, type) => {
		let value = e;
		if (type === "input") {
			value = e.target.value;
		}
		this.setState({
			[name]: value
		});
	};

	addPolicy() {
		this.props.form.validateFields((err) => {
			if (!err) {
				const { scene, mode, dealTypeMappings, effectScope, global_app } = this.state;
				const { onCancel, refresh } = this.props;
				if (mode === "Weighted") {
					if (dealTypeMappings && dealTypeMappings.length > 0) {
						let mapHasEmpty = false;
						for (let i = 0; i < dealTypeMappings.length; i++) {
							let mapItem = dealTypeMappings[i];
							if (!mapItem.dealType || !mapItem.score) {
								mapHasEmpty = true;
								break;
							}
						}
						if (mapHasEmpty) {
							// lang:风险阈值配置存在空值，请补充完整！
							message.error(publicPolicyListLang.publicPolicyModal("riskThresholdEmptyTip"));
							return;
						}
					}
				};
				let sceneNew = [];
				if (effectScope === "set") {
					sceneNew = scene;
				} else {
					sceneNew = [{
						appName: global_app.name,
						eventList: [global_app.eventList && global_app.eventList[0].name]
					}];
				};
				if (!(sceneNew && sceneNew.length > 0)) {
					// lang:请选择有效范围
					message.error(publicPolicyListLang.publicPolicyModal("enterEffectScope"));
					return;
				}
				const params = { ...this.state };
				delete params.other_app;
				delete params.global_app;
				delete params.effectScope;
				params.scene = sceneNew;
				params.terminate = String(params.terminate) === "1";

				publicPolicyEditorAPI.addPublicPolicy({
					...params,
					scene: JSON.stringify(params.scene),
					dealTypeMappings: JSON.stringify(params.dealTypeMappings)
				}).then((res) => {
					if (res.success) {
						message.success(publicPolicyListLang.publicPolicyModal("newPolicySuccessTip")); // 新增公共策略成功
						onCancel(false);
						refresh();
					} else {
						message.error(res.message);
					}
				}).catch(e => {
					message.error(e.message);
				});
			}
		});
	}

	// 修改权重值
	changeWeightMode = (field, value, index) => {
		const { dealTypeMappings: dealTypeMappingsOld } = this.state;
		const dealTypeMappings = [...dealTypeMappingsOld];
		dealTypeMappings[index][field] = value;
		this.setState({
			dealTypeMappings
		});
	}

	// 增加权重
	addDeleteItem = (type, index) => {
		const { dealTypeMappings: dealTypeMappingsOld } = this.state;
		const dealTypeMappings = [...dealTypeMappingsOld];
		let temp = {
			"score": null,
			"dealType": null
		};
		if (type === "add") {
			dealTypeMappings.splice(index + 1, 0, temp);
		} else if (type === "delete") {
			dealTypeMappings.splice(index, 1);
		}
		this.setState({
			dealTypeMappings
		});
	}

	render() {
		let { globalStore, form, onCancel } = this.props;
		const { allMap, policyModel, personalMode } = globalStore || {};
		const { publicPolicyRiskSelect = [], dealTypeList = [] } = allMap || {};
		let { lang } = personalMode;
		const { getFieldDecorator } = form;
		const { effectScope, scene, name, riskType, mode, dealTypeMappings, terminate, terminateOperator, terminateThreshold, description } = this.state;
		const { global_app } = this.state;
		return (
			<Modal
				title={publicPolicyListLang.common("addPublicPolicy")} // lang:新建公共策略
				visible={true}
				maskClosable={false}
				onOk={this.addPolicy.bind(this)}
				onCancel={() => {
					onCancel(false);
				}}
				width="650px"
			>
				<Form {...formItemLayout} className="add-modify-public-policy">
					<Row>
						<Col span="24">
							{/* 生效范围 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("effectScope")}>
								{
									getFieldDecorator("effectScope", {
										initialValue: effectScope || undefined,
										rules: [
											{
												required: true,
												message: publicPolicyListLang.publicPolicyModal("enterEffectScope") // 请选择生效范围
											}
										]
									})(
										<Select
											placeholder={publicPolicyListLang.publicPolicyModal("enterEffectScope")} // 请选择生效范围
											onChange={(e) => { this.changeField("effectScope", e, "select"); }}
										>
											{
												global_app &&
												<Option value={global_app.name}>{global_app.dName}</Option>
											}
											{/* 自定义 */}
											<Option value="set">{publicPolicyListLang.publicPolicyModal("custom")}</Option>
										</Select>
									)
								}
							</Form.Item>
							{
								effectScope === "set" &&
								// 自定义生效范围
								<Form.Item label={publicPolicyListLang.publicPolicyModal("customEffectScope")}>
									{
										getFieldDecorator("scene", {
											initialValue: scene || undefined,
											rules: [
												{
													required: true,
													message: publicPolicyListLang.publicPolicyModal("enterCustomEffectScope") // 请选择自定义生效范围
												}
											]
										})(
											<SelectEffectScope
												scene={scene || []}
												changeField={this.changeField.bind(this)}
											/>
										)
									}
								</Form.Item>
							}
						</Col>
						<Col span="24">
							{/* 策略名称 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("policyName")}>
								{
									getFieldDecorator("name", {
										initialValue: name || undefined,
										rules: [
											{
												required: true,
												message: publicPolicyListLang.publicPolicyModal("enterPolicyName")// "请输入策略名称"
											},
											{
												max: 72,
												message: publicPolicyListLang.publicPolicyModal("max72")// "长度控制在72个字符长度"
											},
											{
												pattern: /^[A-Za-z0-9\u4E00-\u9FA5\_]{1,72}$/,
												message: publicPolicyListLang.publicPolicyModal("policyRegTip") // "请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合"
											}
										]
									})(
										<Input
											placeholder={publicPolicyListLang.publicPolicyModal("enterPolicyName")} // "请输入策略名称"
											onChange={(e) => { this.changeField("name", e, "input"); }}
										/>
									)
								}
								<span className="public-policy-tip-text">
									{/* lang:建议输入：中文、英文、数字和下划线的组合 */}
									{publicPolicyListLang.common("inputSuggest1")}
								</span>
							</Form.Item>
						</Col>
						<Col span="24">
							{/* 风险类型 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("riskType")}>
								{
									getFieldDecorator("riskType", {
										initialValue: riskType || undefined,
										rules: [
											{
												required: true,
												message: publicPolicyListLang.publicPolicyModal("enterRiskType")// "请选择风险类型"
											}
										]
									})(
										<Select
											name="riskType"
											placeholder={publicPolicyListLang.publicPolicyModal("enterRiskType")} // 请选择风险类型
											onChange={(e) => { this.changeField("riskType", e, "select"); }}
										>
											{
												publicPolicyRiskSelect &&
												publicPolicyRiskSelect.map((item, index) => {
													return (
														<Option value={item.name} key={index}>
															{item.dName}
														</Option>
													);
												})
											}
										</Select>
									)
								}
							</Form.Item>
						</Col>
						<Col span="24">
							{/* 策略模式 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("policyMode")}>
								{
									getFieldDecorator("mode", {
										initialValue: mode || undefined,
										rules: [
											{
												required: true,
												message: publicPolicyListLang.publicPolicyModal("selectPolicyMode") // "请选择策略模式"
											}
										]
									})(
										<Select
											name="mode"
											placeholder={publicPolicyListLang.publicPolicyModal("selectPolicyMode")} // "请选择策略模式"
											onChange={(e) => { this.changeField("mode", e, "select"); }}
										>
											{
												policyModel && policyModel.map((item, index) => {
													return (
														<Option value={item.name} key={index}>
															{item.dName}
														</Option>
													);
												})
											}
										</Select>
									)}
							</Form.Item>
						</Col>
						{
							mode === "Weighted" &&
							<Col span="24">
								{/* lang:风险阈值 */}
								<Form.Item label={publicPolicyListLang.publicPolicyModal("riskThreshold")}>
									{
										getFieldDecorator("dealTypeMappings", {
											initialValue: dealTypeMappings || undefined,
											rules: [
												{
													required: true,
													message: publicPolicyListLang.publicPolicyModal("enterRiskThreshold") // "请设置风险阈值"
												}
											]
										})(
											<WeightModeEditor
												dealTypeMappings={dealTypeMappings}
												onChange={this.changeWeightMode.bind(this)}
												operateItem={this.addDeleteItem.bind(this)}
											/>
										)
									}
								</Form.Item>
							</Col>
						}
						<Col span="24">
							{/* 是否中断 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("interrupted")}>
								{
									getFieldDecorator("terminate", {
										initialValue: terminate || undefined,
										rules: [
											{
												required: true,
												message: publicPolicyListLang.publicPolicyModal("enterBreakCon") // 请选择中断条件
											}
										]
									})(
										<Radio.Group
											value={terminate}
											onChange={(e) => { this.changeField("terminate", e, "input"); }}
										>
											{/* 不中断 */}
											<Radio value="0">{publicPolicyListLang.publicPolicyModal("noInterruption")}</Radio>
											{/* 中断 */}
											<Radio value="1">{publicPolicyListLang.publicPolicyModal("interruption")}</Radio>
										</Radio.Group>
									)}
							</Form.Item>
						</Col>
						{
							String(terminate) === "1" &&
							<Col span="24">
								{/* 中断条件 */}
								<Form.Item label={publicPolicyListLang.publicPolicyModal("interruptCondition")}>
									<InputGroup compact>
										{/* 风险决策结果 */}
										<Input value={publicPolicyListLang.publicPolicyModal("riskResult")} style={{ "width": "25%" }} disabled />
										<Select
											style={{ "width": "35%" }}
											placeholder={publicPolicyListLang.publicPolicyModal("enterOperator")} // 请选择中断操作符
											value={terminateOperator || undefined}
											onChange={(e) => { this.changeField("terminateOperator", e, "select"); }}
										>
											{
												countOperatorIntMap.map((v) => {
													return <Option value={v.name}>{lang === "en" ? v.enDName : v.dName}</Option>;
												})
											}
										</Select>
										{
											getFieldDecorator("terminateThreshold", {
												initialValue: terminateThreshold || undefined,
												rules: [
													{
														required: true,
														message: publicPolicyListLang.publicPolicyModal("enterRes") // 请设置决策结果
													}
												]
											})(
												<Select
													style={{ "width": "40%", "display": terminateOperator ? "inherit" : "none" }}
													placeholder={publicPolicyListLang.publicPolicyModal("enterBreakCon")} // 请选择中断条件
													value={terminateThreshold || undefined}
													onChange={(e) => { this.changeField("terminateThreshold", e, "select"); }}
												>
													{
														dealTypeList &&
														dealTypeList.length > 0 &&
														dealTypeList.map((v) => {
															return <Option value={v.name}>{lang === "en" ? v.enDName : v.dName}</Option>;
														})
													}
												</Select>
											)
										}
									</InputGroup>

								</Form.Item>
							</Col>
						}
						<Col span="24">
							{/* 描述 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("description")}>
								{
									getFieldDecorator("description", {
										initialValue: description || undefined,
										rules: [
											{
												max: 200,
												message: publicPolicyListLang.publicPolicyModal("max200") // 字符不超过200
											}
										]
									})(
										<TextArea
											onChange={(e) => { this.changeField("description", e, "input"); }}
											placeholder={publicPolicyListLang.publicPolicyModal("enterPolicyDescription")} // 请输入描述
											rows="3"
										/>
									)
								}
							</Form.Item>
						</Col>
					</Row>
				</Form>
			</Modal >
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(Form.create({ name: "add-edit-public-policy" })(AddModifyPublicPolicy));
