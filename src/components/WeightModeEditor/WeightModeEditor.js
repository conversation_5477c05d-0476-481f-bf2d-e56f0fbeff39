import { PureComponent } from "react";
import { connect } from "dva";
import PropTypes from "prop-types";
import { Input, Select, Col, Icon, Tooltip } from "antd";
import "./WeightModeEditor.less";
import { policyListLang, policyDetailLang } from "@/constants/lang";

const Option = Select.Option;
const InputGroup = Input.Group;

class WeightModeEditor extends PureComponent {
    static childContextTypes = {
    	dealTypeMappings: PropTypes.array,
    	onChange: PropTypes.func,
    	operateItem: PropTypes.func
    };

    constructor(props) {
    	super(props);
    	this.changeFieldValue = this.changeFieldValue.bind(this);
    	this.operateItemHandle = this.operateItemHandle.bind(this);
    }

    changeFieldValue(field, type, index, e) {
    	let { onChange } = this.props;
    	let value = "";

    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}
    	onChange(field, value, index);
    }

    operateItemHandle(type, index) {
    	let { operateItem } = this.props;
    	operateItem(type, index);
    }

    render() {
    	let { globalStore, dealTypeMappings, disabled, page } = this.props;
    	let { allMap } = globalStore;
    	let colorIndexMap = {
    		3: [1, 5, 8],
    		4: [1, 4, 7, 8],
    		5: [1, 3, 5, 7, 9],
    		6: [1, 3, 5, 7, 8, 9],
    		7: [1, 3, 4, 5, 7, 9, 10],
    		8: [1, 2, 3, 4, 5, 7, 9, 10],
    		9: [1, 2, 3, 4, 5, 6, 8, 9, 10],
    		10: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    	};

    	return (
    		<div className={page === "basicPage" ? "weight-mode-editor-wrap basic-page" : "weight-mode-editor-wrap"}>
    			{/* 权重模式规则，设置权重分映射风险决策结果，权重区间段左侧分数开区间，右侧区间段闭区间 */}
    			<Tooltip placement="right" title={policyDetailLang.basicSetup("weightModeInfo")}>
    				<Icon type="question-circle" className="more-weight-info" />
    			</Tooltip>
    			{
    				dealTypeMappings && dealTypeMappings.length &&
                    <ul>
                    	{
                    		dealTypeMappings.map((item, index, arr) => {
                    			let value1 = index === 0 ? 0 : arr[index - 1]["score"];
                    			let value2 = item.score ? item.score : undefined;
                    			return (
                    				<li
                    					className={"color" + colorIndexMap[arr.length][index]}
                    					key={index}
                    				>
                    					<InputGroup>
                    						<Col span={4}>
                    							<Input
                    								value={value1}
                    								disabled={true}
                    							/>
                    						</Col>
                    						<Col span={1} className="fz-span">
                                                -
                    						</Col>
                    						<Col span={4}>
                    							<Input
                    								value={value2}
                    								onChange={this.changeFieldValue.bind(this, "score", "input", index)}
                    								disabled={disabled}
                    							/>
                    						</Col>
                    						<Col span={11}>
                    							<Select
                    								value={item.dealType || undefined}
                    								placeholder={policyListLang.common("selectDealType")} // lang:选择处置方式
                    								style={{ width: "100%" }}
                    								onChange={this.changeFieldValue.bind(this, "dealType", "select", index)}
                    								disabled={disabled}
                    							>
                    								{
                    									allMap && allMap["dealTypeList"].map((mapItem, mapIndex) => {
                    										return (
                    											<Option value={mapItem.name} key={mapIndex}>
                    												{mapItem.dName}
                    											</Option>
                    										);
                    									})
                    								}
                    							</Select>
                    						</Col>
                    						{
                    							!disabled &&
                                                <Col span={4} className="info-oper">
                                                	{
                                                		arr.length < 10 &&
                                                        <Icon
                                                        	className="add"
                                                        	type="plus-circle-o"
                                                        	onClick={this.operateItemHandle.bind(this, "add", index)}
                                                        />
                                                	}
                                                	{
                                                		arr.length > 3 &&
                                                        <Icon
                                                        	className="delete"
                                                        	type="delete"
                                                        	onClick={this.operateItemHandle.bind(this, "delete", index)}
                                                        />
                                                	}
                                                </Col>
                    						}
                    					</InputGroup>
                    				</li>
                    			);
                    		})
                    	}
                    	{/* <li className="color11"></li> */}
                    </ul>
    			}
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyListStore: state.policyList
}))(WeightModeEditor);
