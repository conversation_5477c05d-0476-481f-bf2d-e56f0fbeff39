@import "../../base/variables";

:global {
	.policy-detail-wrap {
		.custom-condition {
			& {

			}
			.custom-condition-item {
				& {
					border-top: 1px dashed #cccccc;
					padding: 15px 0 15px 0;
				}
				&.group-condition {
					.group-item {
						margin-bottom: 10px;
						&:last-child {
							margin-bottom: 0;
						}
					}
				}
			}
			.template-condition {
				.template-condition-row {
					& {
						margin-bottom: 10px;
					}
					&:last-child {
						margin-bottom: 0;
					}
					.param-tip-icon {
						font-size: 18px;
						color: #999;
						cursor: pointer;
						line-height: 32px;
					}
					.ant-checkbox-group {
						line-height: 32px;
					}
					.basic-info-description {
						line-height: 32px;
						margin-bottom: 0;
					}
				}
			}
			.add-condition-handle {
				&.disabled a {
					color: #c8c8c8;
					cursor: not-allowed;
				}
				a {
					margin-right: 16px;
				}
				a:hover {
					text-decoration: underline;
				}
			}
		}
	}
	.rule-param-pop-tip {
		max-width: 250px;
	}
}
