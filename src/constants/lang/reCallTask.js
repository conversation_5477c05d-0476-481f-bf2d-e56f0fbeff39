import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		title: {
			"cn": "回溯任务列表",
			"en": "Backtracking task list"
		}
	};
	return params[field][lang];
};
const operatorModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		addRecall: {
			"cn": "添加回溯",
			"en": "Add backtracking"
		},
		reCallProcessView: {
			"cn": "查看回溯进度",
			"en": "View backtracking progress"
		},
		editReCall: {
			"cn": "修改回溯",
			"en": "Edit backtracking"
		},
		indexBackTrack: {
			"cn": "指标回溯",
			"en": "Index backtracking"
		},
		from: {
			"cn": "从",
			"en": "From"
		},
		startGoBack: {
			"cn": "开始往前回溯",
			"en": "Start going back"
		},
		execute: {
			"cn": "执行方式",
			"en": "Execution"
		},
		rightExecute: {
			"cn": "立即执行",
			"en": "Immediate execution"
		},
		timeExecute: {
			"cn": "定时执行",
			"en": "Timing execution"
		},
		taskTime: {
			"cn": "任务时间",
			"en": "Task time"
		},
		addReCallSuccess: {
			"cn": "添加回溯成功",
			"en": "Add backtracking succeeded"
		},
		addReCallErr: {
			"cn": "添加回溯失败",
			"en": "Add backtracking failed"
		},
		editReCallSuccess: {
			"cn": "编辑回溯成功",
			"en": "Edit backtracking succeeded"
		},
		editReCallErr: {
			"cn": "编辑回溯失败",
			"en": "Edit backtracking failed"
		},
		timeTip1: {
			"cn": "回溯时间需小于等于定时执行时间",
			"en": "The backtracking time should be less than or equal to the scheduled execution time"
		},
		timeTip2: {
			"cn": "回溯时间需小于等于当前时间",
			"en": "Backtracking time should be less than or equal to the current time"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		zbName: {
			"cn": "请输入指标名称",
			"en": "Please enter indicator name"
		},
		backtrackStatus: {
			"cn": "请选择回溯状态",
			"en": "Please select backtracking status"
		},
		clear: {
			"cn": "清空",
			"en": "Clear"
		},
		search: {
			"cn": "搜索",
			"en": "Search"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		zbName: {
			"cn": "指标名称",
			"en": "Index name"
		},
		status: {
			"cn": "回溯状态",
			"en": "Backtracking state"
		},
		progress: {
			"cn": "回溯进度",
			"en": "Retrospective progress"
		},
		completed: {
			"cn": "已完成：",
			"en": "Completed:"
		},
		totalCount: {
			"cn": "总数",
			"en": "Total"
		},
		taskStartTime: {
			"cn": "执行开始时间",
			"en": "Task Start Time"
		},
		taskEndTime: {
			"cn": "执行结束时间",
			"en": "Task End Time"
		},
		taskCreateTime: {
			"cn": "任务创建时间",
			"en": "Task creation time"
		},
		effectStatus: {
			"cn": "生效状态",
			"en": "Effective state"
		},
		operation: {
			"cn": "操作",
			"en": "Operation"
		},
		terminateTask: {
			"cn": "终止",
			"en": "Terminate"
		},
		takeEffect: {
			"cn": "生效",
			"en": "Effect"
		},
		discard: {
			"cn": "废弃",
			"en": "Discard"
		},
		search: {
			"cn": "查询",
			"en": "Search"
		},
		terminateTaskTip: {
			"cn": "终止回溯任务提醒",
			"en": "Terminate backtracking task reminder"
		},
		confirmTerminateTaskTip: {
			"cn": "确认终止回溯任务",
			"en": "Confirm termination of backtracking task"
		},
		terminateTaskSuc: {
			"cn": "终止回测任务成功",
			"en": "Successfully terminated the backtesting task"
		},
		backResultTip: {
			"cn": "回溯结果处理",
			"en": "Backtracking result processing"
		},
		effectTitle: {
			"cn": "确认生效",
			"en": "Confirm effective"
		},
		discardTitle: {
			"cn": "确认废弃",
			"en": "Confirm discard"
		},
		confirm: {
			"cn": "确认",
			"en": "Confrim"
		},
		index: {
			"cn": "指标：",
			"en": "Index:"
		},
		backResultSuc: {
			"cn": "回溯结果处理成功",
			"en": "Backtracking result processed successfully"
		},
		effectReson: {
			"cn": "请输入生效备注",
			"en": "Please enter effective notes"
		},
		zbIndex: {
			"cn": "指标：",
			"en": "Index:"
		},
		effectWarn: {
			"cn": "回溯任务生效，系统自动提交指标版本，请确认！",
			"en": "Task is effective. System submit automatically. "
		}
	};
	return params[field][lang];
};

const terminateTaskTipText = (taskName) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;

	let cn = "确定要终止回测任务" + taskName + "吗？";
	let en = "Are you sure you want to terminate the replay task " + taskName + "?";
	return personalMode && personalMode.lang === "cn" ? cn : en;
};

const reCallTaskModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		queryResult: {
			"cn": "查询执行结果",
			"en": "Query execution results"
		},
		mainAttribute: {
			"cn": "主属性",
			"en": "Main attribute"
		},
		subAttribute: {
			"cn": "从属性",
			"en": "Subordinate attribute"
		},
		fieldRanking: {
			"cn": "字段排行",
			"en": "Field Rank"
		},
		backVal: {
			"cn": "回溯值 = ",
			"en": "Backtracking value = "
		},
		noResult: {
			"cn": "未查询到结果",
			"en": "No results found"
		},
		searchSuc: {
			"cn": "查询成功",
			"en": "Search successful"
		},
		searchFail: {
			"cn": "查询失败",
			"en": "Search failed"
		},
		confirm: {
			"cn": "确定",
			"en": "Confirm"
		}
	};
	return params[field][lang];
};
export default {
	common,
	operatorModal,
	searchParams,
	table,
	terminateTaskTipText,
	reCallTaskModal
};
