@import "../../../styles/base/variables";

:global {
    .add-to-custom-list {
        .add-handle {
            & {
                cursor: pointer;
                margin-top: 20px;
                width: 100%;
                height: 48px;
                line-height: 48px;
                text-align: center;
                border-radius: 5px;
                transition: all 0.3s;
            }

            a.single {
                margin-right: 10px;
            }

            a:hover {
                text-decoration: underline;
            }
        }

        .trans-arrow {
            width: 100%;
            line-height: 32px;
            font-size: 18px;
            color: #b4b4b4;
        }

        .single-section,
        .multiple-section {
            border-bottom: 1px dashed #e6e6e6;
            margin-bottom: 10px;
            padding-bottom: 10px;
        }

        .custom-list-row {
            margin-bottom: 10px;

            &:last-child {
                margin-bottom: 0;
            }

            .custom-list-title {
                line-height: 32px;
                text-align: right;
            }
        }
    }

    .custom-form {
        margin-top: 20px;
    }
}
