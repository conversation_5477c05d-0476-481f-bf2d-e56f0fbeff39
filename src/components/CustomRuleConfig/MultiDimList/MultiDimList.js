import React, { PureComponent, Fragment } from "react";
import { connect } from "dva";
import moment from "moment";
import { isJSON } from "@/utils/isJSON";
import { CommonConstants, PolicyConstants } from "@/constants";
import { commonLang } from "@/constants/lang";
import { Button, Row, Col, Select, Icon, Tooltip, Input, Popover, InputNumber, Dropdown, Tag } from "antd";
import { type } from "os";
import { judgeExistVal } from "@/utils/utils";

const { Option } = Select;

class MultiDimList extends PureComponent {
    state = {

    };

    constructor(props) {
    	super(props);
    }

    changeConditions = (index, field, value) => {
    	const { params, globalStore: { allMap }, onChange } = this.props;
    	let paramsObj = {};
    	params && params.map(param => {
    		if (param.name) {
    			paramsObj[param.name] = param.value;
    		}
    	});
    	let conditions = paramsObj.conditions || [];
    	conditions[index][field] = value;
    	onChange("conditions", conditions, "array");
    };

    deleteCondition = (index) => {
    	const { params, onChange } = this.props;
    	let paramsObj = {};
    	params && params.map(param => {
    		if (param.name) {
    			paramsObj[param.name] = param.value;
    		}
    	});
    	let conditions = paramsObj.conditions || [];

    	conditions.splice(index, 1);
    	onChange("conditions", conditions, "array");
    }

    addCondition = (index) => {
    	const { params, onChange } = this.props;
    	let paramsObj = {};
    	params && params.map(param => {
    		if (param.name) {
    			paramsObj[param.name] = param.value;
    		}
    	});
    	let conditions = paramsObj.conditions || [];

    	let conditionObj = {
    		leftValue: null,
    		right: null,
    		weight: null
    	};

    	conditions.splice(index + 1, 0, conditionObj);
    	onChange("conditions", conditions, "array");
    }

    render() {
    	let { params, globalStore: { allMap }, onChange, disabled } = this.props;
    	let { ruleAndIndexFieldList = [], multiDimCustomListBusiTypeSelect = [], multiDimCustomListTypeSelect = [], multiDimCustomListSelect = [] } = allMap;

    	let paramsObj = {};
    	params && params.map(param => {
    		if (param.name) {
    			paramsObj[param.name] = param.value;
    		}
    	});
    	console.log(params);
    	// console.log(paramsObj);
    	// console.log(multiDimCustomListSelect);

    	// CustomList，根据所选的黑名单白名单等类型，给出对应的列表
    	let customList = [];
    	let defineType = paramsObj.defineType || null;
    	let definitionList = paramsObj.definitionList || null;
    	let conditions = paramsObj.conditions || [];

    	if (defineType) {
    		customList = multiDimCustomListSelect.filter(listItem => {
    			let type = listItem.type ? listItem.type.toString() : null;
    			if (type) {
    				return type === defineType;
    			}
    		});
    	}
    	console.log(paramsObj);

    	const fieldList = ruleAndIndexFieldList.filter(field => field.type === "STRING");
    	console.log(customList);

    	// 根据   definitionList  和  customList   获取用户选择的名单集对象
    	let customObj;
    	if (customList && definitionList) {
    		customObj = customList.find(customItem => customItem.uuid === definitionList);
    	}

    	console.log(customObj);

    	let defineTypeVal = judgeExistVal(paramsObj.defineType || undefined, multiDimCustomListTypeSelect);
    	let definitionListVal = judgeExistVal(paramsObj.definitionList || undefined, customList);
    	return (
    		<Fragment>
    			<Row
    				gutter={CommonConstants.gutterSpan}
    				className="template-condition-row"
    			>
    				<Col
    					span={4}
    					className="basic-info-title"
    				>
                        复核名单列表：
    				</Col>
    				<Col span={4}>
    					<Select
    						// paramsObj.defineType || undefined
    						value={defineTypeVal}
    						placeholder="请选择"
    						onChange={(value) => {
    							onChange("defineType", value, "string");
    							onChange("definitionList", "", "string");
    						}}
    						disabled={disabled}
    					>
    						{
    							multiDimCustomListTypeSelect &&
                                multiDimCustomListTypeSelect.map((customItem, index) => {
                                	return (
                                		<Option
                                			value="black"
                                			key={index}
                                			value={customItem.name}
                                		>
                                			{customItem.dName}
                                		</Option>
                                	);
                                })
    						}
    					</Select>
    				</Col>
    				<Col span={4}>
    					<Select
    						// paramsObj.definitionList || undefined
    						value={definitionListVal}
    						placeholder="请选择"
    						onChange={(value) => {
    							onChange("definitionList", value, "string");
    						}}
    						disabled={disabled}
    					>
    						{
    							customList &&
                                customList.map((listItem, index) => {
                                	return (
                                		<Option
                                			value={listItem.uuid}
                                			key={index}
                                		>
                                			{listItem.name}
                                		</Option>
                                	);
                                })
    						}

    					</Select>
    				</Col>
    				<Col span={1}>
    					<Popover
    						popupClassName="rule-param-pop-tip"
    						placement="right"
    						title="选择符合名单列表"
    						content={
    							<div>选择符合名单列表</div>
    						}
    					>
    						<Icon
    							type="question-circle-o"
    							className="param-tip-icon"
    						/>
    					</Popover>
    				</Col>
    			</Row>
    			<Row
    				gutter={CommonConstants.gutterSpan}
    				style={{
    					marginBottom: "10px"
    				}}
    			>
    				<Col span={4} className="basic-info-title">
                        匹配字段：
                    	</Col>
    				<Col
    					span={20}
    					className="add-new-filter"
    				>
    					<span
    						onClick={() => {
    							this.addCondition(0);
    						}}
    					>
    						<Icon type="plus-square-o" />
                            新增匹配
                    	</span>
    				</Col>
    			</Row>
    			{
    				conditions &&
                    conditions.map((conditionItem, conditionIndex) => {
                    	// customObj
                    	let multiDimCustomListBusiTypeObj;
                    	let busiTypeObj;

                    	if (customObj && customObj.columnBOList && conditionItem.right) {
                    		busiTypeObj = customObj.columnBOList.find(boItem => boItem.uuid === conditionItem.right);
                    	}

                    	if (busiTypeObj) {
                    		multiDimCustomListBusiTypeObj = multiDimCustomListBusiTypeSelect.find(typeItem => typeItem.name === busiTypeObj.busiType.toString());
                    	}

                    	let leftValue = judgeExistVal(conditionItem.leftValue || undefined, fieldList);
                    	let rightValue = judgeExistVal(conditionItem.right || undefined, customObj && customObj.columnBOList);

                    	return (
                    		<Row
                    			gutter={CommonConstants.gutterSpan}
                    			className="template-condition-row"
                    			key={conditionIndex}
                    		>
                    			<Col span={4} className="basic-info-title"></Col>
                    			<Col
                    				span={4}
                    				className="add-new-filter"
                    			>
                    				<Select
                    					placeholder="请选择"
                    					// conditionItem.leftValue || undefined
                    					value={leftValue}
                    					// onChange={this.changeFieldValue.bind(this, indexArr, "select", "rightVar")}
                    					showSearch
                    					optionFilterProp="children"
                    					dropdownMatchSelectWidth={false}
                    					onChange={(value) => {
                    						this.changeConditions(conditionIndex, "leftValue", value);
                    					}}
                    					disabled={disabled}
                    				>
                    					{
                    						fieldList &&
                                            fieldList.map((item, index) => {
                                            	return (
                                            		<Option
                                            			key={index}
                                            			value={item.name}
                                            		>
                                                        [{commonLang.sourceName(item.sourceName)}]&nbsp;{item.dName}
                                            		</Option>
                                            	);
                                            })
                    					}
                    				</Select>
                    			</Col>
                    			<Col
                    				span={4}
                    			>
                    				<Select
                    					// conditionItem.right || undefined
                    					value={rightValue}
                    					placeholder="请选择"
                    					showSearch
                    					optionFilterProp="children"
                    					dropdownMatchSelectWidth={false}
                    					onChange={(value) => {
                    						this.changeConditions(conditionIndex, "right", value);
                    					}}
                    					disabled={disabled}
                    				>
                    					{
                    						customObj &&
                                            customObj.columnBOList &&
                                            customObj.columnBOList.map((item, index) => {
                                            	return (
                                            		<Option
                                            			value={item.uuid}
                                            			key={index}
                                            		>
                                            			{item.name}
                                            		</Option>
                                            	);
                                            })
                    					}

                    				</Select>
                    			</Col>
                    			<Col
                    				span={4}
                    				className="add-new-filter"
                    			>
                    				<Input
                    					addonBefore="权重"
                    					addonAfter="%"
                    					value={conditionItem.weight}
                    					onChange={(e) => {
                    						let value = e.target.value ? parseFloat(e.target.value) : 0;
                    						if (value < 0) {
                    							value = 0;
                    						} else if (value > 100) {
                    							value = 100;
                    						}
                    						this.changeConditions(conditionIndex, "weight", value);
                    					}}
                    					disabled={disabled}
                    				/>
                    			</Col>
                    			{
                    				multiDimCustomListBusiTypeObj &&
                                    <Col
                                    	span={4}
                                    	className="add-new-filter"
                                    >
                                    	<Input
                                    		value={multiDimCustomListBusiTypeObj.dName}
                                    		disabled
                                    	/>
                                    </Col>
                    			}
                    			{
                    				!disabled &&
                                    <Col span={3} className="basic-info-oper">
                                    	<Tooltip
                                    		title="添加字段集"
                                    		placement="left"
                                    	>
                                    		<Icon
                                    			className="add"
                                    			type="plus-circle"
                                    			onClick={() => {
                                    				this.addCondition(conditionIndex);
                                    			}}
                                    		/>
                                    	</Tooltip>
                                    	<Tooltip
                                    		title="删除当前行"
                                    		placement="right"
                                    	>
                                    		<Icon
                                    			className="delete"
                                    			type="delete"
                                    			onClick={() => {
                                    				this.deleteCondition(conditionIndex);
                                    			}}
                                    		/>
                                    	</Tooltip>
                                    </Col>
                    			}

                    		</Row>
                    	);
                    })
    			}
    			<Row
    				gutter={CommonConstants.gutterSpan}
    				className="template-condition-row"
    			>
    				<Col span={4} className="basic-info-title">匹配度：</Col>
    				<Col span={8}>
    					<InputNumber
    						min={0}
    						max={1}
    						step={0.1}
    						defaultValue={0.1}
    						style={{ width: "100%" }}
    						placeholder="请输入匹配度，范围【0～1】"
    						value={paramsObj.score || undefined}
    						onChange={(value) => {
    							onChange("score", value, "int");
    						}}
    						// disabled={disabled}
    					/>
    				</Col>
    				<Col span={1}>
    					<Popover
    						popupClassName="rule-param-pop-tip"
    						placement="right"
    						title="匹配度"
    						content={
    							<div>匹配度范围【0～1】支持保留两位小数，与匹配字段相比较，计算结果大于设置的匹配度，则命中此规则</div>
    						}
    					>
    						<Icon
    							type="question-circle-o"
    							className="param-tip-icon"
    						/>
    					</Popover>
    				</Col>
    			</Row>

    		</Fragment>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global
}))(MultiDimList);
