import React from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Input, Select, Form, Row, Col, Radio, Button } from "antd";
import { CommonConstants, PolicyConstants } from "@/constants";
import EffectScopeTable from "@/components/PublicPolicy/EffectScope/EffectScopeTable";
import WeightModeEditor from "../../WeightModeEditor";
import { publicPolicyListLang, policyDetailLang } from "@/constants/lang";

const Option = Select.Option;
const { TextArea } = Input;
const InputGroup = Input.Group;
const formItemLayout = {
	labelCol: {
		xs: { span: 24 },
		sm: { span: 5 }
	},
	wrapperCol: {
		xs: { span: 24 },
		sm: { span: 18 }
	}
};
const { countOperatorIntMap } = PolicyConstants;

class BasicPublic extends React.PureComponent {
	state = {
		global_app: []
	}
	constructor(props) {
		super(props);
	}
	componentWillMount() {
		let { globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		const global_app = publicPolicyScene && publicPolicyScene.length > 0 && publicPolicyScene.find((v) => v.name === "GLOBAL_APP") || {}; // 全局应用
		this.setState({
			global_app
		});
	}

	render() {
		let { policyDetailStore, globalStore } = this.props;
		let { allMap, policyModel, personalMode } = globalStore;
		let { policyDetail } = policyDetailStore;
		const { publicPolicyRiskSelect = [], dealTypeList = [] } = allMap || {};
		let { lang } = personalMode;
		const { effectScope, name, riskType, mode, dealTypeMappings, terminateOperator, terminate, terminateThreshold, description } = policyDetail;
		let { scene } = { ...policyDetail };
		const { global_app } = this.state;
		if (typeof scene === "string") {
			scene = JSON.parse(scene);
		}
		return (
			<Form {...formItemLayout} className="modify-public-policy basic-form">
				<Row style={{ "height": "auto", "overflow": "inherit" }}>
					<Col span="24">
						{/* 生效范围 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("effectScope")}>
							<Select
								value={effectScope}
								disabled
								placeholder={publicPolicyListLang.publicPolicyModal("enterEffectScope")} // 请选择生效范围
							>
								{
									global_app &&
									<Option value={global_app.name}>{global_app.dName}</Option>
								}
								{/* 自定义 */}
								<Option value="set">{publicPolicyListLang.publicPolicyModal("custom")}</Option>
							</Select>
						</Form.Item>

						{
							effectScope === "set" &&
							// 自定义生效范围
							<Form.Item label={publicPolicyListLang.publicPolicyModal("customEffectScope")}>
								<EffectScopeTable scene={scene}/>
							</Form.Item>
						}

					</Col>
					<Col span="24">
						{/* 策略名称 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("policyName")}>
							<Input
								value={name || undefined}
								disabled
								placeholder={publicPolicyListLang.publicPolicyModal("enterPolicyName")} // "请输入策略名称"
							/>
						</Form.Item>
					</Col>
					<Col span="24">
						{/* 风险类型 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("riskType")}>
							<Select
								value={riskType || undefined}
								disabled
								name="riskType"
								placeholder={publicPolicyListLang.publicPolicyModal("enterRiskType")} // 请选择风险类型
							>
								{
									publicPolicyRiskSelect &&
									publicPolicyRiskSelect.map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Form.Item>
					</Col>
					<Col span="24">
						{/* 策略模式 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("policyMode")}>
							<Select
								disabled
								name="mode"
								value={mode || undefined}
								placeholder={publicPolicyListLang.publicPolicyModal("selectPolicyMode")} // "请选择策略模式"
							>
								{
									policyModel && policyModel.map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>

						</Form.Item>
					</Col>
					{
						mode === "Weighted" &&
						<Col span="24">
							{/* lang:风险阈值 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("riskThreshold")}>
								<WeightModeEditor
									disabled
									dealTypeMappings={dealTypeMappings}
								/>
							</Form.Item>
						</Col>
					}
					<Col span="24">
						{/* 是否中断 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("interrupted")}>
							<Radio.Group
								disabled
								value={String(terminate)}
							>
								{/* 不中断 */}
								<Radio value="0">{publicPolicyListLang.publicPolicyModal("noInterruption")}</Radio>
								{/* 中断 */}
								<Radio value="1">{publicPolicyListLang.publicPolicyModal("interruption")}</Radio>
							</Radio.Group>
						</Form.Item>
					</Col>
					{
						String(terminate) === "1" &&
						<Col span="24">
							{/* 中断条件 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("interruptCondition")}>
								<InputGroup compact={!!terminateOperator}>
									{/* 风险决策结果 */}
									<Input
										value={publicPolicyListLang.publicPolicyModal("riskResult")}
										style={{ "width": "25%" }}
										disabled
									/>
									<Select
										disabled
										style={{ "width": "35%" }}
										placeholder={publicPolicyListLang.publicPolicyModal("enterOperator")} // 请选择中断操作符
										value={terminateOperator || undefined}
									>
										{
											countOperatorIntMap.map((v) => {
												return <Option value={v.name}>{lang === "en" ? v.enDName : v.dName}</Option>;
											})
										}
									</Select>
									<Select
										disabled
										style={{ "width": "40%" }}
										placeholder={publicPolicyListLang.publicPolicyModal("enterBreakCon")} // 请选择中断条件
										value={terminateThreshold || undefined}
									>
										{
											dealTypeList &&
											dealTypeList.length > 0 &&
											dealTypeList.map((v) => {
												return <Option value={v.name}>{lang === "en" ? v.enDName : v.dName}</Option>;
											})
										}
									</Select>
								</InputGroup>
							</Form.Item>
						</Col>
					}
					<Col span="24">
						{/* 描述 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("description")}>
							<TextArea
								disabled
								placeholder={publicPolicyListLang.publicPolicyModal("enterPolicyDescription")} // 请输入描述
								rows="3"
								value={description || undefined}
							/>
						</Form.Item>
					</Col>

				</Row>
				<Row gutter={CommonConstants.gutterSpan} className="mt10" style={{ clear: "both" }}>
					<Col span={5}></Col>
					<Col span={18} className="basic-info-btns">
						<Button onClick={() => {
							let { dispatch } = this.props;

							if (process.env.SYS_ENV === "development") {
								dispatch(routerRedux.push("/policy/publicPolicyList?currentTab=1"));
							} else {
								dispatch(routerRedux.push("/index/policy/publicPolicyList?currentTab=1"));
							}
						}}>
							{/* 返回 */}
							{policyDetailLang.basicSetup("goBack")}
						</Button>
					</Col>
				</Row>
			</Form>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(Form.create({ name: "edit-public-policy" })(BasicPublic));
