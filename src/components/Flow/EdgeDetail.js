import { PureComponent } from "react";
import { connect } from "dva";
import {cloneDeep} from "lodash";
import { Card, Input, Select, Row, Col, Icon, Button, Radio } from "antd";
import "./style/model-setting-modal.less";
import ConditionSetting from "./Modal/ConditionSetting";
import { withPropsAPI } from "gg-editor";
import { workflowLang } from "@/constants/lang";
import { FlowConstants } from "@/constants";

const { Option } = Select;

class EdgeDetail extends PureComponent {

	state = {
		stateConditionsGroup: {
			logicOperator: "&&",
			priority: "1",
			defaultNode: false,
			children: []
		}
	};

	constructor(props) {
		super(props);
		this.changeNodeBase = this.changeNodeBase.bind(this);
		this.changeConditionItem = this.changeConditionItem.bind(this);
		this.openConditionSetting = this.openConditionSetting.bind(this);
	}

	// 修改线条样式
	changeEdgeStyle = (label, conditionsGroup, item) => {
		let [hasSet] = [false];
		if (label && conditionsGroup && conditionsGroup.children && conditionsGroup.children.length > 0) {
			hasSet = true;
		}
		if (hasSet) {
			item.shapeObj.getSelectedStyle = FlowConstants.defaultEdgeSelectedStyle;
			return {
				style: {}
			};
		} else {
			item.shapeObj.getSelectedStyle = FlowConstants.warnEdgeStyle;
			return FlowConstants.warnColorStyle;
		}
	}
	componentDidMount() {
		let { propsAPI } = this.props;
		let { getSelected, update, executeCommand } = propsAPI;

		let item = getSelected()[0];

		if (!item) {
			return null;
		}

		let { conditionsGroup } = item.getModel();
		let emptyConditionGroup = {
			logicOperator: "&&",
			priority: "1",
			defaultNode: false,
			children: []
		};
		let params;
		if (conditionsGroup) {
			this.setState({
				stateConditionsGroup: conditionsGroup
			});
		} else {
			params = {
				conditionsGroup: emptyConditionGroup
			};
		}
		executeCommand(() => {
			update(item, {
				...params
			});
		});
	}

	changeNodeBase(field, type, e) {
		let { propsAPI } = this.props;
		let { getSelected, update, executeCommand } = propsAPI;

		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		let item = getSelected()[0];
		let { conditionsGroup = {} } = item.getModel();
		if (!item) {
			return;
		}
		console.log(item);
		let params = {};
		params[field] = value;
		executeCommand(() => {
			update(item, {
				...params,
				...this.changeEdgeStyle(field === "label" && value, conditionsGroup, item)
			});
		});
	}

	changeConditionItem(field, value) {
		let { propsAPI } = this.props;
		let { getSelected, update, executeCommand } = propsAPI;
		let { stateConditionsGroup } = this.state;
		let item = getSelected()[0];
		let { conditionsGroup, label } = item.getModel();
		if (!item) {
			return;
		}
		let params = {};

		stateConditionsGroup[field] = value;
		conditionsGroup[field] = value;
		params["conditionsGroup"] = conditionsGroup;

		executeCommand(() => {
			update(item, {
				...params,
				...this.changeEdgeStyle(label, conditionsGroup, item)
			});
		});

		this.setState({
			stateConditionsGroup: JSON.parse(JSON.stringify(stateConditionsGroup))
		});
	}

	openConditionSetting() {
		let { propsAPI, dispatch } = this.props;
		let { getSelected } = propsAPI;

		let item = getSelected()[0];

		if (!item) {
			return null;
		}

		const { conditionsGroup } = item.getModel();

		// 点击配置条件详情的时候将数据放到workflow中
		const conditionsGroupFinal = cloneDeep(conditionsGroup);
		dispatch({
			type: "workflow/setAttrValue",
			payload: {
				conditionsGroupFinal,
				conditionsGroup: conditionsGroupFinal
			}
		});

		dispatch({
			type: "workflow/setDialogShow",
			payload: {
				conditionSetting: true
			}
		});

	}

	render() {
		let { stateConditionsGroup } = this.state;
		let { propsAPI, workflowStore, disabled } = this.props;
		let { getSelected } = propsAPI;
		let item = getSelected()[0];
		let { dialogShow } = workflowStore;

		if (!item) {
			return null;
		}
		let { source } = item;
		let { label } = item.getModel();
		console.log(source);
		return (
			<Card
				type="inner"
				title={workflowLang.edgeDetail("title")} // lang:"条件属性"
				bordered="false"
			>
				{
					source &&
					source["model"]["shape"] === "flow-exclusivity" &&
					source["model"]["nodeType"] === "CONDITION_START" &&
					<div className="param-list">
						<Row className="param-item">
							<Col span={24} className="param-title">
								{/* lang:条件名称*/}
								{workflowLang.edgeDetail("conditionalName")}
							</Col>
							<Col span={24} className="param-content">
								<Input
									placeholder={workflowLang.edgeDetail("conditionalNamePlaceholder")} // lang:"请输入条件显示名称"
									defaultValue={label}
									onChange={this.changeNodeBase.bind(this, "label", "input")}
									disabled={disabled}
								/>
							</Col>
						</Row>
						<Row className="param-item">
							<Col span={24} className="param-title">
								{/* lang:优先级*/}
								{workflowLang.edgeDetail("priority")}
							</Col>
							<Col span={24} className="param-content">
								<Select
									placeholder={workflowLang.edgeDetail("priorityPlaceholder")} // lang:"请选择优先级"
									value={stateConditionsGroup.priority || undefined}
									onChange={this.changeConditionItem.bind(this, "priority")}
									disabled={disabled}
								>
									<Option value="1">1</Option>
									<Option value="2">2</Option>
									<Option value="3">3</Option>
									<Option value="4">4</Option>
									<Option value="5">5</Option>
								</Select>
							</Col>
						</Row>
						<Row className="param-item">
							<Col span={24} className="param-title">
								{/* lang:是否默认条件*/}
								{workflowLang.edgeDetail("whetherDefaultCondition")}
							</Col>
							<Col span={24} className="param-content">
								<Radio.Group
									value={stateConditionsGroup.defaultNode ? "yes" : "no"}
									buttonStyle="solid"
									className="radio-group-double"
									onChange={(e) => {
										let value = e.target.value === "yes";
										this.changeConditionItem("defaultNode", value);
									}}
									disabled={disabled}
								>
									<Radio.Button value="yes">
										{/* lang:是*/}
										{workflowLang.edgeDetail("yes")}
									</Radio.Button>
									<Radio.Button value="no">
										{/* lang:否*/}
										{workflowLang.edgeDetail("no")}
									</Radio.Button>
								</Radio.Group>
							</Col>
						</Row>
						<Button
							style={{ width: "100%" }}
							onClick={this.openConditionSetting.bind(this)}
							className="mb20"
						>
							{/* lang: 查看条件详情配置 : 点击配置条件详情 */}
							{disabled ? workflowLang.edgeDetail("viewConditionConfig") : workflowLang.edgeDetail("clickConditionConfigDetail")}
						</Button>
					</div>
				}
				{
					source &&
					(
						source["model"]["shape"] !== "flow-exclusivity" ||
						source["model"]["nodeType"] === "CONDITION_END"
					) &&
					<div className="condition-list">
						<div className="none-data">
							<Icon type="smile" />
							<p>
								{/* lang:无需配置*/}
								{workflowLang.edgeDetail("noNeedConfig")}
							</p>
						</div>
					</div>
				}
				{
					dialogShow.conditionSetting &&
					<ConditionSetting
						onChange={(conditionsGroup) => {
							let logicOperator = conditionsGroup.logicOperator;
							let children = conditionsGroup.children;

							stateConditionsGroup["logicOperator"] = logicOperator;
							stateConditionsGroup["children"] = children;

							this.changeConditionItem("logicOperator", logicOperator);
							this.changeConditionItem("children", children);
						}}
						disabled={disabled}
					/>
				}
			</Card>
		);
	}
}

export default withPropsAPI(connect(state => ({
	globalStore: state.global,
	workflowStore: state.workflow
}))(EdgeDetail));
