import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		addWorkflow: {
			"cn": "新增执行流程",
			"en": "Add Workflow"
		},
		modifyWorkflow: {
			"cn": "修改执行流程",
			"en": "Modify Workflow"
		},
		viewWorkflow: {
			"cn": "查看执行流程",
			"en": "View Workflow"
		},
		deleteRuleStream: {
			"cn": "删除规则流",
			"en": "Delete Rule Stream"
		},
		import: {
			"cn": "导入",
			"en": "Import"
		},
		export: {
			"cn": "导出",
			"en": "Export"
		},
		save: {
			"cn": "保存",
			"en": "Save"
		},
		submit: {
			"cn": "提交",
			"en": "Submit"
		},
		ruleStream: {
			"cn": "规则流",
			"en": "Rule flow"
		},
		deleteWorkflowTitle: {
			"cn": "删除规则流提醒",
			"en": "Delete Rule Flow Reminders"
		},
		deleteWorkflowContent: {
			"cn": "确定要删除当前规则流吗？",
			"en": "Are you sure you want to delete the current rule flow?"
		},
		overwriteBack: {
			"cn": "覆盖回编辑区",
			"en": "Overwrite back to editing area"
		},
		cancel: {
			"cn": "取消",
			"en": "Cancel"
		},
		ok: {
			"cn": "确定",
			"en": "OK"
		},
		offLine: {
			"cn": "申请下线",
			"en": "Application for offline"
		},
		offLineWorkflowTitle: {
			"cn": "规则流下线提醒",
			"en": "Rule flow offline reminder"
		},
		offLineWorkflowContent: {
			"cn": "请确认将规则流申请下线处理",
			"en": "Please confirm to apply for offline processing of rule flow"
		},
		offLineWorkflowSuccess: {
			"cn": "规则流下线成功",
			"en": "Rule flow offline succeeded"
		},
		offLineWorkflowErr: {
			"cn": "规则流下线失败",
			"en": "Rule flow offline error"
		}
	};
	return params[field][lang];
};

const menuAction = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"copy": {
			"cn": "复制",
			"en": "Copy"
		},
		"delete": {
			"cn": "删除",
			"en": "Delete"
		},
		"upLayer": {
			"cn": "向上一层",
			"en": "To The Up Layer"
		},
		"downLayer": {
			"cn": "向下一层",
			"en": "To The Down Layer"
		},
		"allSelect": {
			"cn": "全选",
			"en": "Select All"
		},
		"paste": {
			"cn": "粘贴",
			"en": "Paste"
		},
		"revoke": {
			"cn": "撤销",
			"en": "Revoke"
		},
		"redo": {
			"cn": "重做",
			"en": "Redo"
		},
		"realSize": {
			"cn": "实际尺寸",
			"en": "Real Size"
		},
		"adaptiveSize": {
			"cn": "自适应尺寸",
			"en": "Adaptive Size"
		}
	};
	return params[field][lang];
};

const modal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"policyTitle": {
			"cn": "策略版本审核",
			"en": "Policy Version Approval"
		}
	};
	return params[field][lang];
};
const message = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"policyApprovalSuccessfully": {
			"cn": "策略审批成功",
			"en": "Policy approval successfully"
		},
		"addRuleStreamSuccess": {
			"cn": "添加规则流成功",
			"en": "Added Rule Flow Successfully"
		},
		"modifyRuleStreamSuccess": {
			"cn": "修改规则流成功",
			"en": "Successful modification of rule flow"
		},
		"nodeConfigEmpty": {
			"cn": "规则流节点配置不能为空",
			"en": "Rule flow node configuration cannot be empty"
		},
		"lackLabelName": {
			"cn": "缺少标签名",
			"en": "Lack of label name"
		},
		"labelMessage1": {
			"cn": "不可以设置输入流",
			"en": "Input stream cannot be set"
		},
		"labelMessage2": {
			"cn": "没有设置输出流",
			"en": "No output stream set"
		},
		"labelMessage3": {
			"cn": "输出流只能有一个",
			"en": "There can only be one output stream"
		},
		"labelMessage4": {
			"cn": "不可以设置输出流",
			"en": "Output stream cannot be set"
		},
		"labelMessage5": {
			"cn": "没有设置输入流",
			"en": "No input stream is set"
		},
		"labelMessage6": {
			"cn": "输入流只能有一个",
			"en": "There can only be one input stream"
		},
		"policy": {
			"cn": "策略",
			"en": "Policy"
		},
		"labelMessage7": {
			"cn": "请选择策略",
			"en": "Please choose the strategy"
		},
		"labelMessage7-0": {
			"cn": "请选择公共策略",
			"en": "Please choose the public strategy"
		},
		"labelMessage8": {
			"cn": "没有添加配置",
			"en": "No configuration added"
		},
		"labelMessage9": {
			"cn": "没有配置输入字段",
			"en": "No input fields are configured"
		},
		"labelMessage10": {
			"cn": "没有配置输出字段",
			"en": "No output fields are configured"
		},
		"model": {
			"cn": "模型",
			"en": "Model"
		},
		"labelMessage11": {
			"cn": "没有配置三方接口",
			"en": "No tripartite interface is configured"
		},
		"labelMessage12": {
			"cn": "接口类型不能为空",
			"en": "Interface type cannot be empty"
		},
		"labelMessage13": {
			"cn": "规则流只能有一个Root节点",
			"en": "Rule flow can only have one Root node"
		},
		"labelMessage14": {
			"cn": "规则流配置不合法，原因如下：",
			"en": "Rule flow configuration is illegal for the following reasons:"
		},
		"labelMessage15": {
			"cn": "输出流必须为多个",
			"en": "The output stream must be multiple"
		},
		"labelMessage16": {
			"cn": "输入流必须为多个",
			"en": "The input stream must be multiple"
		},
		"exportSuccess": {
			"cn": "导出规则流成功",
			"en": "Export Rule Flow Successfully"
		},
		"changeRuleStreamSuccess": {
			"cn": "切换规则流成功",
			"en": "Successful handover rule flow"
		},
		"changeEditRuleStreamSuccess": {
			"cn": "规则流编辑区切换成功",
			"en": "Rule Flow Editorial Area Switching Successfully"
		}
	};
	return params[field][lang];
};

const nodeDetail = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"nodeName": {
			"cn": "节点名称",
			"en": "Node Name"
		},
		"nodeNamePlaceholder": {
			"cn": "请输入节点名称",
			"en": "Please enter the node name"
		},
		"exclusiveType": {
			"cn": "排他类型",
			"en": "Exclusive Type"
		},
		"exclusiveStart": {
			"cn": "排他开始",
			"en": "Exclusive Start"
		},
		"exclusiveEnd": {
			"cn": "排他结束",
			"en": "Exclusive End"
		},
		"parallelType": {
			"cn": "并行类型",
			"en": "Parallel Type"
		},
		"parallelStart": {
			"cn": "并行开始",
			"en": "Parallel Start"
		},
		"parallelEnd": {
			"cn": "并行结束",
			"en": "Parallel End"
		},
		"policy": {
			"cn": "策略",
			"en": "Policy"
		},
		"publicPolicy": {
			"cn": "公共策略",
			"en": "Public Policy"
		},
		"policyPlaceholder": {
			"cn": "选择策略",
			"en": "Select Policy"
		},
		"viewModelConfig": {
			"cn": "查看模型配置",
			"en": "View Model Config"
		},
		"clickConfigModel": {
			"cn": "点击配置模型",
			"en": "Click Config Model"
		},
		"viewServiceConfig": {
			"cn": "查看服务配置",
			"en": "View Service Config"
		},
		"clickConfigService": {
			"cn": "点击配置服务",
			"en": "Click Config Service"
		},
		"inputConfiguration": {
			"cn": "入参配置",
			"en": "Input Configuration"
		},
		"fieldName": {
			"cn": "字段名",
			"en": "Field Name"
		},
		"direction": {
			"cn": "方向",
			"en": "Direction"
		},
		"elementName": {
			"cn": "要素名",
			"en": "Element Name"
		},
		"outputConfiguration": {
			"cn": "出参配置",
			"en": "Output Configuration"
		},
		"tripartiteServices": {
			"cn": "三方服务",
			"en": "Tripartite Services"
		},
		"tripartiteServicesPlaceholder": {
			"cn": "请选择三方服务",
			"en": "Please choose a tripartite service"
		},
		"interfaceType": {
			"cn": "接口类型",
			"en": "Interface Type"
		},
		"interfaceTypePlaceholder": {
			"cn": "请选择接口类型",
			"en": "Please select the interface type"
		},
		"priorityBasis": {
			"cn": "优先依据",
			"en": "Priority Basis"
		},
		"confidence": {
			"cn": "置信度",
			"en": "Confidence"
		},
		"invocationCost": {
			"cn": "调用成本",
			"en": "Invocation Cost"
		}
	};
	return params[field][lang];
};

const edgeDetail = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"title": {
			"cn": "条件属性",
			"en": "Conditional Attributes"
		},
		"conditionalName": {
			"cn": "条件名称",
			"en": "Conditional Name"
		},
		"conditionalNamePlaceholder": {
			"cn": "请输入条件显示名称",
			"en": "Please enter a condition display name"
		},
		"priority": {
			"cn": "优先级",
			"en": "Priority"
		},
		"priorityPlaceholder": {
			"cn": "请选择优先级",
			"en": "Please select priority"
		},
		"whetherDefaultCondition": {
			"cn": "是否默认条件",
			"en": "Whether Default Condition"
		},
		"yes": {
			"cn": "是",
			"en": "Yse"
		},
		"no": {
			"cn": "否",
			"en": "No"
		},
		"viewConditionConfig": {
			"cn": "查看条件详情配置",
			"en": "View Conditions Details Configuration"
		},
		"clickConditionConfigDetail": {
			"cn": "点击配置条件详情",
			"en": "Click on Configuration Conditions Details"
		},
		"noNeedConfig": {
			"cn": "无需配置",
			"en": "No configuration required"
		}
	};
	return params[field][lang];
};

const flowDetailPanel = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"title": {
			"cn": "策略集信息",
			"en": "Policy Set Information"
		},
		"policySetName": {
			"cn": "策略集名称",
			"en": "Policy Set Name"
		},
		"noPolicyName": {
			"cn": "暂无策略名称",
			"en": "No policy name yet"
		},
		"application": {
			"cn": "所属应用",
			"en": "Subordinate Applications"
		},
		"eventType": {
			"cn": "事件类型",
			"en": "Event Type"
		},
		"eventId": {
			"cn": "事件标识",
			"en": "Event Identification"
		},
		"noEventId": {
			"cn": "暂无事件标识",
			"en": "No event identification yet"
		},
		"policyDescription": {
			"cn": "策略描述",
			"en": "Policy Description"
		},
		"noPolicyDescription": {
			"cn": "暂无策略描述",
			"en": "No strategy description yet"
		}
	};
	return params[field][lang];
};

const flowItemPanel = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"start": {
			"cn": "开始",
			"en": "Start"
		},
		"end": {
			"cn": "结束",
			"en": "End"
		},
		"exclusiveStart": {
			"cn": "排他开始",
			"en": "Exclusive Start"
		},
		"parallelStart": {
			"cn": "并行开始",
			"en": "Parallel Start"
		},
		"policy": {
			"cn": "策略",
			"en": "Policy"
		},
		"model": {
			"cn": "模型",
			"en": "Model"
		},
		"trilateral": {
			"cn": "三方",
			"en": "Trilateral"
		},
		"publicPolicy": {
			"cn": "公共策略",
			"en": "Public Policy"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"operationType": {
			"cn": "变更类型",
			"en": "Operation Type"
		}

	};
	return params[field][lang];
};

const flowMinimap = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"title": {
			"cn": "缩略图",
			"en": "Thumbnail"
		}
	};
	return params[field][lang];
};

const flowToolTip = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"alterMessage": {
			"cn": "执行流程处于待审批状态，无法修改。",
			"en": "The execution process is pending approval and cannot be modified."
		},
		"revoke": {
			"cn": "撤销",
			"en": "Revoke"
		},
		"redo": {
			"cn": "重做",
			"en": "Redo"
		},
		"delete": {
			"cn": "删除",
			"en": "Delete"
		},
		"enlarge": {
			"cn": "放大",
			"en": "Enlarge"
		},
		"narrow": {
			"cn": "缩小",
			"en": "Narrow"
		},
		"adaptToCanvas": {
			"cn": "适应画布",
			"en": "Adapt To Canvas"
		},
		"actualSize": {
			"cn": "实际尺寸",
			"en": "Actual Size"
		},
		"allSelect": {
			"cn": "全选",
			"en": "Select All"
		}
	};
	return params[field][lang];
};

const oneCondition = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"pleaseSelect": {
			"cn": "请选择",
			"en": "Please Select"
		},
		"fieldAndIndex": {
			"cn": "字段/指标",
			"en": "Fields/Indicators"
		},
		"policyResult": {
			"cn": "策略结果",
			"en": "Policy Result"
		},
		"policy": {
			"cn": "策略",
			"en": "Policy"
		},
		"publicPolicy": {
			"cn": "公共策略",
			"en": "Pub Policy"
		},
		"constant": {
			"cn": "常量",
			"en": "Constant"
		},
		"variable": {
			"cn": "变量",
			"en": "Variable"
		},
		"disposalMethods": {
			"cn": "处置方式",
			"en": "Disposal Methods"
		},
		"constantPlaceholder": {
			"cn": "请输入常量值",
			"en": "Please enter a constant value"
		}
	};
	return params[field][lang];
};

const conditionSettingModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"viewTitle": {
			"cn": "查看条件配置详情",
			"en": "View Conditional Configuration Details"
		},
		"settingTitle": {
			"cn": "设置判定条件",
			"en": "Setting the criteria"
		},
		"executionCondition": {
			"cn": "执行条件",
			"en": "Execution Condition"
		},
		"followAllCondition": {
			"cn": "满足以下所有条件",
			"en": "All the following conditions are satisfied"
		},
		"followAnyCondition": {
			"cn": "满足以下任意条件",
			"en": "Satisfy any of the following conditions"
		},
		"notFollowCondition": {
			"cn": "以下条件均不满足",
			"en": "The following conditions are not satisfied"
		},
		"addSingleCondition": {
			"cn": "添加单条条件",
			"en": "Add a single condition"
		},
		"addConditionGroups": {
			"cn": "添加条件组",
			"en": "Adding conditional groups"
		},
		"conditionsHaveEmptyMsg": {
			"cn": "判定条件存在空值，请填写完整！",
			"en": "There is a blank value in the judgement condition. Please fill in the blank value completely."
		},
		"integerMsg": {
			"cn": "请输入整数",
			"en": "please enter an integer"
		},
		"addConfigConditionMsg": {
			"cn": "请添加配置条件",
			"en": "Please add configuration conditions"
		},
		"noAddConfigConditionMsg": {
			"cn": "还没有添加判断条件",
			"en": "No judgment conditions have been added yet."
		},
		"incompleteConditionMsg": {
			"cn": "判断条件配置不完整，请检查！",
			"en": "Judgment condition configuration is incomplete, please check!"
		}
	};
	return params[field][lang];
};

const modelSettingModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"title": {
			"cn": "模型设置",
			"en": "Model Settings"
		},
		"selectModel": {
			"cn": "请选择模型",
			"en": "Please select the model"
		},
		"inputConfiguration": {
			"cn": "入参配置",
			"en": "Input Configuration"
		},
		"outputConfiguration": {
			"cn": "出参配置",
			"en": "Output Configuration"
		},
		"inputFieldEmptyMsg": {
			"cn": "模型入参选择字段存在空值，请补充完整。",
			"en": "There is a blank value in the model entry selection field. Please complete it."
		},
		"outputFieldEmptyMsg": {
			"cn": "模型出参选择字段存在空值，请补充完整。",
			"en": "There is a blank value in the model parameter selection field. Please complete it."
		},
		"serviceInput": {
			"cn": "服务入参",
			"en": "Service Input"
		},
		"serviceInvolvementPlaceholder": {
			"cn": "请选择入参字段",
			"en": "Please select the entry field"
		},
		"index": {
			"cn": "指标",
			"en": "Index"
		},
		"assignmentDirection": {
			"cn": "赋值方向",
			"en": "Assignment Direction"
		},
		"elementName": {
			"cn": "要素名",
			"en": "Element Name"
		},
		"elementId": {
			"cn": "要素标识",
			"en": "Element Identification"
		},
		"valueType": {
			"cn": "值类型",
			"en": "Value Type"
		},
		"serviceOutput": {
			"cn": "服务出参",
			"en": "Service Output"
		},
		"serviceOutputPlaceholder": {
			"cn": "请选择出参字段",
			"en": "Please select the reference field."
		}
	};
	return params[field][lang];
};

const serviceSettingModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"title": {
			"cn": "服务设置",
			"en": "Service Setting"
		},
		"selectService": {
			"cn": "请选择服务",
			"en": "Please select the service"
		},
		"inputConfiguration": {
			"cn": "入参配置",
			"en": "Input Configuration"
		},
		"outputConfiguration": {
			"cn": "出参配置",
			"en": "Output Configuration"
		},
		"inputFieldEmptyMsg": {
			"cn": "服务入参选择字段存在空值，请补充完整。",
			"en": "There is a blank value in the service entry selection field. Please complete it."
		},
		"outputFieldEmptyMsg": {
			"cn": "服务出参选择字段存在空值，请补充完整。",
			"en": "There is a blank value in the service parameter selection field. Please complete it."
		},
		"serviceInput": {
			"cn": "服务入参",
			"en": "Service Input"
		},
		"serviceInvolvementPlaceholder": {
			"cn": "请选择入参字段",
			"en": "Please select the entry field"
		},
		"index": {
			"cn": "指标",
			"en": "Index"
		},
		"assignmentDirection": {
			"cn": "赋值方向",
			"en": "Assignment Direction"
		},
		"elementName": {
			"cn": "要素名",
			"en": "Element Name"
		},
		"elementId": {
			"cn": "要素标识",
			"en": "Element Identification"
		},
		"valueType": {
			"cn": "值类型",
			"en": "Value Type"
		},
		"serviceOutput": {
			"cn": "服务出参",
			"en": "Service Output"
		},
		"serviceOutputPlaceholder": {
			"cn": "请选择出参字段",
			"en": "Please select the reference field."
		}
	};
	return params[field][lang];
};

const nodeName = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"start": {
			"cn": "开始节点",
			"en": "Start Node"
		},
		"end": {
			"cn": "结束节点",
			"en": "End Node"
		},
		"exclusive": {
			"cn": "排他节点",
			"en": "Exclusive Node"
		},
		"parallel": {
			"cn": "并行节点",
			"en": "Parallel Node"
		},
		"policy": {
			"cn": "策略节点",
			"en": "Policy Node"
		},
		"model": {
			"cn": "模型节点",
			"en": "Model Node"
		},
		"tripartite": {
			"cn": "三方节点",
			"en": "Tripartite Node"
		},
		"publicPolicy": {
			"cn": "公共策略节点",
			"en": "Public Policy Node"
		}
	};
	return params[field][lang];
};

const workflowCommitModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"title": {
			"cn": "规则流版本提交",
			"en": "Rule flow version submission"
		},
		"policySet": {
			"cn": "所属策略集",
			"en": "Policy Set"
		},
		"policySetName": {
			"cn": "策略集名称",
			"en": "Policy Set Name"
		},
		"releaseDescription": {
			"cn": "发版描述",
			"en": "Release Description"
		},
		"releaseDescriptionPlaceholder": {
			"cn": "请输入发版描述",
			"en": "Please enter the release description."
		}
	};
	return params[field][lang];
};

const workflowImportModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"title": {
			"cn": "规则流导入",
			"en": "Rule flow import"
		},
		"policySet": {
			"cn": "策略集",
			"en": "Policy Set"
		},
		"selectFile": {
			"cn": "选择文件",
			"en": "Select File"
		},
		"selectFileMessage1": {
			"cn": "请先选择文件",
			"en": "Please select the file first"
		},
		"selectFileMessage2": {
			"cn": "只允许上传pld格式的文件",
			"en": "Only uploading files in pld format is allowed"
		},
		"selectFileMessage3": {
			"cn": "文件大小请在100M内",
			"en": "File size within 100M"
		},
		"selectFileMessage4": {
			"cn": "请选择要上传的文件！",
			"en": "Please select the file to upload!"
		},
		"selectFileMessage5": {
			"cn": "文件大小在100M内",
			"en": "File size within 100M"
		},
		"upload": {
			"cn": "上传",
			"en": "Upload"
		},
		"policyModeRequired": {
			"cn": "请选择策略导入模式",
			"en": "Please select policy import mode"
		},
		"zbModeRequired": {
			"cn": "请选择指标导入模式",
			"en": "Please select indicator import mode"
		},
		"ruleModeRequired": {
			"cn": "请选择规则导入模式",
			"en": "Please select rule import mode"
		}
	};
	return params[field][lang];
};

export default {
	common,
	table,
	modal,
	message,
	nodeDetail,
	menuAction,
	edgeDetail,
	flowDetailPanel,
	flowItemPanel,
	flowMinimap,
	flowToolTip,
	oneCondition,
	conditionSettingModal,
	modelSettingModal,
	serviceSettingModal,
	nodeName,
	workflowCommitModal,
	workflowImportModal
};
