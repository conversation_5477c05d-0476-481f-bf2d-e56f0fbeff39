:global {

    .model-setting-modal,
    .model-node-detail {
        & {
            position: relative;
        }

        .model-select {
            & {
                margin-bottom: 20px;
                width: 100%;
            }

            .ant-select {
                width: 100%;
            }
        }

        .model-section-wrap {
            & {
                border: 1px dashed #dcdcdc;
                margin-bottom: 20px;
                //border-width: 0 1px 0 1px;
            }

            &:last-child {
                margin-bottom: 0;
            }

            .model-section-header {
                & {
                    position: relative;
                    height: 48px;
                    line-height: 48px;
                    border-bottom: 1px solid #e6e6e6;
                    background: #f0f0f0;
                }

                h3 {
                    font-size: 14px;
                    margin-bottom: 0;
                    padding-left: 12px;
                }
            }

            .model-section-body {
                & {
                    position: relative;
                    min-height: 100px;
                    //max-height: 300px;
                    overflow-x: scroll;
                    overflow-y: auto;
                }
            }
        }
    }

    .model-node-detail .model-section-wrap:last-child {
        margin-bottom: 20px;
    }
}
