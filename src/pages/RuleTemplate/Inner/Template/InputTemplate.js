import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Select, Switch } from "antd";
import ChangeRuleForSelf from "./ChangeRuleForSelf";
import ChangeRuleForMoreSelf from "./ChangeRuleForMoreSelf";

const Option = Select.Option;

class InputTemplate extends PureComponent {
	constructor(props) {
		super(props);
		this.changeParamValue = this.changeParamValue.bind(this);
		this.deleteChild = this.deleteChild.bind(this);
	}

	changeParamValue(type, field, actionType, e) {
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		if (actionType === "onBlur") {
			// 正则表达式去除首尾空格
			value = value.replace(/(^\s*)|(\s*$)/g, "");
		}
		let { templateStore, childIndex, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex][field] = value;
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	deleteChild(index) {
		let { templateStore, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"].splice(index, 1);
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	render() {
		let { templateStore, childIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};
		let componentType = "input";

		return (
			<div className="single-param">
				<div className="single-param-header">
					<h2>Input</h2>
					<Icon
						type="delete"
						onClick={this.deleteChild.bind(this, childIndex)}
					/>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>唯一标识：</span>
					</div>
					<div className="param-field">
						<Input
							value={currentParam["name"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "name", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "name", "onBlur")}
							placeholder="请输入唯一标识"
						/>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>默认值：</span>
					</div>
					<div className="param-field">
						<Input
							value={currentParam["defaultValue"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "defaultValue", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "defaultValue", "onBlur")}
							placeholder="请输入默认值"
						/>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>前缀：</span>
					</div>
					<div className="param-field">
						<Input
							value={currentParam["addonBefore"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "addonBefore", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "addonBefore", "onBlur")}
							placeholder="请输入input前缀"
						/>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>后缀：</span>
					</div>
					<div className="param-field">
						<Input
							value={currentParam["addonAfter"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "addonAfter", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "addonAfter", "onBlur")}
							placeholder="请输入input后缀"
						/>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>后缀匹配列表名：</span>
					</div>
					<div className="param-field">
						<Input
							value={currentParam["addonAfterListName"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "addonAfterListName", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "addonAfterListName", "onBlur")}
							placeholder="请输入input后缀匹配列表名"
						/>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>placeholder：</span>
					</div>
					<div className="param-field">
						<Input
							value={currentParam["placeholder"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "placeholder", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "placeholder", "onBlur")}
							placeholder="中文placeholder（非必填）"
							addonBefore="cn"
						/>
						<Input
							value={currentParam["enPlaceholder"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "enPlaceholder", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "enPlaceholder", "onBlur")}
							placeholder="英文placeholder（非必填）"
							addonBefore="en"
						/>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>字段类型：</span>
					</div>
					<div className="param-field">
						<Select
							value={currentParam["type"] || undefined}
							rows={4}
							placeholder="请选择数据类型"
							style={{ width: "100%" }}
							onChange={this.changeParamValue.bind(this, "select", "type", "onChange")}
							onBlur={this.changeParamValue.bind(this, "select", "type", "onBlur")}
						>
							<Option value="string">string</Option>
							<Option value="int">变量</Option>
							<Option value="int">int</Option>
						</Select>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>二次编辑状态：</span>
					</div>
					<div className="param-field">
						<Switch
							checkedChildren="可以"
							unCheckedChildren="不可以"
							checked={currentParam["canEditSecondTimes"]}
							onChange={this.changeParamValue.bind(this, "select", "canEditSecondTimes", "onChange")}
						/>
					</div>
				</div>
				<ChangeRuleForSelf componentType={componentType} childIndex={childIndex} />
				<ChangeRuleForMoreSelf componentType={componentType} childIndex={childIndex} />
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(InputTemplate);
