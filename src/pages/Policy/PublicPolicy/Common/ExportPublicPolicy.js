import { PureComponent } from "react";
import { message, Tooltip, Icon, notification } from "antd";
import { connect } from "dva";
import { publicPolicyRunningAPI } from "@/services";
import { policyListLang, commonLang, imExportModeLang } from "@/constants/lang";
import checkPermissionPublicPolicy from "@/constants/permission/checkPermissionPublicPolicy";
class ExportPublicPolicy extends PureComponent {
	state = {
		exportLoading: false
	}
	// 导出规则流
	exportRules = async(item, isEditorArea, e) => {
		e.stopPropagation();
		// 系统默认将策略集下的策略/规则及引用到的指标一并导出
		notification.open({
			message: imExportModeLang.exportInfo("warn"),
			description: imExportModeLang.exportInfo("tipPublicPolicy")
		});
		let { uuid, name } = item;
		if (!isEditorArea) {
			uuid = item.policyUuid;
			name = item.policyName;
		}
		const params = {
			uuid: uuid,
			fileName: policyListLang.table("publicPolicyA") + name,
			fileType: "ply",
			isEditorArea
		};
		this.setState({
			exportLoading: true
		});
		const testResult = await publicPolicyRunningAPI.exportPublicPolicyTest(params);
		console.log(testResult);
		if (testResult && testResult.hasOwnProperty("success") && !testResult.success) {
			message.warning(testResult.message);
			this.setState({
				exportLoading: false
			});
		} else {
			publicPolicyRunningAPI.exportPublicPolicy(params).then(() => {
				this.setState({
					exportLoading: false
				});
			}).catch(err => {
				console.log(err);
				this.setState({
					exportLoading: false
				});
			});
		}
	}
	render() {
		const { record, isEditorArea } = this.props;
		const { exportLoading } = this.state;
		const PublicPolicyExport = checkPermissionPublicPolicy.PublicPolicyExport(); // 判断导出权限
		return (
			<Tooltip title={policyListLang.tooltip("exportRules")} >
				<a onClick={(e) => {
					e.stopPropagation();
					if (PublicPolicyExport) {
						!exportLoading && this.exportRules(record, isEditorArea, e);
					} else {
						// lang:无权限操作
						message.warning(commonLang.messageInfo("noPermission"));
					}
				}}>
					{
						exportLoading
							? <Icon type="loading" />
							: <i className={`iconfont icon-upload ${!PublicPolicyExport ? "icon-disabled" : ""}`}></i>
					}
				</a>
			</Tooltip >
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(ExportPublicPolicy);
