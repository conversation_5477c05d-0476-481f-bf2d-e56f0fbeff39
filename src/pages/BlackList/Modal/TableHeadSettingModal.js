import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Select, Form, Row, Col, message } from "antd";
import { blackListAPI } from "@/services";

const Option = Select.Option;

class TableHeadSettingModal extends PureComponent {

	constructor(props) {
		super(props);
		this.submitModal = this.submitModal.bind(this);
		this.changeFieldValue = this.changeFieldValue.bind(this);
	}

	submitModal() {
		let { blackListStore, dispatch } = this.props;
		let { dialogData } = blackListStore;
		let { tableHeadData } = dialogData;
		let { tableHeaderList } = tableHeadData;

		let params = {
			headerConfig: JSON.stringify(tableHeaderList, null, 4)
		};
		blackListAPI.changeBlackListHeader(params).then(res => {
			if (res.success) {
				message.success("表头修改成功");
				this.closeModal();
				dispatch({
					type: "blackList/getBlackListHeader"
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	closeModal() {
		let { dispatch } = this.props;

		dispatch({
			type: "blackList/setDialogShow",
			payload: {
				tableHeadModal: false
			}
		});
		setTimeout(() => {
			dispatch({
				type: "blackList/setDialogData",
				payload: {
					tableHeadData: {
						tableHeaderList: []
					}
				}
			});
		}, 300);
	}

	changeFieldValue(index, field, value) {
		let { blackListStore, dispatch } = this.props;
		let { dialogData } = blackListStore;
		let { tableHeadData } = dialogData;
		let { tableHeaderList } = tableHeadData;

		tableHeaderList[index][field] = value;
		dispatch({
			type: "blackList/setDialogData",
			payload: {
				tableHeadData: tableHeadData
			}
		});
	}

	render() {
		let { blackListStore, globalStore } = this.props;
		let { allMap } = globalStore;
		let { dialogShow, dialogData } = blackListStore;
		let { tableHeadData } = dialogData;
		let { tableHeaderList } = tableHeadData;
		let { ruleFieldList } = allMap;

		return (
			<Modal
				title="设置表头映射"
				visible={dialogShow.tableHeadModal}
				maskClosable={true}
				width={650}
				onOk={this.submitModal.bind(this)}
				onCancel={this.closeModal.bind(this)}
			>
				<Form className="modal-form">
					{
						tableHeaderList &&
                        tableHeaderList.map((item, index) => {
                        	if (item.headerField !== "black_list_number" && item.headerField !== "create" && item.headerField !== "expireTime") {
                        		return (
                        			<Row gutter={20}>
                        				<Col
                        					span={8}
                        					className="basic-info-title"
                        				>
                        					{item["headerFieldName"]}：
                        				</Col>
                        				<Col span={16}>
                        					<Select
                        						value={item.systemField || undefined}
                        						placeholder="请选择"
                        						onChange={this.changeFieldValue.bind(this, index, "systemField")}
                        						showSearch
                        						optionFilterProp="children"
                        						dropdownMatchSelectWidth={false}
                        					>
                        						{
                        							ruleFieldList &&
                                                    ruleFieldList.map((rItem, rIndex) => {
                                                    	let fieldHasInList = tableHeaderList.find(fItem => fItem.systemField === rItem.name);

                                                    	return (
                                                    		<Option
                                                    			key={rIndex}
                                                    			value={rItem.name}
                                                    			disabled={fieldHasInList}
                                                    		>
                                                    			{rItem.dName}
                                                    		</Option>
                                                    	);
                                                    })
                        						}
                        					</Select>
                        				</Col>
                        			</Row>
                        		);
                        	}
                        })
					}
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	blackListStore: state.blackList
}))(TableHeadSettingModal);
