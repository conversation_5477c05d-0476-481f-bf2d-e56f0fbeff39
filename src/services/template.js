import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getTemplateList = async(param) => {
	return request(getUrl("/admin/templateData/all", param), {
		method: "GET",
		headers: getHeader()
	});
};
const addTemplateGroup = async(params) => {
	return request("/admin/templateData/group", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyTemplateGroup = async(params) => {
	return request("/admin/templateData/group", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const deleteTemplateGroup = async(params) => {
	return request(getUrl("/admin/templateData/group", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

const addTemplate = async(params) => {
	return request("/admin/templateData", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const modifyTemplate = async(params) => {
	return request("/admin/templateData", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const deleteTemplate = async(params) => {
	return request(getUrl("/admin/templateData", params), {
		method: "DELETE",
		headers: getHeader()
	});
};
export default {
	getTemplateList,
	addTemplateGroup,
	modifyTemplateGroup,
	deleteTemplateGroup,
	addTemplate,
	modifyTemplate,
	deleteTemplate
};
