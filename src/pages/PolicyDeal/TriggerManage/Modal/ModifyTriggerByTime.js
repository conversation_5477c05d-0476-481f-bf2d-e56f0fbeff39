import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Button, Input, Select, Calendar, message, Icon } from "antd";
import { policyDealAPI } from "@/services";
import moment from "moment";
import ModifyTimeEvent from "./ModifyTimeEvent";
import QuickAddTrigger from "./QuickAddTrigger";
import { checkFunctionHasPermission } from "@/utils/permission";

const TextArea = Input.TextArea;
const Option = Select.Option;

class ModifyTriggerByTime extends PureComponent {

	constructor(props) {
		super(props);
		this.getListData = this.getListData.bind(this);
		this.dateCellRender = this.dateCellRender.bind(this);
		this.changeMonth = this.changeMonth.bind(this);
		this.addEvent = this.addEvent.bind(this);
		this.loadCalendarData = this.loadCalendarData.bind(this);
		this.modifyEvent = this.modifyEvent.bind(this);
		this.quickAddModal = this.quickAddModal.bind(this);
		this.turnToday = this.turnToday.bind(this);
		this.closeCalendarModal = this.closeCalendarModal.bind(this);
	}

	componentDidMount() {
		let { dispatch, policyDealStore } = this.props;
		let { dialogData } = policyDealStore;
		let { modifyTriggerTimeData } = dialogData;

		let params = {
			dealType: modifyTriggerTimeData.dealType
		};
		modifyTriggerTimeData["calendarValue"] = moment(new Date());

		policyDealAPI.getTimeQuickType(params).then(res => {
			if (res.success) {
				modifyTriggerTimeData["quickType"] = res.data;
				dispatch({
					type: "policyDeal/setDialogData",
					payload: {
						modifyTriggerTimeData: modifyTriggerTimeData
					}
				});
			} else {
				console.log(res.message);
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});

		this.loadCalendarData();
	}

	loadCalendarData() {
		let { dispatch, policyDealStore } = this.props;
		let { dialogData } = policyDealStore;
		let { modifyTriggerTimeData } = dialogData;

		let startOfMonth = modifyTriggerTimeData.calendarValue.startOf("month").valueOf();
		let endOfMonth = modifyTriggerTimeData.calendarValue.endOf("month").valueOf();

		dispatch({
			type: "policyDeal/loadCalendarData",
			payload: {
				dealType: modifyTriggerTimeData.dealType,
				calendarValue: modifyTriggerTimeData.calendarValue
			}
		});
	}

	getListData(clickDate) {
		let formatDay = clickDate.format("YYYY-MM-DD");
		let { policyDealStore, globalStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyDealStore;
		let { calendarDataList } = dialogData.modifyTriggerTimeData;
		let result = calendarDataList.filter(item => item.start.startsWith(formatDay));
		return result || [];
	}

	dateCellRender(clickDate) {
		const listData = this.getListData(clickDate);
		return (
			<ul className="events">
				{
					listData &&
                    listData.map((item, index) => {
                    	return (
                    		<li key={index}
                    			onClick={this.modifyEvent.bind(this, item)}>
                    			{item.title}
                    		</li>
                    	);
                    })
				}

				<a className="add-event" onClick={(e) => {
					if (checkFunctionHasPermission("ZB0301", "addUnTriggerByTime")) {
						this.addEvent(clickDate, e);
					} else {
						message.warning("无权限操作");
					}
				}}>添加日程</a>
			</ul>
		);
	}

	modifyEvent(item, e) {
		let { policyDealStore, globalStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyDealStore;
		let { modifyTimeEvent } = dialogData;

		let startDay = null;
		let startHour = null;
		let endHour = null;
		if (item.all) {
			startDay = item.start;
		} else {
			let dateArray = item.start.split(" ");
			startDay = dateArray[0];
			startHour = dateArray[1].replace(":00", "");
			endHour = item.end.split(" ")[1].replace(":00", "");
		}

		modifyTimeEvent = {
			startDay: startDay,
			startHour: startHour,
			endHour: endHour,
			id: item.id,
			all: item.all,
			setType: item.setType,
			isUpdate: true
		};

		dispatch({
			type: "policyDeal/setDialogShow",
			payload: {
				modifyTimeEvent: true
			}
		});

		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				modifyTimeEvent: modifyTimeEvent
			}
		});

		e.stopPropagation();
	}

	addEvent(clickDate, e) {
		let { policyDealStore, globalStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyDealStore;
		let { modifyTimeEvent } = dialogData;

		modifyTimeEvent = {
			startDay: clickDate.format("YYYY-MM-DD"),
			startHour: null,
			endHour: null,
			id: null,
			all: true,
			isUpdate: false
		};

		dispatch({
			type: "policyDeal/setDialogShow",
			payload: {
				modifyTimeEvent: true
			}
		});

		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				modifyTimeEvent: modifyTimeEvent
			}
		});

		e.stopPropagation();

	}

	changeMonth(type) {
		let { dispatch, policyDealStore } = this.props;
		let { dialogData } = policyDealStore;
		let { modifyTriggerTimeData } = dialogData;
		let { calendarValue } = modifyTriggerTimeData;
		let month = calendarValue.month();

		calendarValue.month(type === "pre" ? month - 1 : month + 1);
		calendarValue = moment(calendarValue);

		modifyTriggerTimeData["calendarValue"] = calendarValue;

		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				modifyTriggerTimeData: modifyTriggerTimeData
			}
		});

		this.loadCalendarData();
	}

	quickAddModal(quickType) {
		let { policyDealStore, globalStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyDealStore;
		let { quickAddTrigger } = dialogData;

		quickAddTrigger["quickType"] = quickType;

		dispatch({
			type: "policyDeal/setDialogShow",
			payload: {
				quickAddTrigger: true
			}
		});

		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				quickAddTrigger: quickAddTrigger
			}
		});
	}

	turnToday() {
		let { dispatch, policyDealStore } = this.props;
		let { dialogData } = policyDealStore;
		let { modifyTriggerTimeData } = dialogData;

		modifyTriggerTimeData["calendarValue"] = moment(new Date());

		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				modifyTriggerTimeData: modifyTriggerTimeData
			}
		});

		this.loadCalendarData();
	}

	closeCalendarModal() {
		let { dispatch, policyDealStore } = this.props;
		let { trigger } = policyDealStore;
		let { searchParams } = trigger;

		dispatch({
			type: "policyDeal/setDialogShow",
			payload: {
				modifyTriggerByTime: false
			}
		});

		dispatch({
			type: "policyDeal/getTriggers",
			payload: searchParams
		});
	}

	render() {
		let { policyDealStore, globalStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyDealStore;
		let { calendarDataList, quickType, calendarValue } = dialogData.modifyTriggerTimeData;

		let quickAddOptions;

		if (quickType === "month") {
			quickAddOptions = [<Option value="month">每月</Option>];
		} else if (quickType === "week") {
			quickAddOptions = [<Option value="week">每周</Option>];
		} else if (quickType === "day") {
			quickAddOptions = [<Option value="day">每日</Option>];
		} else {
			quickAddOptions = [<Option value="day">每日</Option>, <Option value="week">每周</Option>, <Option value="month">每月</Option>];
		}

		console.log(calendarValue);

		return (
			<Modal
				title="不触发日程管理"
				visible={dialogShow.modifyTriggerByTime}
				maskClosable={false}
				width={900}
				className="modify-trigger-by-time-wrap"
				onOk={() => {

				}}
				onCancel={this.closeCalendarModal.bind(this)}
				footer={null}
			>
				<div>
					<div className="trigger-calendar-toolbar">
						<div className="left-info">
							<Button.Group>
								<Button
									type="default"
									onClick={this.changeMonth.bind(this, "pre")}
								>
									<Icon type="left" />
								</Button>
								<Button
									type="default"
									onClick={this.changeMonth.bind(this, "next")}
								>
									<Icon type="right" />
								</Button>
								<Button
									type="default"
									onClick={this.turnToday.bind(this)}
								>
                                    今天
								</Button>
							</Button.Group>
						</div>
						<div className="center-info">
							{
								calendarValue ? calendarValue.format("YYYY-MM") : moment(new Date()).format("YYYY-MM")
							}
						</div>
						<div className="right-info">
							<span>快捷设置：</span>
							<Select
								value={quickType || "month"}
								onSelect={(value) => {
									if (checkFunctionHasPermission("ZB0301", "batchAddUnTriggerByTime")) {
										this.quickAddModal(value);
									} else {
										message.warning("无权限操作");
									}
								}}
							>
								{
									quickAddOptions
								}
							</Select>
						</div>
					</div>
					<Calendar
						className="trigger-calendar"
						dateCellRender={this.dateCellRender.bind(this)}
						value={calendarValue || moment()}
					/>
				</div>
				<ModifyTimeEvent />
				<QuickAddTrigger />
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(ModifyTriggerByTime);
