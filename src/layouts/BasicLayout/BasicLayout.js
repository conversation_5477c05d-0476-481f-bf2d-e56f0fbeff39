import React from "react";
import PropTypes from "prop-types";
import { Layout } from "antd";
import DocumentTitle from "react-document-title";
import { connect } from "dva";
import { Route, Redirect, Switch } from "dva/router";
import { ContainerQuery } from "react-container-query";
import classNames from "classnames";
import Debounce from "lodash-decorators/debounce";
import Top from "../Common/Top";
import BasicSideMenu from "../Common/BasicSideMenu";
import { LocaleProvider } from "antd";
import zhCN from "antd/lib/locale-provider/zh_CN";
import enUS from "antd/lib/locale-provider/en_US";

const { Content } = Layout;

const query = {
	"screen-xs": {
		maxWidth: 575
	},
	"screen-sm": {
		minWidth: 576,
		maxWidth: 767
	},
	"screen-md": {
		minWidth: 768,
		maxWidth: 991
	},
	"screen-lg": {
		minWidth: 992,
		maxWidth: 1199
	},
	"screen-xl": {
		minWidth: 1200
	}
};

class BasicLayout extends React.PureComponent {
    static childContextTypes = {
    	location: PropTypes.object,
    	breadcrumbNameMap: PropTypes.object
    };

    constructor(props) {
    	super(props);
    	this.menus = props.navData.reduce((arr, current) => arr.concat(current.children.filter(item => item.name !== "other")), []);
    	this.state = {
    		openKeys: this.getDefaultCollapsedSubMenus(props)
    	};
    }

    componentWillMount() {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "global/getUserMenuTree",
    		payload: {}
    	});
    }

    getChildContext() {
    	let { location, navData, getRouteData } = this.props;
    	let routeData = getRouteData("BasicLayout");
    	let firstMenuData = navData.reduce((arr, current) => arr.concat(current.children), []);
    	let menuData = this.getMenuData(firstMenuData, "");
    	let breadcrumbNameMap = {};

    	routeData.concat(menuData).forEach((item) => {
    		breadcrumbNameMap[item.path] = item.name;
    	});
    	return { location, breadcrumbNameMap };
    }

    componentWillUnmount() {
    	this.triggerResizeEvent.cancel();
    }

    getMenuData = (data, parentPath) => {
    	let arr = [];
    	data.forEach((item) => {
    		if (item.children) {
    			arr.push({ path: `${parentPath}/${item.path}`, name: item.name });
    			arr = arr.concat(this.getMenuData(item.children, `${parentPath}/${item.path}`));
    		}
    	});
    	return arr;
    };

    getDefaultCollapsedSubMenus(props) {
    	let currentMenuSelectedKeys = [...this.getCurrentMenuSelectedKeys(props)];
    	currentMenuSelectedKeys.splice(-1, 1);
    	if (currentMenuSelectedKeys.length === 0) {
    		return ["dashboard"];
    	}
    	return currentMenuSelectedKeys;
    }

    getCurrentMenuSelectedKeys(props) {
    	let { location: { pathname } } = props || this.props;
    	let keys = pathname.split("/").slice(1);
    	if (keys.length === 1 && keys[0] === "") {
    		return [this.menus[0].key];
    	}
    	return keys;
    }

    getPageTitle() { // 获取页面标题
    	let { globalStore, location, getRouteData } = this.props;
    	let { personalMode } = globalStore;
    	let lang = personalMode.lang === "cn" ? "cn" : "en";
    	let { pathname } = location;
    	let title = "策略指标";
    	getRouteData("BasicLayout").forEach((item) => {
    		if (item.path === pathname) {
    			title = lang === "en" ? item.enName : item.name;
    		}
    	});
    	return title;
    }

    handleOpenChange = (openKeys) => {
    	let lastOpenKey = openKeys[openKeys.length - 1];
    	let isMainMenu = this.menus.some(
    		item => lastOpenKey && (item.key === lastOpenKey || item.path === lastOpenKey)
    	);
    	this.setState({
    		openKeys: isMainMenu ? [lastOpenKey] : [...openKeys]
    	});
    };

    @Debounce(600)
    triggerResizeEvent() { // eslint-disable-line
    	let event = document.createEvent("HTMLEvents");
    	event.initEvent("resize", true, false);
    	window.dispatchEvent(event);
    }

    render() {
    	let { globalStore, getRouteData, currentUser, location } = this.props;
    	let { personalMode } = globalStore;
    	let topClass = "development-layout basic-layout themeS2";
    	if (personalMode.theme) {
    		topClass = "development-layout basic-layout " + personalMode.theme;
    	}
    	topClass = `${topClass} collapsed-false`;

    	let layout = (
    		<Layout className={topClass}>
    			<BasicSideMenu
    				menuList={this.menus}
    				location={location}
    				isPublishLayout={false}
    			/>
    			<Layout>
    				<Top
    					currentUser={currentUser}
    					isPublishLayout={false}
    					location={location}
    				/>
    				<Content className="basic-content">
    					<div style={{ minHeight: "calc(100vh - 120px)" }}>
    						<Switch>
    							{
    								getRouteData("BasicLayout").map(item =>
    									(
    										<Route
    											exact={item.exact}
    											key={item.path}
    											path={item.path}
    											component={item.component}
    										/>
    									)
    								)
    							}
    							<Redirect exact from="/" to="/policy/policyList" />
    							<Redirect exact to="/exception/404" />
    						</Switch>
    					</div>
    				</Content>
    			</Layout>
    		</Layout>
    	);
    	return (
    		<LocaleProvider locale={personalMode.lang === "cn" ? zhCN : enUS}>
    			<DocumentTitle title={this.getPageTitle()}>
    				<ContainerQuery query={query}>
    					{params => <div className={classNames(params)}>{layout}</div>}
    				</ContainerQuery>
    			</DocumentTitle>
    		</LocaleProvider>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	currentUser: state.user.currentUser
}))(BasicLayout);
