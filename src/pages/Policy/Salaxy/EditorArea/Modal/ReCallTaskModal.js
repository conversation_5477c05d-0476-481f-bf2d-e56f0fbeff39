import { PureComponent } from "react";
import { connect } from "dva";
import moment from "moment";
import { Modal, Form, DatePicker, Radio, Button, message } from "antd";
import { reCallTaskLang, commonLang } from "@/constants/lang";
import { reCallTaskAPI } from "@/services";
import "./ReCallTaskModal.less";

const DateFormat = "YYYY-MM-DD HH:mm:ss";
const formItemLayout = {
	labelCol: {
		xs: { span: 24 },
		sm: { span: 4 }
	},
	wrapperCol: {
		xs: { span: 24 },
		sm: { span: 20 }
	}
};

class ReCallTaskModal extends PureComponent {
	// 提交数据
	postReCall = () => {
		const { indexEditorStore, refresh } = this.props;
		const { dialogData = {} } = indexEditorStore;
		const {reCallData} = dialogData || {};
		const { executeType, taskStartAt, deadline } = reCallData;
		if (deadline) {
			if (moment(deadline).isAfter(taskStartAt) && executeType === "TIMING") {
				message.warning(reCallTaskLang.operatorModal("timeTip1")); // 回溯时间需小于等于定时执行时间
				return;
			}
			if (moment(deadline).isAfter(moment()) && executeType !== "TIMING") {
				message.warning(reCallTaskLang.operatorModal("timeTip2")); // 回溯时间需小于等于当前时间
				return;
			}
		}
		if (reCallData.operaType === "create") {
			reCallTaskAPI.fillbackAdd(reCallData).then(data=>{
				if (data.success) {
					message.success(data.message || reCallTaskLang.operatorModal("addReCallSuccess"));
					refresh();
					this.onCancel();
				} else {
					message.error(data.message || reCallTaskLang.operatorModal("addReCallErr"));
				}
			}).catch((e)=>{
				message.error(e.message || reCallTaskLang.operatorModal("addReCallErr"));
			});
		} else if (reCallData.operaType === "edit") {
			reCallTaskAPI.fillbackModify(reCallData).then(data=>{
				if (data.success) {
					message.success(data.message || reCallTaskLang.operatorModal("editReCallSuccess"));
					refresh();
					this.onCancel();
				} else {
					message.error(data.message || reCallTaskLang.operatorModal("editReCallErr"));
				}
			}).catch((e)=>{
				message.error(e.message || reCallTaskLang.operatorModal("editReCallErr"));
			});
		};
	}
	// 关闭弹窗
	onCancel=()=>{
		const { dispatch } = this.props;
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				reCall: false
			}
		});
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				reCallData: {
					operaType: "",
					timeType: "",
					zbUuid: "",
					zbName: "",
					zbCalcType: "",
					executeType: "RIGHTNOW",
					deadline: "",
					taskStartAt: ""
				}
			}
		});
	}
	// 修改表单
	changeFieldValue = (field, type, e) => {
    	const { indexEditorStore, dispatch } = this.props;
    	const { dialogData } = indexEditorStore;
		const { reCallData: reCallDataOld } = dialogData;
		let reCallData = {...reCallDataOld};
    	let value = e;
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	} else if (type === "date") {
    		value = moment(e.valueOf()).format(DateFormat);
    	} else {
    		value = e;
    	}
    	reCallData[field] = value;
    	dispatch({
    		type: "indexEditor/setDialogData",
    		payload: {
    			reCallData
    		}
    	});
	}
	render() {
		const { indexEditorStore } = this.props;
		const { dialogShow, dialogData = {} } = indexEditorStore;
		const { executeType, deadline, taskStartAt, operaType } = dialogData.reCallData || {};
		const footerButton = [
			<Button onClick={this.onCancel}>
				{/* lang:取消 */}
				{commonLang.base("cancel")}
			</Button>
		];
		if (operaType !== "view") {
			footerButton.push(
				<Button type="primary" onClick={this.postReCall}>
					{/* lang:确定 */}
					{commonLang.base("ok")}
				</Button>
			);
		}
		return (
			<Modal
				title={reCallTaskLang.operatorModal("indexBackTrack")} // lang:指标回溯
				width={700}
				visible={dialogShow.reCall}
				onCancel={this.onCancel}
				footer={footerButton}
			>
				<Form {...formItemLayout} className="recall-form">
					{/* 从 */}
					<Form.Item label={reCallTaskLang.operatorModal("from")}>
						<DatePicker
							showTime={true}
							className="recall-form-wid-400"
							format={DateFormat}
							value={deadline ? moment(deadline) : undefined}
							onChange={this.changeFieldValue.bind(this, "deadline", "date")}
						/>
						{/* 开始往前回溯 */}
						<span className="ant-form-text ml10">
							{reCallTaskLang.operatorModal("startGoBack")}
						</span>
					</Form.Item>
					{/* 执行方式 */}
					<Form.Item label={reCallTaskLang.operatorModal("execute")}>
						<Radio.Group
							buttonStyle="solid"
							value={executeType}
							onChange={this.changeFieldValue.bind(this, "executeType", "input")}
						>
							{/* 立即执行 */}
							<Radio.Button value="RIGHTNOW">{reCallTaskLang.operatorModal("rightExecute")}</Radio.Button>
							{/* 定时执行 */}
							<Radio.Button value="TIMING">{reCallTaskLang.operatorModal("timeExecute")}</Radio.Button>
						</Radio.Group>
					</Form.Item>
					{
						executeType === "TIMING" &&
						// 任务时间
						<Form.Item label={reCallTaskLang.operatorModal("taskTime")}>
							<DatePicker
								showTime={true}
								className="recall-form-wid-400"
								format={DateFormat}
								value={taskStartAt ? moment(taskStartAt) : undefined}
								onChange={this.changeFieldValue.bind(this, "taskStartAt", "date")}
								disabledDate={(current) => {
									return current && current < moment().subtract(1, "days");
								}}
							/>
						</Form.Item>
					}
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	indexEditorStore: state.indexEditor
}))(ReCallTaskModal);
