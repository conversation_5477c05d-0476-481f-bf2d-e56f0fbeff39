@import "../../base/variables";

:global {
	.policy-detail-wrap {
		& {
			height: ~"calc(100vh - 60px)";
			overflow: auto;
		}
		&.zb-detail-wrap {
			& {
				height: auto;
			}
		}
		&.no-data {
			.page-global-body-main {
				background: none;
			}
		}
		.policy-detail-box {
			padding: 24px;
			height: ~"calc(100%)";
			overflow-y: auto;
		}
		.policy-detail-header {
			position: relative;
		}
		.policy-detail-body {
			position: relative;
		}
	}
}

