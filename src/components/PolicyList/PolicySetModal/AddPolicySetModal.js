import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Select, Form, Row, Col, message } from "antd";
import { policyEditorAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { policyListLang } from "@/constants/lang";

const TextArea = Input.TextArea;
const Option = Select.Option;

class AddPolicySetModal extends PureComponent {
	constructor(props) {
		super(props);
		this.addPolicySet = this.addPolicySet.bind(this);
	}

	componentDidMount() {
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyEditorStore } = this.props;
		let { addPolicySetData } = policyEditorStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		addPolicySetData[field] = value;
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				addPolicySetData: addPolicySetData
			}
		});
	}

	addPolicySet() {
		let { dispatch, policyEditorStore, globalStore } = this.props;
		let { curPage, pageSize, dialogData } = policyEditorStore;
		let { addPolicySetData } = dialogData;
		let { currentApp } = globalStore;

		let params = {
			partnerCode: "kratos",
			appName: addPolicySetData.appName,
			name: addPolicySetData.name,
			// type : addPolicySetData.type,
			type: "normal",
			eventType: addPolicySetData.eventType,
			eventId: addPolicySetData.eventId,
			description: addPolicySetData.description
		};

		if (!params.name) {
			// lang:策略集名称不能为空
			message.error(policyListLang.policySetModal("policySetNameIsEmptyTip"));
			return;
		}

		if (!params.eventId) {
			// lang:事件标识不能为空
			message.error(policyListLang.policySetModal("eventIdIsEmptyTip"));
			return;
		}

		if (params.name.length >= 50 || params.name.length <= 1) {
			// lang:策略集名称长度应大于1,小于50
			message.error(policyListLang.policySetModal("policySetLenTip"));
			return;
		}

		if (params.eventId.length >= 50 || params.eventId.length <= 1) {
			// lang:事件标识长度应大于1,小于50
			message.error(policyListLang.policySetModal("eventIdLenTip"));
			return;
		}

		if (!/^[A-Za-z0-9\u4E00-\u9FA5\_]{2,50}$/.test(params.name)) {
			// lang:策略集名称：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合
			message.error(policyListLang.policySetModal("policySetRegTip"));
			return;
		}

		if (!/^[A-Za-z0-9\u4E00-\u9FA5\_]{2,50}$/.test(params.eventId)) {
			// lang:事件标识：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合
			message.error(policyListLang.policySetModal("eventIdRegTip"));
			return;
		}

		policyEditorAPI.addPolicySet(params).then(res => {
			if (res.success) {
				dispatch({
					type: "policyEditor/setDialogShow",
					payload: {
						addPolicySet: false
					}
				});
				// lang:新建策略集成功
				message.success(policyListLang.policySetModal("newPolicySetSuccessTip"));
				dispatch({
					type: "policyEditor/getPolicySets",
					payload: {
						appName: currentApp && currentApp.name ? currentApp.name : null,
						curPage: curPage,
						pageSize: pageSize
					}
				});

				dispatch({
					type: "global/getAllMap",
					payload: {}
				});

				dispatch({
					type: "policyEditor/setDialogData",
					payload: {
						addPolicySetData: {
							partnerCode: null,
							appName: null,
							name: null,
							type: "normal",
							eventType: null,
							eventId: null,
							description: null
						}
					}
				});

			} else {
				console.log(res.message);
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});

	}

	render() {
		let { policyEditorStore, globalStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyEditorStore;
		let { addPolicySetData } = dialogData;
		let { allMap } = globalStore;

		return (
			<Modal
				title={policyListLang.policySetModal("newPolicySet")} // lang:新建策略集
				visible={dialogShow.addPolicySet}
				maskClosable={false}
				onOk={this.addPolicySet.bind(this)}
				onCancel={() => {
					dispatch({
						type: "policyEditor/setDialogShow",
						payload: {
							addPolicySet: false
						}
					});
					dispatch({
						type: "policyEditor/setAttrValue",
						payload: {
							modalType: null
						}
					});
					dispatch({
						type: "policyEditor/setDialogData",
						payload: {
							addPolicySetData: {
								partnerCode: null,
								appName: null,
								name: null,
								type: "normal",
								eventType: null,
								eventId: null,
								description: null
							}
						}
					});
				}}
			>
				<Form className="modal-form">
					<Row gutter={CommonConstants.gutterSpan}>
						{/* lang:策略集名称 */}
						<Col span={6}
							className="basic-info-title">{policyListLang.policySetModal("policySetName")}：</Col>
						<Col span={18}>
							<Input
								type="text"
								placeholder={policyListLang.policySetModal("enterPolicyName")} // lang:请输入策略名称
								value={addPolicySetData.name}
								onChange={this.changeDialogDataHandle.bind(this, "name", "input")}
							/>
							<span className="tip-text">
								{/* lang:建议输入：中文、英文、数字和下划线的组合 */}
								{policyListLang.policySetModal("inputSuggest")}
							</span>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* lang:应用名 */}
							{policyListLang.policySetModal("appName")}：
						</Col>
						<Col span={18}>
							<Select
								placeholder={policyListLang.policySetModal("selectAppName")} // lang:请选择应用
								value={addPolicySetData.appName || undefined}
								onChange={this.changeDialogDataHandle.bind(this, "appName", "select")}
							>
								{
									allMap &&
                                    allMap["appNames"] &&
                                    allMap["appNames"].map((item, index) => {
                                    	if (item.name !== "all") {
                                    		return (
                                    			<Option
                                    				value={item.name}
                                    				key={index}
                                    			>
                                    				{item.dName}
                                    			</Option>
                                    		);
                                    	}
                                    })
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* lang:事件类型*/}
							{policyListLang.policySetModal("eventType")}：
						</Col>
						<Col span={18}>
							<Select
								placeholder={policyListLang.policySetModal("selectEventType")} // lang:请选择事件类型
								value={addPolicySetData.eventType || undefined}
								onChange={this.changeDialogDataHandle.bind(this, "eventType", "select")}
							>
								{
									allMap && allMap["EventType"] && allMap["EventType"].map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* lang:事件标识 */}
							{policyListLang.policySetModal("eventId")}：
						</Col>
						<Col span={18}>
							<Input
								type="text"
								placeholder={policyListLang.policySetModal("selectEventId")} // lang:请输入事件标识
								value={addPolicySetData.eventId}
								onChange={this.changeDialogDataHandle.bind(this, "eventId", "input")}
							/>
							<span className="tip-text">
								{/* lang:建议输入：英文、数字和下划线的组合 */}
								{policyListLang.policySetModal("inputSuggest2")}
							</span>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* lang:描述 */}
							{policyListLang.policySetModal("description")}：
						</Col>
						<Col span={18}>
							<TextArea
								value={addPolicySetData.description}
								rows={4}
								placeholder={policyListLang.policySetModal("enterPolicyDescription")} // lang:请输入策略描述
								onChange={this.changeDialogDataHandle.bind(this, "description", "input")}
							></TextArea>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(AddPolicySetModal);
