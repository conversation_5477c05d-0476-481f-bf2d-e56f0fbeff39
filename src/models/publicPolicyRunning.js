import { message } from "antd";
import { publicPolicyRunningAPI } from "@/services";

export default {
	namespace: "publicPolicyRunning",
	state: {
		publicPolicyListLoad: true,
		publicPolicyList: [],
		curPage: 1,
		pageSize: 10,
		total: 0,
		pages: 0,
		searchExpand: false,
		searchData: {
			policyName: "",
			riskType: "",
			eventIds: "",
			ruleName: "",
			ruleCustomId: ""
		},
		dialogShow: {
			policyDrawer: false,
			quoteDrawer: false
		},
		dialogData: {
			policyDrawer: {
				policyDetail: {},
				policyUuid: null,
				loading: false
			},
			quoteDrawer: {
				quoteDetail: {},
				policyUuid: null,
				loading: false
			}
		}
	},
	effects: {
		*getPublicRunningPolicy({ }, { call, put, select }) {
			yield put({
				type: "setAttrValue",
				payload: {
					publicPolicyListLoad: true
				}
			});
			const { searchData, curPage, pageSize } = yield select(state => state.publicPolicyRunning);
			let eventIds = "";
			if (searchData.eventIds) {
				eventIds = searchData.eventIds.join(",");
			}
			let response = yield call(publicPolicyRunningAPI.publicRunningList, {
				curPage,
				pageSize,
				...searchData,
				eventIds
			});
			yield put({
				type: "setAttrValue",
				payload: {
					publicPolicyListLoad: false
				}
			});
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initPublicPolicyList",
				payload: response
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}
			return {
				...state
			};
		},
		setSearchData(state, action) {
			let { payload } = action;
			let searchDataNew = { ...state.searchData };
			for (let key in payload) {
				if (payload[key] !== undefined) {
					searchDataNew[key] = payload[key];
				}
			}
			return {
				...state,
				searchData: searchDataNew
			};
		},
		initPublicPolicyList(state, action) {
			let { publicPolicyList, curPage, pageSize, total, pages } = state;
			let data = action.payload.data || null;
			let list = data["dataList"] || [];
			curPage = data.curPage;
			pageSize = data.pageSize;
			total = data.total;
			pages = data.pages;

			publicPolicyList = list || [];

			return {
				...state,
				publicPolicyList: publicPolicyList,
				curPage: curPage,
				pageSize: pageSize,
				total: total,
				pages: pages
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		}
	}
};
