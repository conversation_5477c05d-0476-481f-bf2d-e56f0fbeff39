{"name": "salaxy_admin", "version": "1.0.0", "description": "企业级指标平台", "private": true, "scripts": {"start": "cross-env SYS_ENV=development BABEL_ENV=development PORT=8820 webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start:unstand": "cross-env UNSTAND=true SYS_ENV=development BABEL_ENV=development PORT=8820 webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "build": "cross-env SYS_ENV=production BABEL_ENV=production webpack --progress --config build/webpack.prod.conf.js", "build:unstand": "cross-env UNSTAND=true SYS_ENV=production BABEL_ENV=production webpack --progress --config build/webpack.prod.conf.js", "build:map": "cross-env SYS_ENV=production BABEL_ENV=production SOURCE_MAP=true webpack --progress --config build/webpack.prod.conf.js", "build:dll": "webpack --config ./build/webpack.dll.conf.js"}, "dependencies": {"@tntd/antd-cover": "^1.0.4", "antd": "^3.26.3", "classnames": "^2.2.5", "core-js": "^3.8.1", "dva": "^2.4.1", "echarts": "^4.1.0", "formula-edit-react": "^2.2.3", "gg-editor": "^2.0.4", "jsencrypt": "^3.0.0-rc.1", "keymaster": "^1.6.2", "lodash": "^4.17.4", "lodash-decorators": "^4.5.0", "lodash.clonedeep": "^4.5.0", "md5": "^2.2.1", "moment": "^2.24.0", "numeral": "^2.0.6", "prop-types": "^15.7.2", "qs": "^6.5.0", "query-string": "^5.1.1", "react": "^16.8.6", "react-color": "^2.17.3", "react-container-query": "^0.11.0", "react-document-title": "^2.0.3", "react-dom": "^16.8.6", "react-fittext": "^1.0.0", "react-sortablejs": "^1.5.1", "react-zmage": "^0.8.5", "sortablejs": "^1.8.4", "store": "^2.0.12", "universal-cookie": "^2.2.0"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/polyfill": "^7.4.4", "@babel/preset-env": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/runtime": "7.0.0-beta.46", "@tntd/webpack-branch-plugin": "^1.0.9", "autoprefixer": "^9.1.5", "babel-eslint": "^10.0.2", "babel-loader": "^8.0.2", "babel-plugin-import": "^1.11.0", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-remove-console": "^6.9.4", "clean-webpack-plugin": "^0.1.19", "colors": "^1.3.3", "copy-webpack-plugin": "^5.0.3", "cross-env": "^5.1.1", "css-loader": "^1.0.0", "cssnano": "^4.1.0", "eslint": "^5.16.0", "eslint-config-tdued": "^1.1.4", "eslint-plugin-import": "^2.17.3", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-react": "^7.13.0", "file-loader": "^3.0.1", "gulp": "^4.0.0", "gulp-replace": "^1.0.0", "html-webpack-plugin": "^3.2.0", "less": "^3.8.1", "less-loader": "^4.1.0", "mini-css-extract-plugin": "^0.4.2", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss-loader": "^3.0.0", "react-hot-loader": "^4.11.1", "style-loader": "^0.21.0", "td-upyun": "^2.0.0", "uglifyjs-webpack-plugin": "^1.3.0", "url-loader": "^1.0.1", "webpack": "^4.34.0", "webpack-bundle-analyzer": "^3.0.3", "webpack-cli": "^3.1.0", "webpack-dev-server": "^3.1.5", "webpack-merge": "^4.1.3"}, "optionalDependencies": {"nightmare": "^2.10.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}