export const transferSceneList = (scene, publicPolicyScene) => {
	if (typeof scene === "string") {
		scene = JSON.parse(scene);
	}
	// 过滤有权限的应用
	const newScene = scene && scene.length > 0 && scene.filter((v) => {
		const { appName } = v || {};
		const newAppName = (publicPolicyScene.find((publicAppName) => {
			if (publicAppName.name === appName) {
				return publicAppName;
			}
		}) || {}).dName;
		if (newAppName) {
			v.newAppName = newAppName;
			return v;
		}
	});

	// 获取有权限的所有策略集
	const totalEventList = publicPolicyScene && publicPolicyScene.reduce((totalScenes, cur) => {
		totalScenes = totalScenes.concat(cur.eventList);
		return totalScenes;
	}, []);

	const sceneListSource = [];

	newScene &&
	newScene.length > 0 &&
	newScene.forEach(v=>{
		const { newAppName, appName, eventList: eventIdList } = v;
		let sceneItem = {
			dName: newAppName,
			name: appName,
			eventList: []
		};
		eventIdList && eventIdList.length > 0 && eventIdList.forEach((eventId)=>{
			const transferEventObj = totalEventList.find((eventItem)=>{
				return eventItem.name === eventId;
			});
			if (transferEventObj && Object.keys(transferEventObj).length > 0) {
				sceneItem.eventList.push(transferEventObj);
			}
		});
		sceneListSource.push(sceneItem);
	});
	return sceneListSource;
};
