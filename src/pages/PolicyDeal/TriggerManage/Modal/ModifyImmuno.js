import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Select, Form, Row, Col, message, Input } from "antd";
import { CommonConstants } from "@/constants";
import { policyDealAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";
import OneCondition from "../OneCondition";

const Option = Select.Option;
const { TextArea } = Input;

class ModifyImmuno extends PureComponent {
	constructor(props) {
		super(props);
		this.changeDialogDataHandle = this.changeDialogDataHandle.bind(this);
		this.saveImmunoConfig = this.saveImmunoConfig.bind(this);
		this.state = {
			policySet: []
		};
	}

	async componentWillMount() {
		let { policyDealStore } = this.props;
		let { modifyImmuno } = policyDealStore.dialogData;
		if (modifyImmuno.app) {
			await this.changeApp(modifyImmuno.app);
		}
	}
	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyDealStore } = this.props;
		let { modifyImmuno } = policyDealStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		modifyImmuno[field] = value;
		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				modifyImmuno: modifyImmuno
			}
		});
	}

	saveImmunoConfig() {
		let { dispatch, policyDealStore } = this.props;
		let { modifyImmuno } = policyDealStore.dialogData;
		let { trigger } = policyDealStore;
		let { searchParams } = trigger;
		const { eventId, immunoTime, app, description, immunoConditions, isUpdate } = modifyImmuno;
		if (!app) {
			message.warning("请选择应用");
			return;
		}

		if (!eventId) {
			message.warning("请选择策略集");
			return;
		}
		if (!immunoTime) {
			message.warning("请选择免打扰时间");
			return;
		}
		if (!immunoTime) {
			message.warning("请选择免打扰时间");
			return;
		}
		if (description && description.length > 500) {
			message.warning("免打扰说明字数不超过500");
			return;
		}

		let params = {
			eventId: eventId,
			immunoTime: immunoTime,
			app,
			description,
			immunoConditions: immunoConditions && JSON.stringify(immunoConditions)
		};

		if (isUpdate) {
			policyDealAPI.modifyImmuno(params).then(res => {
				if (res.success) {
					searchParams.manageType = "immuno";
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							modifyImmuno: false
						}
					});
					message.success("修改免打扰配置成功");
					dispatch({
						type: "policyDeal/getTriggers",
						payload: searchParams
					});
				} else {
					console.log(res.message);
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		} else {

			if (!modifyImmuno.immunoField) {
				message.warning("请选择维度");
				return;
			}

			params["immunoField"] = modifyImmuno.immunoField;

			policyDealAPI.addImmuno(params).then(res => {
				if (res.success) {
					searchParams.manageType = "immuno";
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							modifyImmuno: false
						}
					});
					message.success("新增免打扰配置成功");
					dispatch({
						type: "policyDeal/getTriggers",
						payload: searchParams
					});
				} else {
					console.log(res.message);
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		}
	}
    // 变更应用
    changeApp = (e) => {
    	return policyDealAPI.policySetsByApp({
    		"appName": e
    	}).then((res) => {
    		const { data = [] } = res;
    		this.setState({
    			policySet: data || []
    		});
    	}).catch(e => {
    		console.log(e && e.errMsg);
    		this.setState({
    			policySet: []
    		});
    	});
    }
    render() {
    	let { policyDealStore, globalStore, dispatch } = this.props;
    	let { dialogShow } = policyDealStore;
    	let { allMap } = globalStore;
    	let { eventIdSelect } = allMap;
    	let { modifyImmuno } = policyDealStore.dialogData;
    	const { policySet = [] } = this.state;

    	// 以前获取策略集的方式
    	// for (let key in eventIdSelect) {
    	// 	optionDom.push(
    	// 		<Option value={key} index={key}>{eventIdSelect[key]}</Option>
    	// 	);
    	// }

    	let hourOption = [];
    	for (let i = 1; i <= 36; i++) {
    		hourOption.push(
    			<Option value={i} index={i}>{i}小时</Option>
    		);
    	}

    	let title = modifyImmuno.isUpdate ? "修改风险决策免打扰" : "新建风险决策免打扰";
    	return (
    		<Modal
    			width={1200}
    			title={title}
    			visible={dialogShow.modifyImmuno}
    			maskClosable={false}
    			onOk={() => {
    				if (!modifyImmuno.isUpdate && checkFunctionHasPermission("ZB0301", "addDealTypeImmuno")) {
    					this.saveImmunoConfig();
    				} else if (modifyImmuno.isUpdate && checkFunctionHasPermission("ZB0301", "updateDealTypeImmuno")) {
    					this.saveImmunoConfig();
    				} else {
    					message.warning("无权限操作");
    				}
    			}}
    			onCancel={() => {
    				dispatch({
    					type: "policyDeal/setDialogShow",
    					payload: {
    						modifyImmuno: false
    					}
    				});
    			}}
    		>
    			<Form className="modal-form">
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={4} className="basic-info-title" />
    					<Col span={20}>当前策略集在指定维度与指定时间内不返回低级别风险决策</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={4} className="basic-info-title">选择应用：</Col>
    					<Col span={20}>
    						<Select
    							placeholder="请选择应用"
    							value={modifyImmuno.app || undefined}
    							onChange={(v) => {
    								this.changeDialogDataHandle("app", "select", v);
    								this.changeDialogDataHandle("eventId", "select", "");
    								this.changeApp(v);
    							}}
    							disabled={modifyImmuno.isUpdate}
    							showSearch
    							optionFilterProp="children"
    							dropdownMatchSelectWidth={false}
    						>
    							{
    								allMap &&
                                    allMap["appNames"] &&
                                    allMap["appNames"].map((item, index) => {
                                    	if (item.name !== "all") {
                                    		return (
                                    			<Option
                                    				value={item.name}
                                    				key={index}
                                    			>
                                    				{item.dName}
                                    			</Option>
                                    		);
                                    	}
                                    })
    							}
    						</Select>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={4} className="basic-info-title">策略集：</Col>
    					<Col span={20}>
    						<Select
    							placeholder="请选择策略集"
    							value={modifyImmuno.eventId || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "eventId", "select")}
    							disabled={modifyImmuno.isUpdate}
    							showSearch
    							optionFilterProp="children"
    							dropdownMatchSelectWidth={false}
    						>
    							{
    								policySet &&
                                    policySet.length > 0 &&
                                    policySet.map((v) => {
                                    	return (
                                    		<Option
                                    			value={v.eventId}
                                    			key={v.eventId}
                                    		>{v.name}</Option>
                                    	);
                                    })
    							}
    						</Select>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={4} className="basic-info-title">维度：</Col>
    					<Col span={20}>
    						<Select
    							placeholder="请选择维度"
    							value={modifyImmuno.immunoField || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "immunoField", "select")}
    							disabled={modifyImmuno.isUpdate}
    							showSearch
    							optionFilterProp="children"
    							dropdownMatchSelectWidth={false}
    						>
    							{
    								allMap &&
                                    allMap["ruleFieldList"] &&
                                    allMap["ruleFieldList"].map((item, index) => {
                                    	return (<Option value={item.name} index={index}>{item.dName}</Option>);
                                    })
    							}
    						</Select>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={4} className="basic-info-title">免打扰时间：</Col>
    					<Col span={20}>
    						<Select
    							placeholder="请选免打扰时间"
    							value={modifyImmuno.immunoTime || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "immunoTime", "select")}
    						>
    							{
    								hourOption
    							}
    						</Select>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={4} className="basic-info-title">免打扰说明：</Col>
    					<Col span={20}>
    						<TextArea
    							placeholder="请输入免打扰说明"
    							value={modifyImmuno.description || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "description", "input")}
    							autoSize
    						/>
    					</Col>
    				</Row>
    				<OneCondition />
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(ModifyImmuno);
