import { userAPI } from "@/services";

export default {
	namespace: "login",

	state: {},

	effects: {
		// 只调用登出接口
		*signOut({}, { call }) {
			if (process.env.SYS_ENV === "development") {
				return;
			}
			const response = yield call(userAPI.signOut);
			if (response) {
				sessionStorage.setItem("_csrf_", "");
				sessionStorage.clear();
				localStorage.clear();
			}
		},
		// 调用登出接口且跳转到登录页面
		*logout({}, { call, put }) {
			if (process.env.SYS_ENV === "development") {
				return;
			}
			const response = yield call(userAPI.signOut);
			if (response) {
			  yield put({
				  	type: "goLogin",
				  	payload: {
						homePage: true
					}
			  });
			}
		 },
		 // 只跳转到登录页面
		  *goLogin({payload}, { }) {
			if (process.env.SYS_ENV === "development") {
			   return;
			}
			sessionStorage.setItem("_csrf_", "");
			sessionStorage.clear();
			localStorage.clear();
			const { homePage } = payload || {};
			const {origin, pathname, search} = window.location || {};
			const callbackUrl = origin + pathname + encodeURIComponent(search);
			if (pathname !== "/user/login") {
				if (homePage) {
					window.location = "/user/login";
				} else {
					window.location = "/user/login?callbackUrl=" + callbackUrl;
				}
			}
		 }
	},

	reducers: {}
};
