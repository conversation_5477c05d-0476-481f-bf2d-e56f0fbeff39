import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, message, Row, Col, Input, Alert } from "antd";
import { indexEditorAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";

const TextArea = Input.TextArea;

class IndexVersionSubmitModal extends PureComponent {
	constructor(props) {
		super(props);
		this.indexVersionSubmit = this.indexVersionSubmit.bind(this);
	}

	indexVersionSubmit() {
		let { indexEditorStore, globalStore, indexRunningStore, dispatch } = this.props;
		let { userInfoMode } = globalStore;
		let { dialogData } = indexEditorStore;
		let { indexVersionSubmitData } = dialogData;
		let { id, description } = indexVersionSubmitData;

		if (!description) {
			// lang:发版描述不能为空
			message.warning(indexListLang.indexCommitModal("descriptionCannotEmptyTip"));
			return;
		}

		let params = {
			account: userInfoMode.account,
			id: id,
			description: description
		};
		indexEditorAPI.indexVersionSubmit(params).then(res => {
			if (res.success) {
				// lang:指标版本提交成功
				message.success(indexListLang.indexCommitModal("indexSubmitSuccessTip"));
				dispatch({
					type: "indexEditor/setDialogShow",
					payload: {
						indexVersionSubmit: false
					}
				});
				dispatch({
					type: "indexEditor/setDialogData",
					payload: {
						indexVersionSubmitData: {
							id: null,
							description: null
						}
					}
				});
				dispatch({
					type: "indexEditor/changeIndexStatus",
					payload: {
						id: id
					}
				});
				dispatch({
					type: "global/getAllMap"
				});

				// 重新获取列表数据
				let { currentApp } = globalStore;
				let { curPage: runCurPage, pageSize: runPageSize, searchParams: runSearchParams } = indexRunningStore;
				let appName;
				if (currentApp.name) {
					appName = currentApp.name;
				}
				runSearchParams["app"] = appName;
				dispatch({
					type: "indexRunning/getSalaxyList",
					payload: {
						curPage: runCurPage,
						pageSize: runPageSize,
						...runSearchParams
					}
				});

			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { indexEditorStore, dispatch } = this.props;
		let { dialogShow, dialogData, editIndexMap } = indexEditorStore;
		let { indexVersionSubmitData } = dialogData;
		let { id, description, needOffline, needOfflineMessage } = indexVersionSubmitData;
		let currentIndexObj = editIndexMap[id] ? editIndexMap[id] : null;

		return (
			<Modal
				title={indexListLang.indexCommitModal("title")} // lang:指标版本提交
				visible={dialogShow.indexVersionSubmit}
				maskClosable={false}
				onOk={this.indexVersionSubmit.bind(this)}
				className="index-version-modal-wrap"
				onCancel={() => {
					dispatch({
						type: "indexEditor/setDialogShow",
						payload: {
							indexVersionSubmit: false
						}
					});
					dispatch({
						type: "indexEditor/setDialogData",
						payload: {
							indexVersionSubmitData: {
								id: null,
								description: null
							}
						}
					});
				}}
			>
				<div className="basic-form">
					{
						needOffline &&
                        <Row
                        	gutter={CommonConstants.gutterSpan}
                        	style={{ height: "auto" }}
                        >
                        	<Col span={24}>
                        		<Alert
                        			message={needOfflineMessage}
                        			type="warning"
                        			showIcon
                        		/>
                        	</Col>
                        </Row>
					}

					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* lang:指标名称 */}
							{indexListLang.indexCommitModal("indexName")}：
						</Col>
						<Col span={18}>
							<Input
								type="text"
								placeholder={indexListLang.indexCommitModal("inputIndexName")} // lang:请输入指标名称
								value={currentIndexObj && currentIndexObj.name ? currentIndexObj.name : undefined}
								disabled={true}
							/>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
						<Col span={6} className="basic-info-title">
							{/* lang:发版描述 */}
							{indexListLang.indexCommitModal("description")}：
						</Col>
						<Col span={18}>
							<TextArea
								placeholder={indexListLang.indexCommitModal("inputReleaseDes")} // lang:请输入发版描述
								value={description || undefined}
								onChange={(e) => {
									indexVersionSubmitData["description"] = e.target.value;
									dispatch({
										type: "indexEditor/setDialogData",
										payload: {
											indexVersionSubmitData: indexVersionSubmitData
										}
									});
								}}
								rows={4}
							/>
						</Col>
					</Row>
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	indexRunningStore: state.indexRunning
}))(IndexVersionSubmitModal);
