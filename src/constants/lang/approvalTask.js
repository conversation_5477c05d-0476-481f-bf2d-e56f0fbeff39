import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		policyTitle1: {
			"cn": "策略审批任务",
			"en": "Policy Approval Task"
		},
		policyTitle2: {
			"cn": "策略审批任务列表",
			"en": "Policy Approval Task List"
		},
		indicatorTitle1: {
			"cn": "指标审批任务",
			"en": "Indicator Approval Task"
		},
		indicatorTitle2: {
			"cn": "指标审批任务列表",
			"en": "Indicator Approval Task List"
		},
		workflowTitle1: {
			"cn": "规则流审批任务",
			"en": "Workflow Approval Task"
		},
		workflowTitle2: {
			"cn": "规则流审批任务列表",
			"en": "Workflow Approval Task List"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		policyType: {
			"cn": "请选择策略类型",
			"en": "Select policy type"
		},
		policyNamePlaceholder: {
			"cn": "请输入策略名称",
			"en": "Enter policy name"
		},
		indicatorNamePlaceholder: {
			"cn": "请输入指标名称",
			"en": "Enter indicator name"
		},
		workflowNamePlaceholder: {
			"cn": "请输入规则流名称",
			"en": "Enter workflow name"
		},
		search: {
			"cn": "搜索",
			"en": "Search"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"operationType": {
			"cn": "变更类型",
			"en": "Operation Type"
		},
		"policyName": {
			"cn": "策略名称",
			"en": "Policy Name"
		},
		"policySetName": {
			"cn": "策略集",
			"en": "Policy Set Name"
		},
		"indicatorName": {
			"cn": "指标",
			"en": "Indicator Name"
		},
		"publishDesc": {
			"cn": "发版描述",
			"en": "Publish Description"
		},
		"createdBy": {
			"cn": "提交人",
			"en": "Created By"
		},
		"submitTime": {
			"cn": "提交时间",
			"en": "Submit Time"
		},
		"operation": {
			"cn": "操作",
			"en": "Operation"
		},
		"approval": {
			"cn": "审批",
			"en": "Approval"
		},
		"delete": {
			"cn": "删除",
			"en": "Delete"
		},
		"publish": {
			"cn": "发布",
			"en": "Publish"
		},
		"offline": {
			"cn": "下线",
			"en": "Offline"
		},
		"policyType": {
			"cn": "策略类型",
			"en": "Policy type"
		},
		"publicPolicy": {
			"cn": "公共策略",
			"en": "Public policy"
		},
		"normalPolicy": {
			"cn": "普通策略",
			"en": "Normal public policy"
		},
		"applyFillBack": {
			"cn": "回溯发布",
			"en": "Recall publish"
		}
	};
	return params[field][lang];
};

const modal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"policyTitle": {
			"cn": "策略版本审核",
			"en": "Policy Version Approval"
		},
		"indicatorTitle": {
			"cn": "指标版本审核",
			"en": "Indicator Version Approval"
		},
		"policyApproval": {
			"cn": "策略审批",
			"en": "Policy Approval"
		},
		"indicatorApproval": {
			"cn": "指标审批",
			"en": "Indicator Approval"
		},
		"workflowApproval": {
			"cn": "规则流审批",
			"en": "Workflow Approval"
		},
		"changeDetail": {
			"cn": "修改详情",
			"en": "Change Detail"
		},
		"approvalResult": {
			"cn": "审批结果",
			"en": "Approval Result"
		},
		"approvalReason": {
			"cn": "审批原因",
			"en": "Approval Reason"
		},
		"pass": {
			"cn": "通过",
			"en": "Pass"
		},
		"reject": {
			"cn": "拒绝",
			"en": "Reject"
		},
		"ok": {
			"cn": "确定",
			"en": "Ok"
		},
		"cancel": {
			"cn": "取消",
			"en": "Cancel"
		},
		"enterDescriptionPlaceholder": {
			"cn": "请输入审批描述",
			"en": "Please enter approval description"
		},
		"descriptionLengthTip": {
			"cn": "审批描述不能超过200个字符",
			"en": "The approval description cannot exceed 200 characters"
		},
		"policyVersionSubmitOk": {
			"cn": "策略版本提交成功",
			"en": "Policy version was submitted successfully"
		},
		"currentVersion": {
			"cn": "现在的版本",
			"en": "Current version"
		},
		"beforeVersion": {
			"cn": "之前的版本",
			"en": "Before version"
		},
		//	======================policy======================
		"base": {
			"cn": "基本设置",
			"en": "Base"
		},
		"rules": {
			"cn": "规则列表",
			"en": "Rules"
		},
		"policyName": {
			"cn": "策略名称",
			"en": "Policy Name"
		},
		"riskRange": {
			"cn": "风险阈值",
			"en": "Risk Range"
		},
		"description": {
			"cn": "描述",
			"en": "Description"
		},
		"effectScope": {
			"cn": "生效范围",
			"en": "Effect scope"
		},
		"customEffectScope": {
			"cn": "自定义生效范围",
			"en": "Custom effective range"
		},
		"riskType": {
			"cn": "风险类型",
			"en": "Risk Type"
		},
		"policyMode": {
			"cn": "策略模式",
			"en": "Policy Mode"
		},
		"interrupted": {
			"cn": "是否中断",
			"en": "Interrupted"
		},
		"noInterruption": {
			"cn": "不中断",
			"en": "No interruption"
		},
		"interruption": {
			"cn": "中断",
			"en": "interruption"
		},
		"interruptCondition": {
			"cn": "中断条件",
			"en": "Interruption condition"
		},
		"riskResult": {
			"cn": "风险决策结果",
			"en": "Risk decision results"
		},
		"custom": {
			"cn": "自定义",
			"en": "Custom"
		},
		// =======================
		"workflowTitle": {
			"cn": "规则流版本审核",
			"en": "Workflow Version Approval"
		},
		"policySetName": {
			"cn": "策略集",
			"en": "Policy Set"
		}
	};
	return params[field][lang];
};
const message = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"policyApprovalSuccessfully": {
			"cn": "策略审批成功",
			"en": "Policy approval successfully"
		},
		"indicatorApprovalSuccessfully": {
			"cn": "指标审批成功",
			"en": "Indicator approval successfully"
		},
		"workflowApprovalSuccessfully": {
			"cn": "规则流审批成功",
			"en": "Rule flow approval successfully"
		}
	};
	return params[field][lang];
};

export default {
	common,
	searchParams,
	table,
	modal,
	message
};
