import { PureComponent } from "react";
import { ItemPanel, Item } from "gg-editor";
import "./style/itemPanel.less";
import { ImagesConstants } from "@/constants";
import { workflowLang } from "@/constants/lang";

export default class FlowItemPanel extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { disabled } = this.props;
		return (
			<ItemPanel className="flow-item-panel">
				{
					disabled &&
					<div className="flow-item-panel-mask"></div>
				}
				<div className="flow-item-body">
					<Item
						type="node"
						size="50*50"
						shape="flow-start"
						model={{
							color: "#ca808b",
							label: {
								text: workflowLang.flowItemPanel("start"), // lang: 开始
								fill: "#fff"
							}
						}}
					>
						<div className="flow-item flow-circle flow-start">
							{/* lang: 开始*/}
							{workflowLang.flowItemPanel("start")}
						</div>
					</Item>
					<Item
						type="node"
						size="50*50"
						shape="flow-end"
						model={{
							color: "#748993",
							label: {
								text: workflowLang.flowItemPanel("end"), // lang:"结束",
								fill: "#fff"
							}
						}}
					>
						<div className="flow-item flow-circle flow-end">
							{/* lang: 结束*/}
							{workflowLang.flowItemPanel("end")}
						</div>
					</Item>
					<Item
						type="node"
						size="78*72"
						shape="flow-exclusivity"
						model={{
							color: "#f1bba9",
							label: {
								text: workflowLang.flowItemPanel("exclusiveStart")// lang:排他开始
							 }
						}}
						src={ImagesConstants.flowImages("exclusivity")}
					>
						<div className="flow-item flow-rhombus">
							{/* lang: 排他开始*/}
							{workflowLang.flowItemPanel("exclusiveStart")}
						</div>
					</Item>
					<Item
						type="node"
						size="78*72"
						shape="flow-parallel"
						model={{
							color: "#c6e5d6",
							label: {
								text: workflowLang.flowItemPanel("parallelStart") // lang:"并行开始",
							}
						}}
						src={ImagesConstants.flowImages("parallel")}
					>
						<div className="flow-item flow-rhombus">
							{/* lang: 并行开始*/}
							{workflowLang.flowItemPanel("parallelStart")}
						</div>
					</Item>
					<Item
						type="node"
						size="100*40"
						shape="flow-public-policy"
						model={{
							color: "#c4dfa7",
							label: {
								text: workflowLang.flowItemPanel("publicPolicy") // lang:"公共策略",
							}
						}}
					>
						<div className="flow-item flow-public-policy">
							{/* lang: 公共策略*/}
							{workflowLang.flowItemPanel("publicPolicy")}
						</div>
					</Item>
					<Item
						type="node"
						size="100*40"
						shape="flow-policy"
						model={{
							color: "#d9dffc",
							label: {
								text: workflowLang.flowItemPanel("policy") // lang:"策略",
							}
						}}
					>
						<div className="flow-item flow-policy">
							{/* lang: 策略*/}
							{workflowLang.flowItemPanel("policy")}
						</div>
					</Item>
					<Item
						type="node"
						size="100*40"
						shape="flow-model"
						model={{
							color: "#bdd9fc",
							label: {
								text: workflowLang.flowItemPanel("model") // lang:"模型",
							}
						}}
					>
						<div className="flow-item flow-model">
							{/* lang: 模型*/}
							{workflowLang.flowItemPanel("model")}
						</div>
					</Item>
					<Item
						type="node"
						size="100*40"
						shape="flow-data"
						model={{
							color: "#7ab6f1",
							label: {
								text: workflowLang.flowItemPanel("trilateral") // lang:"三方",
							}
						}}
					>
						<div className="flow-item flow-service">
							{/* lang: 三方*/}
							{workflowLang.flowItemPanel("trilateral")}
						</div>
					</Item>
				</div>
			</ItemPanel>
		);
	}
}
