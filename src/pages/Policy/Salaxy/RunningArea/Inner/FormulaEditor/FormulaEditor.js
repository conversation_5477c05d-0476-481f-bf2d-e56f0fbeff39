import { PureComponent } from "react";
import { connect } from "dva";
import { commonLang } from "@/constants/lang";
import { Input } from "antd";
import { formulaMethodList } from "@/constants/common";

const TextArea = Input.TextArea;

class FormulaEditor extends PureComponent {

    state = {
    	defaultCnCode: ""
    };

    constructor(props) {
    	super(props);
    }

    componentDidMount() {
    	let { indexData } = this.props;

    	let formulaCode = "";

    	if (indexData && indexData.attachFieldsObj && indexData.attachFieldsObj.formula) {
    		formulaCode = indexData.attachFieldsObj.formula;
    	}
    	let cnCode = this.enCodeToCn(formulaCode);

    	this.setState({
    		defaultCnCode: cnCode
    	});
    }

    enCodeToCn = (code) => {
    	let { globalStore } = this.props;
    	let { allMap } = globalStore;
    	let { ruleAndIndexFieldList = [] } = allMap;

    	let newFieldList = [];
    	let keywords = [];
    	ruleAndIndexFieldList &&
            ruleAndIndexFieldList.length > 0 &&
            ruleAndIndexFieldList.forEach(item => {
            	if (item.name && item.dName) {
            		if (item.type === "INT" || item.type === "DOUBLE") {
            			keywords.push(`@${item.name}`);

            			newFieldList.push({
            				name: `@[${commonLang.sourceName(item.sourceName)}]${item.dName}`,
            				value: `@${item.name}`
            			});
            		}
            	}
            });

    	// 正则替换函数名
    	keywords = formulaMethodList.reduce((pre, cur) => {
    		pre.push(cur.realValue);
    		return pre;
    	}, keywords || []);

    	keywords = keywords.sort((a, b) => b.length - a.length);

    	const invalid = /[°"§%()\[\]{}=\\?´`'<>,;.:]+/g;
    	// 正则替换关键词
    	let newCode = code.replace(
    		new RegExp(`(${keywords.join("|")})`.replace(invalid, ""), "g"),
    		function(match) {
    			let turnStr = match;
    			let hasFind = false;
    			newFieldList.forEach(item => {
    				if (item.value === match) {
    					turnStr = item.name;
    					hasFind = true;
    				}
    			});
    			if (!hasFind) {
    				formulaMethodList.forEach(item => {
    					if (item.realValue === match) {
    						turnStr = item.name;
    					}
    				});
    			}
    			return turnStr;
    		}
    	);

    	return newCode;
    };

    render() {
    	let { defaultCnCode } = this.state;
    	let { globalStore } = this.props;
    	let { allMap } = globalStore;
    	let { ruleAndIndexFieldList = [] } = allMap;

    	let newFieldList = [];
    	ruleAndIndexFieldList &&
            ruleAndIndexFieldList.length > 0 &&
            ruleAndIndexFieldList.forEach(item => {
            	if (item.name && item.dName) {
            		if (item.type === "INT" || item.type === "DOUBLE") {
            			newFieldList.push({
            				name: `[${commonLang.sourceName(item.sourceName)}]${item.dName}`,
            				value: item.name
            			});
            		}
            	}
            });

    	return (
    		<div className="ml-1">
    			<TextArea
    				rows={6}
    				value={defaultCnCode || undefined}
    				disabled={true}
    				autoSize={true}
    			/>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global
}))(FormulaEditor);

