import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Input, Icon, Select } from "antd";

const { Option, OptGroup } = Select;

class ChangeRuleForSelf extends PureComponent {
	constructor(props) {
		super(props);
		this.addChangeRuleItem = this.addChangeRuleItem.bind(this);
		this.changeRuleValue = this.changeRuleValue.bind(this);
		this.removeChangeRuleItem = this.removeChangeRuleItem.bind(this);
	}

	changeRuleValue(field, type, e) {
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		let { templateStore, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJ<PERSON>["params"] ? cfg<PERSON>son["params"] : [];
		let currentLine = params && params[operatorTemplateItemIndex] ? params[operatorTemplateItemIndex] : null;
		if (currentLine["willChangeLine"]) {
			currentLine["willChangeLine"][field] = value;

		}

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	removeChangeRuleItem() {
		let { templateStore, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let currentLine = params && params[operatorTemplateItemIndex] ? params[operatorTemplateItemIndex] : null;

		delete currentLine["willChangeLine"];

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	addChangeRuleItem() {
		let { templateStore, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let currentLine = params && params[operatorTemplateItemIndex] ? params[operatorTemplateItemIndex] : null;

		let ruleItemTemp = {
			name: null,
			changeValue: null,
			changeType: null
		};
		currentLine["willChangeLine"] = ruleItemTemp;

		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	render() {
		let { templateStore } = this.props;
		let { operatorTemplateItemIndex, dialogData: { modifyTemplateData } } = templateStore;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let currentLine = params && params[operatorTemplateItemIndex] ? params[operatorTemplateItemIndex] : null;
		let willChangeRule = currentLine && currentLine["willChangeLine"] && JSON.stringify(currentLine["willChangeLine"]) !== "{}" ? currentLine["willChangeLine"] : undefined;

		return (
			<Fragment>
				{
					(JSON.stringify(currentLine["willChangeLine"]) === "{}" || !currentLine["willChangeLine"]) &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>添加行控制规则：</span>
                    	</div>
                    	<div className="param-field">
                    		<span
                    			className="add-new-operation"
                    			onClick={this.addChangeRuleItem.bind(this)}
                    		>
                    			<Icon type="plus-square-o" />新增
                    		</span>
                    	</div>
                    </div>
				}
				{
					currentLine &&
                    currentLine["willChangeLine"] &&
                    willChangeRule &&
                    <div className="template-param-rule-list">
                    	<div className="template-param-rule-section">
                    		<div
                    			className="remove-rule-item"
                    			onClick={this.removeChangeRuleItem.bind(this)}
                    		>
                    			<Icon type="delete" />
                    		</div>
                    		<div className="template-param-rule-item">
                    			<div className="template-param-rule-title">
                    				<span>节点handle：</span>
                    			</div>
                    			<div className="template-param-rule-field">
                    				<Select
                    					placeholder="选择节点handle"
                    					value={willChangeRule.name || undefined}
                    					onChange={this.changeRuleValue.bind(this, "name", "select")}
                    				>
                    					{
                    						params &&
                                            params.filter((fItem, fIndex) => fIndex !== operatorTemplateItemIndex).map((paramItem, paramIndex) => {
                                            	return (
                                            		<OptGroup
                                            			label={paramItem.labelText || "暂无标题"}
                                            			key={paramIndex}
                                            		>
                                            			{
                                            				paramItem.children &&
                                                            paramItem.children.map((subItem, subIndex) => {
                                                            	return (
                                                            		<Option
                                                            			value={subItem.name || undefined}
                                                            			key={subIndex}
                                                            		>
                                                                        [{subItem.componentType}] {subItem.name}
                                                            		</Option>
                                                            	);
                                                            })
                                            			}
                                            		</OptGroup>
                                            	);
                                            })
                    					}
                    				</Select>
                    			</div>
                    		</div>

                    		{
                    			willChangeRule.name &&
                                <div className="template-param-rule-item">
                                	<div className="template-param-rule-title">
                                		<span>改变值：</span>
                                	</div>
                                	<div className="template-param-rule-field">
                                		<Input
                                			placeholder="请输入改变值"
                                			value={willChangeRule.changeValue || undefined}
                                			onChange={this.changeRuleValue.bind(this, "changeValue", "input")}
                                		/>
                                		<div>如果handle为改变值，则当前行显示</div>
                                	</div>
                                </div>
                    		}
                    	</div>
                    </div>
				}
			</Fragment>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(ChangeRuleForSelf);
