import { PureComponent } from "react";
import { connect } from "dva";
import { Icon } from "antd";

class FormulaTemplate extends PureComponent {
	constructor(props) {
		super(props);
		this.deleteChild = this.deleteChild.bind(this);
	}

	deleteChild(index) {
		let { templateStore, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"].splice(index, 1);
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	render() {
		let { childIndex } = this.props;

		return (
			<div className="single-param">
				<div className="single-param-header">
					<h2>计算公式</h2>
					<Icon
						type="delete"
						onClick={this.deleteChild.bind(this, childIndex)}
					/>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>计算公式需求：</span>
					</div>
					<div className="param-field">
                        计算公式专用模板，细节你不用管，因为没有细节。
					</div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(FormulaTemplate);
