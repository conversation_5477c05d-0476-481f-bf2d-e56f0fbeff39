import { PureComponent } from "react";
import { connect } from "dva";
import moment from "moment";
import { Table, Pagination, Spin, Tooltip } from "antd";
import { checkFunctionHasPermission } from "@/utils/permission";
import { immuneHistoryLang, commonLang } from "@/constants/lang";
import { ImmuneConstants } from "@/constants";
import ViewImmuneBtn from "../../Common/ViewImmuneBtn";

class ImmuneHistoryList extends PureComponent {
	getColumns = () => {
		return (
			[
				{
					title: immuneHistoryLang.table("ruleName"), // lang:"规则名称",
					dataIndex: "ruleName",
					key: "ruleName",
					width: 200,
					render: (text)=>{
						return (
							<Tooltip title={text}>
								<span
									className="text-overflow"
									style={{"maxWidth": "200px", "display": "inline-block"}}
								>{text}</span>
							</Tooltip>
						);
					}
				},
				{
					title: immuneHistoryLang.table("updateType"), // lang:"更新类型",
					dataIndex: "updateType",
					key: "updateType",
					width: 100,
					render: (text)=>{
						return (
							ImmuneConstants.getUpdateType().find(v=>{
								return v.value === text;
							}) || {}
						).name || "";
					}
				},
				{
					title: immuneHistoryLang.table("operaType"), // lang:"操作类型",
					dataIndex: "operationType",
					key: "operationType",
					width: 100,
					render: (text)=>{
						return (
							ImmuneConstants.getOperaType().find(v=>{
								return v.value === text;
							}) || {}
						).name || "";
					}
				},
				{
					title: immuneHistoryLang.table("effectTime"), // lang:"生效时间",
					render: (record)=>{
						let { snapshot } = record || {};
						if (snapshot) {
							snapshot = JSON.parse(snapshot);
							const { effectFrom } = snapshot;
							return effectFrom ? moment(effectFrom).format("YYYY-MM-DD HH:mm:ss") : "";
						}

					}
				},
				{
					title: immuneHistoryLang.table("failureTime"), // lang:"失效时间",
					render: (record)=>{
						let { snapshot } = record || {};
						if (snapshot) {
							snapshot = JSON.parse(snapshot);
							const { effectTo } = snapshot;
							return effectTo ? moment(effectTo).format("YYYY-MM-DD HH:mm:ss") : "";
						}

					}
				},
				{
					title: immuneHistoryLang.table("operaPerson"), // lang:"操作人",
					dataIndex: "createdBy",
					key: "createdBy",
					width: 100
				},
				{
					title: immuneHistoryLang.table("operaTime"), // lang:"操作时间",
					dataIndex: "gmtCreate",
					key: "gmtCreate"
				},
				{
					title: immuneHistoryLang.table("remarks"), // lang:"备注",
					width: 130,
					render: (record)=>{
						let { snapshot } = record || {};
						if (snapshot) {
							snapshot = JSON.parse(snapshot);
							let { remarks } = snapshot;
							if (remarks && remarks.length > 6) {
								remarks = <Tooltip title={remarks}>{remarks.slice(0, 6) + "..."}</Tooltip>;
							}
							return remarks;
						}
					}
				},
				{
					title: immuneHistoryLang.table("operation"), // lang: "操作",
					width: 70,
					render: (record, index) => {
						const isDelete = record.operationType === "delete";
						const viewTip = isDelete ? immuneHistoryLang.tooltip("viewDeleteImmune") : immuneHistoryLang.tooltip("viewHistoryImmune");
						let { snapshot, createdBy, gmtCreate, updateType } = record || {};
						snapshot = snapshot && JSON.parse(snapshot);
						updateType = (ImmuneConstants.getUpdateType().find(v=>{
							return v.value === updateType;
						}) || {}
						).name || "";
						const operaData = {
							createdBy,
							gmtCreate,
							updateType
						};
						return (
							<div className="operation-box" key={index}>
								{/* lang:查看 */}
								<Tooltip title={viewTip}>
									<ViewImmuneBtn
										operaData={operaData}
										openType="view"
										className={isDelete ? "a-disabled" : ""}
										record={snapshot}
										permission={checkFunctionHasPermission("ZB0106", "RuleImmunoLogDetail")}
										noRightTip={commonLang.messageInfo("noPermission")}
									>
										<i className="salaxy-iconfont salaxy-view1" style={{"fontSize": "18px"}}></i>
									</ViewImmuneBtn>
								</Tooltip>
							</div>
						);
					}
				}
			]
		);
	}
	render() {
		const {immuneHistoryStore = {}, paginationOnChange} = this.props;
		const { immuneHistoryListLoad, immuneHistoryList, total, curPage, pageSize } = immuneHistoryStore;
		const columns = this.getColumns();
		return (
			<Spin spinning={immuneHistoryListLoad}>
				<div className="page-global-body-main has-table-border mt10">
					<Table
						className="table-out-border"
						columns={columns}
						pagination={false}
						dataSource={immuneHistoryList}
						rowKey="id"
					/>
					<div className="page-global-body-pagination">
						{/* lang:共x条记录 */}
						<span className="count">{commonLang.getRecords(total)}</span>
						<Pagination
							showSizeChanger
							onChange={paginationOnChange}
							onShowSizeChange={paginationOnChange}
							defaultCurrent={1}
							total={total}
							current={curPage}
							pageSize={pageSize}
						/>
					</div>
				</div>
			</Spin>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	immuneHistoryStore: state.immuneHistory
}))(ImmuneHistoryList);

