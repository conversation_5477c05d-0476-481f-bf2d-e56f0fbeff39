import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Input, Select, Row, Col, Radio } from "antd";
import { CommonConstants, PolicyConstants } from "@/constants";
import WeightModeEditor from "@/components/WeightModeEditor";
import EffectScopeTable from "@/components/PublicPolicy/EffectScope/EffectScopeTable";
import { approvalTaskLang } from "@/constants/lang";
const { countOperatorIntMap } = PolicyConstants;

const { TextArea } = Input;
const Option = Select.Option;
const InputGroup = Input.Group;

class PolicyApprovalDiffBase extends PureComponent {
	state = {
		global_app: []
	}
	constructor(props) {
		super(props);
		this.changeDiffNav = this.changeDiffNav.bind(this);
	}
	componentWillMount() {
		let { globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		const global_app = publicPolicyScene && publicPolicyScene.length > 0 && publicPolicyScene.find((v) => v.name === "GLOBAL_APP") || {}; // 全局应用
		this.setState({
			global_app
		});
	}
	changeDiffNav(index) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;

		policyApprovalData.tabIndex = index;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				policyApprovalData: policyApprovalData
			}
		});
	}

	render() {
		let { approvalTaskStore, globalStore } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;
		let { diffDetail, diffTime, publicPolicy } = policyApprovalData;
		let { onlineVersion, pendVersion } = diffDetail;

		let baseInfo = null;
		if (diffTime === "online") {
			baseInfo = onlineVersion;
		} else if (diffTime === "pend") {
			baseInfo = pendVersion;
		}
		// 公共策略下
		// 有效范围
		const { allMap, personalMode } = globalStore;
		const { lang } = personalMode;
		const { publicPolicyRiskSelect = [], dealTypeList = [] } = allMap || {};
		const { global_app } = this.state;
		let [scene, effectScope, terminate] = ["", "", "0"];
		if (publicPolicy && baseInfo && Object.keys(baseInfo).length > 0) {
			effectScope = baseInfo.riskEventId.indexOf("GLOBAL_APP") > -1 ? "GLOBAL_APP" : "set";
			scene = baseInfo.scene;
			terminate = baseInfo.terminate ? "1" : "0";
			if (typeof scene === "string") {
				scene = JSON.parse(scene);
			}
		}

		return (
			<div className="basic-form mb20">
				{
					baseInfo &&
                    baseInfo.name &&
                    <Row gutter={CommonConstants.gutterSpan}>
                    	<Col span={3} className="basic-info-title">
                    		{/* lang:策略名称 */}
                    		{approvalTaskLang.modal("policyName")}：
                    	</Col>
                    	<Col span={12}>
                    		<Input
                    			type="text"
                    			value={baseInfo.name}
                    			disabled={true}
                    		/>
                    	</Col>
                    </Row>
				}
				{
					publicPolicy &&
					<Fragment>
						{
							effectScope &&
							<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
								<Col span={3} className="basic-info-title">
									{/* lang:生效范围 */}
									{approvalTaskLang.modal("effectScope")}：
								</Col>
								<Col span={12}>
									<Select
										value={effectScope}
										disabled
									>
										{
											global_app &&
										<Option value={global_app.name}>{global_app.dName}</Option>
										}
										{/* 自定义 */}
										<Option value="set">{approvalTaskLang.modal("custom")}</Option>
									</Select>
								</Col>
							</Row>
						}
						{
							effectScope === "set" &&
							<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
								<Col span={3} className="basic-info-title">
									{/* lang:自定义生效范围 */}
									{approvalTaskLang.modal("customEffectScope")}：
								</Col>
								<Col span={12}>
									<EffectScopeTable
										scene={scene}
										styleCss={{"maxWidth": "100%"}}
									/>
								</Col>
							</Row>
						}
						{
							baseInfo &&
							baseInfo.riskType &&
							<Row gutter={CommonConstants.gutterSpan}>
								<Col span={3} className="basic-info-title" style={{ height: "auto" }}>
									{/* lang:风险类型 */}
									{approvalTaskLang.modal("riskType")}：
								</Col>
								<Col span={12}>
									<Select
										value={baseInfo.riskType || undefined}
										disabled
										name="riskType"
									>
										{
											publicPolicyRiskSelect &&
										publicPolicyRiskSelect.map((item, index) => {
											return (
												<Option value={item.name} key={index}>
													{item.dName}
												</Option>
											);
										})
										}
									</Select>
								</Col>
							</Row>
						}

						{
							!(String(terminate) === "0" || String(terminate) === "1") &&
							<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
								<Col span={3} className="basic-info-title">
									{/* lang:是否中断 */}
									{approvalTaskLang.modal("interrupted")}：
								</Col>
								<Col span={12}>
									<Radio.Group
										disabled
										value={String(terminate)}
									>
										{/* 不中断 */}
										<Radio value="0">{approvalTaskLang.modal("noInterruption")}</Radio>
										{/* 中断 */}
										<Radio value="1">{approvalTaskLang.modal("interruption")}</Radio>
									</Radio.Group>
								</Col>
							</Row>
						}

						{
							String(terminate) === "1" &&
							<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
								<Col span={3} className="basic-info-title">
									{/* lang:中断条件 */}
									{approvalTaskLang.modal("interruptCondition")}：
								</Col>
								<Col span={12}>
									<InputGroup compact={true}>
										{/* 风险决策结果 */}
										<Input
											value={approvalTaskLang.modal("riskResult")}
											style={{ "width": "25%" }}
											disabled
										/>
										<Select
											disabled
											style={{ "width": "35%" }}
											value={baseInfo.terminateOperator || undefined}
										>
											{
												countOperatorIntMap.map((v) => {
													return <Option value={v.name}>{lang === "en" ? v.enDName : v.dName}</Option>;
												})
											}
										</Select>
										<Select
											disabled
											style={{ "width": "40%" }}
											value={baseInfo.terminateThreshold || undefined}
										>
											{
												dealTypeList &&
												dealTypeList.length > 0 &&
												dealTypeList.map((v) => {
													return <Option value={v.name}>{lang === "en" ? v.enDName : v.dName}</Option>;
												})
											}
										</Select>
									</InputGroup>
								</Col>
							</Row>
						}
					</Fragment>
				}
				{
					baseInfo &&
                    baseInfo["dealTypeMapping"] &&
                    baseInfo["dealTypeMapping"].length > 0 &&
                    <Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
                    	<Col span={3} className="basic-info-title">
                    		{/* lang:风险阈值 */}
                    		{approvalTaskLang.modal("riskRange")}：
                    	</Col>
                    	<Col span={12}>
                    		<WeightModeEditor
                    			dealTypeMappings={baseInfo["dealTypeMapping"]}
                    			page="basicPage"
                    			disabled={true}
                    		/>
                    	</Col>
                    </Row>
				}
				{

					baseInfo &&
                    baseInfo.description &&
                    <Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
                    	<Col span={3} className="basic-info-title">
                    		{/* lang:描述 */}
                    		{approvalTaskLang.modal("description")}：
                    	</Col>
                    	<Col span={12}>
                    		<TextArea
                    			rows={4}
                    			value={baseInfo["description"]}
                    			disabled={true}
                    		/>
                    	</Col>
                    </Row>
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(PolicyApprovalDiffBase);
