import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Button, Row, Col, message, Tooltip } from "antd";
import { indexEditorAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import ImportMode from "@/components/ImportModal/FileImportCondition/ImportMode";
import SelectScene from "@/components/ImportModal/FileImportCondition/SelectScene";
import FileImportResult from "@/components/ImportModal/FileImportResult";
import { cloneDeep } from "lodash";

class IndexImportModal extends PureComponent {
	state = {
		hasSuccess: false,
		successData: [],
		importLoad: false
	};

	constructor(props) {
		super(props);
		this.importIndex = this.importIndex.bind(this);
		this.closeModal = this.closeModal.bind(this);
	}

	changeImport = (e, { type, valueType = "input" }) => {
		let value = "";
		if (valueType === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		let { indexEditorStore, dispatch } = this.props;
		let { dialogData } = indexEditorStore;
		let { indexImportData } = dialogData;
		const newIndexImportData = cloneDeep(indexImportData);

		newIndexImportData[type] = value;
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				indexImportData: newIndexImportData
			}
		});
	}

	importIndex = async () => {
		let { indexEditorStore, dispatch, globalStore } = this.props;
		let { currentApp } = globalStore;
		let { curPage, pageSize, searchParams } = indexEditorStore;

		let { dialogData } = indexEditorStore;
		let { indexImportData } = dialogData;
		if (!indexImportData["zbMode"]) {
			// lang:请选择指标导入模式
			message.warning(indexListLang.importIndexModal("zbModeRequired"));
			return;
		}
		if (!indexImportData["file"]) {
			// lang:请选择要上传的文件
			message.warning(indexListLang.importIndexModal("selectFileFirst"));
			return;
		}
		const newIndexImportData = cloneDeep(indexImportData);
		if (!newIndexImportData.replaceScene) {
			delete newIndexImportData.replaceScene;
		} else {
			newIndexImportData.replaceScene = JSON.stringify(newIndexImportData.replaceScene);
		}
		await this.setState({
			importLoad: true
		});
		await indexEditorAPI.importIndex(newIndexImportData).then(res => {
			this.setState({
				importLoad: false
			});
			if (res.success) {
				this.refs.indexImportFile.value = "";
				this.setState({
					hasSuccess: true,
					successData: res.data
				});
				// 导入成功后
				// 1.重新刷新编辑区指标列表
				let appName;
				if (currentApp.name) {
					appName = currentApp.name;
				}
				searchParams["app"] = appName;
				dispatch({
					type: "indexEditor/getSalaxyList",
					payload: {
						curPage: curPage,
						pageSize: pageSize,
						...searchParams
					}
				});
				// 2.刷新allMap
				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
				// 3.清空每个策略详情
				dispatch({
					type: "indexEditor/setAttrValue",
					payload: {
						indexActiveKey: [],
						currentIndexId: null,
						indexActiveKeyLoading: false,
						currentLoadingId: null,
						indexActiveIndex: null,
						editIndexMap: {}
					}
				});
			} else {
				message.error(res.message);
				this.setState({
					importLoad: false
				});
			}
		}).catch(err => {
			console.log(err);
		});

	}

	closeModal() {
		let { dispatch } = this.props;
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				indexImport: false
			}
		});
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				indexImportData: {
					importMode: "SKIP",
					file: null,
					replaceScene: "",
					zbMode: ""
				}
			}
		});
		if (this.refs.indexImportFile && this.refs.indexImportFile.value) {
			this.refs.indexImportFile.value = "";
		}
		this.setState({
			hasSuccess: false,
			successData: [],
			importLoad: false
		});
	}

	render() {
		let { indexEditorStore, dispatch } = this.props;
		let { successData, hasSuccess, importLoad } = this.state;
		let { dialogShow, dialogData } = indexEditorStore;
		let { indexImportData } = dialogData;
		const { replaceScene } = indexImportData || {};
		let footerButton = [];
		if (hasSuccess) {
			footerButton = [
				<Button type="primary" onClick={this.closeModal.bind(this)}>
					{/* lang:确定*/}
					{indexListLang.importIndexModal("ok")}
				</Button>
			];
		} else {
			footerButton = [
				<Button onClick={this.closeModal.bind(this)}>
					{/* lang:取消 */}
					{indexListLang.importIndexModal("cancel")}
				</Button>,
				<Button
					type="primary"
					onClick={this.importIndex.bind(this)}
					disabled={importLoad}
					loading={importLoad}
				>
					{/* lang:上传 */}
					{indexListLang.importIndexModal("upload")}
				</Button>
			];
		}

		return (
			<Modal
				title={indexListLang.importIndexModal("indexImport")} // lang:指标导入
				visible={dialogShow.indexImport}
				maskClosable={false}
				className="index-import-modal-wrap"
				onCancel={this.closeModal.bind(this)}
				footer={footerButton}
				width={700}
			>
				<div className="basic-form s2">
					{
						!hasSuccess &&
						<div>
							{/* 导入场景 */}
							<ImportMode
								changeImport={this.changeImport}
								data={indexImportData}
								showMode={{ "zbMode": true }}
							/>
							<Row gutter={CommonConstants.gutterSpan}>
								<Col span={5} className="basic-info-title line-height-32">
									{/* lang:选择文件 */}
									{indexListLang.importIndexModal("selectFile")}：
                        		</Col>
								<Col span={19}>
									{/* lang:文件大小在100M以内 */}
									<Tooltip title={indexListLang.importIndexModal("fileSizeTip")}>
										<a className="file line-height-32">
											{/* lang:选择文件 */}
											{indexListLang.importIndexModal("selectFile")}
											<input
												type="file"
												ref="indexImportFile"
												onChange={(e) => {
													let file = e.target && e.target.files && e.target.files[0] ? e.target.files[0] : undefined;
													if (indexImportData["file"]) {
														if (!file) {
															return;
														}
													} else {
														if (!file) {
															// lang:请先选择文件
															message.warning(indexListLang.importIndexModal("selectFileFirst"));
															return;
														}
													}
													let filePath = file.name;
													let fileSize = file.size / 1024;
													let reg = new RegExp(".(zb)$", "i");
													if (!reg.test(filePath)) { // 校验不通过
														// lang:只允许上传zb格式的文件
														message.warning(indexListLang.importIndexModal("fileAllowedTip"));
														return;
													}
													if (fileSize > 100000) {
														// lang:文件大小在100M以内
														message.warning(indexListLang.importIndexModal("fileSizeTip"));
														return;
													}

													indexImportData["file"] = file;
													dispatch({
														type: "indexEditor/setDialogData",
														payload: {
															indexImportData: indexImportData
														}
													});
												}}
											/>
										</a>
									</Tooltip>
									<div className="mb10">
										{indexImportData && indexImportData["file"] ? indexImportData["file"]["name"] : undefined}
									</div>
								</Col>
							</Row>
							{/* 选择场景 */}
							<SelectScene
								changeImport={this.changeImport}
								data={indexImportData}
								type="index"
							/>
						</div>
					}
					{
						hasSuccess &&
						<FileImportResult
							type="index"
							showMode={{ "zbMode": true }}
							successData={successData}
							replaceScene={replaceScene}
						/>
					}
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor
}))(IndexImportModal);
