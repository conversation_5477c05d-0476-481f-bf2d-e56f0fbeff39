import { PureComponent } from "react";
import { connect } from "dva";
import { Link } from "dva/router";
import { Layout } from "antd";

const { Sider } = Layout;

class BasicSideMenu extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { menuList, globalStore } = this.props;
		let { personalMode } = globalStore;
		let { lang } = personalMode;

		return (
			<Sider
				collapsible
				breakpoint="md"
				className="basic-sider"
				collapsed={false}
				width={220}
				trigger={null}
			>
				<div className="logo-shadow-mask"></div>
				<div className="logo">
					<div className="logo-icon">
						<img
							alt="logo"
							src="/salaxy-resource/logo/logo11-white.svg"
							style={{ opacity: 0.85 }}
						/>
					</div>
					<span>
						{lang === "en" ? "SALAXY" : "策略中心"}
					</span>
				</div>

				<div className="side-menu-warp">
					{
						menuList.map((item, index) => {
							let itemPath;
							let subMenu = item.children.map((subItem, subIndex) => {
								if (subItem.notRender) {
									return;
								}
								itemPath = `/${item.path}/${subItem.path || ""}`.replace(/\/+/g, "/");
								return <li
									className={itemPath === this.props.location.pathname ? "side-menu-item active" : "side-menu-item"}
									key={subIndex}>
									<Link
										to={itemPath}
										target={subItem.target}
										replace={itemPath === this.props.location.pathname}
									>
										<i className={subItem.icon ? "iconfont icon-" + subItem.icon : "iconfont icon-puzzle"}></i>
										<span>{personalMode.lang === "en" ? subItem.enName : subItem.name}</span>
									</Link>
								</li>;
							});
							return <div className="side-menu-section" key={index}>
								<h2 key={index}>
									{
										personalMode.lang === "en" ? item.enName : item.name
									}
								</h2>
								<ul className="side-menu-list">{subMenu}</ul>
							</div>;
						})
					}
				</div>
			</Sider>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(BasicSideMenu);
