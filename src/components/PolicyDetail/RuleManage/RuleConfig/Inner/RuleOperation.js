import React from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Row, Col } from "antd";
import { CommonConstants } from "@/constants";
import { policyDetailLang } from "@/constants/lang";
import { judgeExistVal } from "@/utils/utils";

const Option = Select.Option;
const InputGroup = Input.Group;

class RuleOperation extends React.PureComponent {
	constructor(props) {
		super(props);
		this.addOperationActions = this.addOperationActions.bind(this);
		this.deleteOperationAction = this.deleteOperationAction.bind(this);
		this.changeOperationField = this.changeOperationField.bind(this);
	}

	addOperationActions() {
		let { policyDetailStore, dispatch, ruleIndexArr } = this.props;
		let { policyRules } = policyDetailStore;
		let operationTemplate = {
			left: null,
			leftFieldType: null,
			right: null,
			rightType: "constant"
		};
		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["operationActions"].push(operationTemplate);
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["operationActions"].push(operationTemplate);
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		console.log(policyRules);
		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	changeOperationField(field, type, index, e) {
		let { policyDetailStore, globalStore, dispatch, ruleIndexArr } = this.props;
		let { policyRules } = policyDetailStore;
		let { allMap } = globalStore;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		let operationActions = "";
		if (ruleIndexArr.length === 1) {
			operationActions = policyRules[ruleIndexArr[0]]["operationActions"];
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			operationActions = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["operationActions"];
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}
		operationActions[index][field] = value;

		let mapItem = allMap && allMap["ruleFieldList"] && allMap["ruleFieldList"].filter(item => item.name === value)[0];
		console.log(mapItem);
		if (field === "left") {
			operationActions[index]["leftFieldType"] = mapItem.type ? mapItem.type : "STRING";
			operationActions[index]["right"] = null;
		} else if (field === "rightType") {
			operationActions[index]["right"] = null;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	deleteOperationAction(index) {
		let { policyDetailStore, dispatch, ruleIndexArr } = this.props;
		let { policyRules } = policyDetailStore;
		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["operationActions"].splice(index, 1);
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["operationActions"].splice(index, 1);
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	render() {
		let { policyDetailStore, globalStore, ruleIndexArr } = this.props;
		let { policyRules } = policyDetailStore;
		let { allMap } = globalStore;
		let currentRule = null;
		if (ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
		} else if (ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
		}
		console.log(currentRule);

		return (
			<div>
				<Row gutter={CommonConstants.gutterSpan}>
					<Col span={4} className="basic-info-title"></Col>
					<Col span={20} className="add-new-operation">
						<span onClick={this.addOperationActions.bind(this)}>
							<span><Icon type="plus-square-o" />
								{/* 新增操作 */}
								{policyDetailLang.ruleOperation("insertOperation")}
							</span>
						</span>
					</Col>
				</Row>
				{
					currentRule &&
					currentRule.operationActions &&
					currentRule.operationActions.map((item, index) => {
						let left = judgeExistVal(
							item.left || undefined,
							allMap && allMap["ruleFieldList"]
						);
						let right = judgeExistVal(
							item.right || undefined,
							allMap && allMap["ruleFieldList"]
						);
						return (
							<Row gutter={CommonConstants.gutterSpan} className="rule-operation-item" key={index}>
								<Col span={4} className="basic-info-title">
									{/* 设置： */}
									{policyDetailLang.ruleOperation("setting")}：
    							</Col>
								<Col span={5}>
									<Select
										// item.left || undefined
										value={left}
										placeholder={policyDetailLang.ruleOperation("select")} // 请选择
										onChange={this.changeOperationField.bind(this, "left", "select", index)}
										showSearch
										optionFilterProp="children"
										dropdownMatchSelectWidth={false}
									>
										{
											allMap && allMap["ruleFieldList"] && allMap["ruleFieldList"].map((item, index) => {
												return (
													<Option value={item.name} key={index} title={item.dName}>
														{
															item.dName
														}
													</Option>
												);
											})
										}
									</Select>
								</Col>
								<Col span={3} className="basic-info-text">
									{/* 等于 */}
									<Select value={policyDetailLang.ruleOperation("equal")} disabled={true}></Select>
								</Col>
								<Col span={8} className="basic-info-text">
									{
										item.rightType === "constant" &&
                                        <InputGroup compact>
                                        	<Select
                                        		value={item["rightType"] || undefined}
                                        		style={{ width: "30%" }}
                                        		placeholder={policyDetailLang.ruleOperation("select")} // 请选择
                                        		onChange={this.changeOperationField.bind(this, "rightType", "select", index)}
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		<Option value="constant">
                                        			{/* 常量 */}
                                        			{policyDetailLang.ruleOperation("constant")}
                                        		</Option>
                                        		<Option value="variable">
                                        			{/* 变量 */}
                                        			{policyDetailLang.ruleOperation("variable")}
                                        		</Option>
                                        	</Select>
                                        	<Input
                                        		style={{ width: "70%", textAlign: "left" }}
                                        		value={item.right || undefined}
                                        		placeholder={policyDetailLang.ruleOperation("inputConstant")} // 请填写常量内容
                                        		onChange={this.changeOperationField.bind(this, "right", "input", index)}
                                        	/>
                                        </InputGroup>
									}
									{
										item.rightType === "variable" &&
                                        <InputGroup compact>
                                        	<Select
                                        		value={item["rightType"] || undefined}
                                        		style={{ width: "30%" }}
                                        		placeholder={policyDetailLang.ruleOperation("select")} // 请选择
                                        		onChange={this.changeOperationField.bind(this, "rightType", "select", index)}
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		<Option value="constant">
                                        			{/* 常量 */}
                                        			{policyDetailLang.ruleOperation("constant")}
                                        		</Option>
                                        		<Option value="variable">
                                        			{/* 变量 */}
                                        			{policyDetailLang.ruleOperation("variable")}
                                        		</Option>
                                        	</Select>
                                        	<Select
                                        		style={{ width: "70%" }}
                                        		// item.right || undefined
                                        		value={right}
                                        		placeholder={policyDetailLang.ruleOperation("select")} // 请选择
                                        		onChange={this.changeOperationField.bind(this, "right", "select", index)}
                                        		showSearch
                                        		optionFilterProp="children"
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["ruleFieldList"] &&
                                                    allMap["ruleFieldList"].filter(fItem => item.leftFieldType ? fItem.type === item.leftFieldType : true).map((item, index) => {
                                                    	return (
                                                    		<Option value={item.name} key={index} title={item.dName}>
                                                    			{policyDetailLang.ruleOperation("current")} {item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </InputGroup>
									}
								</Col>
								<Col span={3} className="basic-info-oper">
									{/* 删除当前行 */}
									<Tooltip title={policyDetailLang.ruleOperation("deleteLine")} placement="right">
										<Icon
											className="delete"
											type="delete"
											onClick={this.deleteOperationAction.bind(this, index)}
										/>
									</Tooltip>
								</Col>
							</Row>
						);
					})
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(RuleOperation);
