/*
 * @Author: liu<PERSON>
 * @CreatDate: 2018-10-08 10:40:01
 * @Describe: 长时间登陆无任何操作，弹框提示并跳转登陆页
 */

import { PureComponent } from "react";
import { Modal, Button } from "antd";
import { connect } from "dva";
import moment from "moment";
import Cookies from "universal-cookie";

import "./index.less";

class NoOperate extends PureComponent {

    state = {
    	setTime: 1800000, // 暂定30分钟
    	visible: false
    };
    constructor(props) {
    	super(props);
    	this.listenner = this.listenner.bind(this);
    	this.removeListener = this.removeListener.bind(this);
    }

    componentDidMount() {
    	this.listenner(); // 初始化设定
    	document.body.addEventListener("click", this.listenner);
    	document.body.addEventListener("keydown", this.listenner);
    	document.body.addEventListener("mousemove", this.listenner);
    	document.body.addEventListener("mousewheel", this.listenner);
    }

    componentWillUnmount() {
    	this.removeListener();
    }

    removeListener() {
    	document.body.removeEventListener("click", this.listenner);
    	document.body.removeEventListener("keydown", this.listenner);
    	document.body.removeEventListener("mousemove", this.listenner);
    	document.body.removeEventListener("mousewheel", this.listenner);
    	clearInterval(this.timer);
    }

    listenner() {
    	const { setTime } = this.state;
    	const { dispatch } = this.props;
    	clearInterval(this.timer);
    	localStorage.curTime = moment().valueOf();
    	// 设置定时器
    	this.timer = setInterval(() => {
    		const nowTime = moment().valueOf();
    		const durTime = nowTime - parseInt(localStorage.curTime, 10);
    		if (durTime >= setTime) {
    			dispatch({
    				type: "login/signOut"
    			});
    			this.removeListener();
    			this.setState({
    				visible: true
    			});
    		}
    	}, 1000);
    }

    goToLogin() {
    	const { dispatch } = this.props;
    	dispatch({
    		type: "login/goLogin"
    	});
    }

    render() {
    	return (
    		<div className="m-no-operate">
    			<Modal
    				className="m-no-operate-modal"
    				visible={this.state.visible}
    				closable={false}
    				footer={null}
    				width={450}
    			>
    				<div className="u-pic"></div>
    				<p>您长时间未执行操作，系统自动退出了哦~</p>
    				<p>点击确定重新登陆</p>
    				<div className="btn"><Button type="primary" onClick={this.goToLogin.bind(this)}>确定</Button></div>
    			</Modal>
    		</div>
    	);
    }
}

export default connect(state => ({
	loginStore: state.login
}))(NoOperate);
