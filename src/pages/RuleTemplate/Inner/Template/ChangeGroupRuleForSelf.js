import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Select } from "antd";

const { Option, OptGroup } = Select;

class ChangeGroupRuleForSelf extends PureComponent {
	constructor(props) {
		super(props);
		this.addChangeRuleItem = this.addChangeRuleItem.bind(this);
		this.addChangeRuleCaseItem = this.addChangeRuleCaseItem.bind(this);
		this.changeRuleBaseValue = this.changeRuleBaseValue.bind(this);
		this.changeRuleCaseValue = this.changeRuleCaseValue.bind(this);
		this.removeChangeRuleItem = this.removeChangeRuleItem.bind(this);
		this.removeChangeRuleCaseItem = this.removeChangeRuleCaseItem.bind(this);
	}

	changeRuleBaseValue(field, type, e) {
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		let { templateStore, childIndex, dispatch, groupItemIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam = currentParam.children[groupItemIndex];

		currentParam["willChangeSelf"][field] = value;

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	changeRuleCaseValue(index, field, type, e) {
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		let { templateStore, childIndex, groupItemIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam = currentParam.children[groupItemIndex];

		currentParam["willChangeSelf"]["caseList"][index][field] = value;

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	removeChangeRuleItem() {
		let { templateStore, childIndex, dispatch, groupItemIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam = currentParam.children[groupItemIndex];

		delete currentParam["willChangeSelf"];

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	removeChangeRuleCaseItem(index) {
		let { templateStore, childIndex, groupItemIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam = currentParam.children[groupItemIndex];

		currentParam["willChangeSelf"]["caseList"].splice(index, 1);

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	addChangeRuleItem() {
		let { templateStore, childIndex, groupItemIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};
		currentParam = currentParam.children[groupItemIndex];
		let ruleItemTemp = {
			name: null,
			changeMode: null,
			caseList: [
				{
					modeValueList: [],
					changeType: null,
					mapName: null,
					mapLocation: null
				}
			]
		};
		currentParam["willChangeSelf"] = ruleItemTemp;

		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	addChangeRuleCaseItem() {
		let { templateStore, childIndex, dispatch, groupItemIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam = currentParam.children[groupItemIndex];

		let ruleCaseTemp = {
			modeValueList: [],
			changeType: null,
			selectMap: null,
			mapLocation: null
		};
		if (currentParam["willChangeSelf"]) {
			if (currentParam["willChangeSelf"]["caseList"]) {
				currentParam["willChangeSelf"]["caseList"].push(ruleCaseTemp);
			} else {
				currentParam["willChangeSelf"]["caseList"] = [ruleCaseTemp];
			}
		}

		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	render() {
		// childIndex 第n个群组
		// groupItemIndex 群组中的第n个
		let { templateStore, childIndex, groupItemIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam = children[childIndex].children[groupItemIndex];
		params = children;

		let willChangeRule = JSON.stringify(currentParam["willChangeSelf"]) !== "{}" ? currentParam["willChangeSelf"] : undefined;
		return (
			<div>
				{
					(JSON.stringify(currentParam["willChangeSelf"]) === "{}" || !currentParam["willChangeSelf"]) &&
                    <div className="single-param-item">
                    	<div className="param-title">
                    		<span>添加自身规则：</span>
                    	</div>
                    	<div className="param-field">
                    		<span
                    			className="add-new-operation"
                    			onClick={this.addChangeRuleItem.bind(this)}
                    		>
                    			<Icon type="plus-square-o" />新增
                    		</span>
                    	</div>
                    </div>
				}
				{
					currentParam["willChangeSelf"] &&
                    willChangeRule &&
                    <div className="template-param-rule-list">
                    	<div className="template-param-rule-section">
                    		<div
                    			className="remove-rule-item"
                    			onClick={this.removeChangeRuleItem.bind(this)}
                    		>
                    			<Icon type="delete" />
                    		</div>
                    		<div className="template-param-rule-item">
                    			<div className="template-param-rule-title">
                    				<span>节点handle：</span>
                    			</div>
                    			<div className="template-param-rule-field">
                    				<Select
                    					placeholder="选择节点handle"
                    					value={willChangeRule.name || undefined}
                    					onChange={this.changeRuleBaseValue.bind(this, "name", "select")}
                    				>
                    					{
                    						params &&
                                            params.map((paramItem, paramIndex) => {
                                            	return (
                                            		<OptGroup
                                            			label={paramItem.labelText || "暂无标题"}
                                            			key={paramIndex}
                                            		>
                                            			{
                                            				paramItem.children &&
                                                            paramItem.children.filter(fItem => fItem.name).map((subItem, subIndex) => {
                                                            	return (
                                                            		<Option
                                                            			value={subItem.name || undefined}
                                                            			disabled={subItem["name"] === currentParam["name"]}
                                                            			key={subIndex}
                                                            		>
                                                                        [{subItem.componentType}] {subItem.name}
                                                            		</Option>
                                                            	);
                                                            })
                                            			}
                                            		</OptGroup>
                                            	);
                                            })
                    					}
                    				</Select>
                    			</div>
                    		</div>

                    		{
                    			willChangeRule.name &&
                                <div className="template-param-rule-item">
                                	<div className="template-param-rule-title">
                                		<span>改变形式：</span>
                                	</div>
                                	<div className="template-param-rule-field">
                                		<Select
                                			placeholder="选择被改变节点类型"
                                			value={willChangeRule.changeMode || undefined}
                                			onChange={this.changeRuleBaseValue.bind(this, "changeMode", "select")}
                                		>
                                			<Option value="whenSomeValue">特定值时改变</Option>
                                		</Select>
                                	</div>
                                </div>
                    		}
                    		{
                    			willChangeRule.name &&
                                <div className="template-param-rule-item">
                                	<div className="template-param-rule-title">
                                		<span>添加条件：</span>
                                	</div>
                                	<div className="template-param-rule-field">
                                		<span
                                			className="add-new-operation"
                                			onClick={this.addChangeRuleCaseItem.bind(this)}
                                		>
                                			<Icon type="plus-square-o" />添加
                                		</span>
                                	</div>
                                </div>
                    		}

                    		{
                    			willChangeRule.name &&
                                willChangeRule.caseList &&
                                willChangeRule.caseList.length > 0 &&
                                <div className="template-rule-case-list">
                                	{
                                		willChangeRule.caseList.map((caseItem, caseIndex) => {
                                			return (
                                				<div className="template-rule-case-item" key={caseIndex}>
                                					<div
                                						className="remove-rule-case-item"
                                						onClick={this.removeChangeRuleCaseItem.bind(this, caseIndex)}
                                					>
                                						<Icon type="delete" />
                                					</div>
                                					{
                                						willChangeRule.changeMode &&
                                                        willChangeRule.changeMode === "whenSomeValue" &&
                                                        <div className="template-param-rule-item">
                                                        	<div className="template-param-rule-title">
                                                        		<span>改变形式value：</span>
                                                        	</div>
                                                        	<div className="template-param-rule-field">
                                                        		<Select
                                                        			mode="tags"
                                                        			placeholder="输入特定值，可以输入多个"
                                                        			value={caseItem.modeValueList || undefined}
                                                        			onChange={this.changeRuleCaseValue.bind(this, caseIndex, "modeValueList", "select")}
                                                        		>
                                                        		</Select>
                                                        	</div>
                                                        </div>
                                					}

                                					{
                                						willChangeRule.name &&
                                                        <div className="template-param-rule-item">
                                                        	<div className="template-param-rule-title">
                                                        		<span>改变类型：</span>
                                                        	</div>
                                                        	<div className="template-param-rule-field">
                                                        		{
                                                        			params.map((paramItem) => {
                                                        				let findObj = paramItem.children && paramItem.children.find(fItem => fItem.name === willChangeRule.name);
                                                        				if (findObj) {
                                                        					if (findObj.componentType === "input" || findObj.componentType === "checkbox") {
                                                        						return (
                                                        							<Select
                                                        								placeholder="选择被改变节点类型"
                                                        								value={caseItem.changeType || undefined}
                                                        								onChange={this.changeRuleCaseValue.bind(this, caseIndex, "changeType", "select")}
                                                        							>
                                                        								<Option value="disabled">禁用当前</Option>
                                                        								<Option value="show">显示当前</Option>
                                                        							</Select>
                                                        						);
                                                        					} else if (findObj.componentType === "select") {
                                                        						return (
                                                        							<Select
                                                        								placeholder="选择被改变节点类型"
                                                        								value={caseItem.changeType || undefined}
                                                        								onChange={this.changeRuleCaseValue.bind(this, caseIndex, "changeType", "select")}
                                                        							>
                                                        								<Option value="disabled">禁用当前</Option>
                                                        								<Option value="show">显示当前</Option>
                                                        								<Option
                                                        									value="selectMap">改变当前selectMap</Option>
                                                        							</Select>
                                                        						);
                                                        					}
                                                        				}
                                                        			})
                                                        		}
                                                        	</div>
                                                        </div>
                                					}
                                					{
                                						caseItem.changeType &&
                                                        caseItem.changeType === "selectMap" &&
                                                        <div className="template-param-rule-item">
                                                        	<div className="template-param-rule-title">
                                                        		<span>新的Map位置：</span>
                                                        	</div>
                                                        	<div className="template-param-rule-field">
                                                        		<Select
                                                        			placeholder="选择新的Map位置"
                                                        			value={caseItem.mapLocation || undefined}
                                                        			onChange={this.changeRuleCaseValue.bind(this, caseIndex, "mapLocation", "select")}
                                                        		>
                                                        			<Option value="local">local</Option>
                                                        			<Option value="service">service</Option>
                                                        		</Select>
                                                        	</div>
                                                        </div>
                                					}
                                					{
                                						caseItem.changeType &&
                                                        caseItem.changeType === "selectMap" &&
                                                        <div className="template-param-rule-item">
                                                        	<div className="template-param-rule-title">
                                                        		<span>新的Map名称：</span>
                                                        	</div>
                                                        	<div className="template-param-rule-field">
                                                        		<Input
                                                        			value={caseItem.mapName || undefined}
                                                        			onChange={this.changeRuleCaseValue.bind(this, caseIndex, "mapName", "input")}
                                                        			placeholder="请输入新的Map名称"
                                                        		/>
                                                        	</div>
                                                        </div>
                                					}
                                				</div>
                                			);
                                		})
                                	}
                                </div>
                    		}
                    	</div>
                    </div>
				}
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(ChangeGroupRuleForSelf);
