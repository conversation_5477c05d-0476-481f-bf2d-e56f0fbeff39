import { Row, Col, Input, But<PERSON>, <PERSON>, DatePicker } from "antd";
import { immuneConfigLang, timePickerLang } from "@/constants/lang";
import {ImmuneConstants} from "@/constants";

const { RangePicker } = DatePicker;

export default (props) => {
	const { reset, search, changeSearchField, searchData = {} } = props;
	return (
		<Row gutter={10}>
			<Col span={5}>
				<Input
					placeholder={immuneConfigLang.searchParams("ruleName")} // 规则名称
					value={searchData.ruleName || undefined}
					onChange={(e) => {
						changeSearchField("ruleName", e, "input");
					}}
					allowClear
					onPressEnter={search}
				/>
			</Col>
			<Col span={3}>
				<Select
					className="wid-100-percent"
					placeholder={immuneConfigLang.searchParams("createType")} // 创建类型
					value={searchData.createType || undefined}
					showSearch
					allowClear
					dropdownMatchSelectWidth={false}
					optionFilterProp="children"
					onChange={(e) => {
						changeSearchField("createType", e);
						search();
					}}
				>
					{
						ImmuneConstants.getCreateType().map((item, index) => {
							return (
								<Option value={item.value} key={index}>
									{item.name}
								</Option>
							);
						})
					}
				</Select>
			</Col>
			<Col span={6}>
				<RangePicker
					placeholder={[immuneConfigLang.searchParams("effectStartTime"), immuneConfigLang.searchParams("effectEndTime")]} // 生效时间
					value={searchData.effectFrom || []}
					onChange={(e) => {
						changeSearchField("effectFrom", e);
						search();
					}}
					className="wid-100-percent"
					format="YYYY-MM-DD HH:mm:ss"
					allowClear
					showTime
					ranges={timePickerLang.timePicker("dateMap")}
				/>
			</Col>
			<Col span={6}>
				<RangePicker
					placeholder={[immuneConfigLang.searchParams("expirationStartTime"), immuneConfigLang.searchParams("expirationEndTime")]} // 失效时间
					value={searchData.effectTo || []}
					onChange={(e) => {
						changeSearchField("effectTo", e);
						search();
					}}
					className="wid-100-percent"
					format="YYYY-MM-DD HH:mm:ss"
					ranges={timePickerLang.timePicker("dateMap")}
					allowClear
					showTime
				/>
			</Col>
			<Col span={4}>
				<Button onClick={reset}>
					{/* 重置 */}
					{immuneConfigLang.common("reset")}
				</Button>
				<Button onClick={search} type="primary" className="ml10">
					{/* 查询 */}
					{immuneConfigLang.common("search")}
				</Button>
			</Col>
		</Row>
	);
};
