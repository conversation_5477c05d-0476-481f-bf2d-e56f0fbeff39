import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Row, Col, Popover } from "antd";
import { publicPolicyListLang } from "@/constants/lang";
import EffectScopeTable from "./EffectScopeTable";
import "./EffectScope.less";

class EffectScope extends PureComponent {
	state = {
		expand: false,
		notFirst: false,
		totalEventList: []
	}
	componentWillMount() {
		const { globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		const totalEventList = publicPolicyScene.reduce((totalScenes, cur) => {
			totalScenes = totalScenes.concat(cur.eventList);
			return totalScenes;
		}, []);
		this.setState({
			totalEventList
		});
	}
	render() {
		const { expand, notFirst, totalEventList } = this.state;
		const arrowTransform = expand ? "transform180" : "";
		const arrowTransformBack = (notFirst && !expand) ? "transform360" : "";
		const { scene = [], globalStore } = this.props;
		const sceneLen = scene && scene.length;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		let newScene = [];
		if (sceneLen > 0) {
			newScene = scene.filter((v) => {
				const { appName } = v || {};
				const newAppName = (publicPolicyScene.find((publicAppName) => {
					if (publicAppName.name === appName) {
						return publicAppName;
					}
				}) || {}).dName;
				if (newAppName) {
					v.newAppName = newAppName;
					return v;
				}
			});
		}
		const newSceneLen = newScene && newScene.length;
		console.log(newScene);
		return (
			<Fragment>
				<Row
					gutter={16}
					type="flex"
					justify="between"
					align="middle"
					onClick={(e)=>{
						e.stopPropagation();
					}}
				>
					{/* 生效范围 */}
					<Col span={newSceneLen > 1 ? 21 : 24}>
						<Popover
							placement="topLeft"
							title={publicPolicyListLang.common("effectScope")}
							content={<EffectScopeTable scene={scene} className="max-scroll"/>}
							onClick={(e)=>{
								e.stopPropagation();
							}}
							trigger="hover"
						>
							<div className="wid-100-percent txt-wrap">
								{
									newSceneLen > 0 &&
									newScene.map((v, i) => {
										if (i < 2 || (i > 1 && expand)) {
											const { eventList = [], newAppName } = v || {};
											const currentEventListName = totalEventList.filter((scene) => {
												if (eventList.indexOf(scene.name) > -1) {
													return scene;
												}
											}).map(sceneName => {
												return sceneName.dName;
											}).join(",");
											return (
												<span className="wid-100-percent text-overflow">
													{newAppName}
													{currentEventListName && currentEventListName.length > 0 ? "：" : ""}
													<label className="grey-txt">{currentEventListName || ""}</label>
												</span>
											);
										}
									})
								}
							</div>
						</Popover>
					</Col>
					{
						newSceneLen > 2 &&
							<Col span="3" className={`grey-txt transform-default ${arrowTransform} ${arrowTransformBack}`}>
								<i
									className="icon-arrow-bottom iconfont"
									onClick={(e) => {
										e.stopPropagation();
										this.setState({
											expand: !expand,
											notFirst: true
										});
									}}
								/>
							</Col>
					}
				</Row>
			</Fragment>

		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(EffectScope);
