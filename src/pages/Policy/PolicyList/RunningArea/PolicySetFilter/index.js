import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Tooltip, Input, Select, Icon, Row, Col } from "antd";
import { compareSignList } from "@/constants/common";
import { replayTaskLang } from "@/constants/lang";
import "./index.less";

const { Option } = Select;

class PolicySetFilter extends PureComponent {
	constructor(props) {
		super(props);
		this.addConditionItem = this.addConditionItem.bind(this);
		this.deleteConditionItem = this.deleteConditionItem.bind(this);
		this.verify = this.verify.bind(this);
	}
	addConditionItem(index) {
		let { value: FieldFilters = {}, changeFieldValue } = this.props;
		const newFieldFilters = [...FieldFilters];
		let obj = {
			fieldKey: null,
			compareSign: null,
			fieldValue: null
		};
		newFieldFilters.splice(index + 1, 0, obj);
		changeFieldValue(newFieldFilters);
	};
	deleteConditionItem(index) {
		const { value: FieldFilters = {}, changeFieldValue } = this.props;
		const newFieldFilters = [...FieldFilters];
		newFieldFilters.splice(index, 1);
		changeFieldValue(newFieldFilters);
	};
	changeValue(index, field, type, e) {
		let { value: FieldFilters = [], changeFieldValue } = this.props;
		if (!(FieldFilters && FieldFilters.length > 0)) {
			FieldFilters = [];
		}
		const newFieldFilters = [...FieldFilters];
		let value;

		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		newFieldFilters[index][field] = value;
		if (field === "fieldKey") {
			const valArr = value.split("&");
			newFieldFilters[index]["compareSign"] = null;
			newFieldFilters[index]["fieldValue"] = null;
			newFieldFilters[index][field] = valArr[0];
			newFieldFilters[index]["fieldType"] = valArr[1];
		}
		changeFieldValue(newFieldFilters);
	};

	verify(compareSignValue, fieldType) {
		if ((compareSignValue === ">" || compareSignValue === "<" || compareSignValue === "<=" || compareSignValue === ">=") && fieldType === "STRING") {
			return true;
		} else {
			return false;
		}
	}
	render() {
		let { globalStore, value: FieldFilters } = this.props;
		let { allMap, personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		let ruleFieldList = allMap.ruleFieldList && allMap.ruleFieldList.length > 0 ? allMap.ruleFieldList : [];
		// let newRuleFieldList = staticMap; // _.uniqWith(staticMap, _.isEqual);
		return (
			<Fragment>
				{
					FieldFilters && FieldFilters.map((item, index) => {
						return (
							<Row gutter={10} className="field-filter-row">
								<Col span={8}>
									<Select
										value={item.fieldKey ? (`${item.fieldKey}&${item.fieldType}`) : undefined}
										placeholder={replayTaskLang.operatorModal("selectPlaceholder")}
										dropdownMatchSelectWidth={false}
										showSearch
										optionFilterProp="children"
										filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
										onChange={this.changeValue.bind(this, index, "fieldKey", "select")}>
										{
											ruleFieldList.map((subItem, subIndex) => {
												return (
													<Option
														key={subIndex}
														value={`${subItem.name}&${subItem.type}`}
													>
														{lang === "cn" ? subItem.dName || subItem.enDName : subItem.enDName || subItem.dName}
													</Option>
												);
											})
										}
									</Select>
								</Col>
								<Col span={5}>
									<Select
										value={item.compareSign || undefined}
										placeholder={replayTaskLang.operatorModal("selectPlaceholder")}
										dropdownMatchSelectWidth={false}
										onChange={this.changeValue.bind(this, index, "compareSign", "select")}>
										{
											compareSignList.map((subItem, subIndex) =>
												<Option value={subItem.value} key={subIndex}
													disabled={this.verify(subItem.value, item.fieldType)}>
													{lang === "cn" ? subItem.name : subItem.enName}
												</Option>
											)
										}
									</Select>
								</Col>
								<Col span={8}>
									<Input
										value={item.fieldValue || undefined}
										placeholder={(item.compareSign === "null" || item.compareSign === "notnull") ? "" : replayTaskLang.operatorModal("inputPlaceholder")}
										disabled={item.compareSign === "null" || item.compareSign === "notnull"}
										onChange={this.changeValue.bind(this, index, "fieldValue", "input")} />
								</Col>
								<Col span={3}>
									<Tooltip
										title={replayTaskLang.operatorModal("addOne")} // lang:添加一项
										placement="top"
									>
										<Icon
											className="add param-tip-icon"
											type="plus-circle-o"
											onClick={this.addConditionItem.bind(this, index)}
										/>
									</Tooltip>

									{
										index !== 0 &&
                                        <Tooltip
                                        	title={replayTaskLang.operatorModal("removeCurrentField")} // lang:移除当前行
                                        	placement="top"
                                        >
                                        	<Icon
                                        		className="delete param-tip-icon ml10"
                                        		type="delete"
                                        		onClick={this.deleteConditionItem.bind(this, index)}
                                        	/>
                                        </Tooltip>
									}
								</Col>
							</Row>
						);
					})
				}
			</Fragment>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetail: state.policyDetail
}))(PolicySetFilter);
