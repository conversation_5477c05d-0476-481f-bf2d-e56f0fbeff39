:global {
    .flow-detail-panel {
        & {
            flex: 1;
            background: #fafafa;
            height: 100%;
        }

        .flow-detail-main {
            & {
                height: 100%;
            }

            .ant-card {
                & {
                    height: 100%;
                    border-bottom: 1px solid #e8e8e8;
                }

                .ant-card-head {
                    position: relative;
                    z-index: 8;
                }

                .ant-card-body {
                    padding: 16px 24px 4px;
                    height: ~"calc(100% - 44px)";
                    overflow: auto;
                }
            }

            .none-data {
                & {
                    text-align: center;
                    padding-bottom: 10px;
                }

                i {
                    font-size: 40px;
                }

                p {
                    margin-bottom: 0;
                }
            }

            .param-list {
                & {}

                .none-data {
                    & {
                        text-align: center;
                        padding-bottom: 10px;
                    }

                    i {
                        font-size: 40px;
                    }

                    p {
                        margin-bottom: 0;
                    }
                }

                .param-item {
                    & {
                        margin-bottom: 20px;
                    }

                    .param-title {
                        & {
                            margin-bottom: 10px;
                        }

                        .add-condition {
                            float: right;
                        }
                    }

                    .param-content {
                        .ant-select {
                            & {
                                width: 100%;
                                margin-bottom: 10px;
                            }

                            &:last-child {
                                margin-bottom: 0;
                            }
                        }

                        .condition-list {
                            & {
                                padding: 5px 10px 10px;
                                border: 1px dashed #dcdcdc;
                                background: #f5f5f5;
                            }

                            .condition-item {
                                & {
                                    margin-bottom: 10px;
                                    border-bottom: 1px dashed #dcdcdc;
                                    padding-bottom: 20px;
                                }

                                &:last-child {
                                    margin-bottom: 0;
                                    border-bottom: 0;
                                    padding-bottom: 0;
                                }

                                .condition-item-header {
                                    & {
                                        position: relative;
                                        height: 40px;
                                        line-height: 40px;
                                        //border-bottom: 1px dotted #dcdcdc;
                                        margin-bottom: 10px;
                                    }

                                    h3 {
                                        & {
                                            float: left;
                                            margin-bottom: 0;
                                            font-size: 14px;
                                            padding-left: 12px;
                                        }

                                        &:before {
                                            position: absolute;
                                            left: 0;
                                            top: 11px;
                                            width: 2px;
                                            height: 18px;
                                            background: #2f80f7;
                                            content: "";
                                        }
                                    }

                                    .delete {
                                        & {
                                            float: right;
                                            width: auto;
                                            height: 40px;
                                            line-height: 40px;
                                            text-align: center;
                                            cursor: pointer;
                                        }

                                        &:hover {
                                            color: #ff4d4f;
                                        }

                                        i {
                                            font-size: 16px;
                                        }
                                    }
                                }

                                .condition-item-body {
                                    & {
                                        clear: both;
                                    }
                                }
                            }
                        }
                    }
                }

                .radio-group-double {
                    & {
                        width: 100%;
                    }

                    .ant-radio-button-wrapper {
                        width: 50%;
                        text-align: center;
                    }
                }
            }

            //	param-policy-list
            .param-policy-list {
                & {
                    padding: 10px;
                    border: 1px dashed #dcdcdc;
                    background: #f5f5f5;
                }

                .param-policy-item {
                    & {
                        margin-bottom: 10px;
                        border-bottom: 1px dashed #dcdcdc;
                        padding-bottom: 20px;
                    }

                    &:last-child {
                        margin-bottom: 0;
                        border-bottom: 0;
                        padding-bottom: 0;
                    }
                }
            }
        }
    }
}
