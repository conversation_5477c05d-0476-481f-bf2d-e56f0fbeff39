import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request, { downloadFileHandle } from "../utils/request";

const publicRunningList = async (params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/publicPolicy/listOnline", params), {
		method: "GET",
		headers: getHeader()
	});
};

const publicVersionPolicyDetail = async (params) => {
	return request(getUrl("/admin/publicPolicy/versionPolicyDetail", params), {
		method: "GET",
		headers: getHeader()
	});
};

const offlinePublicPolicy = async (params) => {
	return request("/admin/publicPolicy/offline", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const flowRef = async (params) => {
	return request(getUrl("/admin/publicPolicy/flowRef", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 导出公共策略
const exportPublicPolicy = async (params) => {
	return downloadFileHandle(getUrl("/admin/publicPolicy/export", params), {
		method: "GET",
		headers: getHeader()
	}, params.fileName, params.fileType);
};

// 导出公共策略的测试
const exportPublicPolicyTest = async (params) => {
	return request(getUrl("/admin/publicPolicy/export", params), {
		method: "GET",
		headers: getHeader()
	});
};

export default {
	publicRunningList,
	publicVersionPolicyDetail,
	offlinePublicPolicy,
	flowRef,
	exportPublicPolicyTest,
	exportPublicPolicy
};
