import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Select, Form, Radio, message, Button, Row, Col } from "antd";
import {cloneDeep} from "lodash";
import OneCondition from "../OneCondition";
import { workflowLang } from "../../../constants/lang";

const Option = Select.Option;
const RadioGroup = Radio.Group;

class ConditionSetting extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.commit = this.commit.bind(this);
    	this.addCondition = this.addCondition.bind(this);
    	this.changeAllConditionType = this.changeAllConditionType.bind(this);
    	this.changeGroupConditionType = this.changeGroupConditionType.bind(this);
    }

    verify = () => {
    	let { workflowStore } = this.props;
    	let { conditionsGroup } = workflowStore;

    	let hasEmptyValue = false;
    	if (conditionsGroup["children"]) {
    		conditionsGroup["children"].map((item) => {
    			item.ruleList.map((subItem) => {
    				if ((!subItem.leftPropertyValue && subItem.leftPropertyValue !== 0) || (!subItem.rightPropertyValue && subItem.rightPropertyValue !== 0)) {
    					if (!hasEmptyValue) {
    						message.error(workflowLang.conditionSettingModal("conditionsHaveEmptyMsg"), 1); // lang:"判定条件存在空值，请填写完整！"
    						hasEmptyValue = true;
    					} else {
    						return;
    					}
    				}
    				if (subItem.leftPropertyType === "INT") {
    					let rightVal = subItem.rightPropertyValue;
    					let y = String(rightVal).indexOf(".") + 1;
    					if (y > 0) {
    						message.error(workflowLang.conditionSettingModal("integerMsg")); // lang:请输入整数
    						hasEmptyValue = true;
    					}
    				}
    			});
    		});
    	} else {
    		message.error(workflowLang.conditionSettingModal("addConfigConditionMsg"), 1); // lang:请添加配置条件
    		hasEmptyValue = true;
    	}

    	if (hasEmptyValue) {
    		return false;
    	}
    };

    commit() {
    	let { dispatch, workflowStore, onChange } = this.props;
    	let { conditionsGroup } = workflowStore;

    	if (conditionsGroup && conditionsGroup.children.length === 0) {
    		message.warning(workflowLang.conditionSettingModal("noAddConfigConditionMsg")); // lang:还没有添加判断条件
    		return;
    	}
    	let hasEmptyField = false;
    	conditionsGroup &&
            conditionsGroup.children.length > 0 &&
            conditionsGroup.children.map((item) => {
            	if (item.logicOperator && item["children"]) {
            		// 如果是多层
            		item.children.map(subItem => {
            			if (subItem.operator) {
            				if (subItem.operator !== "isnull" && subItem.operator !== "notnull") {
            					if (!subItem.leftVar || !subItem.leftVarType || !subItem.rightVar) {
            						hasEmptyField = true;
            					}
            				}
            			} else {
            				hasEmptyField = true;
            			}
            		});
            	} else {
            		// 如果是单层条件
            		if (item.operator) {
            			if (item.operator !== "isnull" && item.operator !== "notnull") {
            				if (!item.leftVar || !item.leftVarType || !item.rightVar) {
            					hasEmptyField = true;
            				}
            			}
            		} else {
            			hasEmptyField = true;
            		}
            	}
            });

    	if (hasEmptyField) {
    		message.warning(workflowLang.conditionSettingModal("incompleteConditionMsg")); // lang:判断条件配置不完整，请检查！
    		return;
    	}

    	// 保存成功之后将值赋值给最终的conditionsGroupFinal
    	const conditionsGroupFinal = cloneDeep(conditionsGroup);
    	dispatch({
    		type: "workflow/setAttrValue",
    		payload: {
    			conditionsGroupFinal
    		}
    	});
    	onChange(conditionsGroupFinal);
    	console.log(conditionsGroupFinal);
    	dispatch({
    		type: "workflow/setDialogShow",
    		payload: {
    			conditionSetting: false
    		}
    	});
    };

    addCondition(type) {
    	let { workflowStore, dispatch } = this.props;
    	let { conditionsGroup } = workflowStore;
    	let conditionTemp = {};

    	if (type === "single") {
    		conditionTemp = {
    			operator: "==",
    			leftValueType: "STRING",
    			leftVarType: "context",
    			leftVar: null,
    			rightVarType: "context",
    			rightVar: null
    		};
    	} else if (type === "group") {
    		conditionTemp = {
    			logicOperator: "&&",
    			children: [
    				{
    					operator: "==",
    					leftValueType: "STRING",
    					leftVarType: "context",
    					leftVar: null,
    					rightVarType: "context",
    					rightVar: null
    				}
    			]
    		};
    	}
    	conditionsGroup["children"].push(conditionTemp);
    	dispatch({
    		type: "workflow/setAttrValue",
    		payload: {
    			conditionsGroup: conditionsGroup
    		}
    	});
    };

    deleteCondition = (i) => {
    	return () => {
    		let { workflowStore, dispatch } = this.props;
    		let { conditionsGroup } = workflowStore;

    		conditionsGroup["children"].splice(i, 1);
    		dispatch({
    			type: "workflow/setAttrValue",
    			payload: {
    				conditionsGroup: conditionsGroup
    			}
    		});
    	};
    };

    changeAllConditionType(e) {
    	let { workflowStore, dispatch } = this.props;
    	let { conditionsGroup } = workflowStore;

    	let value = e.target.value;
    	conditionsGroup["logicOperator"] = value;
    	dispatch({
    		type: "workflow/setAttrValue",
    		payload: {
    			conditionsGroup: conditionsGroup
    		}
    	});
    }

    changeGroupConditionType(index, value) {
    	let { workflowStore, dispatch } = this.props;
    	let { conditionsGroup } = workflowStore;

    	conditionsGroup["children"][index]["logicOperator"] = value;
    	dispatch({
    		type: "workflow/setAttrValue",
    		payload: {
    			conditionsGroup: conditionsGroup
    		}
    	});
    }

    renderRuleTemplate(item, index) {
    	let { disabled } = this.props;
    	let ConditionDom = [];
    	let ruleType = item.hasOwnProperty("children") ? "group" : "single";
    	if (ruleType === "group") {
    		ConditionDom = item["children"].map((obj, index2) => {
    			let key = item.ruleType + "_" + index[0] + "_" + index2;
    			return (
    				<OneCondition
    					key={key}
    					item={obj}
    					type="group"
    					indexArr={[index, index2]}
    					disabled={disabled}
    				/>
    			);
    		});
    	} else {
    		let key = item.ruleType + "_" + index;
    		ConditionDom = (
    			<OneCondition
    				key={key}
    				item={item}
    				type="single"
    				indexArr={[index]}
    				disabled={disabled}
    			/>
    		);
    	}

    	return (
    		<div
    			className={ruleType === "single" ? "one-condition custom-item" : "group-condition custom-item"}
    			key={index}
    		>
    			<Row gutter={8}>
    				<Col span={3} push={1}>
    					{
    						ruleType === "group" &&
                            <Select
                            	value={item.logicOperator}
                            	onChange={this.changeGroupConditionType.bind(this, index)}
                            	disabled={disabled}
                            >
                            	<Option value="&&">AND</Option>
                            	<Option value="||">OR</Option>
                            </Select>
    					}
    				</Col>
    				<Col span={21} push={1}>{ConditionDom}</Col>
    			</Row>
    		</div>
    	);
    };

    cancelModal() {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "workflow/setDialogShow",
    		payload: {
    			conditionSetting: false
    		}
    	});
    }

    render() {
    	let { workflowStore, disabled } = this.props;
    	let { conditionsGroup, dialogShow } = workflowStore;

    	let footer = [];
    	if (disabled) {
    		footer = null;
    	} else {
    		footer = (
    			[
    				<Button onClick={this.cancelModal.bind(this)}>
    					{/* lang:取消*/}
    					{workflowLang.common("cancel")}
    				</Button>,
    				<Button type="primary" onClick={this.commit.bind(this)}>
    					{/* lang:确定*/}
    					{workflowLang.common("ok")}
    				</Button>
    			]
    		);
    	}
    	return (
    		<Modal
    			title={disabled ? workflowLang.conditionSettingModal("viewTitle") : workflowLang.conditionSettingModal("settingTitle")} // lang:"查看条件配置详情" : "设置判定条件"
    			visible={dialogShow.conditionSetting}
    			maskClosable={false}
    			width={1200}
    			footer={footer}
    			onCancel={this.cancelModal.bind(this)}
    		>
    			<Form layout="horizontal">
    				<Row gutter={8} className="rule-condition">
    					<Col className="gutter-row" span={4}>
    						<div
    							className="gutter-box"
    							style={{ textAlign: "right" }}
    						>
    							{/* lang:执行条件 */}
    							{workflowLang.conditionSettingModal("executionCondition")}
    						</div>
    					</Col>
    					<Col className="gutter-row" span={20}>
    						<div className="gutter-box">
    							<RadioGroup
    								onChange={this.changeAllConditionType.bind(this)}
    								value={conditionsGroup.logicOperator}
    								disabled={disabled}
    							>
    								<Radio value="&&">
    									{/* lang:满足以下所有条件 */}
    									{workflowLang.conditionSettingModal("followAllCondition")}
    								</Radio>
    								<Radio value="||">
    									{/* lang:满足以下任意条件 */}
    									{workflowLang.conditionSettingModal("followAnyCondition")}
    								</Radio>
    								<Radio value="!&&">
    									{/* lang:以下条件均不满足 */}
    									{workflowLang.conditionSettingModal("notFollowCondition")}
    								</Radio>
    							</RadioGroup>
    						</div>
    					</Col>
    				</Row>
    				<div className="rule-content">
    					{
    						conditionsGroup.children &&
                            conditionsGroup.children.map((item, index) => {
                            	return this.renderRuleTemplate(item, index);
                            })
    					}
    					{
    						!disabled &&
                            <Row gutter={8} className="rule-ctrl">
                            	<Col className="gutter-row" span={24} push={4}>
                            		<span onClick={this.addCondition.bind(this, "single")}>
                            			{/* lang:添加单条条件 */}
                            			{workflowLang.conditionSettingModal("addSingleCondition")}
                            		</span>
                            		<span onClick={this.addCondition.bind(this, "group")}>
                            			{/* lang:添加条件组 */}
                            			{workflowLang.conditionSettingModal("addConditionGroups")}
                            		</span>
                            	</Col>
                            </Row>
    					}
    				</div>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	workflowStore: state.workflow
}))(ConditionSetting);

