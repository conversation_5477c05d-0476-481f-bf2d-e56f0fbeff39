import { PureComponent } from "react";
import { connect } from "dva";
import moment from "moment";
import * as Echarts from "echarts";
import macarons from "echarts/theme/macarons";
import { replayTaskLang } from "@/constants/lang";

class ReportChart extends PureComponent {
	constructor(props) {
		super(props);
		this.draw = this.draw.bind(this);
		this.initOption = this.initOption.bind(this);
	}

	componentDidMount() {
		const { idName, chartData } = this.props;
		this.initOption(idName, chartData);
	}

	componentWillReceiveProps(nextProps) {
		let preChartData = this.props.chartData;
		let nextChartData = nextProps.chartData;
		if (preChartData !== nextChartData) {
			// 检测到数据改变，重绘图表
			const { idName, chartData } = nextProps;
			this.initOption(idName, chartData);
		}
	}

	initOption(idName, chartData) {
		let { reportData = {} } = this.props;
		let optionObj = {
			ruleAxis: [],
			totalCount: [],
			hitCount: []
		};
		chartData.forEach((item, index) => {
			optionObj.ruleAxis.push(item.ruleName);
			optionObj.totalCount.push(reportData.totalCount);
			optionObj.hitCount.push(item.hitRuleTransCount);
		});

		this.draw(idName, optionObj);
	}

	draw(idName, optionObj) {
		let { taskDetail } = this.props;
		if (!optionObj) return;
		const dom = document.getElementById(idName);
		const myChart = Echarts.init(dom, "macarons");

		const option = {
			// title: {
			// 	text: replayTaskLang.report("ruleHitReport")		// lang:规则命中报告
			// },
			legend: {
				top: 0,
				right: 0,
				data: [replayTaskLang.report("hitCount2"), replayTaskLang.report("totalCount")]		// lang:"命中数", "总数"
			},
			grid: {
				left: "3%",
				right: "4%",
				bottom: "10%",
				containLabel: true
			},
			tooltip: {
				show: "true",
				trigger: "axis",
				axisPointer: { // 坐标轴指示器，坐标轴触发有效
					type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
				},
				formatter: function(params) {
					let hitObj = params.find(pItem => pItem.seriesName === replayTaskLang.report("hitCount2"));
					let totalObj = params.find(pItem => pItem.seriesName === replayTaskLang.report("totalCount"));
					let hitPercent = 0;
					let textDom;

					if (hitObj && totalObj) {
						hitPercent = hitObj.value / totalObj.value * 100;
						textDom = replayTaskLang.report("rule") + "：" + totalObj.axisValueLabel + "<br>" + replayTaskLang.report("totalCount") + "：" + totalObj.value + "<br>" + replayTaskLang.report("hitCount2") + "：" + hitObj.value + "<br>" + replayTaskLang.report("rulePercent") + "：" + hitPercent.toFixed(2) + "%";
					}

					return textDom;
				}
			},
			xAxis: {
				type: "value",
				axisTick: { show: false },
				axisLine: {
					show: false,
					lineStyle: {
						color: "#666"
					}
				},
				splitLine: {
					show: false
				}
			},
			yAxis: [
				{
					type: "category",
					axisTick: { show: false },
					axisLine: {
						show: true,
						lineStyle: {
							color: "#666"
						}
					},
					data: optionObj.ruleAxis
				},
				{
					type: "category",
					axisLine: { show: false },
					axisTick: { show: false },
					axisLabel: { show: false },
					splitArea: { show: false },
					splitLine: { show: false },
					data: optionObj.ruleAxis
				}

			],
			series: [
				{
					name: replayTaskLang.report("totalCount"),
					type: "bar",
					yAxisIndex: 1,

					itemStyle: {
						normal: {
							show: true,
							color: "#25a8f9",
							barBorderRadius: 0,
							borderWidth: 0,
							borderColor: "#333"
						}
					},
					barGap: "0%",
					barCategoryGap: "50%",
					data: optionObj.totalCount
				},
				{
					name: replayTaskLang.report("hitCount2"),
					type: "bar",
					itemStyle: {
						normal: {
							show: true,
							color: "#ff6727",
							barBorderRadius: 0,
							borderWidth: 0,
							borderColor: "#333"
						}
					},
					barGap: "0%",
					barCategoryGap: "50%",
					data: optionObj.hitCount
				}

			]
		};
		if (option && typeof option === "object") {
			myChart.setOption(option, true);
		}
	}

	render() {
		let { chartData, idName } = this.props;
		let realHeight;
		if (chartData && chartData.length) {
			if (chartData.length === 1) {
				realHeight = "200px";
			}
			if (chartData.length > 1) {
				realHeight = chartData.length * 70 + 130 + "px";
			}
		}

		return (

			<div
				className="report-panel"
				style={{ borderBottom: "1px dashed #e6e6e6" }}
			>
				<div className="report-panel-box">
					<span className="report-panel-title">
						{/* lang:规则命中报告 */}
						{replayTaskLang.report("ruleHitReport")}
					</span>
				</div>
				<div
					className="report-panel-content-personal"
					style={{ paddingTop: 0 }}
				>
					<div id={idName} style={{ height: realHeight }}></div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	replayTaskStore: state.replayTask
}))(ReportChart);
