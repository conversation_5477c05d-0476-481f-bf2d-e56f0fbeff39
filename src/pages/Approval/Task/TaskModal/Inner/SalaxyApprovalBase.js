import { PureComponent } from "react";
import { connect } from "dva";
import { Button, Input, Radio, Row, Col, message } from "antd";
import { approvalTaskAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { approvalTaskLang, commonLang } from "@/constants/lang";
import { checkFunctionHasPermission } from "@/utils/permission";

const { TextArea } = Input;

class SalaxyApprovalBase extends PureComponent {

	constructor(props) {
		super(props);
		this.changeBaseField = this.changeBaseField.bind(this);
		this.submitApproval = this.submitApproval.bind(this);
		this.closeModal = this.closeModal.bind(this);
		this.refreshSalaxyEditArea = this.refreshSalaxyEditArea.bind(this);
	}

	changeBaseField(field, e) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { salaxyApprovalData } = dialogData;

		salaxyApprovalData[field] = e.target.value;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				salaxyApprovalData: salaxyApprovalData
			}
		});
	}

	submitApproval() {
		let { approvalTaskStore, indexRunningStore, globalStore, dispatch } = this.props;
		let { dialogData, policyPage } = approvalTaskStore;
		let { salaxyApprovalData } = dialogData;
		let { curPage, pageSize, searchParams } = policyPage;
		let { uuid, desc, status } = salaxyApprovalData;
		let { indexPages } = indexRunningStore;

		let { currentApp } = globalStore;
		if (currentApp.name !== "all") {
			searchParams["appName"] = currentApp.name;
		} else {
			searchParams["appName"] = "";
		}

		if (!desc) {
			message.error("请输入审批描述");
			return;
		}

		if (desc && desc.length > 200) {
			// lang:审批描述不能超过200个字符
			message.error(approvalTaskLang.modal("descriptionLengthTip"));
			return;
		}

		let params = {
			uuid: uuid,
			status: status,
			desc: desc
		};
		approvalTaskAPI.doSalaxyApproval(params).then(res => {
			if (res.success) {
				// lang:指标审批成功
				message.success(approvalTaskLang.message("indicatorApprovalSuccessfully"));
				// 关闭策略版本提交modal
				this.closeModal();
				// 重新拉取策略审批任务列表
				dispatch({
					type: "approvalTask/getSalaxyApprovalTaskList",
					payload: {
						curPage: curPage,
						pageSize: pageSize,
						...searchParams
					}
				});
				dispatch({
					type: "indexRunning/setAttrValue",
					payload: {
						indexPages: {
							...indexPages,
							approvalTask: {
								indexList: [],
								indexListReady: false,
								curPage: 1,
								pageSize: 20,
								total: 0,
								pages: 0
							}
						}
					}
				});
				// TODO
				// dispatch({
				// 	type: "indexEditor/changeIndexStatus",
				// 	payload: {
				// 		id: id,
				// 	}
				// });
				// 刷新map状态
				dispatch({
					type: "global/getAllMap"
				});

				// 重新刷新指标编辑区列表
				this.refreshSalaxyEditArea();
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	refreshSalaxyEditArea() {
		let { indexEditorStore, dispatch, globalStore } = this.props;
		let { pageSize, searchParams } = indexEditorStore;
		let { currentApp } = globalStore;
		let appName = currentApp.name;
		if (appName !== "all") {
			searchParams["app"] = appName;
		} else {
			searchParams["app"] = "";
		}

		dispatch({
			type: "indexEditor/getSalaxyList",
			payload: {
				curPage: 1,
				pageSize: pageSize,
				...searchParams
			}
		});
	}

	closeModal() {
		let { indexRunningStore, dispatch } = this.props;
		let { indexPages } = indexRunningStore;

		dispatch({
			type: "approvalTask/setDialogShow",
			payload: {
				salaxyApproval: false
			}
		});
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				salaxyApprovalData: {
					uuid: null,
					status: "APPROVED",
					desc: null,
					leftNavIndex: 0,
					tabIndex: 0,
					diffDetail: {
						onlineVersion: null,
						pendVersion: null
					}
				}
			}
		});
		dispatch({
			type: "indexRunning/setAttrValue",
			payload: {
				indexPages: {
					...indexPages,
					approvalTask: {
						indexList: [],
						indexListReady: false,
						curPage: 1,
						pageSize: 20,
						total: 0,
						pages: 0
					}
				}
			}
		});
	}

	render() {
		let { approvalTaskStore, globalStore } = this.props;
		let { menuTreeReady } = globalStore;
		let { dialogData } = approvalTaskStore;
		let { salaxyApprovalData } = dialogData;
		let { desc, status } = salaxyApprovalData;

		return (
			<div className="basic-form">
				<Row gutter={CommonConstants.gutterSpan}>
					<Col span={4} className="basic-info-title">
						{/* lang:审批结果 */}
						{approvalTaskLang.modal("approvalResult")}：
					</Col>
					<Col span={12}>
						<Radio.Group
							value={status || undefined}
							buttonStyle="solid"
							onChange={this.changeBaseField.bind(this, "status")}
						>
							<Radio.Button value="APPROVED">
								{/* lang:通过 */}
								{approvalTaskLang.modal("pass")}
							</Radio.Button>
							<Radio.Button value="REJECTED">
								{/* lang:拒绝 */}
								{approvalTaskLang.modal("reject")}
							</Radio.Button>
						</Radio.Group>
					</Col>
				</Row>
				<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
					<Col span={4} className="basic-info-title">
						{/* lang:审批原因 */}
						{approvalTaskLang.modal("approvalReason")}：
					</Col>
					<Col span={12}>
						<TextArea
							rows={4}
							value={desc || undefined}
							placeholder={approvalTaskLang.modal("enterDescriptionPlaceholder")} // lang:请输入审批描述
							onChange={this.changeBaseField.bind(this, "desc")}
						/>
					</Col>
				</Row>
				<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
					<Col span={4} className="basic-info-title"></Col>
					<Col span={12}>
						{
							menuTreeReady &&
                            checkFunctionHasPermission("ZB0401", "changeIndexApprovalStatus") &&
                            <Button
                            	type="primary"
                            	className="mr10"
                            	onClick={this.submitApproval.bind(this)}
                            >
                            	{/* lang:确定 */}
                            	{approvalTaskLang.modal("ok")}
                            </Button>
						}
						{
							menuTreeReady &&
                            !checkFunctionHasPermission("ZB0401", "changeIndexApprovalStatus") &&
                            <Button
                            	type="primary"
                            	className="mr10"
                            	disabled={checkFunctionHasPermission("ZB0401", "changeIndexApprovalStatus")}
                            >
                            	{/* lang:暂无权限 */}
                            	{commonLang.messageInfo("noPermission")}
                            </Button>
						}
						<Button onClick={this.closeModal.bind(this)}>
							{/* lang:取消 */}
							{approvalTaskLang.modal("cancel")}
						</Button>
					</Col>
				</Row>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask,
	indexRunningStore: state.indexRunning
}))(SalaxyApprovalBase);
