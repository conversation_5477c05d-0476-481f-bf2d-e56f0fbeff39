import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		title: {
			"cn": "字段集管理",
			"en": "Field Set Management"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		fieldSetNamePleaseholder: {
			"cn": "请输入字段集标识",
			"en": "Please enter the field set identifier"
		},
		fieldSetDisPlayNamePleaseholder: {
			"cn": "请输入字段集显示名",
			"en": "Please enter the field set display name"
		},
		fieldSetTypePlaceholder: {
			"cn": "请选择字段集类型",
			"en": "Select the field set type"
		},
		allFieldType: {
			"cn": "全部字段集类型",
			"en": "All field set types"
		},
		search: {
			"cn": "搜索",
			"en": "Search"
		},
		add: {
			"cn": "新增",
			"en": "Add"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		id: {
			"cn": "标识",
			"en": "Identification"
		},
		name: {
			"cn": "显示名",
			"en": "Display Name"
		},
		type: {
			"cn": "类型",
			"en": "Type"
		},
		operation: {
			"cn": "操作",
			"en": "operation"
		},
		modify: {
			"cn": "编辑",
			"en": "Modify"
		},
		delete: {
			"cn": "删除",
			"en": "Delete"
		},
		deleteMsg: {
			"cn": "确认要删除此字段集吗？",
			"en": "Are you sure you want to delete this field set?"
		},
		cancel: {
			"cn": "取消",
			"en": "Cancel"
		}
	};
	return params[field][lang];
};

const message = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		deleteSuccess: {
			"cn": "删除成功",
			"en": "Delete Success"
		}
	};
	return params[field][lang];
};

const operateFieldSetModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		addFieldSet: {
			"cn": "新增字段集",
			"en": "Add Field Set"
		},
		modifyFieldSet: {
			"cn": "修改字段集",
			"en": "Modify Field Set"
		},
		fieldSetId: {
			"cn": "字段集标识",
			"en": "Field Set ID"
		},
		fieldSetIdPlaceholder: {
			"cn": "请输入字段集标识",
			"en": "Please enter the field set identifier"
		},
		fieldSetIdLength: {
			"cn": "字段集标识长度不能超过32个字符",
			"en": "Id length cannot exceed 32 characters"
		},
		fieldSetName: {
			"cn": "字段集显示名",
			"en": "Field Set Name"
		},
		fieldSetNameLength: {
			"cn": "字段集显示名长度不能超过24个字符",
			"en": "Field set name length cannot exceed 24 characters"
		},
		fieldSetNamePlaceholder: {
			"cn": "请输入字段集显示名",
			"en": "Please enter the field set name"
		},
		fieldSetType: {
			"cn": "字段集类型",
			"en": "Field Set Type"
		},
		fieldSetTypePlaceholder: {
			"cn": "请选择字段集类型",
			"en": "Select the field set type"
		},
		fields: {
			"cn": "字段",
			"en": "Field"
		},
		fieldsPlaceholder: {
			"cn": "请选择字段",
			"en": "Please select the field"
		},
		addSuccess: {
			"cn": "新增成功",
			"en": "Add Success"
		},
		modifySuccess: {
			"cn": "修改成功",
			"en": "Modify Success"
		},
		deleteFieldConfirm: {
			"cn": "确认要删除此字段吗？",
			"en": "Are you sure you want to delete this field?"
		}
	};
	return params[field][lang];
};

export default {
	common,
	searchParams,
	table,
	message,
	operateFieldSetModal
};
