import { PureComponent } from "react";
import { connect } from "dva";
import { Radio } from "antd";
import { approvalTaskLang } from "@/constants/lang";
import Flow from "@/components/Flow";

const RadioButton = Radio.Button;
const RadioGroup = Radio.Group;

class WorkflowApprovalDiff extends PureComponent {

	constructor(props) {
		super(props);
		this.changeDiffNav = this.changeDiffNav.bind(this);
		this.changeDiffTime = this.changeDiffTime.bind(this);
	}

	componentDidMount() {
		let { approvalTaskStore } = this.props;
		let { dialogData } = approvalTaskStore;
		let { workflowApprovalData } = dialogData;
		let { diffDetail, diffTime } = workflowApprovalData;
		let { onlineVersion, pendVersion } = diffDetail;
		let flowDataObj = diffTime === "pend" ? pendVersion : onlineVersion;

		this.flowEditor.initGraph(flowDataObj);
	}

	changeDiffNav(index) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { workflowApprovalData } = dialogData;

		workflowApprovalData.tabIndex = index;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				workflowApprovalData: workflowApprovalData
			}
		});
	}

	changeDiffTime(e) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { workflowApprovalData } = dialogData;
		let { diffDetail } = workflowApprovalData;
		let { onlineVersion, pendVersion } = diffDetail;
		let flowDataObj = e.target.value === "pend" ? pendVersion : onlineVersion;

		this.flowEditor.initGraph(flowDataObj);
		workflowApprovalData.diffTime = e.target.value;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				workflowApprovalData: workflowApprovalData
			}
		});
	}

	render() {
		let { approvalTaskStore } = this.props;
		let { dialogData } = approvalTaskStore;
		let { workflowApprovalData } = dialogData;
		let { diffTime } = workflowApprovalData;

		return (
			<div>
				<RadioGroup
					value={diffTime}
					style={{ marginBottom: "10px" }}
					onChange={this.changeDiffTime.bind(this)}
				>
					<RadioButton value="pend">
						{/* lang:现在的版本 */}
						{approvalTaskLang.modal("currentVersion")}
					</RadioButton>
					<RadioButton value="online">
						{/* lang:之前的版本 */}
						{approvalTaskLang.modal("beforeVersion")}
					</RadioButton>
				</RadioGroup>
				<div className="approval-body" style={{"background": "#f2f3f4"}}>
					<div className="flow-wrap">
						<Flow
							disabled={true}
							isApprovalPage={true}
							bindThis={(that) => {
								this.flowEditor = that;
							}}
						/>
					</div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(WorkflowApprovalDiff);
