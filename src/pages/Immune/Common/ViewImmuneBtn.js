import { PureComponent } from "react";
import { message, Tooltip } from "antd";
import { connect } from "dva";
import moment from "moment";
import { transferSceneList } from "@/utils/scene";

class ViewImmuneBtn extends PureComponent {
	// 获取普通策略规则相关的数据列表
	getNormalPolicyInterface = async(record)=>{
		const { policySetUuid, policyUuid } = record;
		const { dispatch } = this.props;
		// 获取策略集
		await dispatch({
			type: "immuneConfig/fetchPolicySet"
		});
		// 匹配到策略
		const {immuneConfigStore = {} } = this.props;
		const { policySetList } = immuneConfigStore;
		const { policyList = [] } = policySetList.find((v)=>(Number(v.id) === Number(policySetUuid))) || {};
		await dispatch({
			type: "immuneConfig/setAttrValue",
			payload: {
				policyList
			}
		});
		// 获取策略下的规则
		const policy = policyList.find((v)=>(String(v.uuid) === String(policyUuid))) || {};
		await dispatch({
			type: "immuneConfig/getPolicyRules",
			payload: {
				isFirst: true,
				policyUuid: policy.uuid,
				policyVersion: policy.policyVersion
			}
		});
	}
	// 后去公共策略规则相关数据列表
	getPublicPolicyInterface = async(record) => {
		const { policyUuid } = record;
		const { dispatch } = this.props;

		// 获取策略下的规则
		const {immuneConfigStore = {}, globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};

		const { publicPolicyList } = immuneConfigStore;
		const policy = publicPolicyList.find((v)=>(String(v.policyUuid) === String(policyUuid))) || {};

		// 免疫应用
		let { scene } = policy || {};
		let sceneListSource = [];
		if (typeof scene === "string" && scene) {
			scene = JSON.parse(scene);
		};

		if (scene && scene.length > 0) {
			const isGlobal = scene.filter(v=>{
				return v.appName === "GLOBAL_APP";
			});
			if (isGlobal && isGlobal.length > 0) {
				sceneListSource = publicPolicyScene.filter(v=>{
					return v.name !== "GLOBAL_APP";
				});
			} else {
				sceneListSource = transferSceneList(scene, publicPolicyScene); // 数据转换
			}
		}

		await dispatch({
			type: "immuneConfig/setAttrValue",
			payload: {
				sceneListSource
			}
		});

		// 免疫规则
		await dispatch({
			type: "immuneConfig/getPublicPolicyRules",
			payload: {
				isFirst: true,
				policyUuid: policy.policyUuid,
				policyVersion: policy.policyVersion
			}
		});
	}
	// 编辑免疫配置
	viewImmuneConfig= async(record) => {
		const { dispatch, openType, operaData } = this.props;
		const { createdBy, gmtCreate, updateType } = operaData || {};
		await dispatch({
			type: "immuneConfig/setAttrValue",
			payload: {
				openType,
				createdBy,
				gmtCreate,
				updateType
			}
		});

		const { uuid, conditionJson, effectRange, policyType, appName, effectTo, effectFrom, policySetUuid, remarks, policyUuid, ruleUuid, ruleName } = record;
		let params = {
			uuid,
			policyType,
			policySetUuid: Number(policySetUuid),
			conditionJson: conditionJson ? JSON.parse(conditionJson) : {
				logicOperator: "&&",
				priority: "1",
				defaultNode: false,
				children: []
			},
			effectTo: moment(effectTo),
			effectFrom: moment(effectFrom),
			remarks
		};
		// 拆成普通和公共的场景 以方便在两者之间切换的信息丢失
		if (policyType === "normalPolicy") {
			params = {
				...params,
				appName,
				policyUuid,
				ruleName,
				ruleUuid
			};
		} else {
			params = {
				...params,
				publicPolicyUuid: policyUuid,
				publicRuleName: ruleName,
				publicRuleUuid: ruleUuid,
				effectRange: effectRange ? JSON.parse(effectRange) : ""
			};
		}
		dispatch({
			type: "immuneConfig/setAddModifyImmuneData",
			payload: params
		});
		console.log(params);
		// 获取公共策略
		await dispatch({
			type: "immuneConfig/fetchPolicy"
		});
		// 普通、公共策略场景处理
		if (policyType === "normalPolicy") {
			await this.getNormalPolicyInterface(record);
		} else if (policyType === "publicPolicy") {
			await this.getPublicPolicyInterface(record);
		}
		// 弹窗
		await dispatch({
			type: "immuneConfig/setAttrValue",
			payload: {
				addModifyImmune: true
			}
		});
	}
	render() {
		const { record, title, permission, noRightTip, className } = this.props;
		return (
			<Tooltip title={title}>
				<a
					className={className || ""}
					onClick={(e) => {
						e.stopPropagation();
						if (permission) {
							this.viewImmuneConfig(record);
						} else {
							message.warning(noRightTip);
						}
					}}
				>
					{this.props.children}
				</a>
			</Tooltip>
		);
	}
};
export default connect(state => ({
	globalStore: state.global,
	immuneConfigStore: state.immuneConfig
}))(ViewImmuneBtn);
