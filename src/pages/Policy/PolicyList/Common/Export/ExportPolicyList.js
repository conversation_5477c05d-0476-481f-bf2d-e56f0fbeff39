import { PureComponent, Fragment } from "react";
import { message, Tooltip, Icon, notification } from "antd";
import { connect } from "dva";
import { policyEditorAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";
import { policyListLang, commonLang, imExportModeLang } from "@/constants/lang";

class ExportPolicyList extends PureComponent {
	state = {
		exportLoading: false
	}
	// 导出策略集
	exportPolicySets = async(item, e) => {
		e.stopPropagation();
		// 系统默认将策略集下的策略/规则及引用到的指标一并导出
		notification.open({
			message: imExportModeLang.exportInfo("warn"),
			description: imExportModeLang.exportInfo("tipPolicySet")
		});
		const { isEditorArea } = this.props;
		const params = {
			uuid: item.uuid,
			fileName: policyListLang.table("policySetA") + item.name,
			fileType: "pls",
			isEditorArea
		};
		this.setState({
			exportLoading: true
		});
		const testResult = await policyEditorAPI.exportPolicySetsTest(params);
		if (testResult && testResult.hasOwnProperty("success") && !testResult.success) {
			message.warning(testResult.message);
			this.setState({
				exportLoading: false
			});
		} else {
			policyEditorAPI.exportPolicySets(params).then(res => {
				// if (res.success) {
				// 	message.success(policyListLang.table("exportPolicyListSuccessfully"));
				// } else {
				// 	message.error(res.message);
				// }
				this.setState({
					exportLoading: false
				});
			}).catch(err => {
				console.log(err);
				this.setState({
					exportLoading: false
				});
			});
		}
	}

	render() {
		const { record } = this.props;
		const { exportLoading } = this.state;
		return (
			<Fragment>
				{
					checkFunctionHasPermission("ZB0101", "editorExportPolicySet")
						// lang：导出策略集
						? <Tooltip title={policyListLang.tooltip("exportPolicySet")}>
							<a
								onClick={(e) => {
									e.stopPropagation();
									if (!exportLoading) {
										this.exportPolicySets(record, e);
									}
								}}
							>
								{
									exportLoading
										? <Icon type="loading" />
										: <i className="iconfont icon-upload"></i>
								}
							</a>
						</Tooltip>
						: null
				}
			</Fragment>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(ExportPolicyList);
