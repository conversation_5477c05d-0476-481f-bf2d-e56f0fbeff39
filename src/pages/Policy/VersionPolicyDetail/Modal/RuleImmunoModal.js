import { PureComponent } from "react";
import { connect } from "dva";
import { <PERSON>dal, <PERSON>ton, Select, Form, Input, Radio, message, Icon, Row, Col, DatePicker } from "antd";
import { PolicyConstants } from "@/constants";
import { policyDetailAPI } from "@/services";
import { ruleImmunoLang } from "@/constants/lang";
import moment from "moment";

const Option = Select.Option;
const RadioGroup = Radio.Group;
const InputGroup = Input.Group;
const TextArea = Input.TextArea;
const confirm = Modal.confirm;

class RuleImmunoModal extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.commit = this.commit.bind(this);
    	this.disabledDate = this.disabledDate.bind(this);
    	this.addCondition = this.addCondition.bind(this);
    	this.deleteCondition = this.deleteCondition.bind(this);
    	this.changeBaseField = this.changeBaseField.bind(this);
    	this.changeFieldValue = this.changeFieldValue.bind(this);
    	this.clearModalData = this.clearModalData.bind(this);
    	this.deleteThisImmuno = this.deleteThisImmuno.bind(this);
    }

    addCondition(index) {
    	let { policyDetailStore, dispatch } = this.props;
    	let { dialogData } = policyDetailStore;
    	let { ruleImmunoData } = dialogData;
    	let { conditions } = ruleImmunoData;
    	let conditionTemp = {
    		operator: null,
    		leftVarType: "string",
    		leftVar: null,
    		rightVarType: "input",
    		rightVar: null
    	};

    	conditions.push(conditionTemp);
    	dispatch({
    		type: "policyDetail/setDialogData",
    		payload: {
    			ruleImmunoData: ruleImmunoData
    		}
    	});
    };

    deleteCondition(index) {
    	let { policyDetailStore, dispatch } = this.props;
    	let { dialogData } = policyDetailStore;
    	let { ruleImmunoData } = dialogData;
    	let { conditions } = ruleImmunoData;

    	conditions.splice(index, 1);

    	dispatch({
    		type: "policyDetail/setDialogData",
    		payload: {
    			ruleImmunoData: ruleImmunoData
    		}
    	});
    }

    disabledDate(current) {
    	// Can not select days before today and today
    	return current && current < moment();
    }

    changeBaseField(field, type, e) {
    	let { dispatch, policyDetailStore, onChange } = this.props;
    	let { dialogData } = policyDetailStore;
    	let { ruleImmunoData } = dialogData;

    	let value;
    	if (type === "input" || type === "radio") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	} else if (type === "date") {
    		value = e.valueOf();
    	}

    	ruleImmunoData[field] = value;
    	dispatch({
    		type: "policyDetail/setDialogData",
    		payload: {
    			ruleImmunoData: ruleImmunoData
    		}
    	});
    }

    changeFieldValue(index, field, type, e) {
    	let { dispatch, globalStore, policyDetailStore, onChange } = this.props;
    	let { dialogData } = policyDetailStore;
    	let { ruleImmunoData } = dialogData;

    	let { allMap } = globalStore;
    	let { ruleFieldList } = allMap;

    	let value;
    	if (type === "input" || type === "radio") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	} else if (type === "date") {
    		value = e.valueOf();
    	}

    	if (field === "leftVar") {
    		let currentRuleField = ruleFieldList.find(item => item.name === value);
    		ruleImmunoData["conditions"][index]["leftVarType"] = currentRuleField.type;
    		ruleImmunoData["conditions"][index]["operator"] = null;
    	}
    	if (field === "rightVarType") {
    		ruleImmunoData["conditions"][index]["rightVar"] = null;
    	}
    	ruleImmunoData["conditions"][index][field] = value;
    	dispatch({
    		type: "policyDetail/setDialogData",
    		payload: {
    			ruleImmunoData: ruleImmunoData
    		}
    	});
    }

    commit() {
    	let { dispatch, policyDetailStore } = this.props;
    	let { dialogData, dialogShow, policyRules } = policyDetailStore;
    	let { ruleImmunoData } = dialogData;
    	let { ruleUuid, expireAt, logicOperator, description, conditions } = ruleImmunoData;
    	let currentObj = policyRules.find(rule => rule.uuid === ruleUuid);

    	if (!expireAt) {
    		// lang:免打扰时间不能为空
    		message.warning(ruleImmunoLang.tooltip("timeHasEmpty"));
    		return;
    	}
    	if (!logicOperator) {
    		// lang:请选择执行条件
    		message.warning(ruleImmunoLang.tooltip("logicHasEmpty"));
    		return;
    	}
    	if (conditions.length === 0) {
    		// lang:请配置执行规则
    		message.warning(ruleImmunoLang.tooltip("ruleHasEmpty"));
    		return;
    	}
    	let hasEmptyField = false;

    	conditions.map((item, index) => {
    		if (!item.operator || !item.leftVarType || !item.leftVar || !item.rightVarType || !item.rightVar) {
    			hasEmptyField = true;
    		}
    	});
    	if (hasEmptyField) {
    		// lang:执行规则存在空值，请补充完整!
    		message.warning(ruleImmunoLang.tooltip("ruleFieldHasEmpty"));
    		return;
    	}

    	let params = {
    		ruleUuid: ruleUuid,
    		expireAt: expireAt,
    		logicOperator: logicOperator,
    		description: description,
    		conditions: JSON.stringify(conditions, null, 4)
    	};
    	if (dialogShow.addRuleImmuno) {
    		policyDetailAPI.addRuleImmuno(params).then(res => {
    			if (res.success) {
    				// lang:新增免打扰成功
    				message.success(ruleImmunoLang.tooltip("addSuccess"));

    				currentObj["hasImmuno"] = true;
    				dispatch({
    					type: "policyDetail/setAttrValue",
    					payload: {
    						policyRules: policyRules
    					}
    				});
    				dispatch({
    					type: "policyDetail/setDialogShow",
    					payload: {
    						addRuleImmuno: false,
    						modifyRuleImmuno: false
    					}
    				});
    				// 清除数据
    				this.clearModalData();
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	} else {
    		policyDetailAPI.modifyRuleImmuno(params).then(res => {
    			if (res.success) {
    				// lang:修改免打扰成功
    				message.success(ruleImmunoLang.tooltip("modifySuccess"));

    				currentObj["hasImmuno"] = true;
    				dispatch({
    					type: "policyDetail/setAttrValue",
    					payload: {
    						policyRules: policyRules
    					}
    				});
    				dispatch({
    					type: "policyDetail/setDialogShow",
    					payload: {
    						addRuleImmuno: false,
    						modifyRuleImmuno: false
    					}
    				});
    				// 清除数据
    				this.clearModalData();
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	}
    };

    clearModalData() {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "policyDetail/setDialogShow",
    		payload: {
    			addRuleImmuno: false,
    			modifyRuleImmuno: false
    		}
    	});
    	dispatch({
    		type: "policyDetail/setDialogData",
    		payload: {
    			ruleImmunoData: {
    				ruleUuid: null,
    				ruleName: null,
    				expireAt: null,
    				logicOperator: "&&",
    				description: null,
    				conditions: [
    					{
    						operator: null,
    						leftVarType: "string",
    						leftVar: null,
    						rightVarType: "input",
    						rightVar: null
    					}
    				]
    			}
    		}
    	});
    }

    deleteThisImmuno() {
    	let _this = this;
    	confirm({
    		title: ruleImmunoLang.tooltip("deleteImmuneAlertTitle"),		// lang:删除免打扰提醒
    		content: ruleImmunoLang.tooltip("deleteImmuneAlertDesc"),		// lang:确认要删除当前规则免打扰吗？
    		onOk() {
    			let { dispatch, policyDetailStore } = _this.props;
    			let { dialogData, policyRules } = policyDetailStore;
    			let { ruleImmunoData } = dialogData;
    			let { ruleUuid } = ruleImmunoData;
    			let currentObj = policyRules.find(rule => rule.uuid === ruleUuid);

    			let params = {
    				ruleUuid: ruleUuid
    			};
    			policyDetailAPI.deleteRuleImmuno(params).then(res => {
    				if (res.success) {
    					// lang:修改免打扰成功
    					message.success(ruleImmunoLang.tooltip("deleteSuccess"));

    					currentObj["hasImmuno"] = false;
    					dispatch({
    						type: "policyDetail/setAttrValue",
    						payload: {
    							policyRules: policyRules
    						}
    					});
    					dispatch({
    						type: "policyDetail/setDialogShow",
    						payload: {
    							addRuleImmuno: false,
    							modifyRuleImmuno: false
    						}
    					});
    					// 清除数据
    					this.clearModalData();
    				} else {
    					message.error(res.message);
    				}
    			}).catch(err => {
    				console.log(err);
    			});
    		},
    		onCancel() {
    			console.log("Cancel");
    		}
    	});
    }

    render() {
    	let { policyDetailStore, globalStore } = this.props;
    	let { dialogShow, dialogData } = policyDetailStore;
    	let { ruleImmunoData } = dialogData;
    	let { ruleName, expireAt, logicOperator, conditions } = ruleImmunoData;

    	let { allMap, personalMode } = globalStore;
    	let { lang } = personalMode;
    	let { ruleFieldList } = allMap;

    	// lang:添加免打扰or修改免打扰
    	let title = dialogShow.addRuleImmuno ? ruleImmunoLang.common("addTitle") : ruleImmunoLang.common("modifyTitle");

    	let footer;
    	if (dialogShow.addRuleImmuno) {
    		footer = [
    			<Button onClick={this.clearModalData.bind(this)}>
    				{/* lang:取消 */}
    				{ruleImmunoLang.common("cancel")}
    			</Button>,
    			<Button type="primary" onClick={this.commit.bind(this)}>
    				{/* lang:确定 */}
    				{ruleImmunoLang.common("ok")}
    			</Button>
    		];
    	} else {
    		footer = [
    			<Button onClick={this.deleteThisImmuno.bind(this)}>
    				{/* lang:删除当前免打扰 */}
    				{ruleImmunoLang.common("deleteImmunity")}
    			</Button>,
    			<Button onClick={this.clearModalData.bind(this)}>
    				{/* lang:取消 */}
    				{ruleImmunoLang.common("cancel")}
    			</Button>,
    			<Button type="primary" onClick={this.commit.bind(this)}>
    				{/* lang:确定*/}
    				{ruleImmunoLang.common("ok")}
    			</Button>
    		];
    	}

    	return (
    		<Modal
    			title={title}
    			visible={dialogShow.addRuleImmuno || dialogShow.modifyRuleImmuno}
    			maskClosable={false}
    			width="1024px"
    			onOk={this.commit}
    			onCancel={this.clearModalData.bind(this)}
    			footer={footer}
    		>
    			<Form layout="horizontal">
    				<Row gutter={8} className="rule-condition">
    					<Col className="gutter-row" span={4}>
    						<div className="gutter-box">
    							{/* lang:规则名称：*/}
    							{ruleImmunoLang.common("ruleName")}
    						</div>
    					</Col>
    					<Col className="gutter-row" span={12}>
    						<div className="gutter-box">
    							<Input value={ruleName} disabled={true} />
    						</div>
    					</Col>
    				</Row>
    				<Row gutter={8} className="rule-condition">
    					<Col className="gutter-row" span={4}>
    						<div className="gutter-box">
    							{/* lang:免打扰时间：*/}
    							{ruleImmunoLang.common("expireTs")}
    						</div>
    					</Col>
    					<Col className="gutter-row" span={12}>
    						<div className="gutter-box">
    							<DatePicker
    								style={{ width: "100%" }}
    								showTime
    								disabledDate={this.disabledDate.bind(this)}
    								format="YYYY-MM-DD HH:mm:ss"
    								placeholder={ruleImmunoLang.common("expireTsPlaceholder")} // lang:请选择免打扰时间
    								value={expireAt ? moment(expireAt) : null}
    								onChange={this.changeBaseField.bind(this, "expireAt", "date")}
    							/>
    						</div>
    					</Col>
    				</Row>
    				<Row gutter={8} className="rule-condition">
    					<Col className="gutter-row" span={4}>
    						<div className="gutter-box">
    							{/* lang:免打扰说明：*/}
    							{ruleImmunoLang.common("description")}
    						</div>
    					</Col>
    					<Col className="gutter-row" span={12}>
    						<div className="gutter-box">
    							<TextArea
    								onChange={this.changeBaseField.bind(this, "description", "input")}
    								placeholder={ruleImmunoLang.common("descriptionPlaceholder")} // lang:请输入免打扰说明
    							/>
    						</div>
    					</Col>
    				</Row>
    				<Row gutter={8} className="rule-condition">
    					<Col className="gutter-row" span={4}>
    						<div className="gutter-box">
    							{/* lang:执行条件：*/}
    							{ruleImmunoLang.common("logicOperator")}
    						</div>
    					</Col>
    					<Col className="gutter-row" span={20}>
    						<div className="gutter-box">
    							<RadioGroup
    								onChange={this.changeBaseField.bind(this, "logicOperator", "radio")}
    								value={logicOperator}
    							>
    								<Radio value="&&">
    									{/* lang:满足以下所有条件 */}
    									{ruleImmunoLang.common("logic1")}
    								</Radio>
    								<Radio value="||">
    									{/* lang:满足以下任意条件 */}
    									{ruleImmunoLang.common("logic2")}
    								</Radio>
    							</RadioGroup>
    						</div>
    					</Col>
    				</Row>
    				<div className="rule-content">
    					<div
    						className="one-condition custom-item rule-immuno"
    					>
    						<Row gutter={8}>
    							<Col span={3} push={1}></Col>
    							<Col span={21} push={1}>
    								{
    									conditions.map((item, index, arr) => {
    										return (
    											<Row gutter={8} className="one-condition custom-item mb10" key={index}>
    												<Col className="gutter-row" span={6}>
    													<div className="gutter-box">
    														<Select
    															placeholder={ruleImmunoLang.rule("pleaseSelectPlaceholder")}
    															value={item.leftVar || undefined}
    															onChange={this.changeFieldValue.bind(this, index, "leftVar", "select")}
    															showSearch
    															optionFilterProp="children"
    														>
    															{
    																ruleFieldList &&
                                                                    ruleFieldList.map((item, index) => {
                                                                    	return (
                                                                    		<Option
                                                                    			key={index}
                                                                    			value={item.name}
                                                                    		>
                                                                    			{item.dName}
                                                                    		</Option>
                                                                    	);
                                                                    })
    															}
    														</Select>
    													</div>
    												</Col>
    												<Col className="gutter-row" span={3}>
    													<div className="gutter-box">
    														<Select
    															placeholder={ruleImmunoLang.rule("pleaseSelectPlaceholder")}
    															value={item.operator || undefined}
    															onChange={this.changeFieldValue.bind(this, index, "operator", "select")}
    														>
    															{
    																PolicyConstants.conditionOperator[item.leftVarType] &&
                                                                    PolicyConstants.conditionOperator[item.leftVarType].map((item, index) => {
                                                                    	return (
                                                                    		<Option
                                                                    			key={index}
                                                                    			value={item.name}
                                                                    		>
                                                                    			{lang === "en" ? item.enDName : item.dName}
                                                                    		</Option>
                                                                    	);
                                                                    })
    															}
    														</Select>
    													</div>
    												</Col>
    												<Col className="gutter-row" span={10}>
    													<div className="gutter-box">
    														<InputGroup compact>
    															<Select
    																style={{ width: "30%" }}
    																placeholder={ruleImmunoLang.rule("pleaseSelectPlaceholder")}
    																value={item.rightVarType || undefined}
    																onChange={this.changeFieldValue.bind(this, index, "rightVarType", "select")}
    															>
    																<Option value="input">
    																	{/* lang:常量 */}
    																	{ruleImmunoLang.rule("constant")}
    																</Option>
    																<Option value="context">
    																	{/* lang:变量 */}
    																	{ruleImmunoLang.rule("variable")}
    																</Option>
    															</Select>
    															{
    																item.rightVarType === "input" &&
                                                                    <Input
                                                                    	style={{ width: "70%" }}
                                                                    	value={item.rightVar || undefined}
                                                                    	placeholder={ruleImmunoLang.rule("constantInputPlaceholder")} // lang:请输入常量值
                                                                    	onChange={this.changeFieldValue.bind(this, index, "rightVar", "input")}
                                                                    />
    															}
    															{
    																item.rightVarType === "context" &&
                                                                    <Select
                                                                    	placeholder={ruleImmunoLang.rule("pleaseSelectPlaceholder")}
                                                                    	style={{ width: "70%" }}
                                                                    	value={item.rightVar || undefined}
                                                                    	onChange={this.changeFieldValue.bind(this, index, "rightVar", "select")}
                                                                    	showSearch
                                                                    	optionFilterProp="children"
                                                                    >
                                                                    	{
                                                                    		ruleFieldList &&
                                                                            ruleFieldList.map((item, index) => {
                                                                            	return (
                                                                            		<Option
                                                                            			key={index}
                                                                            			value={item.name}
                                                                            		>
                                                                            			{item.dName}
                                                                            		</Option>
                                                                            	);
                                                                            })
                                                                    	}
                                                                    </Select>
    															}
    														</InputGroup>
    													</div>
    												</Col>
    												<Col className="gutter-row" span={3}>
    													<div className="gutter-box oper-icon">
    														<Icon
    															className="add"
    															type="plus-circle-o"
    															onClick={this.addCondition.bind(this, index)}
    														/>
    														{
    															arr.length > 1 &&
                                                                <Icon
                                                                	className="delete"
                                                                	type="delete"
                                                                	onClick={this.deleteCondition.bind(this, index)}
                                                                />
    														}
    													</div>
    												</Col>
    											</Row>
    										);
    									})
    								}
    							</Col>
    						</Row>
    					</div>
    				</div>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(RuleImmunoModal);

