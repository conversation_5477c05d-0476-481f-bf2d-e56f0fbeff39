import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		tabForEditor: {
			"cn": "编辑区",
			"en": "Editing Area"
		},
		tabForRunning: {
			"cn": "运行区",
			"en": "Running Area"
		},
		titleForEditor: {
			"cn": "编辑区列表",
			"en": "Editing Area List"
		},
		titleForRunning: {
			"cn": "运行区列表",
			"en": "Running Area List"
		},
		addPublicPolicy: {
			"cn": "新建公共策略",
			"en": "Add public policy"
		},
		inputSuggest1: {
			"cn": "建议输入：中文、英文、数字和下划线的组合",
			"en": "Suggested input: combination of Chinese, English, Numbers and underlining"
		},
		application: {
			"cn": "应用",
			"en": "Application"
		},
		policySet: {
			"cn": "策略集",
			"en": "Policy set"
		},
		effectScope: {
			"cn": "生效范围",
			"en": "Effective scope"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		publicPolicyName: {
			"cn": "请输入公共策略名称",
			"en": "Please enter a public policy name"
		},
		effectScope: {
			"cn": "请选择生效范围",
			"en": "Please select the effective range"
		},
		ruleName: {
			"cn": "请输入规则名称",
			"en": "Please enter a rule name"
		},
		ruleCustomId: {
			"cn": "请输入规则编号",
			"en": "Custom Id"
		},
		riskType: {
			"cn": "风险类型",
			"en": "riskType"
		},
		reset: {
			"cn": "重置",
			"en": "Reset"
		},
		search: {
			"cn": "查询",
			"en": "Search"
		},
		expand: {
			"cn": "展开",
			"en": "Expand"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"publicPolicyName": {
			"cn": "公共策略名称",
			"en": "Public policy name"
		},
		"version": {
			"cn": "版本号",
			"en": "Version"
		},
		"effectScope": {
			"cn": "生效范围",
			"en": "Effective scope"
		},
		"description": {
			"cn": "描述",
			"en": "Description"
		},
		"modifyTime": {
			"cn": "修改时间",
			"en": "Modification time"
		},
		"modifier": {
			"cn": "修改人",
			"en": "Modifier"
		},
		"operation": {
			"cn": "操作",
			"en": "Operation"
		}
	};
	return params[field][lang];
};

const tooltip = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"viewReference": {
			"cn": "查看引用",
			"en": "View reference"
		},
		"offline": {
			"cn": "下线",
			"en": "Offline"
		},
		"importPublicPolicy": {
			"cn": "导入规则",
			"en": "Import rules"
		},
		"editPublicPolicy": {
			"cn": "编辑公共策略",
			"en": "Edit public policy"
		},
		"delPublicPolicy": {
			"cn": "删除公共策略",
			"en": "Delete public policy"
		}
	};
	return params[field][lang];
};

const publicPolicyModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"effectScope": {
			"cn": "生效范围",
			"en": "Effect scope"
		},
		"customEffectScope": {
			"cn": "自定义生效范围",
			"en": "Custom effective range"
		},
		"policyName": {
			"cn": "策略名称",
			"en": "Policy Name"
		},
		"riskType": {
			"cn": "风险类型",
			"en": "Risk Type"
		},
		"policyMode": {
			"cn": "策略模式",
			"en": "Policy Mode"
		},
		"riskThreshold": {
			"cn": "风险阈值",
			"en": "Risk threshold"
		},
		"interrupted": {
			"cn": "是否中断",
			"en": "Interrupted"
		},
		"noInterruption": {
			"cn": "不中断",
			"en": "No interruption"
		},
		"interruption": {
			"cn": "中断",
			"en": "interruption"
		},
		"interruptCondition": {
			"cn": "中断条件",
			"en": "Interruption condition"
		},
		"riskResult": {
			"cn": "风险决策结果",
			"en": "Risk decision results"
		},
		"description": {
			"cn": "描述",
			"en": "Description"
		},
		"custom": {
			"cn": "自定义",
			"en": "Custom"
		},
		"enterEffectScope": {
			"cn": "请选择生效范围",
			"en": "Please select the effective range"
		},
		"enterCustomEffectScope": {
			"cn": "请选择自定义生效范围",
			"en": "Please select a custom effective range"
		},
		"enterPolicyName": {
			"cn": "请输入策略名称",
			"en": "Please enter policy Name"
		},
		"max72": {
			"cn": "长度控制在72个字符长度",
			"en": "The length is controlled at 72 characters"
		},
		"policyRegTip": {
			"cn": "策略名称：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合",
			"en": "Policy name: please do not enter illegal characters such as complete Angle character, crossed, suggested that the combination of input Chinese, English, Numbers, and underscores"
		},
		"enterRiskType": {
			"cn": "请选择风险类型",
			"en": "Please select Risk Type"
		},
		"selectPolicyMode": {
			"cn": "请选择策略模式",
			"en": "Please select Policy Mode"
		},
		"enterRiskThreshold": {
			"cn": "请设置风险阈值",
			"en": "Please set the risk threshold"
		},
		"enterPolicyDescription": {
			"cn": "请输入策略描述",
			"en": "Please enter policy description"
		},
		"enterOperator": {
			"cn": "请选择中断操作符",
			"en": "Please select break operator"
		},
		"enterRes": {
			"cn": "请设置决策结果",
			"en": "Please set the decision result"
		},
		"enterBreakCon": {
			"cn": "请选择中断条件",
			"en": "Please select interrupt condition"
		},
		"max200": {
			"cn": "长度控制在200个字符长度",
			"en": "The length is controlled at 200 characters"
		},
		//	operator
		"policyNameIsEmptyTip": {
			"cn": "策略名称不能为空",
			"en": "The policy name cannot be empty"
		},
		"policyLenTip": {
			"cn": "策略名称长度应大于1,小于50",
			"en": "The policy name length should be greater than 1 and less than 50"
		},
		"newPolicySuccessTip": {
			"cn": "新建公共策略成功",
			"en": "The new public policy was successful"
		},
		"modifyPolicySetSuccessTip": {
			"cn": "修改公共策略成功",
			"en": "Modify public policy was successful"
		},
		"riskThresholdEmptyTip": {
			"cn": "风险阈值配置存在空值，请补充完整！",
			"en": "The risk threshold configuration has a null value, please complete!"
		}
	};
	return params[field][lang];
};
const offLine = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"confirmOffLine": {
			"cn": "确认下线",
			"en": "Confirm offline"
		},
		"offPolicyDesc1": {
			"cn": "您真的要下线《",
			"en": "You really want to go offline "
		},
		"offPolicyDesc2": {
			"cn": "》吗？",
			"en": "?"
		}
	};
	return params[field][lang];
};
const deleteModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"deletePolicyTip": {
			"cn": "确认删除公共策略",
			"en": "Delete public policy tip"
		},
		"deletePolicyDesc1": {
			"cn": "您真的要删除《",
			"en": "Do you really want to delete "
		},
		"deletePolicyDesc2": {
			"cn": "》吗？",
			"en": "?"
		},
		"deletePolicySetRegTip": {
			"cn": "当前策略集下存在策略，不能删除！",
			"en": "A policy exists under the current policy set and cannot be deleted!"
		}
	};
	return params[field][lang];
};

const quoteDrawer = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		title: {
			"cn": "公共策略引用详情",
			"en": "Public policy reference details"
		},
		modifyTime: {
			"cn": "修改时间",
			"en": "Modification time"
		},
		modifier: {
			"cn": "修改人",
			"en": "Modifier"
		},
		runtimeReferDetail: {
			"cn": "运行区引用明细",
			"en": "Runtime reference details"
		},
		editorReferDetail: {
			"cn": "编辑区引用明细",
			"en": "Edit area reference details"
		},
		application: {
			"cn": "应用",
			"en": "Application"
		},
		policySet: {
			"cn": "策略集",
			"en": "Policy set"
		},
		noRefer: {
			"cn": "暂无引用",
			"en": "No reference"
		},
		noRight: {
			"cn": "暂无权限查看",
			"en": "No permission to view"
		}
	};
	return params[field][lang];
};
const detailDrawer = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"title": {
			"cn": "公共策略详情",
			"en": "Public policy details"
		},
		"riskType": {
			"cn": "风险类型",
			"en": "Risk Type"
		},
		"policyMode": {
			"cn": "策略模式",
			"en": "Policy Mode"
		},
		"policyDescription": {
			"cn": "策略描述",
			"en": "Description"
		},
		"noDescription": {
			"cn": "暂无描述",
			"en": "No Description"
		},
		"lastModify": {
			"cn": "最后修改",
			"en": "Last Modify"
		},
		"modifyTime": {
			"cn": "修改时间",
			"en": "Modify Time"
		},
		"ruleList": {
			"cn": "规则列表",
			"en": "Rule List"
		},
		"tag": {
			"cn": "策略标签",
			"en": "Tag"
		},
		"effectScope": {
			"cn": "生效范围",
			"en": "Effective scope"
		}
	};
	return params[field][lang];
};

const fetchError = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"policyDetail": {
			"cn": "查看公共策略详情失败",
			"en": "Failed to view public policy details"
		},
		"quote": {
			"cn": "查看引用失败",
			"en": "View reference failed"
		}
	};
	return params[field][lang];
};

const ruleRecords = (total, x, y, z) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;

	let cn = "当前策略规则总计：" + total + "条，启用：" + x + "条，禁用：" + y + "条，模拟：" + z + "条";
	let en = "Total number of current policy rules: " + total + ", enabled: " + x + ", disabled: " + y + ", simulated: " + z;
	return personalMode && personalMode.lang === "cn" ? cn : en;
};

export default {
	common,
	searchParams,
	table,
	tooltip,
	publicPolicyModal,
	deleteModal,
	quoteDrawer,
	detailDrawer,
	ruleRecords,
	offLine,
	fetchError
};
