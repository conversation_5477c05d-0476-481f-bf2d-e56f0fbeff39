import React, { PureComponent } from "react";
import { Tabs } from "antd";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { searchToObject } from "@/utils/utils";
import checkPermissionPublicPolicy from "@/constants/permission/checkPermissionPublicPolicy";
import NoPermission from "@/components/NoPermission";
import { publicPolicyListLang } from "@/constants/lang";

const TabPane = Tabs.TabPane;
const EditorArea = React.lazy(() => import("./EditorArea"));
const RunningArea = React.lazy(() => import("./RunningArea"));

class PublicPolicy extends PureComponent {
	constructor(props) {
		super(props);
	}

	switchTab = (key) => {
		this.closeModal();
		const { location, dispatch } = this.props;
		const { pathname } = location;
		const search = "?currentTab=" + key;
		dispatch(routerRedux.push(pathname + search));
	}

	// 关闭打开的弹窗或者抽屉
	closeModal = () => {
		// 关闭公共策略详情抽屉
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyRunning/setDialogShow",
			payload: {
				policyDrawer: false
			}
		});
		dispatch({
			type: "publicPolicyRunning/setDialogData",
			payload: {
				policyDrawer: {
					policyDetail: {},
					policyUuid: null
				}
			}
		});
		// 关闭编辑区详情
		dispatch({
			type: "publicPolicyEditor/setDialogShow",
			payload: {
				policyDrawer: false
			}
		});
		dispatch({
			type: "publicPolicyEditor/setDialogData",
			payload: {
				policyDrawer: {
					policyDetail: {},
					uuid: null
				}
			}
		});
	}
	render() {
		const { globalStore, location } = this.props;
		const { menuTreeReady } = globalStore;
		const { search } = location;
		const searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		const currentTab = searchObj && searchObj.currentTab ? parseInt(searchObj.currentTab, 10) : 1;
		return (
			<div>
				<div className="main-area-wrap">
					<div className="page-global-tab">
						<Tabs
							activeKey={currentTab.toString()}
							onChange={this.switchTab}
							animated={false}
						>
							<TabPane tab={publicPolicyListLang.common("tabForRunning")} key="1">
								{
									menuTreeReady &&
									checkPermissionPublicPolicy.PubPolicyListOnline() &&
									<React.Suspense fallback={null}>
										<RunningArea pagePosition="runningArea" location={location} />
									</React.Suspense>
								}
								{
									menuTreeReady && !checkPermissionPublicPolicy.PubPolicyListOnline() &&
									<NoPermission />
								}
							</TabPane>
							<TabPane tab={publicPolicyListLang.common("tabForEditor")} key="2">
								{
									menuTreeReady && checkPermissionPublicPolicy.PubPolicyList() &&
									<React.Suspense fallback={null}>
										<EditorArea pagePosition="editorArea" location={location} />
									</React.Suspense>
								}
								{
									menuTreeReady && !checkPermissionPublicPolicy.PubPolicyList() &&
									<NoPermission />
								}
							</TabPane>
						</Tabs>
					</div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	publicPolicyRunningStore: state.publicPolicyRunning,
	publicPolicyEditorStore: state.publicPolicyEditor
}))(PublicPolicy);
