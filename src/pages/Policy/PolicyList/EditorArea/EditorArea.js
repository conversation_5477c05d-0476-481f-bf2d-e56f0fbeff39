import React, { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { policyEditorAPI, policyRunningAPI } from "@/services";
import { Table, Button, Pagination, Tag, message, Modal, Tooltip, Input, Select, Spin } from "antd";
import { checkFunctionHasPermission } from "@/utils/permission";
import { policyListLang, commonLang } from "@/constants/lang";
import { PolicyConstants } from "@/constants";
import { searchToObject } from "@/utils/utils";

import ExportRules from "../Common/Export/ExportRules";
import ExportPolicyList from "../Common/Export/ExportPolicyList";
import ViewPublicBtn from "../Common/ViewPublicBtn/ViewPublicBtn";

const PolicyModal = React.lazy(() => import("@/components/PolicyList/PolicyModal"));
const AddPolicySetModal = React.lazy(() => import("@/components/PolicyList/PolicySetModal/AddPolicySetModal"));
const ModifyPolicySetModal = React.lazy(() => import("@/components/PolicyList/PolicySetModal/ModifyPolicySetModal"));
const PolicyDrawer = React.lazy(() => import("@/components/PolicyList/PolicyDrawer"));
const RulesImportModal = React.lazy(() => import("@/components/ImportModal/RulesImportModal"));
const PolicySetImportModal = React.lazy(() => import("@/components/ImportModal/PolicySetImportModal"));
const PublicPolicyModal = React.lazy(() => import("@/components/PublicPolicy/PublicPolicyModal"));
const ModifyOutputParams = React.lazy(() => import("@/components/PolicyList/OutputParamsModal/ModifyOutputParams"));

const InputGroup = Input.Group;
const Search = Input.Search;
const Option = Select.Option;
const confirm = Modal.confirm;

class RuleTemplate extends PureComponent {

	constructor(props) {
		super(props);
	}

	componentWillMount() {
		// 页面初始化数据
		const { dispatch, location } = this.props;
		const { search } = location;
		const searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		const { policySetName, policyUuid } = searchObj || {};
		dispatch({
			type: "policyEditor/setSearchData",
			payload: {
				"searchValue": policySetName
			}
		});
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policyDrawer: false
			}
		});
		dispatch({
			type: "policyEditor/setAttrValue",
			payload: {
				expandedRowKeys: policyUuid ? [policyUuid] : []
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				policyDrawer: {
					policyDetail: {},
					uuid: null
				}
			}
		});
	}

	componentDidMount() {
		this.timer = setInterval(() => {
			const { globalStore } = this.props;
			const { menuTreeReady } = globalStore;

			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkFunctionHasPermission("ZB0101", "editorSearch")) {
					this.search();
				}
			}
		}, 100);
	}

	search() {
		const { policyEditorStore, globalStore, dispatch } = this.props;
		const { curPage, pageSize, searchData } = policyEditorStore;
		const { currentApp } = globalStore;
		if (currentApp && currentApp.name) {
			dispatch({
				type: "policyEditor/getPolicySets",
				payload: {
					appName: currentApp.name ? currentApp.name : null,
					curPage: curPage,
					pageSize: pageSize,
					tag: searchData.tag,
					[searchData.searchField]: searchData.searchValue
				}
			});
		}
	}

	componentWillReceiveProps(nextProps) {
		let { policyEditorStore, dispatch, globalStore } = this.props;
		let { menuTreeReady } = globalStore;
		let { curPage, pageSize, searchData } = policyEditorStore;

		if (menuTreeReady && checkFunctionHasPermission("ZB0101", "editorSearch")) {
			const preCurrentApp = this.props.globalStore.currentApp;
			const nextCurrentApp = nextProps.globalStore.currentApp;

			if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
				curPage = 1;
				// 检测到切换应用，开始刷新列表
				// 首先清空搜索项目
				dispatch({
					type: "policyEditor/setAttrValue",
					payload: {
						searchData: {
							searchField: "name",
							searchValue: null
						}
					}
				});
				dispatch({
					type: "policyEditor/getPolicySets",
					payload: {
						appName: nextCurrentApp.name ? nextCurrentApp.name : null,
						curPage: curPage,
						pageSize: pageSize,
						tag: searchData.tag
					}
				});
			}
		}
	}

	addPolicySetHandle = (e) => {
		let { policyEditorStore, globalStore, dispatch } = this.props;
		let { currentApp, appList } = globalStore;
		let { dialogData } = policyEditorStore;
		let { addPolicySetData } = dialogData;
		e.stopPropagation();

		dispatch({
			type: "policyEditor/setAttrValue",
			payload: {
				modalType: "addPolicySet"
			}
		});
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				addPolicySet: true
			}
		});
		let firstName = appList && appList.length > 1 ? appList[1]["name"] : null;
		addPolicySetData["appName"] = currentApp.name && currentApp.name !== "all" ? currentApp.name : firstName;
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				addPolicySetData: addPolicySetData
			}
		});
	}

	modifyPolicySetHandle = (item, e) => {
		e.stopPropagation();
		const { dispatch } = this.props;

		dispatch({
			type: "policyEditor/setAttrValue",
			payload: {
				modalType: "modifyPolicySet"
			}
		});
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				modifyPolicySet: true
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				modifyPolicySetData: {
					uuid: item.uuid,
					partnerCode: item.partnerCode,
					appName: item.appName,
					appDisplayName: item.appDisplayName,
					name: item.name,
					description: item.description,
					type: item.type,
					eventType: item.eventType,
					eventId: item.eventId
				}
			}
		});
	}

	deletePolicySetHandle = (item, e) => {
		e.stopPropagation();
		if (item.policyList && item.policyList.length) {
			// lang:当前策略集下存在策略，不能删除！
			message.error(policyListLang.deleteModal("deletePolicySetRegTip"));
			return;
		} else {
			const uuid = item.uuid;
			const { policyEditorStore, globalStore, dispatch } = this.props;
			const { curPage, pageSize } = policyEditorStore;
			const { currentApp } = globalStore;

			confirm({
				title: policyListLang.deleteModal("deletePolicySetTip"),	// lang:删除策略集提醒
				content: policyListLang.deleteModal("deletePolicyDesc1") + item.name + policyListLang.deleteModal("deletePolicyDesc2"),		// lang:您真的要删除《发版策略》吗？
				onOk() {
					policyEditorAPI.deletePolicySet(uuid).then(res => {
						if (res.success) {
							dispatch({
								type: "policyEditor/getPolicySets",
								payload: {
									appName: currentApp.name ? currentApp.name : null,
									curPage: curPage,
									pageSize: pageSize
								}
							});

							dispatch({
								type: "global/getAllMap",
								payload: {}
							});
						} else {
							message.error(res.message);
						}
					}).catch(err => {
						console.log(err);
					});
				},
				onCancel() {
					console.log("Cancel");
				}
			});
		}
	}

	addPolicyHandle = (item, e) => {
		e.stopPropagation();
		const { dispatch } = this.props;

		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policyModal: true
			}
		});
		dispatch({
			type: "policyEditor/setAttrValue",
			payload: {
				modalType: "addPolicy"
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				addPolicyData: {
					partner: item.partnerCode,
					fkPolicySetUuid: item.uuid,
					name: null,
					riskType: null,
					mode: null,
					riskEventType: item.eventType,
					riskEventId: item.eventId,
					appName: item.appName,
					description: null,
					level: null,
					dealTypeCount: 3,
					dealTypeMappings: [
						{
							"score": null,
							"dealType": null
						},
						{
							"score": null,
							"dealType": null
						},
						{
							"score": null,
							"dealType": null
						}
					]
				}
			}
		});
	}

	deletePolicyHandle = (item, e) => {
		e.stopPropagation();
		const params = {
			uuid: item.uuid
		};
		const { policyEditorStore, globalStore, dispatch } = this.props;
		const { curPage, pageSize } = policyEditorStore;
		const { currentApp } = globalStore;

		confirm({
			title: policyListLang.deleteModal("deletePolicyTip"),	// lang:删除策略提醒
			content: policyListLang.deleteModal("deletePolicyDesc1") + item.name + policyListLang.deleteModal("deletePolicyDesc2"),		// lang:您真的要删除《发版策略》吗？
			onOk() {
				policyEditorAPI.deletePolicy(params).then(res => {
					if (res.success) {
						dispatch({
							type: "policyEditor/getPolicySets",
							payload: {
								appName: currentApp.name ? currentApp.name : null,
								curPage: curPage,
								pageSize: pageSize
							}
						});

						dispatch({
							type: "global/getAllMap",
							payload: {}
						});
					} else {
						message.error(res.message);
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel() {
				console.log("Cancel");
			}
		});
	}

	paginationOnChange = (current, pageSize) => {
		const { policyEditorStore, globalStore, dispatch } = this.props;
		const { currentApp } = globalStore;
		const { searchData } = policyEditorStore;

		dispatch({
			type: "policyEditor/setAttrValue",
			payload: {
				curPage: current,
				pageSize: pageSize
			}
		});

		const payload = {
			appName: currentApp.name ? currentApp.name : null,
			curPage: current,
			pageSize: pageSize,
			tag: searchData.tag
		};

		payload[searchData.searchField] = searchData.searchValue;

		dispatch({
			type: "policyEditor/getPolicySets",
			payload: payload
		});
	}

	viewPolicyDetailHandle = (item, e) => {
		const { policyEditorStore, dispatch } = this.props;
		const { dialogData } = policyEditorStore;
		e.stopPropagation();

		if (dialogData.policyDrawer && dialogData.policyDrawer.uuid && dialogData.policyDrawer.uuid === item.uuid) {
			dispatch({
				type: "policyEditor/setDialogShow",
				payload: {
					policyDrawer: false
				}
			});
			dispatch({
				type: "policyEditor/setDialogData",
				payload: {
					policyDrawer: {
						policyDetail: {},
						uuid: null
					}
				}
			});
			return;
		}
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policyDrawer: true
			}
		});

		// 请求策略详情
		const params = {
			uuid: item.uuid
		};
		policyEditorAPI.getPolicyDetail(params).then(res => {
			const { dispatch } = this.props;

			if (res.success) {
				dispatch({
					type: "policyEditor/setDialogData",
					payload: {
						policyDrawer: {
							policyDetail: res.data || {},
							uuid: item.uuid
						}
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	changeSearchValue = (searchField, type, e) => {
		const { policyEditorStore, dispatch } = this.props;
		const { searchData } = policyEditorStore;

		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		searchData[searchField] = value;

		dispatch({
			type: "policyEditor/setSearchData",
			payload: searchData
		});
	}

	// 导入策略集
	importPolicySets = (item, e) => {
		e.stopPropagation();
		const { dispatch } = this.props;

		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policySetImport: true
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				policySetImportData: {
					policySetName: item.name,
					uuid: item.uuid,
					importMode: "SKIP",
					file: null,
					replaceScene: "",
					zbMode: "",
					policyMode: ""
				}
			}
		});
	}

	// 导入规则
	importRules = (item, e) => {
		e.stopPropagation();
		const { dispatch } = this.props;

		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				RulesImport: true
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				rulesImportData: {
					uuid: item.uuid,
					importMode: "SKIP",
					file: null,
					replaceScene: "",
					zbMode: "",
					ruleMode: ""
				}
			}
		});
	}

	renderSubTable = (policyList, rowRecord) => {
		let { globalStore } = this.props;
		let { personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		let statusMap = lang === "cn" ? PolicyConstants.policyStatusMap : PolicyConstants.policyStatusMap2;

		let tableRows = policyList.map((item, index) => {
			return (
				<div
					className="row"
					key={index}
					onClick={(e) => {
						this.viewPolicyDetailHandle(item, e);
					}}
				>
					<div style={{ flex: "1 1 0%", lineHeight: "30px" }}>
						<a onClick={() => {
							if (checkFunctionHasPermission("ZB0101", "getPolicy")) {
								let { dispatch, location } = this.props;
								let pathname = location.pathname;
								let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
								dispatch(routerRedux.push(prefix + "/policy/policyDetail/" + item.uuid + "?tabIndex=1"));
							} else {
								// lang:无权限操作
								message.warning(commonLang.messageInfo("noPermission"));
							}
						}}>
							<span className="mr10">
								{item.name}
							</span>
							{
								statusMap[item.status] &&
								<Tag color={statusMap[item.status]["color"]}>
									{statusMap[item.status]["text"]}
								</Tag>
							}
						</a>
					</div>
					<div className="row-item" style={{ width: "160px", lineHeight: "30px" }}>
						{rowRecord.appDisplayName}
					</div>
					<div className="row-item" style={{ width: "212px", lineHeight: "30px" }}>
						<Tooltip title={item.description && item.description.length > 15 ? item.description : undefined}>
							<div
								className="text-overflow"
								style={{ width: "180px" }}
							>
								{item.description}
							</div>
						</Tooltip>
					</div>
					<div className="row-item" style={{ width: "204px" }}>
						<div className="table-action t-r plr8">
							{/* lang:导入规则 */}
							<Tooltip title={policyListLang.tooltip("importRules")}>
								<a onClick={(e) => {
									if (checkFunctionHasPermission("ZB0101", "editorImportRules")) {
										this.importRules(item, e);
									} else {
										// lang:无权限操作
										e.stopPropagation();
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}}><i className="iconfont icon-download"></i></a>
							</Tooltip>

							{/* lang:导出规则 */}
							<ExportRules record={item} isEditorArea={true} />

							{/* lang:编辑策略 */}
							<Tooltip title={policyListLang.tooltip("editPolicy")}>
								<a onClick={() => {
									if (checkFunctionHasPermission("ZB0101", "editorModifyPolicy")) {
										let { dispatch, location } = this.props;
										let pathname = location.pathname;
										let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
										dispatch(routerRedux.push(prefix + "/policy/policyDetail/" + item.uuid + "?tabIndex=0"));
									} else {
										// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}}>
									<i className="iconfont icon-edit"></i>
								</a>
							</Tooltip>
							{/* lang:删除策略 */}
							<Tooltip title={policyListLang.tooltip("deletePolicy")}>
								<a onClick={(e) => {
									if (checkFunctionHasPermission("ZB0101", "editorDeletePolicy")) {
										this.deletePolicyHandle(item, e);
									} else {
										// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}}>
									<i className="iconfont icon-delete delete"></i>
								</a>
							</Tooltip>
						</div>
					</div>
				</div>
			);
		});
		let noneData = (
			<div className="none-data">
				<i className="iconfont icon-empty"></i>
				{/* lang:当前策略集暂无策略 */}
				<p>
					{policyListLang.common("policySetNoPolicy")}
				</p>
			</div>
		);
		return <div className="table-row">
			{
				policyList && policyList.length ? tableRows : noneData
			}
		</div>;
	};

	openWorkflow = async(item, e) => {
		e.stopPropagation();
		const { dispatch } = this.props;

		// 根据策略集获取规则流数据
		await dispatch({
			type: "workflow/getDecisionFlow",
			payload: {
				policySetUuid: item.uuid
			}
		});
		await dispatch({
			type: "workflow/setAttrValue",
			payload: {
				policySetItem: item,
				pageName: "policyEditor"
			}
		});
		await dispatch({
			type: "workflow/setDialogShow",
			payload: {
				workflow: true
			}
		});
	}

	/*
		查看公共策略列表模块
	*/
	// 查看公共策略列表
	searchPublicPolicy = ({ curPage = 1, pageSize = 10, cb }) => {
		const { dispatch, policyEditorStore } = this.props;
		const { curUuid } = policyEditorStore;
		policyRunningAPI.refPublicPolicy({
			publicSetUuid: curUuid,
			isEditorArea: true,
			curPage,
			pageSize
		}).then(res => {
			if (res.success) {
				dispatch({
					type: "policyEditor/setDialogData",
					payload: {
						publicPolicyData: res.data || {}
					}
				});
				cb && cb();
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}
	viewPublicPolicy = (record) => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyEditor/setAttrValue",
			payload: {
				curUuid: record.uuid
			}
		});
		this.searchPublicPolicy({
			curPage: 1,
			pageSize: 10,
			cb: () => {
				this.controlPublicPolicy(true);
			}
		});
	}
	// 控制应用的公共策略弹窗
	controlPublicPolicy = (visible) => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				publicPolicy: visible
			}
		});
		// 如果是关闭
		if (!visible) {
			dispatch({
				type: "policyEditor/setAttrValue",
				payload: {
					curUuid: ""
				}
			});
			dispatch({
				type: "policyEditor/setDialogData",
				payload: {
					publicPolicyData: {}
				}
			});
		}
	}
	// 新增出参
	addOutputParams = async(record, e) => {
		e.stopPropagation();
		const { uuid } = record;
		const {dispatch} = this.props;
		await dispatch({
			type: "policyEditor/getCustomReturnFields",
			payload: {
				publicSetUuid: uuid
			}
		});
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				outputParams: true
			}
		});
	}
	render() {
		let { policyEditorStore, globalStore, pagePosition, dispatch, location } = this.props;
		let { personalMode, allMap: { policyTags = [] } } = globalStore;
		let { policySetsList, curPage, total, expandedRowKeys, searchData, policySetsListLoad, dialogShow, dialogData } = policyEditorStore;
		const { publicPolicy } = dialogShow;
		const { publicPolicyData } = dialogData;
		let columns = [
			{
				title: policyListLang.table("policySet"),
				dataIndex: "name",
				render: (text, record) => {
					return (
						<div>
							<span className="mr10">{text}</span>
							{/* 公共策略 */}
							<ViewPublicBtn record={record} viewPublicPolicy={this.viewPublicPolicy} />
						</div>
					);
				}
			},
			{
				title: policyListLang.table("app"),
				width: 160,
				dataIndex: "appDisplayName"
			},
			{
				title: policyListLang.table("description"),
				width: 180,
				dataIndex: "description",
				key: "description",
				render: (text) => {
					return (
						<Tooltip title={text && text.length > 15 ? text : undefined}>
							<div
								className="text-overflow"
								style={{ width: "180px" }}
							>
								{text}
							</div>
						</Tooltip>
					);
				}
			},
			{
				title: policyListLang.table("operation"),
				width: 220,
				dataIndex: "",
				render: (record) => {
					return (
						<div className="table-action">
							{/* lang:设置执行流程*/}
							{
								checkFunctionHasPermission("ZB0101", "editorDecisionFlow")
									? <Tooltip title={policyListLang.tooltip("setUpTheExecutionProcess")}>
										<a
											className={record.hasDecisionFlow ? "blue" : ""}
											onClick={(e) => {
												if (checkFunctionHasPermission("ZB0101", "editorDecisionFlow")) {
													this.openWorkflow(record, e);
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}}
										><i className="iconfont icon-flow"></i></a>
									</Tooltip> : null
							}
							{
								checkFunctionHasPermission("ZB0101", "editorImportPolicyList")
									// lang：导入策略列表
									? <Tooltip title={policyListLang.tooltip("importPolicyList")}>
										<a onClick={(e) => {
											if (true) {
												this.importPolicySets(record, e);
											} else {
												// lang:无权限操作
												e.stopPropagation();
												message.warning(commonLang.messageInfo("noPermission"));
											}
										}}><i className="iconfont icon-download"></i></a>
									</Tooltip>
									: null
							}

							{/* 导出策略集合 */}
							<ExportPolicyList record={record} isEditorArea={true} />

							{
								checkFunctionHasPermission("ZB0101", "editorAddPolicy")
									// lang:添加策略
									? <Tooltip title={policyListLang.tooltip("addPolicy")}>
										<a onClick={(e) => {
											this.addPolicyHandle(record, e);
										}}>
											<i className="iconfont icon-plus-square-o"></i>
										</a>
									</Tooltip>
									: null
							}
							{
								checkFunctionHasPermission("ZB0101", "editorModifyPolicySet")
									// lang:编辑策略集
									? <Tooltip title={policyListLang.tooltip("editPolicySet")}>
										<a onClick={(e) => {
											this.modifyPolicySetHandle(record, e);
										}}>
											<i className="iconfont icon-edit"></i>
										</a>
									</Tooltip>
									: null
							}
							{
								checkFunctionHasPermission("ZB0101", "editorDeletePolicySet")
									// lang:删除策略集
									? <Tooltip title={policyListLang.tooltip("deletePolicySet")}>
										<a onClick={(e) => {
											this.deletePolicySetHandle(record, e);
										}}>
											<i className="iconfont icon-delete delete"></i>
										</a>
									</Tooltip>
									: null
							}
							{
								checkFunctionHasPermission("ZB0101", "queryResponseConfig")
									// lang:新增出参
									? <Tooltip title={policyListLang.tooltip("addOutput")}>
										<a onClick={(e) => {
											this.addOutputParams(record, e);
										}}>
											<i className="salaxy-iconfont salaxy-chucan"></i>
										</a>
									</Tooltip>
									: null
							}
						</div>
					);
				}
			}
		];

		return (
			<div className="page-global-body">
				<div className="page-global-body-search">
					<div className="left-info">
						<h2>{policyListLang.common(pagePosition === "editorArea" ? "titleForEditor" : "titleForRunning")}</h2>
					</div>
					<div className="right-info">
						{/* lang:全部标签 */}
						{/* <div className="right-info-item">
    						<Select
    							style={{ width: "150px" }}
    							value={searchData["tag"] || ""}
    							onChange={async(e) => {
    								await this.changeSearchValue("tag", "select", e);
    								await this.paginationOnChange(1, 10);
    							}}
    						>
    							<Option value="">
    								{policyListLang.searchParams("allTags")}
    							</Option>
    							{
    								policyTags.map((item, index) => {
    									return (
    										<Option value={item.name} key={index}>
    											{item.dName}
    										</Option>
    									);
    								})
    							}
    						</Select>
    					</div> */}
						<div className="right-info-item">
							<InputGroup compact>
								<Select
									value={searchData["searchField"] || undefined}
									onChange={(e) => {
										this.changeSearchValue("searchField", "select", e);
									}}
									dropdownMatchSelectWidth={false}
								>
									<Option value="name">
										{policyListLang.searchParams("policySetName")}
									</Option>
									<Option value="policyName">
										{policyListLang.searchParams("policyName")}
									</Option>
									<Option value="eventId">
										{policyListLang.searchParams("event")}
									</Option>
									<Option value="ruleName">
										{policyListLang.searchParams("ruleName")}
									</Option>
									<Option value="ruleCustomId">
										{policyListLang.searchParams("ruleCustomId")}
									</Option>
								</Select>
								<Search
									placeholder={policyListLang.searchParams("searchPlaceholder")}
									onSearch={val => {
										this.paginationOnChange(1, 10);
									}}
									value={searchData["searchValue"] || undefined}
									onChange={(e) => {
										this.changeSearchValue("searchValue", "input", e);
									}}
									style={{ width: 200 }}
									enterButton={policyListLang.searchParams("search")} // lang:搜索
								/>
							</InputGroup>
						</div>
						<div className="right-info-item">
							<Button
								type="primary"
								onClick={(e) => {
									this.addPolicySetHandle(e);
								}}
								disabled={!checkFunctionHasPermission("ZB0101", "editorAddPolicySet")}
							>
								{policyListLang.common("addNewPolicySet")}
							</Button>
						</div>
					</div>
				</div>
				<Spin spinning={policySetsListLoad}>
					<div className="page-global-body-main has-table-border">
						<Table
							className="table-out-border"
							columns={columns}
							expandedRowRender={record => {
								let policyList = record["policyList"] ? record["policyList"] : [];
								return this.renderSubTable(policyList, record);
							}}
							pagination={false}
							dataSource={policySetsList}
							rowKey="uuid"
							expandedRowKeys={expandedRowKeys}
							size={personalMode.layout === "default" ? "middle" : "small"}
							onExpand={(expanded, record) => {
								let uuidArray = [];
								if (expandedRowKeys[0] !== record.uuid) {
									uuidArray = record.uuid ? [record.uuid] : [];
								}

								dispatch({
									type: "policyEditor/setAttrValue",
									payload: {
										expandedRowKeys: uuidArray
									}
								});

								dispatch({
									type: "policyEditor/setDialogShow",
									payload: {
										policyDrawer: false
									}
								});
							}}
							onRow={(record) => {
								return {
									onClick: () => {
										let uuidArray = [];
										if (expandedRowKeys[0] !== record.uuid) {
											uuidArray = record.uuid ? [record.uuid] : [];
										}

										dispatch({
											type: "policyEditor/setAttrValue",
											payload: {
												expandedRowKeys: uuidArray
											}
										});

										dispatch({
											type: "policyEditor/setDialogShow",
											payload: {
												policyDrawer: false
											}
										});
									}
								};
							}}
						/>
						<div className="page-global-body-pagination">
							{/* lang:共x条记录 */}
							<span className="count">{commonLang.getRecords(total)}</span>
							<Pagination
								showSizeChanger
								onChange={this.paginationOnChange}
								onShowSizeChange={this.paginationOnChange}
								defaultCurrent={1}
								total={total}
								current={curPage}
							/>
						</div>
					</div>
				</Spin>
				<React.Suspense fallback={null}>
					<PolicyModal />
				</React.Suspense>
				<React.Suspense fallback={null}>
					<AddPolicySetModal />
				</React.Suspense>
				<React.Suspense fallback={null}>
					<ModifyPolicySetModal />
				</React.Suspense>
				<React.Suspense fallback={null}>
					<PolicyDrawer
						isRunning={false}
						location={location}
					/>
				</React.Suspense>
				<React.Suspense fallback={null}>
					<RulesImportModal />
				</React.Suspense>
				<React.Suspense fallback={null}>
					<PolicySetImportModal />
				</React.Suspense>
				<React.Suspense fallback={null}>
					<PublicPolicyModal
						location={location}
						visible={publicPolicy}
						onCancel={this.controlPublicPolicy}
						publicPolicyData={publicPolicyData}
						paginationOnChange={this.searchPublicPolicy}
					/>
				</React.Suspense>
				<React.Suspense fallback={null}>
					<ModifyOutputParams />
				</React.Suspense>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor,
	workflowStore: state.workflow
}))(RuleTemplate);

