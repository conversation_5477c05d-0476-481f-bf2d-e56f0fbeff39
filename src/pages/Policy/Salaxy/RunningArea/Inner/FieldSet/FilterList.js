import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Select, Row, Col } from "antd";
import { CommonConstants, PolicyConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import cloneDeep from "lodash.clonedeep";
import "./Less/FilterList.less";

const InputGroup = Input.Group;
const Option = Select.Option;

let addressSetOperator = [
	{
		name: "similarityMatch",
		dName: "匹配",
		enDName: "Match"
	},
	{
		name: "similarityUnMatch",
		dName: "不匹配",
		enDName: "UnMatch"
	}
];

let commonSetOperator = [
	{
		name: "match",
		dName: "匹配",
		enDName: "match"
	},
	{
		name: "unmatch",
		dName: "不匹配",
		enDName: "unmatch"
	}
];

let belongOperator = [
	{
		name: "belong",
		dName: "属于",
		enDName: "belong"
	},
	{
		name: "unbelong",
		dName: "不属于",
		enDName: "unbelong"
	}
];

class FilterList extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { globalStore, indexData } = this.props;
		let { allMap, personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		let currentIndexObj = indexData;
		let filterList = currentIndexObj && currentIndexObj.filterList ? currentIndexObj.filterList : [];

		return (
			<div>
				<Row gutter={CommonConstants.gutterSpan}>
					<Col span={4} className="basic-info-title">
						{/* lang:过滤条件 */}
						{indexListLang.ruleAttr("filterConditions")}
					</Col>
					<Col
						span={20}
						className="add-new-filter"
					>
						<span style={{ color: "#999", cursor: "default" }}>
							<Icon type="plus-square-o" />
							{/* lang:新增过滤 */}
							{indexListLang.ruleAttr("addFilter")}
						</span>
					</Col>
				</Row>
				<div className="mb20">
					{
						filterList && filterList.map((item, index) => {
							let conditionArr = PolicyConstants.conditionOperator[item.type ? item.type : "STRING"] || [];
							let newBelongOperator = cloneDeep(belongOperator);
							conditionArr.map((cItem) => {
								newBelongOperator.push(cItem);
							});

							return (
								<Row
									gutter={CommonConstants.gutterSpan}
									className="rule-operation-item"
									key={index}
								>
									<Col span={4} className="basic-info-title"></Col>
									<Col span={3}>
										<Select
											value={item["fieldType"] || undefined}
											placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
											disabled={true}
										>
											<Option value="systemField">系统字段</Option>
											<Option value="fieldSet">字段集</Option>
										</Select>
									</Col>

									{/* 如果选择的字段类型是系统字段 */}
									{
										item["fieldType"] &&
                                        item["fieldType"] === "systemField" &&
                                        <Col span={4}>
                                        	<Select
                                        		value={item["leftPropertyName"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		showSearch
                                        		optionFilterProp="children"
                                        		disabled={true}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["ruleFieldList"] &&
                                                    allMap["ruleFieldList"].map((item, index) => {
                                                    	return (
                                                    		<Option value={item.name} key={index} title={item.dName}>
                                                    			{item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
									}
									{/* 如果选择的字段类型是字段集 */}
									{
										item["fieldType"] &&
                                        item["fieldType"] === "fieldSet" &&
                                        <Col span={4}>
                                        	<Select
                                        		value={item["leftPropertyName"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		showSearch
                                        		optionFilterProp="children"
                                        		disabled={true}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["fieldSetSelect"] &&
                                                    allMap["fieldSetSelect"].map((item, index) => {
                                                    	return (
                                                    		<Option
                                                    			value={item.name}
                                                    			key={index}
                                                    			title={item.dName}
                                                    		>
                                                    			{item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
									}
									{
										item["fieldType"] === "systemField" &&
                                        item["leftPropertyName"] &&
                                        <Col span={3} className="basic-info-text">
                                        	<Select
                                        		value={item["operator"] || undefined}
                                        		disabled={true}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        	>
                                        		{
                                        			newBelongOperator.map((item, index) => {
                                        				return (
                                        					<Option
                                        						value={item.name}
                                        						key={index}
                                        						title={lang === "cn" ? item.dName : item.enDName}
                                        					>
                                        						{lang === "cn" ? item.dName : item.enDName}
                                        					</Option>
                                        				);
                                        			})
                                        		}
                                        	</Select>
                                        </Col>
									}
									{/* 这里判断operator */}
									{
										item["fieldType"] === "fieldSet" &&
                                        item["type"] === "fieldSet" &&
                                        <Col span={3} className="basic-info-text">
                                        	<Select
                                        		value={item["operator"] || undefined}
                                        		disabled={true}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        	>
                                        		{
                                        			commonSetOperator.map((item, index) => {
                                        				return (
                                        					<Option
                                        						value={item.name}
                                        						key={index}
                                        						title={lang === "cn" ? item.dName : item.enDName}
                                        					>
                                        						{lang === "cn" ? item.dName : item.enDName}
                                        					</Option>
                                        				);
                                        			})
                                        		}
                                        	</Select>
                                        </Col>
									}
									{
										item["fieldType"] === "fieldSet" &&
                                        item["type"] === "addressSet" &&
                                        <Col span={3} className="basic-info-text">
                                        	<Select
                                        		value={item["operator"] || undefined}
                                        		disabled={true}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        	>
                                        		{
                                        			addressSetOperator.map((item, index) => {
                                        				return (
                                        					<Option
                                        						value={item.name}
                                        						key={index}
                                        						title={lang === "cn" ? item.dName : item.enDName}
                                        					>
                                        						{lang === "cn" ? item.dName : item.enDName}
                                        					</Option>
                                        				);
                                        			})
                                        		}
                                        	</Select>
                                        </Col>
									}
									{/* 如果选择的是字段集，并且字段集类型是fieldSet */}
									{
										item["fieldType"] &&
                                        item["fieldType"] === "fieldSet" &&
                                        item["type"] === "fieldSet" &&
                                        <Col span={4} className="basic-info-text">
                                        	<Select
                                        		value={item["rightValue"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		disabled={true}
                                        		showSearch
                                        		optionFilterProp="children"
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["fieldSetSelect"] &&
                                                    allMap["fieldSetSelect"].map((item, index) => {
                                                    	return (
                                                    		<Option value={item.name} key={index} title={item.dName}>
                                                    			{
                                                    				item.dName
                                                    			}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
									}
									{
										item["fieldType"] &&
                                        item["fieldType"] === "fieldSet" &&
                                        item["type"] === "addressSet" &&
                                        <Col span={3} className="basic-info-text">
                                        	<Input
                                        		className="similarity-warp"
                                        		addonBefore="相似度"
                                        		addonAfter="%"
                                        		value={item.similarity}
                                        		disabled={true}
                                        	/>
                                        </Col>
									}
									{
										item["fieldType"] &&
                                        item["fieldType"] === "fieldSet" &&
                                        item["type"] === "addressSet" &&
                                        <Col span={4} className="basic-info-text">
                                        	<Select
                                        		value={item["rightValue"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		showSearch
                                        		optionFilterProp="children"
                                        		disabled={true}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["fieldSetSelect"] &&
                                                    allMap["fieldSetSelect"].map((item, index) => {
                                                    	return (
                                                    		<Option
                                                    			value={item.name}
                                                    			key={index}
                                                    			title={item.dName}
                                                    		>
                                                    			{/* lang:当前*/}
                                                    			{indexListLang.ruleAttr("current") + item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
									}
									{
										item["fieldType"] &&
                                        item["fieldType"] === "systemField" &&
                                        item["operator"] !== "belong" &&
                                        item["operator"] !== "unbelong" &&
                                        <Col span={7} className="basic-info-text">
                                        	{
                                        		item.rightValueType === "input" &&
                                                item.type !== "ENUM" &&
                                                <InputGroup compact>
                                                	<Select
                                                		value={item["rightValueType"] || undefined}
                                                		style={{ width: "30%" }}
                                                		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                                		disabled={true}
                                                	>
                                                		{/* lang:常量变量*/}
                                                		<Option value="input">
                                                			{indexListLang.ruleAttr("constant")}
                                                		</Option>
                                                		<Option
                                                			value="context">
                                                			{indexListLang.ruleAttr("variable")}
                                                		</Option>
                                                	</Select>
                                                	<Input
                                                		style={{ width: "70%" }}
                                                		value={item.rightValue || undefined}
                                                		placeholder={indexListLang.ruleAttr("constantInputPlaceholder")} // lang:请填写常量内容
                                                		disabled={true}
                                                	/>
                                                </InputGroup>
                                        	}
                                        	{
                                        		item.rightValueType === "context" &&
                                                item.type !== "ENUM" &&
                                                <InputGroup compact>
                                                	<Select
                                                		value={item["rightValueType"] || undefined}
                                                		style={{ width: "30%" }}
                                                		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                                		disabled={true}
                                                	>
                                                		{/* lang:常量变量*/}
                                                		<Option value="input">
                                                			{indexListLang.ruleAttr("constant")}
                                                		</Option>
                                                		<Option
                                                			value="context">
                                                			{indexListLang.ruleAttr("variable")}
                                                		</Option>
                                                	</Select>
                                                	<Select
                                                		style={{ width: "70%" }}
                                                		value={item.rightValue || undefined}
                                                		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                                		showSearch
                                                		optionFilterProp="children"
                                                		disabled={true}
                                                	>
                                                		{
                                                			allMap &&
                                                            allMap["ruleFieldList"] &&
                                                            allMap["ruleFieldList"].filter(fItem => {
                                                            	return (
                                                            		fItem.type === item.type ||
																	(["DOUBLE", "INT"].indexOf(fItem.type) > -1 && ["DOUBLE", "INT"].indexOf(item.type) > -1)
                                                            	);
                                                            }).map((item, index) => {
                                                            	return (
                                                            		<Option
                                                            			value={item.name}
                                                            			key={index}
                                                            			title={item.dName}
                                                            		>
                                                            			{/* lang:当前*/}
                                                            			{indexListLang.ruleAttr("current") + item.dName}
                                                            		</Option>
                                                            	);
                                                            })
                                                		}
                                                	</Select>
                                                </InputGroup>
                                        	}
                                        	{
                                        		item.type === "ENUM" &&
                                                <Select
                                                	value={item.rightValue || undefined}
                                                	placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                                	disabled={true}
                                                >
                                                	{
                                                		allMap &&
                                                        allMap["fieldEnumObj"] &&
                                                        allMap["fieldEnumObj"][item.leftPropertyName] &&
                                                        allMap["fieldEnumObj"][item.leftPropertyName].map((item, index) => {
                                                        	return (
                                                        		<Option
                                                        			value={item.value}
                                                        			key={index}
                                                        			title={item.value}
                                                        		>
                                                        			{item.description}
                                                        		</Option>
                                                        	);
                                                        })
                                                	}
                                                </Select>
                                        	}
                                        </Col>
									}
									{
										item["fieldType"] &&
                                        item["fieldType"] === "systemField" &&
                                        (item["operator"] === "belong" || item["operator"] === "unbelong") &&
                                        <Col span={4}>
                                        	<Select
                                        		value={item["rightValue"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		disabled={true}
                                        		showSearch
                                        		optionFilterProp="children"
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["customList"] &&
                                                    allMap["customList"].map((item, index) => {
                                                    	return (
                                                    		<Option
                                                    			value={item.name}
                                                    			key={index}
                                                    			title={item.dName}
                                                    		>
                                                    			{item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
									}
								</Row>
							);
						})
					}
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(FilterList);

