import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Form, Row, Col, Radio, DatePicker, Input, message, Button } from "antd";
import moment from "moment";
import { immuneAPI } from "@/services";
import { immuneConfigLang, commonLang } from "@/constants/lang";
import OneCondition from "@/components/OneCondition";
import NormalPolicy from "./NormalPolicy";
import PublicPolicy from "./PublicPolicy";
import "./AddModifyImmune.less";

const { TextArea } = Input;
const formItemLayout = {
	labelCol: {
		xs: { span: 24 },
		sm: { span: 3 }
	},
	wrapperCol: {
		xs: { span: 24 },
		sm: { span: 14 }
	}
};
class AddModifyImmune extends PureComponent {
	changeField = (name, e, type) => {
		const { dispatch } = this.props;
		let value = e;
		if (type === "input") {
			value = e.target.value;
		}
		dispatch({
			type: "immuneConfig/setAddModifyImmuneData",
			payload: {
				[name]: value
			}
		});
	}

	// 修改免疫条件
	changeCondition = (conditionJson) => {
		const { dispatch } = this.props;
		dispatch({
			type: "immuneConfig/setAddModifyImmuneData",
			payload: {
				"conditionJson": {...conditionJson}
			}
		});
	}
	//  提交
	submit = () => {
	 	const { immuneConfigStore } = this.props;
	 	const { addModifyImmuneData } = immuneConfigStore || {};
		const { uuid, policyType, conditionJson, effectRange } = addModifyImmuneData;
	 	this.props.form.validateFields((err) => {
	 		if (!err) {
				if (!(effectRange && effectRange.length > 0) && policyType === "publicPolicy") {
					message.error(immuneConfigLang.immuneConfigModal("immuneAppMsg"));// 请选择免疫应用
					return;
				}
				if (!(conditionJson && conditionJson.children && conditionJson.children.length > 0)) {
					message.error(immuneConfigLang.immuneConfigModal("immuneConMsg"));// 必须要配置一条免疫条件
					return;
				}
				 const params = {
					...addModifyImmuneData,
					conditionJson: JSON.stringify(conditionJson),
					effectRange: JSON.stringify(effectRange),
					effectTo: moment(addModifyImmuneData.effectTo).valueOf(),
					effectFrom: moment(addModifyImmuneData.effectFrom).valueOf()
				 };
				 if (policyType === "publicPolicy") {
					params.policySetUuid = "";
					params.appName = "";
					params.policyUuid = addModifyImmuneData.publicPolicyUuid; // 策略
					params.ruleName = addModifyImmuneData.publicRuleName;// 规则名
					params.ruleUuid = addModifyImmuneData.publicRuleUuid;// 规则
				 } else {
					 params.effectRange = "";
				 }
				 delete params.publicPolicyUuid;
				 delete params.publicRuleName;
				 delete params.publicRuleUuid;
				 if (uuid) {
					immuneAPI.modifyImmune(params).then((res)=>{
						if (res.success) {
							message.success(immuneConfigLang.immuneConfigModal("editSuccess")); // 编辑免疫配置成功
							this.onCancel();
							this.props.refresh();
						} else {
							message.error(res.message);
						}
					 }).catch(e=>{
						message.error(e.message);
					 });
				 } else {
					immuneAPI.addImmune(params).then((res)=>{
						if (res.success) {
							message.success(immuneConfigLang.immuneConfigModal("createSuccess")); // 新增免疫配置成功
							this.onCancel();
							this.props.refresh();
						} else {
							message.error(res.message);
						}
					 }).catch(e=>{
						message.error(e.message);
					 });
				 }

	 		}
	 	});
	 }

	 // 关闭弹窗
	 onCancel = () => {
	 	const { dispatch } = this.props;
	 	// 重置数据
	 	dispatch({
	 		type: "immuneConfig/initAddModifyImmuneData"
	 	});
	 	dispatch({
	 		type: "immuneConfig/setAttrValue",
	 		payload: {
				 addModifyImmune: false,
				 openType: "create",
				 createdBy: "",
	 			 gmtCreate: "",
	 			 updateType: "",
				 policySetList: [],
				 policyList: [],
				 ruleList: [],
				 publicPolicyList: [],
				 publicRuleList: [],
	 			 sceneListSource: []
	 		}
	 	});
	 }

	 render() {
	 	const { form, immuneConfigStore } = this.props;
	 	const { getFieldDecorator } = form;
	 	const { addModifyImmuneData, openType, createdBy, gmtCreate, updateType } = immuneConfigStore || {};
	 	const {
	 		policyType,
	 		conditionJson,
	 		effectFrom,
	 		effectTo,
	 		remarks
		 } = addModifyImmuneData || {};
		 let { disabled } = this.props;
		 let title = immuneConfigLang.immuneConfigModal("addTitle"); // "新增规则免疫"
		 switch (openType) {
	 		case "create":
	 			disabled = false;
				 title = immuneConfigLang.immuneConfigModal("addTitle");// "新增规则免疫"
				 break;
	 		case "edit":
	 			disabled = false;
	 			title = immuneConfigLang.immuneConfigModal("editTitle"); break; // "编辑规则免疫"
	 		case "view":
	 			disabled = true;
	 			title = immuneConfigLang.immuneConfigModal("viewTitle"); break; // "查看规则免疫"
		 }

		 let footerButton = [
	 		<Button onClick={this.onCancel}>
	 			{/* lang:取消 */}
	 			{commonLang.base("cancel")}
	 		</Button>
		 ];
		 if (!disabled) {
	 		footerButton.push(
	 			<Button type="primary" onClick={this.submit}>
	 				{/* lang:确定 */}
	 				{commonLang.base("ok")}
	 			</Button>
	 		);
		 }
	 	return (
	 		<Modal
	 			title={title}
	 			maskClosable={false}
	 			visible={true}
	 			onCancel={this.onCancel}
	 			onOk={this.submit}
	 			footer={footerButton}
	 			width="900px"
	 		>
	 			<Form {...formItemLayout} className="add-modify-immune-config">
	 				<Row>
	 					<Col span="24">
	 						{/* 策略类型 */}
	 						<Form.Item label={immuneConfigLang.immuneConfigModal("policyType")}>
	 							{
	 								getFieldDecorator("policyType", {
	 									initialValue: policyType || "undefined",
	 									rules: [
	 										{
	 											required: true,
	 											message: immuneConfigLang.immuneConfigModal("policyTypeMsg") // "请选择策略类型"
	 										}
	 									]
	 								})(
	 									<Radio.Group
										 	disabled={disabled || openType === "edit"}
	 										onChange={(e)=>{
	 											this.changeField("policyType", e, "input");
											 }}
	 									>
	 										{/* 普通策略规则 */}
	 										<Radio value="normalPolicy">{immuneConfigLang.immuneConfigModal("generalRule")}</Radio>
	 										{/* 公共策略规则 */}
	 										<Radio value="publicPolicy">{immuneConfigLang.immuneConfigModal("publicRule")}</Radio>
	 									</Radio.Group>
	 								)
	 							}
	 						</Form.Item>
	 					</Col>
	 					{/* 普通策略规则 */}
	 					{
	 						policyType === "normalPolicy"
	 							? <NormalPolicy
								 	disabled={disabled}
	 								form={form}
	 								changeField={this.changeField}
	 								onRef={(ref)=>{ this.normalPolicy = ref;}}
	 							/>
	 							: <PublicPolicy
								 	disabled={disabled}
	 								form={form}
	 								changeField={this.changeField}
	 								onRef={(ref)=>{ this.publicPolicy = ref;}}
	 							/>
	 					}
	 					{/* 免疫条件 */}
	 					<Col span={24}>
	 						<OneCondition
							 	disabled={disabled}
	 							conditionJson={conditionJson}
	 							changeCondition={this.changeCondition}
	 							type="formItem"
	 							required
	 							form={form}
	 							title= {immuneConfigLang.immuneConfigModal("immuneCondition")} // "免疫条件"
	 						/>
	 					</Col>
	 					<Col span={24}>
							 {/* 生效时间 */}
	 						<Form.Item label={immuneConfigLang.immuneConfigModal("immuneEffectTime")}>
	 							{
	 								getFieldDecorator("effectFrom", {
	 									initialValue: effectFrom || undefined,
	 									rules: [
	 										{
												 required: true,
												 message: immuneConfigLang.immuneConfigModal("effectTimeMsg") // 请选择生效时间
	 										}
	 									]
	 								})(
	 									<DatePicker
										 	disabled={disabled}
	 										className="wid-100-percent"
	 										format="YYYY-MM-DD HH:mm:ss"
	 										showTime
	 										onChange={(e)=>{
	 											this.changeField("effectFrom", e, "select");
	 										}}
	 									/>
	 								)
	 							}
	 						</Form.Item>
	 					</Col>
	 					<Col span={24}>
							 {/* 失效时间 */}
	 						<Form.Item label={immuneConfigLang.immuneConfigModal("immuneUnEffectTime")}>
	 							{
	 								getFieldDecorator("effectTo", {
	 									initialValue: effectTo || undefined,
	 									rules: [
	 										{
												 required: true,
												 message: immuneConfigLang.immuneConfigModal("unEffectTimeMsg") // 请选择生效时间
	 										}
	 									]
	 								})(
	 									<DatePicker
										 	disabled={disabled}
	 										className="wid-100-percent"
	 										format="YYYY-MM-DD HH:mm:ss"
	 										showTime
	 										onChange={(e)=>{
	 											this.changeField("effectTo", e, "select");
	 										}}
	 									/>
	 								)
	 							}
	 						</Form.Item>
	 					</Col>
	 					<Col span={24}>
							 {/* 备注 */}
	 						<Form.Item label={immuneConfigLang.immuneConfigModal("remarks")}>
	 							{
	 								getFieldDecorator("remarks", {
	 									initialValue: remarks || undefined,
	 									rules: [
	 										{
	 											max: 24,
	 											message: immuneConfigLang.immuneConfigModal("max24") // "不能超过24个字符"
	 										}
	 									]
	 								})(
	 									<TextArea
										 	disabled={disabled}
	 										placeholder={immuneConfigLang.immuneConfigModal("remarksMsg")}// "请输入备注"
	 										autoSize={{ minRows: 2, maxRows: 6 }}
	 										onChange={(e)=>{
	 											this.changeField("remarks", e, "input");
	 										}}
	 									/>
	 								)
	 							}
	 						</Form.Item>
	 					</Col>
						 {
							 createdBy &&
							 <Col span={24}>
							 	<Form.Item label="操作人">
									 {createdBy}
								 </Form.Item>
							 </Col>
						 }
						 {
							 gmtCreate &&
							 <Col span={24}>
							 	<Form.Item label="操作时间">
								 {gmtCreate}
								 </Form.Item>
							 </Col>
						 }
	 					{
	 						updateType &&
							<Col span={24}>
								<Form.Item label="更新类型">
									{updateType}
								</Form.Item>
							</Col>
	 					}
	 				</Row>
	 			</Form>
	 		</Modal >
	 	);
	 }
}

export default connect(state => ({
	globalStore: state.global,
	immuneConfigStore: state.immuneConfig
}))(Form.create({ name: "add-edit-immune" })(AddModifyImmune));
