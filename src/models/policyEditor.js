import { message } from "antd";
import { policyEditorAPI } from "@/services";
import { StandFieldConstants } from "@/constants";

const S_DT_VS_PARTNERCODE = StandFieldConstants("S_DT_VS_PARTNERCODE");
const S_DT_VS_APPNAME = StandFieldConstants("S_DT_VS_APPNAME");
const S_DT_VS_EVENTID = StandFieldConstants("S_DT_VS_EVENTID");
const D_T_VB_EVENTOCCURTIME = StandFieldConstants("D_T_VB_EVENTOCCURTIME");


export default {
	namespace: "policyEditor",
	state: {
		policySetsListLoad: true,
		policySetsList: [],
		policyEditorApiReady: false,
		curPage: 1,
		pageSize: 10,
		total: 0,
		pages: 0,
		expandedRowKeys: [],
		modalType: null,
		dialogShow: {
			policyModal: false,
			addPolicySet: false,
			modifyPolicySet: false,
			policyDrawer: false,
			policyTestDrawer: false,
			policyTestDrawerChild: false,
			RulesImport: false,
			policySetImport: false,
			publicPolicy: false,
			outputParams: false
		},
		searchData: {
			searchField: "name",
			searchValue: null,
			tag: ""
		},
		dialogData: {
			addPolicySetData: {
				partnerCode: null,
				appName: null,
				name: null,
				type: "normal",
				eventType: null,
				eventId: null,
				description: null
			},
			modifyPolicySetData: {
				uuid: null,
				partnerCode: null,
				appName: null,
				name: null,
				type: "normal",
				description: null
			},
			addPolicyData: {
				partner: null,
				fkPolicySetUuid: null,
				name: null,
				riskType: null,
				mode: null,
				riskEventType: null,
				riskEventId: null,
				appName: null,
				description: null,
				level: null,
				dealTypeCount: 3,
				dealTypeMappings: [
					{
						"score": null,
						"dealType": null
					},
					{
						"score": null,
						"dealType": null
					},
					{
						"score": null,
						"dealType": null
					}
				]
			},
			modifyPolicyData: {
				uuid: null,
				name: null,
				denyThreshold: null,
				reviewThreshold: null,
				description: null
			},
			policyDrawer: {
				policyDetail: {},
				uuid: null,
				loading: false
			},
			policyTestDrawer: {
				policyDetail: {
					operationType: "send",
					[S_DT_VS_PARTNERCODE]: null,
					[S_DT_VS_APPNAME]: null,
					[S_DT_VS_EVENTID]: null,
					S_DT_VS_EVENTTYPE: null,
					[D_T_VB_EVENTOCCURTIME]: null,
					times: 1,
					selectFieldList: []
				},
				resultList: [],
				uuid: null,
				loading: false
			},
			rulesImportData: {
				uuid: null,
				importMode: null,
				file: null,
				replaceScene: "",
				zbMode: "",
				ruleMode: ""
			},
			policySetImportData: {
				policySetName: null,
				uuid: null,
				importMode: null,
				file: null,
				replaceScene: "",
				zbMode: "",
				policyMode: ""
			},
			publicPolicyData: {},
			outputParamsData: {
				publicSetUuid: "",
				returnFields: [""]
			}
		},
		curUuid: ""
	},
	effects: {
		*getPolicySets({ payload }, { call, put }) {
			yield put({
				type: "setAttrValue",
				payload: {
					policySetsListLoad: true
				}
			});
			let response = yield call(policyEditorAPI.getPolicySets, payload);
			yield put({
				type: "setAttrValue",
				payload: {
					policySetsListLoad: false
				}
			});
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "setAttrValue",
				payload: {
					policyEditorApiReady: true
				}
			});
			yield put({
				type: "initPolicySets",
				payload: response
			});
		},
		*getCustomReturnFields({ payload }, { call, put }) {
			let response = yield call(policyEditorAPI.customReturnFieldsList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const { data} = response;
			yield put({
				type: "setDialogData",
				payload: {
					outputParamsData: {
						publicSetUuid: payload.publicSetUuid,
						returnFields: data && data.length > 0 ? data : [""]
					}
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setSearchData(state, action) {
			let { payload } = action;
			let { searchData } = state;

			for (let key in payload) {
				if (payload[key] !== undefined) {
					searchData[key] = payload[key];
				}
			}

			return {
				...state,
				searchData: searchData
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		initPolicySets(state, action) {
			let { policySetsList, curPage, pageSize, total, pages } = state;
			let data = action.payload.data || null;
			let list = data["dataList"] || [];
			curPage = data.curPage;
			pageSize = data.pageSize;
			total = data.total;
			pages = data.pages;

			policySetsList = list || [];

			return {
				...state,
				policySetsList: policySetsList,
				curPage: curPage,
				pageSize: pageSize,
				total: total,
				pages: pages
			};
		}
	}
};
