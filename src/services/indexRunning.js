import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getSalaxyList = async(params) => {
	return request(getUrl("/admin/salaxyVersion", params), {
		method: "GET",
		headers: getHeader()
	});
};

const viewIndexItemDetail = async(params) => {
	return request(getUrl("/admin/salaxyVersion/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getIndexVersionList = async(params) => {
	return request(getUrl("/admin/salaxyVersion/history", params), {
		method: "GET",
		headers: getHeader()
	});
};

const indexVersionSwitch = async(params) => {
	return request("/admin/salaxyVersion/switch", {
		method: "PUT",
		headers: getHeader(),
		body: params
	});
};

const batchCloseOnlineZb = async(params) => {
	return request("/admin/salaxyVersion/batchCloseOnlineZb", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};
export default {
	getSalaxyList,
	viewIndexItemDetail,
	getIndexVersionList,
	indexVersionSwitch,
	batchCloseOnlineZb
};
