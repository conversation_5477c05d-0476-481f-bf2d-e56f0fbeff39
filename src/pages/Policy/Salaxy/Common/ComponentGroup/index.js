import { PureComponent, Fragment } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Col } from "antd";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import RowTemplateItem from "./RowTemplateItem";
import "./index.less";

export default class ComponentGroup extends PureComponent {
	constructor(props) {
		super(props);
	};
	judgeAttachField = (fieldName) => {
		return fieldName.indexOf("attachFields.") > -1;
	}

	getAttachFieldName = (fieldName) => {
		if (fieldName.indexOf("attachFields.") > -1) {
			fieldName = fieldName.split(".")[1];
		}
		return fieldName;
	}
	// 修改选中项的值
	changeBaseField(name, index, type, e) {
		const { subItem = {} } = this.props;
		const { children = [] } = subItem || {};
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		} else if (type === "checkbox") {
			value = e ? e.toString() : "";
		}
		const { groupList = [], onChange } = this.props;

		const newGroupList = [...groupList];
		newGroupList[index] = Object.assign({}, newGroupList[index] || {}, {
			[name]: value
		});

		// 如果隐藏联动需要清空隐藏联动字段
		children.map((item) => {
			const { willChangeSelf = {}, willChangeParent = {} } = item || {};
			// 改变同层级
			if (willChangeSelf && JSON.stringify(willChangeSelf) !== "{}") {
				const { name: itemName, caseList = [] } = willChangeSelf || {};
				if (name === itemName) {
					caseList &&
                        caseList.length > 0 &&
                        caseList.map((caseScene) => {
                        	const { modeValueList = [], changeType } = caseScene || {};
                        	// 不显示时清空数据
                        	if ((modeValueList.indexOf(value) < 0) && changeType === "show") {
                        		newGroupList[index] = Object.assign({}, newGroupList[index] || {}, {
                        			[item.name]: ""
                        		});
                        	}
                        });
				}
			}
			// 当前修改数据 联动改变 父节点
			if (willChangeParent && willChangeParent.length > 0) {
				let childData = [];
				newGroupList.forEach(group=>{
					childData.push(group[item.name]);
				});
				willChangeParent.forEach(changeSelf=>{
					// 匹配到联动的组件
					const { name: itemName, modeValueList, changeType } = changeSelf || {};
					let matchVal = false;
					modeValueList.forEach(modeVal=>{
						if (childData.indexOf(modeVal) > -1 && !matchVal) {
							matchVal = true;
						}
					});
					if (matchVal && changeType === "emptyValue") {
						onChange && onChange({
							fieldName: this.getAttachFieldName(itemName),
							data: null,
							groupIsAttachField: this.judgeAttachField(itemName)
						});
					}
				});
			}
		});

		// 修改群组信息
		this.changeGroup(newGroupList);
	}

	changeGroup = (data) => {
		const { subItem = {}, onChange } = this.props;
		const { name } = subItem;
		onChange && onChange({fieldName: this.getAttachFieldName(name), data, groupIsAttachField: this.judgeAttachField(name)});
	}

	// 新增条件
	addGroup() {
		const { groupList = [] } = this.props;
		const newGroupList = [...groupList];
		newGroupList.push({});
		this.changeGroup(newGroupList);
	};

	// 删除条件
	delGroup(groupIndex) {
		const { groupList = [] } = this.props;
		let newGroupList = [...groupList];
		if (newGroupList.length === 1) {
			newGroupList.splice(newGroupList[0], 1);
		} else {
			newGroupList.splice(groupIndex, 1);
		}
		this.changeGroup(newGroupList);
	};

	render() {
		const { groupList = [], subItem, children, isRunning, currentIndexObj } = this.props;
		const { publishStatus, hasCommited } = currentIndexObj || {};
		const hasPublished = String(hasCommited) === "1"; // 已经发布

		let { disabled } = this.props;
		const { limitNum } = subItem || {};
		let warnText = "";
		if (limitNum && groupList && groupList.length === Number(limitNum)) {
			warnText = `最多只能添加${Number(limitNum)}条`;
		}
		// 如果可编辑，但是已经发布过版本 且 不支持二次编辑 则不能进行编辑
		if (!disabled && hasPublished && !subItem.canEditSecondTimes) {
			disabled = true;
		}
		return (
			<Fragment>
				<Col
					span={8}
					className={`add-new-filter ${(disabled || warnText) && "disabled-txt"}`}
				>
					<Tooltip placement="topLeft" title={warnText}>
						<span onClick={() => { !disabled && !warnText && this.addGroup(); }}>
							<Icon type="plus-square-o" />
							{/* lang:新增 */}
							{indexListLang.ruleAttr("add")}
						</span>
					</Tooltip>
				</Col>
				{children}
				{
					groupList && groupList.length > 0 && groupList.map((groupItem, groupIndex) => {
						return (
							<Row gutter={CommonConstants.gutterSpan} className="mb10 group-clear-both" key={(new Date()).getTime() + "_" + groupIndex}>
								<Col span={4} className="basic-info-title"></Col>
								<RowTemplateItem
									currentIndexObj={currentIndexObj}
									subItem={subItem}
									groupItem={groupItem}
									groupIndex={groupIndex}
									changeBaseField={this.changeBaseField.bind(this)}
									hasPublished={hasPublished}
									publishStatus={publishStatus}
									isRunning={isRunning}
								/>
								{
									!disabled
										? <Col span={2} className={disabled && "disabled-txt"}>
											<Icon
												type="delete"
												onClick={() => { !disabled && this.delGroup(groupIndex); }}
												className="param-tip-icon"
											/>
										</Col> : null
								}
							</Row>
						);
					})
				}
			</Fragment>

		);
	}
};
