import { message } from "antd";
import { workflowAPI } from "@/services";
import { isJSON } from "@/utils/isJSON";

export default {
	namespace: "workflow",
	state: {
		pageName: null,
		operationType: "add",		// 操作类型，新增or修改 add or modify
		status: null,
		running: null, // 是否有正在运行的规则流
		policySetItem: null,
		exportFlowVersion: null,		// 规则流预览的时候，导出用到这个参数
		flowData: {
			nodes: [],
			edges: []
		},
		conditionsGroup: {
			logicOperator: "&&",
			priority: "1",
			defaultNode: false,
			children: []
		},
		conditionsGroupFinal: {
			logicOperator: "&&",
			priority: "1",
			defaultNode: false,
			children: []
		},
		dialogShow: {
			workflow: false,
			workflowCommit: false,
			WorkflowImport: false,
			modelSetting: false,
			serviceSetting: false,
			conditionSetting: false
		},
		workflowVersionList: [],
		exportLoad: false
	},
	effects: {
		*getDecisionFlow({ payload }, { call, put, select }) {
			let response = yield call(workflowAPI.getWorkflow, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let data = response.data;

			let flowData = {
				nodes: [],
				edges: []
			};
			let operationType = "add";
			let status = null;
			let running = null;
			if (data && data.id) {
				let processContent = data["processContent"];
				let workflowContent = processContent && isJSON(processContent) ? JSON.parse(processContent) : flowData;

				flowData = workflowContent;
				operationType = "modify";
				status = data.status;
				running = data.running;
			} else {
				operationType = "add";
			}
			yield put({
				type: "setAttrValue",
				payload: {
					operationType: operationType,
					status: status,
					flowData: flowData,
					running
				}
			});
		},
		*getVersionList({ payload }, { call, put, select }) {
			let response = yield call(workflowAPI.getVersionList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}

			yield put({
				type: "setAttrValue",
				payload: {
					workflowVersionList: response.data || []
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		}
	}
};
