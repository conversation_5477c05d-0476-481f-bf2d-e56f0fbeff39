import { PureComponent } from "react";
import { connect } from "dva";
import { Radio } from "antd";
import { approvalTaskLang } from "@/constants/lang";
import PolicyApprovalDiffBase from "./PolicyApprovalDiffBase";
import PolicyApprovalDiffRules from "./PolicyApprovalDiffRules";

const RadioButton = Radio.Button;
const RadioGroup = Radio.Group;

class PolicyBase extends PureComponent {

	constructor(props) {
		super(props);
		this.changeDiffNav = this.changeDiffNav.bind(this);
		this.changeDiffTime = this.changeDiffTime.bind(this);
	}

	changeDiffNav(index) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;

		policyApprovalData.tabIndex = index;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				policyApprovalData: policyApprovalData
			}
		});
	}

	changeDiffTime(e) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;

		policyApprovalData.diffTime = e.target.value;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				policyApprovalData: policyApprovalData
			}
		});
	}

	render() {
		let { approvalTaskStore } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;
		let { tabIndex, diffTime, mode } = policyApprovalData;
		return (
			<div>
				<div className="approval-header">
					<div className="approval-tab-menu">
						<span
							className={tabIndex === 0 ? "first active" : "first"}
							onClick={this.changeDiffNav.bind(this, 0)}
						>
							{/* lang:基本设置 */}
							{approvalTaskLang.modal("base")}
						</span>
						<span
							className={tabIndex === 1 ? "second active" : "second"}
							onClick={this.changeDiffNav.bind(this, 1)}
						>
							{/* lang:规则列表 */}
							{approvalTaskLang.modal("rules")}
						</span>
					</div>
				</div>
				<RadioGroup
					value={diffTime}
					style={{ marginBottom: "10px" }}
					onChange={this.changeDiffTime.bind(this)}
				>
					<RadioButton value="pend">
						{/* lang:现在的版本 */}
						{approvalTaskLang.modal("currentVersion")}
					</RadioButton>
					<RadioButton value="online">
						{/* lang:之前的版本 */}
						{approvalTaskLang.modal("beforeVersion")}
					</RadioButton>
				</RadioGroup>
				<div className="approval-body">
					{
						tabIndex === 0 &&
                        <PolicyApprovalDiffBase />
					}
					{
						tabIndex === 1 &&
                        <PolicyApprovalDiffRules />
					}
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(PolicyBase);
