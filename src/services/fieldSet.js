import { getApiDetailHeader, getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request, { PostForm, downloadFileHandle } from "../utils/request";

const getFieldSetList = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/fieldSet", params), {
		method: "GET",
		headers: getHeader()
	});
};

const addFieldSet = async(params) => {
	return request("/admin/fieldSet", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const modifyFieldSet = async(params) => {
	return request("/admin/fieldSet", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const deleteFieldSet = async(params) => {
	return request(getUrl("/admin/fieldSet", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

export default {
	getFieldSetList,
	addFieldSet,
	modifyFieldSet,
	deleteFieldSet
};
