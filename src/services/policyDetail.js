import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getPolicyDetail = async(params) => {
	return request(getUrl("/admin/policys/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getPublicPolicyDetail = async(params) => {
	return request(getUrl("/admin/publicPolicy/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getPolicyRules = async(params) => {
	return request(getUrl("/admin/policy/rules", params), {
		method: "GET",
		headers: getHeader()
	});
};
const getPolicyVersionRules = async(params) => {
	return request(getUrl("/admin/policys/versionRules", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getPublicPolicyVersionRules = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/publicPolicy/versionRules", params), {
		method: "GET",
		headers: getHeader()
	});
};
const getRuleConditionById = async(params) => {
	return request(getUrl("/admin/rule/conditions", params), {
		method: "GET",
		headers: getHeader()
	});
};

const changeRuleStatus = async(params) => {
	return request("/admin/rule/state", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const changeRuleCustomId = async(params) => {
	return request("/admin/rule/customId", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const addCustomList = async(params) => {
	return request("/admin/rule/customizeTemplate", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const addPolicyRule = async(params) => {
	return request("/admin/rule", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyPolicyRule = async(params) => {
	return request("/admin/rule", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const deletePolicyRule = async(params) => {
	return request(getUrl("/admin/rule", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

const rulePaste = async(params) => {
	return request("/admin/rule/paste", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const policyCommit = async(params) => {
	return request("/admin/policys/commit", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const getPolicyVersionList = async(params) => {
	return request(getUrl("/admin/policys/versionHistory", params), {
		method: "GET",
		headers: getHeader()
	});
};

const policyVersionSwitch = async(params) => {
	return request("/admin/policys/versionSwitch", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};
/*
* 获取当前策略指标引用列表
* @倪春龙
* */
const getPolicyCiteIndexList = async(params) => {
	return request(getUrl("/admin/salaxy/ruleRefZb", params), {
		method: "GET",
		headers: getHeader()
	});
};

/*
* 获取规则免打扰想去
* @倪春龙
* */
const getRuleImmuno = async(params) => {
	return request(getUrl("/admin/ruleImmuno", params), {
		method: "GET",
		headers: getHeader()
	});
};

const addRuleImmuno = async(params) => {
	return request("/admin/ruleImmuno", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};

const modifyRuleImmuno = async(params) => {
	return request("/admin/ruleImmuno", {
		method: "PUT",
		headers: getHeader(),
		body: params
	});
};

const deleteRuleImmuno = async(params) => {
	return request(getUrl("/admin/ruleImmuno", params), {
		method: "DELETE",
		headers: getHeader()
	});
};

const changeRulesOrder = async(params) => {
	return request("/admin/rule/displayOrder", {
		method: "PUT",
		headers: getHeader(),
		body: params
	});
};

/*
公共策略模块
*/
// 历史版本
const getPublicPolicyVersionList = async(params) => {
	return request(getUrl("/admin/publicPolicy/versionHistory", params), {
		method: "GET",
		headers: getHeader()
	});
};
// 版本提交
const publicPolicyCommit = async(params) => {
	return request("/admin/publicPolicy/commit", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
// 版本切换
const publicPolicyVersionSwitch = async(params) => {
	return request("/admin/publicPolicy/versionSwitch", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};

// 获取规则提示
const getReminders = async(params) => {
	return request(getUrl("/admin/rule/reminders", params), {
		method: "GET",
		headers: getHeader()
	});
};

export default {
	getPolicyDetail,
	getPublicPolicyDetail,
	getPolicyRules,
	getPolicyVersionRules,
	getPublicPolicyVersionRules,
	getRuleConditionById,
	changeRuleStatus,
	changeRuleCustomId,
	addCustomList,
	addPolicyRule,
	modifyPolicyRule,
	deletePolicyRule,
	rulePaste,
	policyCommit,
	getPolicyVersionList,
	policyVersionSwitch,
	getPolicyCiteIndexList,
	getRuleImmuno,
	addRuleImmuno,
	modifyRuleImmuno,
	deleteRuleImmuno,
	changeRulesOrder,
	getPublicPolicyVersionList,
	publicPolicyCommit,
	publicPolicyVersionSwitch,
	getReminders
};
