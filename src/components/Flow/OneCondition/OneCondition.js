import { PureComponent } from "react";
import { connect } from "dva";
import { Select, Input, InputNumber, Icon, Row, Col, DatePicker } from "antd";
import { PolicyConstants } from "@/constants";
import { commonLang, workflowLang } from "@/constants/lang";
import { filterAvailableFieldList } from "@/utils/filterFieldList";

const Option = Select.Option;
const InputGroup = Input.Group;
const format = "YYYY-MM-DD HH:mm:ss";

class OneCondition extends PureComponent {
	constructor(props) {
		super(props);
		this.deleteCondition = this.deleteCondition.bind(this);
		this.addCondition = this.addCondition.bind(this);
		this.changeFieldValue = this.changeFieldValue.bind(this);
	}

	deleteCondition(indexArr) {
		let { workflowStore, dispatch } = this.props;
		let { conditionsGroup } = workflowStore;

		if (indexArr.length === 1) {
			// 单条件
			conditionsGroup["children"].splice(indexArr[0], 1);
		} else if (indexArr.length === 2) {
			// 多条件组
			if (conditionsGroup["children"][indexArr[0]]["children"].length === 1) {
				// 如果多条件组中长度为1
				conditionsGroup["children"].splice(indexArr[0], 1);
			} else {
				// 如果多条件组中长度不为1，则按照index删除
				conditionsGroup["children"][indexArr[0]]["children"].splice(indexArr[1], 1);
			}
		}

		dispatch({
			type: "workflow/setAttrValue",
			payload: {
				conditionsGroup
			}
		});
	}

	addCondition(indexArr) {
		let { workflowStore, dispatch } = this.props;
		let { conditionsGroup } = workflowStore;

		let baseTemp = {
			operator: "==",
			leftValueType: "STRING",
			leftVarType: "context",
			leftVar: null,
			rightVarType: "context",
			rightVar: null
		};

		conditionsGroup["children"][indexArr[0]]["children"].splice(indexArr[1] + 1, 0, baseTemp);

		dispatch({
			type: "workflow/setAttrValue",
			payload: {
				conditionsGroup
			}
		});
	}

	/**
	 * select 选择事件
	 * @param {标示出要修改的位置，他是一个数组，最大两位数} indexArr
	 * @param {判断是select还是input} type
	 * @param {field 修改的阈} field
	 * @param {select value:input value} e
	 */
	changeFieldValue(indexArr, type, field, e, date, inputNumberValue) {
		let { workflowStore, globalStore, dispatch, item } = this.props;
		let { conditionsGroup } = workflowStore;
		let { allMap } = globalStore;
		const {ruleAndIndexFieldList = []} = allMap || {};

		let ruleObj = {};
		let conditionObj;
		if (indexArr.length === 1) {
			conditionObj = conditionsGroup["children"][indexArr[0]];
		} else if (indexArr.length === 2) {
			conditionObj = conditionsGroup["children"][indexArr[0]]["children"][indexArr[1]];
		}

		if (field === "leftVar" || field === "rightVar" && type === "select") {
			ruleObj = ruleAndIndexFieldList.filter(item => item.name === e)[0];
		}
		let value = null;
		if (type === "select") {
			value = e;
		} else if (type === "input") {
			value = e.target.value;
		}

		conditionObj[field] = value;
		if (field === "rightVarType") {
			conditionObj["rightVar"] = null;
		}
		if (field === "leftVar" && (field !== "rightVar" || type !== "inputNumber")) {
			item.operator = "";
		}
		// 如果左变量类型修改，那清空右侧的一些
		if (field === "leftVarType") {
			conditionObj["leftVar"] = null;
			conditionObj["operator"] = null;
			conditionObj["rightVar"] = null;

			if (value === "policyResult") {
				conditionObj["leftValueType"] = "POLICY";
				conditionObj["rightVarType"] = "dealType";
			} else {
				conditionObj["leftValueType"] = ruleObj ? ruleObj.type : "STRING";
			}
		}

		if (field === "leftVar") {
			console.log(ruleObj);
			conditionObj["rightVar"] = null;
			if (conditionObj && conditionObj.leftVarType !== "policyResult") {
				conditionObj["leftValueType"] = ruleObj ? ruleObj.type : "STRING";
			}
		}
    	/**
		 * @description 右值输入时间转换为毫秒数
		 */
		if (field === "rightVar" && type === "date") {
			let dates = new Date(date.replace(/-/g, "/")); // - 转换 /
			conditionObj["rightVar"] = dates.getTime();
		}

		if (field === "rightVar" && type === "inputNumber") {
			conditionObj["rightVar"] = e;
		}

		dispatch({
			type: "workflow/setAttrValue",
			payload: {
				conditionsGroup
			}
		});
	}

	render() {
		let { globalStore, workflowStore, item, type, indexArr, disabled } = this.props;
		let { allMap } = globalStore;
		let { policySetItem } = workflowStore;
		let { dealTypeList } = allMap;
		let policySetUuid = policySetItem && policySetItem.uuid ? policySetItem.uuid : null;
		let [policyList, publicPolicyList] = [[], []];
		if (policySetUuid) {
			policyList = allMap[policySetUuid + "_policys_publish"] ? allMap[policySetUuid + "_policys_publish"] : [];
			publicPolicyList = allMap[policySetUuid + "_public_policys"] || []; // 公共策略列表数据源
		}
		const { appName } = policySetItem || {};
		const ruleAndIndexFieldList = filterAvailableFieldList({allMap, appName});
		return (
			<Row gutter={8} className="one-condition custom-item">
				<Col className="gutter-row" span={8}>
					<div className="gutter-box">
						<InputGroup compact>
							<Select
								style={{ width: "40%" }}
								placeholder={workflowLang.oneCondition("pleaseSelect")} // lang:"请选择"
								value={item.leftVarType || undefined}
								onChange={(value) => {
									this.changeFieldValue(indexArr, "select", "leftVarType", value);
									if (value === "context") {
										this.changeFieldValue(indexArr, "select", "rightVarType", "input");
									}
									if (value === "policyResult") {
										this.changeFieldValue(indexArr, "select", "rightVarType", "dealType");
									}
								}}
								disabled={disabled}
							>
								<Option value="context">
									{/* lang:字段/指标 */}
									{workflowLang.oneCondition("fieldAndIndex")}
								</Option>
								<Option value="policyResult">
									{/* lang:策略结果 */}
									{workflowLang.oneCondition("policyResult")}
								</Option>
							</Select>
							<Select
								style={{ width: "60%" }}
								placeholder={workflowLang.oneCondition("pleaseSelect")} // lang:"请选择"
								value={item.leftVar || undefined}
								onChange={this.changeFieldValue.bind(this, indexArr, "select", "leftVar")}
								showSearch
								optionFilterProp="children"
								disabled={disabled}
								dropdownMatchSelectWidth={false}
							>
								{
									item.leftVarType === "context" &&
                                    ruleAndIndexFieldList &&
                                    ruleAndIndexFieldList.map((item, index) => {
                                    	return (
                                    		<Option key={index} value={item.name}>
                                                [{commonLang.sourceName(item.sourceName)}]&nbsp;
                                    			{item.dName}
                                    		</Option>
                                    	);
                                    })
								}
								{
									item.leftVarType === "policyResult" &&
                                    policyList.map((item, index) => {
                                    	return (
                                    		<Option key={index} value={item.name}>
                                                [{workflowLang.oneCondition("policy")}]&nbsp;
                                    			{item.dName}
                                    		</Option>
                                    	);
                                    })
								}
								{
									item.leftVarType === "policyResult" &&
                                    publicPolicyList.map((item, index) => {
                                    	return (
                                    		<Option key={index} value={item.name}>
                                                [{workflowLang.oneCondition("publicPolicy")}]&nbsp;
                                    			{item.dName}
                                    		</Option>
                                    	);
                                    })
								}
							</Select>

						</InputGroup>
					</div>
				</Col>
				<Col className="gutter-row" span={3}>
					<div className="gutter-box">
						<Select
							placeholder={workflowLang.oneCondition("pleaseSelect")} // lang:"请选择"
							value={item.operator || undefined}
							onChange={this.changeFieldValue.bind(this, indexArr, "select", "operator")}
							disabled={disabled}
						>
							{
								PolicyConstants.conditionOperator[item.leftValueType] &&
                                PolicyConstants.conditionOperator[item.leftValueType].map((item, index) => {
                                	return (
                                		<Option key={index} value={item.name}>
                                			{item.dName}
                                		</Option>
                                	);
                                })
							}
						</Select>
					</div>
				</Col>
				{
					item &&
                    item["operator"] &&
                    item["operator"] !== "isnull" &&
                    item["operator"] !== "notnull" &&
                    <Col className="gutter-row" span={10}>
                    	<div className="gutter-box">
                    		{
                    			item.leftValueType !== "ENUM" &&
                                <InputGroup compact>
                                	{
                                		item.leftVarType === "context" &&
                                        <Select
                                        	style={{ width: "40%" }}
                                        	placeholder={workflowLang.oneCondition("pleaseSelect")} // lang:"请选择"
                                        	value={item.rightVarType || undefined}
                                        	onChange={this.changeFieldValue.bind(this, indexArr, "select", "rightVarType")}
                                        	disabled={disabled}
                                        >

                                        	<Option value="input">
                                        		{/* lang:常量 */}
                                        		{workflowLang.oneCondition("constant")}
                                        	</Option>
                                        	<Option value="context">
                                        		{/* lang:变量 */}
                                        		{workflowLang.oneCondition("variable")}
                                        	</Option>
                                        </Select>
                                	}
                                	{
                                		item.leftVarType === "policyResult" &&
                                        <Select
                                        	style={{ width: "40%" }}
                                        	placeholder={workflowLang.oneCondition("pleaseSelect")} // lang:"请选择"
                                        	value={item.rightVarType || undefined}
                                        	onChange={this.changeFieldValue.bind(this, indexArr, "select", "rightVarType")}
                                        	disabled={disabled}
                                        >
                                        	<Option value="dealType">
                                        		{/* lang:处置方式 */}
                                        		{workflowLang.oneCondition("disposalMethods")}
                                        	</Option>
                                        </Select>
                                	}
                                	{
                                		item.rightVarType === "input" &&
                                        item.leftValueType === "STRING" &&
                                        <Input
                                        	style={{ width: "60%" }}
                                        	value={item.rightVar || undefined}
                                        	placeholder={workflowLang.oneCondition("constantPlaceholder")} // lang:"请输入常量值"
                                        	onChange={this.changeFieldValue.bind(this, indexArr, "input", "rightVar")}
                                        	disabled={disabled}
                                        />
                                	}
                                	{
                                		item.rightVarType === "input" &&
                                        item.leftValueType === "BOOLEAN" &&
                                        <Input
                                        	style={{ width: "60%" }}
                                        	value={item.rightVar || undefined}
                                        	placeholder={workflowLang.oneCondition("constantPlaceholder")} // lang:"请输入常量值"
                                        	onChange={this.changeFieldValue.bind(this, indexArr, "input", "rightVar")}
                                        	disabled={disabled}
                                        />
                                	}
                                	{
                                		item.rightVarType === "input" &&
                                        item.leftValueType === "DATETIME" &&
                                        <DatePicker
                                        	style={{ width: "60%" }}
                                        	showTime
                                        	format={format}
                                        	onChange={this.changeFieldValue.bind(this, indexArr, "date", "rightVar")}
                                        	disabled={disabled}
                                        />
                                	}
                                	{
                                		item.rightVarType === "input" &&
                                        (item.leftValueType === "INT" || item.leftValueType === "DOUBLE") &&
                                        <InputNumber
                                        	style={{ width: "60%" }}
                                        	defaultValue={1}
                                        	step={0}
                                        	value={item.rightVar}
                                        	onChange={this.changeFieldValue.bind(this, indexArr, "inputNumber", "rightVar")}
                                        	disabled={disabled}
                                        />
                                	}
                                	{
                                		item.rightVarType === "context" &&
                                        <Select
                                        	placeholder={workflowLang.oneCondition("pleaseSelect")} // lang:"请选择"
                                        	value={item.rightVar || undefined}
                                        	style={{ width: "60%" }}
                                        	onChange={this.changeFieldValue.bind(this, indexArr, "select", "rightVar")}
                                        	showSearch
                                        	optionFilterProp="children"
                                        	disabled={disabled}
                                        	dropdownMatchSelectWidth={false}
                                        >
                                        	{
                                        		ruleAndIndexFieldList &&
                                                ruleAndIndexFieldList.map((item, index) => {
                                                	return (
                                                		<Option key={index} value={item.name}>
                                                            [{commonLang.sourceName(item.sourceName)}]&nbsp;
                                        			    {item.dName}
                                                		</Option>
                                                	);
                                                })
                                        	}
                                        </Select>
                                	}
                                	{
                                		item.rightVarType === "dealType" &&
                                        <Select
                                        	placeholder={workflowLang.oneCondition("pleaseSelect")} // lang:"请选择"
                                        	value={item.rightVar || undefined}
                                        	style={{ width: "60%" }}
                                        	onChange={this.changeFieldValue.bind(this, indexArr, "select", "rightVar")}
                                        	showSearch
                                        	optionFilterProp="children"
                                        	disabled={disabled}
                                        >
                                        	{
                                        		dealTypeList &&
                                                dealTypeList.map((item, index) => {
                                                	return (
                                                		<Option key={index} value={item.name}>{item.dName}</Option>
                                                	);
                                                })
                                        	}
                                        </Select>
                                	}
                                </InputGroup>
                    		}

                    		{
                    			item.leftValueType === "ENUM" &&
                                <Select
                                	value={item.rightVar || undefined}
                                	placeholder={workflowLang.oneCondition("pleaseSelect")} // lang:"请选择"
                                	onChange={(value) => {
                                		this.changeFieldValue(indexArr, "select", "rightVarType", "input");
                                		this.changeFieldValue(indexArr, "select", "rightVar", value);
                                	}}
                                	showSearch
                                	optionFilterProp="children"
                                	dropdownMatchSelectWidth={false}
                                	disabled={disabled}
                                >
                                	{
                                		allMap &&
                                        allMap["fieldEnumObj"] &&
                                        allMap["fieldEnumObj"][item.leftVar] &&
                                        allMap["fieldEnumObj"][item.leftVar].map((item, index) => {
                                        	return (
                                        		<Option
                                        			value={item.value}
                                        			key={index}
                                        			title={item.value}
                                        		>
                                        			{item.description}  [{item.value}]
                                    		</Option>
                                        	);
                                        })
                                	}
                                </Select>
                    		}
                    	</div>
                    </Col>
				}
				{
					!disabled &&
                    <Col className="gutter-row" span={3}>
                    	<div className="gutter-box oper-icon">
                    		{
                    			type === "group" &&
                                <Icon
                                	className="add"
                                	type="plus-circle-o"
                                	onClick={this.addCondition.bind(this, indexArr)}
                                />
                    		}
                    		<Icon
                    			className="delete"
                    			type="delete"
                    			onClick={this.deleteCondition.bind(this, indexArr)}
                    		/>
                    	</div>
                    </Col>
				}
			</Row>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	workflowStore: state.workflow
}))(OneCondition);
