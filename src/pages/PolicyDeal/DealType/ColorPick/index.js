import { PureComponent } from "react";
import { Input, Tabs } from "antd";
import { dealTypeLang } from "@/constants/lang";
import { CompactPicker } from "react-color";
import "./index.less";

const { TabPane } = Tabs;
class ColorPick extends PureComponent {
	constructor(props) {
		super(props);
		const { color } = props;
    	const activeKey = color && color.indexOf("linear") > -1 ? "2" : "1";
		this.state = {
			showColorPicker: false,
			activeKey
		};
	}
	componentDidMount() {
		this.closePicker = (e)=>{
			const chooseColorWrap =  document.querySelector(".choose-color").contains(e.target);
			if (!chooseColorWrap) {
				this.setState({
					showColorPicker: false
				});
			}
		};
		document.body.addEventListener("click", this.closePicker);
	}
	componentWillUnmount() {
		document.body.removeEventListener("click", this.closePicker);
	}
    handleColorPick = () => {
    	const { color } = this.props;
    	const { showColorPicker } = this.state;
    	const activeKey = color && color.indexOf("linear") > -1 ? "2" : "1";
    	this.setState({
    		showColorPicker: !showColorPicker,
    		activeKey
    	});
    }
    render() {
    	const { color, changeDialogDataHandle } = this.props;
    	const { showColorPicker, activeKey } = this.state;
    	return (
    		<div className="choose-color">
    			<Input
    				placeholder={dealTypeLang.modal("colorPlaceholder")}
    				value={color || undefined}
    				onClick={this.handleColorPick}
    				addonAfter={
    					color &&
						<span
							className="show-color-detail"
							style={{ "background": color }}
							onClick={this.handleColorPick}
						/>
    				}
    			/>
    			{
    				showColorPicker &&
					<div className="color-picker-wrap">
						<Tabs
							defaultActiveKey="1"
							className="color-pick-tab"
							activeKey={activeKey}
							onChange={(e)=>{
								this.setState({activeKey: e});
							}}
						>
							{/* 纯色 */}
							<TabPane tab={dealTypeLang.modal("color1")} key="1">
								<CompactPicker
									colors={dealTypeLang.colors}
									color={color || undefined}
									onChange={ (e)=>changeDialogDataHandle("color", "color", e) }
									onChangeComplete={this.handleColorPick}
								/>
							</TabPane>
							{/* 渐变色 */}
							<TabPane tab={dealTypeLang.modal("color2")} key="2">
								<ul className="color-picker-linear">
									{
										dealTypeLang.linerColors.map((v)=>{
											const className = color === v ? "active" : "";
											return (
												<li
													className ={className}
													style={{"background": v}}
													onClick={()=>{
														changeDialogDataHandle("color", "select", v);
														this.handleColorPick();
													}}
												></li>
											);
										})
									}
								</ul>
							</TabPane>
						</Tabs>
					</div>
    			}
    		</div>
    	);
    }
}

export default ColorPick;
