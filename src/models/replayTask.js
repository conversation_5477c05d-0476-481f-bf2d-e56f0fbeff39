import { message } from "antd";
import { replayTaskAPI } from "@/services";

export default {
	namespace: "replayTask",
	state: {
		searchParams: {
			eventId: null,
			taskStartAt: null,
			taskEndAt: null,
			taskStatus: null,
			curPage: 1,
			pageSize: 10
		},
		total: 0,
		replayTaskList: [],
		dialogShow: {
			replayTaskReportModal: false
		},
		dialogData: {
			replayTaskReportData: {
				ruleList: [],
				taskName: null,
				dataSourceType: null,
				taskStartTime: null,
				taskEndTime: null,
				totalCount: null,
				totalAmount: null,
				taskNo: null,
				taskFrom: null,
				taskTo: null,
				createTime: null
			}
		}
	},
	effects: {
		*getReplayTaskList({ payload }, { call, put }) {
			let response = yield call(replayTaskAPI.getReplayTaskList, payload);

			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initReplayTask",
				payload: response
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		initReplayTask(state, action) {
			let { searchParams, total, replayTaskList } = state;
			let data = action.payload.data || null;
			let list = data["dataList"] || [];
			total = data.total;
			replayTaskList = data["dataList"] || [];

			return {
				...state,
				total,
				replayTaskList
			};
		}
	}
};

