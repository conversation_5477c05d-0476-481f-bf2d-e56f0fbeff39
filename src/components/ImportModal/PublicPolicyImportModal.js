import { PureComponent } from "react";
import { connect } from "dva";
import ImportCommon from "./ImportCommon";
import { imExportModeLang } from "@/constants/lang";
import { publicPolicyEditorAPI } from "@/services";

class PublicPolicyImportModal extends PureComponent {
	changeImport = (e, fileName, type = "input") => {
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		let { publicPolicyEditorStore, dispatch } = this.props;
		let { dialogData } = publicPolicyEditorStore;
		let { publicPolicyImportData } = dialogData;
		const newPublicPolicyImportData = { ...publicPolicyImportData };

		newPublicPolicyImportData[fileName] = value;
		dispatch({
			type: "publicPolicyEditor/setDialogData",
			payload: {
				publicPolicyImportData: newPublicPolicyImportData
			}
		});
	}
	closeModal = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyEditor/setDialogShow",
			payload: {
				publicPolicyImport: false
			}
		});
		dispatch({
			type: "publicPolicyEditor/setDialogData",
			payload: {
				publicPolicyImportData: {
					uuid: null,
					importMode: null,
					file: null,
					replaceScene: "",
					zbMode: "",
					ruleMode: ""
				}
			}
		});
	}
	importCallback = () => {
		let { dispatch } = this.props;
		// 导入成功后
		// 1. 重新刷新编辑区策略列表
		dispatch({
			type: "publicPolicyEditor/setAttrValue",
			payload: {
				curPage: 1
			}
		});
		dispatch({
			type: "publicPolicyEditor/getPublicPolicy"
		});
	}
	render() {
		const { publicPolicyEditorStore } = this.props;
		const { dialogShow, dialogData } = publicPolicyEditorStore;
		const { publicPolicyImportData } = dialogData;
		return (
			<ImportCommon
				title={imExportModeLang.importPolicyModal("ruleImport")} // lang:规则导入
				visible={dialogShow.publicPolicyImport}
				modalData={publicPolicyImportData}
				importType="publicRules"
				changeField={this.changeImport.bind(this)}
				onCancel={this.closeModal.bind(this)}
				importCallback={this.importCallback.bind(this)}
				fetchMethod={publicPolicyEditorAPI.importPublicRules}
			/>
		);
	}
}

export default connect(state => ({
	publicPolicyEditorStore: state.publicPolicyEditor
}))(PublicPolicyImportModal);
