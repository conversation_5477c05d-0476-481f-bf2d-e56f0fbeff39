import { PureComponent } from "react";
import { connect } from "dva";
import { Button } from "antd";
import moment from "moment";
import { checkFunctionHasPermission } from "@/utils/permission";
import { immuneConfigLang } from "@/constants/lang";
import Search from "./Search/Search";
import ImmuneConfigList from "./ImmuneConfigList/ImmuneConfigList";
import AddModifyImmune from "../Common/Modal/AddModifyImmune";

class ImmuneConfig extends PureComponent {
	constructor(props) {
		super(props);
	}
	componentWillMount() {
		this.props.dispatch({
			type: "immuneConfigList/setAttrValue",
			payload: {
				immuneConfigList: [],
				immuneConfigListLoad: true
			}
		});
	}
	componentDidMount() {
		this.timer = setInterval(() => {
			const { globalStore, dispatch, ruleUuid } = this.props;
			const { menuTreeReady, currentApp } = globalStore;
			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkFunctionHasPermission("ZB0106", "RuleImmunoList")) {
					let appName = currentApp.name;
					if (appName) {
						appName = currentApp.name === "all" ? "" : currentApp.name;
						dispatch({
							type: "immuneConfigList/setSearchData",
							payload: {
								appName,
								ruleUuid: ruleUuid || ""
							}
						});
						this.search();
					}
				}
			}
		}, 100);
	}

	// 监听app切换
	componentWillReceiveProps(nextProps) {
		let { dispatch, globalStore } = this.props;
		let { menuTreeReady } = globalStore;
		if (menuTreeReady && checkFunctionHasPermission("ZB0106", "RuleImmunoList")) {
			const preCurrentApp = this.props.globalStore.currentApp;
			const nextCurrentApp = nextProps.globalStore.currentApp;
			if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
				// 检测到切换应用，开始刷新列表
				// 首先清空搜索项目
				let { name } = nextCurrentApp || {};
				if (name === "all") {
					name = "";
				}
				dispatch({
					type: "immuneConfigList/setSearchData",
					payload: {
						appName: name
					}
				});
				this.reset();
				this.search();
			}
		}
	}

	// 查询条件
	changeSearchField = (name, e, type) => {
		const { dispatch } = this.props;
		let value = e || "";
		if (type === "input") {
			value = e.target.value;
		}
		dispatch({
			type: "immuneConfigList/setSearchData",
			payload: {
				[name]: value
			}
		});
	}

	// 重置
	reset = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "immuneConfigList/setSearchData",
			payload: {
				ruleName: "",
				createType: "",
				effectFrom: null,
				effectTo: null
			}
		});
	}

	// 分页
	paginationOnChange = (curPage, pageSize) => {
    	const { dispatch } = this.props;
    	dispatch({
    		type: "immuneConfigList/setAttrValue",
    		payload: {
				curPage,
				pageSize
    		}
    	});
    	dispatch({
			type: "immuneConfigList/immuneList"
		});
	};

	// 搜索
	search = () => {
		const { dispatch } = this.props;
		dispatch({
    		type: "immuneConfigList/setAttrValue",
    		payload: {
				curPage: 1
    		}
    	});
		dispatch({
			type: "immuneConfigList/immuneList"
		});
	}

	// 打开新增编辑弹窗
	controlAddModifyImmune = async() => {
		const { dispatch } = this.props;
		// 获取公共策略
		await dispatch({
			type: "immuneConfig/fetchPolicy"
		});
		// 默认设置生效时间为当前
		dispatch({
			type: "immuneConfig/setAddModifyImmuneData",
			payload: {
				effectFrom: moment()
			}
		});
		dispatch({
			type: "immuneConfig/setAttrValue",
			payload: {
				openType: "create",
				addModifyImmune: true
			}
		});
	}
	render() {
		const { immuneConfigStore, immuneConfigListStore, isOtherPageCite, refreshCallBack } = this.props;
		const { searchData } = immuneConfigListStore;
		const { addModifyImmune } = immuneConfigStore;
		return (
			<div className={!isOtherPageCite ? "page-global-body" : ""}>
				{
					!isOtherPageCite &&
					<Search
						changeSearchField={this.changeSearchField}
						searchData={searchData}
						search={this.search}
						reset={this.reset}
					/>
				}
				{
					!isOtherPageCite &&
					checkFunctionHasPermission("ZB0106", "RuleImmunoAdd") &&
					<div className="mt10">
						<Button type="primary" onClick={this.controlAddModifyImmune}>
							{/* 新增 */}
							{immuneConfigLang.common("add")}
						</Button>
					</div>
				}

				<ImmuneConfigList
					refreshCallBack={refreshCallBack}
					paginationOnChange={this.paginationOnChange}
					refresh={this.search}
				/>
				{
					addModifyImmune &&
					<AddModifyImmune refresh={this.search}/>
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	immuneConfigListStore: state.immuneConfigList,
	immuneConfigStore: state.immuneConfig
}))(ImmuneConfig);

