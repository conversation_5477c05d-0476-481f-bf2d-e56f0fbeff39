:global {
    .approval-modal-wrap {
        .ant-modal-body {
            padding: 0;
        }

        .approval-wrap {
            & {
                position: relative;
                height: 100%;
                min-height: 400px;
            }

            .left-nav {
                & {
                    position: absolute;
                    width: 150px;
                    float: left;
                    height: 100%;
                    border-right: 1px solid #dcdcdc;
                }

                ul {
                    & {
                        list-style: none;
                        padding-left: 0;
                        margin-bottom: 0;
                    }

                    li {
                        & {
                            height: 40px;
                            line-height: 40px;
                            border-bottom: 1px solid #dcdcdc;
                            padding-left: 24px;
                            cursor: pointer;
                        }

                        &.active {
                            background: #dcdcdc;
                            color: #222;
                        }

                        &:hover:not(.active) {
                            background: #e6e6e6;
                            color: #333;
                        }
                    }
                }
            }

            .right-content {
                & {
                    margin-left: 150px;
                    padding: 20px 20px 0;
                }
            }

            .approval-header {
                & {
                    height: 32px;
                    margin-bottom: 30px;
                }

                .approval-tab-menu {
                    & {
                        width: 242px;
                        margin: 0 auto;
                        padding: 0;
                        text-align: center;
                        border: 1px solid #3498db;
                        overflow: hidden;
                        border-radius: 4px;
                    }

                    span {
                        margin: 0;
                        padding: 0;
                        display: inline-block;
                        width: 120px;
                        height: 32px;
                        line-height: 32px;
                        font-size: 14px;
                        color: #428bca;
                        cursor: pointer;
                        -o-transition: all 0.5s cubic-bezier(0, 1, 0.5, 1);
                        transition: all 0.5s cubic-bezier(0, 1, 0.5, 1);
                        -webkit-transition: all 0.5s cubic-bezier(0, 1, 0.5, 1);
                        -moz-transition: all 0.5s cubic-bezier(0, 1, 0.5, 1);
                    }

                    span.active {
                        background-color: #3498db;
                        color: #fff !important;
                    }
                }
            }

            .approval-body {
				height:auto;
				overflow-y: scroll;
                .flow-wrap {
                    border: 1px solid #dcdcdc;
                    height: 500px;
                    overflow-x: hidden;
                    overflow-y: auto;
                }
            }
        }
    }
}
