import { PureComponent } from "react";
import { connect } from "dva";
import VersionRuleList from "@/components/VersionPolicyDetail/RuleManage/RuleList";

class PolicyApprovalDiffRules extends PureComponent {

	constructor(props) {
		super(props);
		this.changeDiffNav = this.changeDiffNav.bind(this);
	}

	changeDiffNav(index) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;

		policyApprovalData.tabIndex = index;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				policyApprovalData: policyApprovalData
			}
		});
	}

	render() {
		let { approvalTaskStore } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;
		let { diffDetail, diffTime } = policyApprovalData;
		let { onlineVersion, pendVersion } = diffDetail;

		let [rules, mode] = [[], ""];
		if (diffTime === "online") {
			rules = onlineVersion && onlineVersion.rules ? onlineVersion.rules : [];
			mode = onlineVersion && onlineVersion.mode;
			console.log(onlineVersion.mode);
		} else if (diffTime === "pend") {
			rules = pendVersion && pendVersion.rules ? pendVersion.rules : [];
			mode = pendVersion && pendVersion.mode;
		}

		return (
			<div className="policy-detail-wrap">
				<VersionRuleList
					policyRules={rules}
					policyRulesReady={true}
					isOtherPageCite={true}
					ruleActiveKey={[]}
					mode={mode}
					currentRuleUuid="468f58d5bd054d2b83995b436f96f873"
				/>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(PolicyApprovalDiffRules);
