import { PureComponent } from "react";
import { connect } from "dva";
import "./Workflow.less";
import { workflowAPI } from "@/services";
import { cloneDeep } from "lodash";
import { Button, message, Modal, notification } from "antd";
import { checkFunctionHasPermission } from "@/utils/permission";
import { commonLang, imExportModeLang } from "@/constants/lang";
import WorkflowCommitModal from "./Modal/WorkflowCommitModal";
// import WorkflowImportModal from "./Modal/WorkflowImportModal";
import WorkflowImportModal from "@/components/ImportModal/WorkflowImportModal";
import Flow from "@/components/Flow";
import { workflowLang } from "@/constants/lang";

const confirm = Modal.confirm;

class Workflow extends PureComponent {
	constructor(props) {
		super(props);
	}

	componentDidMount() {
		const { dispatch, workflowStore } = this.props;
		const { pageName, policySetItem } = workflowStore;
		// pageName
		if (pageName === "policyRunning" && checkFunctionHasPermission("ZB0101", "decisionFlowHistory")) {
			dispatch({
				type: "workflow/getVersionList",
				payload: {
					policySetUuid: policySetItem["uuid"]
				}
			});
		}
	}

	closeEditorModal = () => {
		const { dispatch } = this.props;

		dispatch({
			type: "workflow/setDialogShow",
			payload: {
				workflow: false
			}
		});
		dispatch({
			type: "workflow/setAttrValue",
			payload: {
				operationType: "add",
				policySetItem: {},
				flowData: {
					nodes: [],
					edges: []
				},
				conditionsGroup: {
					logicOperator: "&&",
					priority: "1",
					defaultNode: false,
					children: []
				}
			}
		});
	}

	submitWorkflow = async() => {
		let { dispatch, globalStore, policyEditorStore, workflowStore } = this.props;
		let { curPage, pageSize } = policyEditorStore;
		let { currentApp } = globalStore;
		let { policySetItem, operationType } = workflowStore;
		let flowData = this.getFlowData();

		if (!flowData) {
			return;
		}

		const newFlowData = cloneDeep(flowData);
		const { nodes = [] } = newFlowData || {};
		nodes.map(v=>{
			if (typeof v.label === "object" && v.label) {
				v.label = v.label.text;
			}
			return v;
		});
		console.log(newFlowData);
		let params = {
			policySetUuid: policySetItem.uuid,
			processContent: JSON.stringify(newFlowData, null, 4)
		};

		if (operationType === "add") {
			workflowAPI.addWorkflow(params).then(res => {
				if (res.success) {
					message.success(workflowLang.message("addRuleStreamSuccess")); // lang:"添加规则流成功"

					dispatch({
						type: "policyEditor/getPolicySets",
						payload: {
							appName: currentApp && currentApp.name ? currentApp.name : null,
							curPage: curPage,
							pageSize: pageSize
						}
					});
					// 根据策略集获取规则流数据
					dispatch({
						type: "workflow/getDecisionFlow",
						payload: {
							policySetUuid: policySetItem.uuid
						}
					});
				} else {
					message.error(res.message);
				}
			});
		} else if (operationType === "modify") {
			workflowAPI.modifyWorkflow(params).then(res => {
				if (res.success) {
					message.success(workflowLang.message("modifyRuleStreamSuccess")); // lang:修改规则流成功;

					dispatch({
						type: "policyEditor/getPolicySets",
						payload: {
							appName: currentApp && currentApp.name ? currentApp.name : null,
							curPage: curPage,
							pageSize: pageSize
						}
					});
					// 根据策略集获取规则流数据
					dispatch({
						type: "workflow/getDecisionFlow",
						payload: {
							policySetUuid: policySetItem.uuid
						}
					});
				} else {
					message.error(res.message);
				}
			});
		}
	}

	getFlowData = () => {
		let flowData = this.flowEditor.getFlowData();
		if (flowData.length === 0) {
			message.error(workflowLang.message("nodeConfigEmpty")); // lang"规则流节点配置不能为空");
			return;
		}
		let noIncomingNodeList = [];
		let errorMsgList = [];
		let flowNameMap = {
			"flow-start": workflowLang.nodeName("start"), // lang:"开始节点",
			"flow-end": workflowLang.nodeName("end"), // lang:"结束节点",
			"flow-exclusivity": workflowLang.nodeName("exclusive"), // lang:"排他节点",
			"flow-parallel": workflowLang.nodeName("parallel"), // lang:"并行节点",
			"flow-policy": workflowLang.nodeName("policy"), // lang:"策略节点",
			"flow-model": workflowLang.nodeName("model"), // lang:"模型节点",
			"flow-data": workflowLang.nodeName("tripartite"), // lang:"三方节点"
			"flow-public-policy": workflowLang.nodeName("publicPolicy") // lang:"公共策略节点"
		};

		if (flowData.length > 0) {
    		/*
			* node和edge规则判断
			* 1、检测node节点：不作为target节点的数量
			* */
			flowData.map((item, index) => {
				let id = item.id;
				let sourceConnectedLinksAsOutgoing = flowData.filter(fItem => {
					return fItem.type === "edge" && fItem.source.id === id;
				});
				let targetConnectedLinksAsIncoming = flowData.filter(fItem => {
					return fItem.type === "edge" && fItem.target.id === id;
				});
				if (item.type === "node") {
					// 当前为node
					let model = item.model;
					let { shape, label, nodeConfig } = model;
					const labelName = typeof label === "string" ? label : label.text;
					if (!labelName) {
						errorMsgList.push(
							<p>[{flowNameMap[shape]}] <em>{/* lang:缺少标签名*/}{workflowLang.message("lackLabelName")}</em>
							</p>
						);
					}
					if (targetConnectedLinksAsIncoming.length === 0) {
						noIncomingNodeList.push(item);
					}
					if (shape === "flow-start") {
						if (targetConnectedLinksAsIncoming.length > 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:不可以设置输入流*/}{workflowLang.message("labelMessage1")}</em></p>
							);
						}
						if (sourceConnectedLinksAsOutgoing.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输出流*/}{workflowLang.message("labelMessage2")}</em></p>
							);
						}
						if (sourceConnectedLinksAsOutgoing.length > 1) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:输出流只能有一个*/}{workflowLang.message("labelMessage3")}</em></p>
							);
						}
					} else if (shape === "flow-end") {
						if (sourceConnectedLinksAsOutgoing.length > 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:不可以设置输出流*/}{workflowLang.message("labelMessage4")}</em></p>
							);
						}
						if (targetConnectedLinksAsIncoming.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输入流*/}{workflowLang.message("labelMessage5")}</em></p>
							);
						}
						if (targetConnectedLinksAsIncoming.length > 1) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:输入流只能有一个*/}{workflowLang.message("labelMessage6")}</em></p>
							);
						}
					} else if (shape === "flow-policy" || shape === "flow-public-policy") {
						if (!nodeConfig || (nodeConfig && !nodeConfig["policyUuid"])) {
							// 如果没有选择字段
							// lang: 策略
							errorMsgList.push(
								<p>[{workflowLang.message("policy")}]
    								<em>
										{/* lang:请选择策略*/}
										{
											shape === "flow-policy" ? workflowLang.message("labelMessage7") : workflowLang.message("labelMessage7-0")
										}
									</em>
								</p>
							);
						}
						if (targetConnectedLinksAsIncoming.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输入流*/}{workflowLang.message("labelMessage5")}</em></p>
							);
						}
						if (targetConnectedLinksAsIncoming.length > 1) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:输入流只能有一个*/}{workflowLang.message("labelMessage6")}</em></p>
							);
						}
						if (sourceConnectedLinksAsOutgoing.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输出流*/}{workflowLang.message("labelMessage2")}</em></p>
							);
						}
						if (sourceConnectedLinksAsOutgoing.length > 1) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:输出流只能有一个*/}{workflowLang.message("labelMessage3")}</em></p>
							);
						}
					} else if (shape === "flow-model") {
						let model = item.model;
						let { shape, label, nodeConfig } = model;
						let { modelId = null, modelInput = [], modelOutput = [] } = nodeConfig;
						// 当前是决策
						if (!modelId) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有添加配置*/}{workflowLang.message("labelMessage8")}</em></p>
							);
						} else {
							if (modelInput.length === 0) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:没有配置输入字段*/}{workflowLang.message("labelMessage9")}</em>
									</p>
								);
							}
							if (modelOutput.length === 0) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:没有配置输出字段*/}{workflowLang.message("labelMessage10")}</em>
									</p>
								);
							}
						}
						if (targetConnectedLinksAsIncoming.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输入流*/}{workflowLang.message("labelMessage5")}</em></p>
							);
						}
						if (targetConnectedLinksAsIncoming.length > 1) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:输入流只能有一个*/}{workflowLang.message("labelMessage6")}</em></p>
							);
						}
						if (sourceConnectedLinksAsOutgoing.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输出流*/}{workflowLang.message("labelMessage2")}</em></p>
							);
						}
						if (sourceConnectedLinksAsOutgoing.length > 1) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:输出流只能有一个*/}{workflowLang.message("labelMessage3")}</em></p>
							);
						}
						if (targetConnectedLinksAsIncoming.length > 0) {
							targetConnectedLinksAsIncoming.map(icItem => {
								// 这里可以做决策节点链接决策节点的检测情况
							});
						}
					} else if (shape === "flow-data") {
						let model = item.model;
						let { shape, label, nodeConfig } = model;
						let { invokeType, interfaceName, interfaceType, invokeAccording } = nodeConfig;
						// 当前是三方服务
						// 模型
						if (!nodeConfig) {
							errorMsgList.push(
								<p>[{workflowLang.message("model")}]
    								<em>{/* lang:没有添加配置*/}{workflowLang.message("labelMessage8")}</em></p>
							);
						}
						if (targetConnectedLinksAsIncoming.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输入流*/}{workflowLang.message("labelMessage5")}</em></p>
							);
						}
						if (targetConnectedLinksAsIncoming.length > 1) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:输入流只能有一个*/}{workflowLang.message("labelMessage6")}</em></p>
							);
						}
						if (sourceConnectedLinksAsOutgoing.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输出流*/}{workflowLang.message("labelMessage2")}</em></p>
							);
						}
						if (sourceConnectedLinksAsOutgoing.length > 1) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:输出流只能有一个*/}{workflowLang.message("labelMessage3")}</em></p>
							);
						}
						if (targetConnectedLinksAsIncoming.length > 0) {
							targetConnectedLinksAsIncoming.map(icItem => {
								// 这里可以做决策节点链接决策节点的检测情况
							});
						}

						if (invokeType === "byInterface") {
							if (!interfaceName) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:没有配置三方接口*/}{workflowLang.message("labelMessage11")}</em>
									</p>
								);
							}
						} else if (invokeType === "byType") {
							if (!interfaceType) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:接口类型不能为空*/}{workflowLang.message("labelMessage12")}</em>
									</p>
								);
							}
						}
					} else if (shape === "flow-exclusivity") {
						// 排他节点
						let model = item.model;
						let { shape, label, nodeType, nodeConfig } = model;
						console.log(nodeType);

						if (targetConnectedLinksAsIncoming.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输入流*/}{workflowLang.message("labelMessage5")}</em></p>
							);
						}

						if (nodeType === "CONDITION_START") {
							// 如果是排他开始
							if (targetConnectedLinksAsIncoming.length > 1) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:输入流只能有一个*/}{workflowLang.message("labelMessage6")}</em>
									</p>
								);
							}
							if (sourceConnectedLinksAsOutgoing.length < 2) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:输出流必须为多个*/}{workflowLang.message("labelMessage15")}</em>
									</p>
								);
							}
						}
						if (nodeType === "CONDITION_END") {
							// 如果是并行结束
							if (targetConnectedLinksAsIncoming.length < 2) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:输入流必须为多个*/}{workflowLang.message("labelMessage16")}</em>
									</p>
								);
							}
							if (sourceConnectedLinksAsOutgoing.length > 1) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:输出流只能有一个*/}{workflowLang.message("labelMessage3")}</em>
									</p>
								);
							}
						}

						if (sourceConnectedLinksAsOutgoing.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输出流*/}{workflowLang.message("labelMessage2")}</em></p>
							);
						}
					} else if (shape === "flow-parallel") {
						// 并行节点
						let model = item.model;
						let { shape, label, nodeType, nodeConfig } = model;

						if (targetConnectedLinksAsIncoming.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输入流*/}{workflowLang.message("labelMessage5")}</em></p>
							);
						}
						if (nodeType === "CONCURRENT_START") {
							// 如果是并行开始
							if (targetConnectedLinksAsIncoming.length > 1) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:输入流只能有一个*/}{workflowLang.message("labelMessage6")}</em>
									</p>
								);
							}
							if (sourceConnectedLinksAsOutgoing.length < 2) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:输出流必须为多个*/}{workflowLang.message("labelMessage15")}</em>
									</p>
								);
							}
						}
						if (nodeType === "CONCURRENT_END") {
							// 如果是并行结束
							if (targetConnectedLinksAsIncoming.length < 2) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:输入流必须为多个*/}{workflowLang.message("labelMessage16")}</em>
									</p>
								);
							}
							if (sourceConnectedLinksAsOutgoing.length > 1) {
								errorMsgList.push(
									<p>[{labelName}] <em>{/* lang:输出流只能有一个*/}{workflowLang.message("labelMessage3")}</em>
									</p>
								);
							}
						}
						if (sourceConnectedLinksAsOutgoing.length === 0) {
							errorMsgList.push(
								<p>[{labelName}] <em>{/* lang:没有设置输出流*/}{workflowLang.message("labelMessage2")}</em></p>
							);
						}
					}
				} else if (item.type === "edge") {
					// 当前为线
					let model = item.model;
					let { shape, label, fieldName, conditionsGroup } = model;
					let sourceName = item.source.model.label;
					let targetName = item.target.model.label;
					let tipList = [];
					if (!label) {
						tipList.push(workflowLang.message("lackLabelName")); // lang:缺少标签名
					}
				}
			});
			if (noIncomingNodeList.length > 1) {
				errorMsgList.unshift(
					<p className="no-incoming-node-list">
						<b>{/* lang:规则流只能有一个Root节点*/}{workflowLang.message("labelMessage13")}</b></p>
				);
			}
			if (errorMsgList.length > 0) {
				Modal.warning({
					zIndex: 1100,
					title: workflowLang.message("labelMessage14"), // lang:"规则流配置不合法，原因如下：",
					content: (<div className="modal-err-msg-list-wrap">{errorMsgList}</div>)
				});
				return false;
			}
		} else {
			return false;
		}
		let rootId = noIncomingNodeList ? noIncomingNodeList[0].id : null;
		let newFlowData = {
			nodes: [],
			edges: []
		};
		flowData.map(item => {
			let objModel = item.model;
			if (objModel.id === rootId) {
				objModel["isRoot"] = true;
			} else {
				objModel["isRoot"] = false;
			}
			if (item.type === "node") {
				newFlowData.nodes.push(objModel);
			} else {
				newFlowData.edges.push(objModel);
			}
		});
		console.log(newFlowData);
		return newFlowData;
	}

	workflowCommitHandle = () => {
		const { dispatch } = this.props;

		dispatch({
			type: "workflow/setDialogShow",
			payload: {
				workflowCommit: true
			}
		});
	}

	importWorkflowHandle = () => {
		const { dispatch } = this.props;

		dispatch({
			type: "workflow/setDialogShow",
			payload: {
				WorkflowImport: true
			}
		});
	}

	importWorkflowThenRefresh() {
		this.flowEditor.getWorkflowFromEditor();
	}

	exportWorkflow = async() => {
		let { workflowStore, dispatch } = this.props;
		let { policySetItem, operationType, exportFlowVersion, pageName, exportLoad } = workflowStore;
		let params = {
			policySetUuid: policySetItem.uuid,
			fileName: workflowLang.common("ruleStream") + "_" + policySetItem.name,
			fileType: "pld",
			isEditorArea: true
		};

		if (operationType === "view") {
			params.flowVersion = exportFlowVersion;
		}
		if (pageName === "policyRunning") {
			params.isEditorArea = false;
		}
		// 系统默认将规则流中引用策略/规则/指标一并导出
		notification.open({
			message: imExportModeLang.exportInfo("warn"),
			description: imExportModeLang.exportInfo("tipWorkFlow")
		});
		dispatch({
			"type": "workflow/setAttrValue",
			"payload": {
				exportLoad: true
			}
		});
		const testResult = await workflowAPI.exportWorkflowTest(params);
		if (testResult && testResult.hasOwnProperty("success") && !testResult.success) {
			message.warning(testResult.message);
			dispatch({
				"type": "workflow/setAttrValue",
				"payload": {
					exportLoad: false
				}
			});
		} else {
			workflowAPI.exportWorkflow(params).then(() => {
				dispatch({
					"type": "workflow/setAttrValue",
					"payload": {
						exportLoad: false
					}
				});
			}).catch(err => {
				console.log(err);
				dispatch({
					"type": "workflow/setAttrValue",
					"payload": {
						exportLoad: false
					}
				});
			});
		}
	}

	deleteWorkflow() {
		let { workflowStore, policyEditorStore, policyRunningStore, globalStore, dispatch } = this.props;
		let { currentApp } = globalStore;
		let { policySetItem } = workflowStore;
		let _this = this;
		let params = {
			policySetUuid: policySetItem.uuid
		};

		confirm({
			title: workflowLang.common("deleteWorkflowTitle"), // lang:"删除规则流提醒",
			content: workflowLang.common("deleteWorkflowContent"), // lang:"确定要删除当前规则流吗？",
			onOk() {
				workflowAPI.deleteWorkflow(params).then(res => {
					if (res.success) {
						message.success(res.message);
						// 关闭弹窗
						_this.closeEditorModal();
						// 刷新列表
						// 编辑区
						dispatch({
							type: "policyEditor/getPolicySets",
							payload: {
								appName: currentApp && currentApp.name ? currentApp.name : null,
								curPage: policyEditorStore.curPage,
								pageSize: policyEditorStore.pageSize
							}
						});
						// 运行区
						dispatch({
							type: "policyRunning/getPolicySets",
							payload: {
								appName: currentApp && currentApp.name ? currentApp.name : null,
								curPage: policyRunningStore.curPage,
								pageSize: policyRunningStore.pageSize
							}
						});
					} else {
						message.error(res.message);
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel() {
				console.log("Cancel");
			}
		});
	}

	// 申请下线
	offLineWorkflow = () => {
		let { workflowStore, policyRunningStore, policyEditorStore, globalStore, dispatch } = this.props;
		let { currentApp } = globalStore;
		let { policySetItem } = workflowStore;
		let params = {
			policySetUuid: policySetItem.uuid
		};
		confirm({
			title: workflowLang.common("offLineWorkflowTitle"), // lang:"规则流下线提醒",
			content: workflowLang.common("offLineWorkflowContent"), // lang:"请确认将规则流申请下线处理",
			onOk: () => {
				workflowAPI.workFlowOffLine(params).then(res => {
					if (res.success) {
						message.success(res.message || workflowLang.common("offLineWorkflowSuccess")); // 规则流下线成功
						// 关闭弹窗
						this.closeEditorModal();
						// 运行区
						dispatch({
							type: "policyRunning/getPolicySets",
							payload: {
								appName: currentApp && currentApp.name ? currentApp.name : null,
								curPage: policyRunningStore.curPage,
								pageSize: policyRunningStore.pageSize
							}
						});
						// 编辑区
						dispatch({
							type: "policyEditor/getPolicySets",
							payload: {
								appName: currentApp && currentApp.name ? currentApp.name : null,
								curPage: policyEditorStore.curPage,
								pageSize: policyEditorStore.pageSize
							}
						});
					} else {
						message.error(res.message || workflowLang.common("offLineWorkflowErr")); // 规则流下线失败
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel() {
				console.log("Cancel");
			}
		});
	}
	render() {
		let { workflowStore } = this.props;
		let { pageName, operationType, status, exportLoad, running } = workflowStore;
		let title;
		if (operationType === "add") {
			title = workflowLang.common("addWorkflow"); // lang:"新增执行流程";
		} else if (operationType === "modify") {
			title = workflowLang.common("modifyWorkflow"); // lang:"修改执行流程";
		} else if (operationType === "view") {
			title = workflowLang.common("viewWorkflow"); // lang:"预览执行流程";
		}
		let disabled = operationType === "view" || status === "wait_review";
		return (
			<div className="algo-modal">
				<div className="algo-config-wrap">
					<div className="algo-config-mask">
						<div className="algo-config-content">
							<div
								className="algo-config-close"
								onClick={this.closeEditorModal}
							>
								<i className="iconfont icon-close-thin"></i>
							</div>
							<div className="algo-config-header">
								<div className="algo-config-title">
									<h2>
										{title}
									</h2>
								</div>
							</div>
							<div
								className="algo-config-body"
								style={{ height: "calc(100% - 137px)", padding: "0" }}
							>
								<Flow
									disabled={disabled}
									bindThis={(that) => {
										this.flowEditor = that;
									}}
								/>
							</div>
							<div className="algo-config-footer">
								<div className="footer-btns">
									{/* 在编辑区中如果该规则流没有在运行区生效则可以删除 */}
									{
										operationType === "modify" &&
										!disabled &&
										!running &&
										<Button
											style={{
												width: "auto",
												color: "red"
											}}
											type="danger"
											onClick={() => {
												if (checkFunctionHasPermission("ZB0101", "editorDecisionFlowDelete")) {
													this.deleteWorkflow();
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}}
										>
											{/* lang:删除规则流 */}
											{workflowLang.common("deleteRuleStream")}
										</Button>
									}
									{
										!disabled &&
										<Button
											icon="upload"
											onClick={() => {
												if (checkFunctionHasPermission("ZB0101", "editorDecisionFlowImport")) {
													this.importWorkflowHandle();
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}}
										>
											{/* lang:导入 */}
											{workflowLang.common("import")}
										</Button>
									}
									{
										operationType !== "add" &&
										<Button
											icon="download"
											loading={exportLoad}
											disabled={exportLoad}
											onClick={() => {
												if (checkFunctionHasPermission("ZB0101", "editorDecisionFlowExport")) {
													this.exportWorkflow();
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}}
										>
											{/* lang:导出 */}
											{workflowLang.common("export")}
										</Button>
									}
									{
										pageName !== "policyRunning" &&
										!disabled &&
										<Button
											onClick={() => {
												if (checkFunctionHasPermission("ZB0101", "editorDecisionFlowSave")) {
													this.submitWorkflow();
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}}
										>
											{/* lang:保存 */}
											{workflowLang.common("save")}
										</Button>
									}

									{/* 申请下线 */}
									{
										pageName === "policyRunning" &&
										<Button
											style={{
												color: "red"
											}}
											type="danger"
											onClick={() => {
												if (checkFunctionHasPermission("ZB0101", "offRunRuleFlow")) {
													this.offLineWorkflow();
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}}
										>
											{/* lang:申请下线 */}
											{workflowLang.common("offLine")}
										</Button>
									}
									{
										operationType === "modify" &&
										pageName !== "policyRunning" &&
										!disabled &&
										<Button
											onClick={() => {
												if (checkFunctionHasPermission("ZB0101", "editorDecisionFlowCommit")) {
													this.workflowCommitHandle();
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}}
											disabled={pageName === "policyRunning" || disabled}
										>
											{/* lang:提交 */}
											{workflowLang.common("submit")}
										</Button>
									}
								</div>
							</div>
						</div>
					</div>
				</div>
				<WorkflowCommitModal />
				<WorkflowImportModal
					refreshWorkflow={() => {
						this.importWorkflowThenRefresh();
					}}
				/>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor,
	policyRunningStore: state.policyRunning,
	workflowStore: state.workflow
}))(Workflow);

