import { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import "./VersionPolicyDetail.less";
import { policyDetailAPI } from "@/services";
import { message, Icon, Tooltip, Input, Alert } from "antd";
import VersionPolicyBasic from "@/components/VersionPolicyDetail/Basic/BasicPublic";
import VersionRuleList from "@/components/VersionPolicyDetail/RuleManage/RuleList";
import AddTemplateModal from "@/components/PolicyDetail/AddTemplateModal";
import AddToCustomListModal from "@/components/PolicyDetail/AddToCustomListModal";
import PolicyCiteIndexModal from "@/components/PolicyDetail/PolicyCiteIndexModal";
import PolicyVersionDrawer from "@/components/PolicyVersionDrawer";
import RuleImmunoModal from "./Modal/RuleImmunoModal";
import ImmuneConfigListModal from "./Modal/ImmuneConfigListModal";
import { searchToObject } from "@/utils/utils";
import { checkFunctionHasPermission } from "@/utils/permission";
import checkPermissionPublicPolicy from "@/constants/permission/checkPermissionPublicPolicy";
import { commonLang, policyDetailLang } from "@/constants/lang";

let policyVersionDetailTimeout = null;
class VersionPublicPolicyDetail extends PureComponent {
	state = {};

	constructor(props) {
		super(props);
		this.changeTabIndex = this.changeTabIndex.bind(this);
		this.openPolicyVersionDrawer = this.openPolicyVersionDrawer.bind(this);
	}

	componentWillMount() {
		let { dispatch, match, location } = this.props;
		let { policyUuid } = match["params"];
		let { search } = location;
		let searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		let tabIndex = searchObj && searchObj.tabIndex ? parseInt(searchObj.tabIndex, 10) : 0;
		let policyVersion = searchObj && searchObj.policyVersion ? parseInt(searchObj.policyVersion, 10) : null;
		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				policyUuid: policyUuid,
				tabIndex: tabIndex,
				policyVersionInfo: {
					onlineVersion: policyVersion,
					currentVersion: policyVersion
				}
			}
		});
	}

	async componentDidMount() {
		let { dispatch, location, match } = this.props;
		let { policyUuid } = match["params"];
		let { search } = location;
		let searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		let ruleUuid = searchObj && searchObj.ruleUuid ? searchObj.ruleUuid : null;
		let policyVersion = searchObj && searchObj.policyVersion ? parseInt(searchObj.policyVersion, 10) : null;
		await dispatch({
			type: "policyDetail/getPolicyVersionRules",
			payload: {
				policyUuid: policyUuid,
				policyVersion: policyVersion,
				isFirst: true
			}
		});

		if (ruleUuid) {
			dispatch({
				type: "policyDetail/setAttrValue",
				payload: {
					currentRuleUuid: ruleUuid
				}
			});
			policyVersionDetailTimeout = setTimeout(() => {
				dispatch({
					type: "policyDetail/setAttrValue",
					payload: {
						ruleActiveKey: [ruleUuid]
					}
				});
			}, 20);
		} else {
			dispatch({
				type: "policyDetail/setAttrValue",
				payload: {
					ruleActiveKey: [],
					currentRuleUuid: null
				}
			});
		}
	}

	componentWillUnmount() {
		policyVersionDetailTimeout && clearTimeout(policyVersionDetailTimeout);
	}
	changeTabIndex(index) {
		let { dispatch } = this.props;

		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				tabIndex: index
			}
		});
	}

	openPolicyVersionDrawer() {
		let { policyDetailStore, dispatch } = this.props;
		let { policyDetail, dialogData } = policyDetailStore;
		let { policyVersionDrawerData } = dialogData;
		let params = {
			policyUuid: policyDetail.uuid
		};

		policyDetailAPI.getPublicPolicyVersionList(params).then(res => {
			if (res.success) {
				policyVersionDrawerData["versionList"] = res.data || [];
				dispatch({
					type: "policyDetail/setDialogData",
					payload: {
						policyVersionDrawerData: policyVersionDrawerData
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});

		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				policyVersionDrawer: true
			}
		});
	}

	//  刷新列表
	immuneListRefreshRuleList = () => {
		let { dispatch, location, match } = this.props;
		let { policyUuid } = match["params"];
		let { search } = location;
		let searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		let policyVersion = searchObj && searchObj.policyVersion ? parseInt(searchObj.policyVersion, 10) : null;
		dispatch({
			type: "policyDetail/getPolicyVersionRules",
			payload: {
				policyUuid: policyUuid,
				policyVersion: policyVersion,
				isFirst: true
			}
		});
	}

	render() {
		let { policyDetailStore, dispatch, location } = this.props;
		let { policyDetail, tabIndex, ruleSearch, policyRulesReady, policyRules, policyOriginalAllInfo, policyVersionInfo, dialogShow } = policyDetailStore;
		let { showSearch, searchWord } = ruleSearch;
		return (
			<div className="policy-detail-wrap">
				<div className="policy-detail-box">
					<div className="policy-detail-header">
						<div className="policy-detail-guide">
							<div
								className="go-back"
								onClick={() => {
									let { dispatch, location } = this.props;
									let pathname = location.pathname;
									let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
									dispatch(routerRedux.push(prefix + "/policy/publicPolicyList?currentTab=1"));
								}}
							>
								{/* 返回策略集列表 */}
								<Tooltip title={policyDetailLang.tipInfo("goBackPublic")}>
									<Icon type="left" />
								</Tooltip>
							</div>
							<Tooltip title={policyDetail.name}>
								<h3>
									{policyDetail.name}
								</h3>
							</Tooltip>
							<Tooltip title={policyDetailLang.tipInfo("toEditArea")}>
								<Icon
									type="link"
									className="jump-to-edit-area"
									onClick={() => {
										if (checkPermissionPublicPolicy.PubPolicyDetail()) { // 判断编辑区公共策略详情
											let { dispatch, location } = this.props;
											let pathname = location.pathname;
											let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
											dispatch(routerRedux.push(prefix + "/policy/publicPolicyDetail/" + policyDetail.uuid + "?tabIndex=1"));
										} else {
											// lang:无权限操作
											message.warning(commonLang.messageInfo("noPermission"));
										}
									}}
								/>
							</Tooltip>
						</div>
						{
							<div className="tab-menu">
								<span
									className={tabIndex === 0 ? "first active" : "first"}
									onClick={this.changeTabIndex.bind(this, 0)}
								>
									{/* 基本设置 */}
									{policyDetailLang.common("basicSet")}
								</span>
								<span
									className={tabIndex === 1 ? "second active" : "second"}
									onClick={this.changeTabIndex.bind(this, 1)}
								>
									{/* 规则管理 */}
									{policyDetailLang.common("ruleManagement")}
								</span>
							</div>
						}
						{
							tabIndex === 1 &&
							<div className="rule-tools-bar">
								{/* lang:查看历史版本 */}
								<Tooltip title={policyDetailLang.tipInfo("viewHistoryVersion")}>
									<div
										className="tool-item"
										onClick={() => {
											if (checkPermissionPublicPolicy.PubPolicyHistory()) { // 公共策略历史版本权限
												this.openPolicyVersionDrawer();
											} else {
												message.warning(policyDetailLang.tipInfo("noPermission")); // 无权限操作
											}
										}}
									>
										<i className="iconfont icon-version"></i>
									</div>
								</Tooltip>
								{/* 查看本策略指标 */}
								<Tooltip title={policyDetailLang.tipInfo("lookStrat")}>
									<div
										className="tool-item"
										onClick={() => {
											if (checkFunctionHasPermission("ZB0101", "viewPolicyCiteIndex")) {
												dispatch({
													type: "policyDetail/setDialogShow",
													payload: {
														PolicyCiteIndex: true
													}
												});
												dispatch({
													type: "indexRunning/getPolicyCiteIndexList",
													payload: {
														curPage: 1,
														pageSize: 20,
														policyUuid: policyDetail.uuid
													}
												});
											} else {
												message.warning(policyDetailLang.tipInfo("noPermission")); // 无权限操作
											}
										}}
									>
										<i className="iconfont icon-log"></i>
									</div>
								</Tooltip>
								{
									!showSearch &&
									<Tooltip title={policyDetailLang.tipInfo("searchRule")}>
										<div
											className="tool-item"
											onClick={() => {
												ruleSearch["showSearch"] = true;
												dispatch({
													type: "policyDetail/setAttrValue",
													payload: {
														ruleSearch: ruleSearch
													}
												});
											}}
										>
											<i className="iconfont icon-search"></i>
										</div>
									</Tooltip>
								}
								{
									showSearch &&
									<div className="tool-item rule-search">
										<Input
											value={searchWord || undefined}
											placeholder={policyDetailLang.tipInfo("inputKey")} // 请输入关键词搜索
											autoFocus="autoFocus"
											ref="policyRuleSearchInput"
											onChange={(e) => {
												ruleSearch["searchWord"] = e.target.value;
												dispatch({
													type: "policyDetail/setAttrValue",
													payload: {
														ruleSearch: ruleSearch
													}
												});
											}}
										/>
										<Icon
											type="close"
											onClick={() => {
												ruleSearch["showSearch"] = false;
												ruleSearch["searchWord"] = null;
												dispatch({
													type: "policyDetail/setAttrValue",
													payload: {
														ruleSearch: ruleSearch
													}
												});
											}}
										/>
									</div>
								}
							</div>
						}
					</div>
					<div className="policy-detail-body">
						{
							tabIndex === 0 &&
							<VersionPolicyBasic policyDetail={policyDetail} />
						}
						{
							tabIndex === 1 &&
							policyVersionInfo.onlineVersion !== policyVersionInfo.currentVersion &&
							<Alert
								className="policy-version-alert"
								message={
									<div>
										{/* lang:当前查看的策略版本 */}
										<span>{policyDetailLang.policyVersion("currentViewVersion")}V{policyVersionInfo.currentVersion}</span>
										<a
											className="ml20"
											onClick={() => {
												let { policyInfo, rules } = policyOriginalAllInfo;
												console.log(policyOriginalAllInfo);
												dispatch({
													type: "policyDetail/setAttrValue",
													payload: {
														policyDetail: policyInfo,
														policyRules: rules || []
													}
												});
												dispatch({
													type: "policyDetail/setAttrValue",
													payload: {
														policyVersionInfo: {
															onlineVersion: policyVersionInfo.onlineVersion,
															currentVersion: policyVersionInfo.onlineVersion
														}
													}
												});
											}}
										>
											{/* lang:点击返回当前版本 */}
											{policyDetailLang.policyVersion("returnCurrentVersion")}
										</a>
									</div>
								}
								type="info"
								showIcon
							/>
						}
						{
							tabIndex === 1 &&
							<VersionRuleList
								policyRulesReady={policyRulesReady}
								policyRules={policyRules}
								ruleSearch={ruleSearch}
								searchWord={searchWord}
							/>
						}
					</div>
					<AddTemplateModal />
					<AddToCustomListModal readOnly={true} />
					<PolicyCiteIndexModal />
					<PolicyVersionDrawer location={location} />
					<RuleImmunoModal />
					<ImmuneConfigListModal
						refreshCallBack={this.immuneListRefreshRuleList}
					/>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(VersionPublicPolicyDetail);
