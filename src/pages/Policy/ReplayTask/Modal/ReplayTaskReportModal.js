import React, { PureComponent } from "react";
import { connect } from "dva";
import { Modal } from "antd";
import { replayTaskLang } from "@/constants/lang";

import ReportInfo from "./Inner/ReportInfo";
const ReportChart = React.lazy(() => import("./Inner/ReportChart"));
const RuleHitDetail = React.lazy(() => import("./Inner/RuleHitDetail"));

class ReplayTaskReportModal extends PureComponent {
	constructor(props) {
		super(props);
	}

    closeModal = () => {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "replayTask/setDialogShow",
    		payload: {
    			replayTaskReportModal: false
    		}
    	});
    }

    render() {
    	let { replayTaskStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = replayTaskStore;
    	let { replayTaskReportModal } = dialogShow;
    	let { replayTaskReportData } = dialogData;
    	let { ruleList, ...other } = replayTaskReportData;

    	console.log(other);
    	return (
    		<Modal
    			title="回测报告"
    			visible={replayTaskReportModal}
    			maskClosable={true}
    			width={850}
    			footer={null}
    			onCancel={this.closeModal}
    			afterClose={() => {
    				dispatch({
    					type: "replayTask/setDialogData",
    					payload: {
    						replayTaskReportData: {
    							ruleList: [],
    							taskName: null,
    							taskStartTime: null,
    							taskEndTime: null,
    							totalCount: null,
    							totalAmount: null,
    							taskNo: null,
    							taskFrom: null,
    							taskTo: null,
    							createTime: null
    						}
    					}
    				});
    			}}
    			className="report-modal"
    		>
    			<div className="report-wrap">
    				<div className="report-header">
    					<div className="report-base-info">
    						{/* <img src={reportImages.logo}/>*/}
    						<div className="info">
    							<span className="report-title">
    								{other.taskName}-{replayTaskLang.report("replayTaskReport")}
    							</span>
    							<p className="show-content">
    								<span className="report-id">
    									{/* lang:报告编号 */}
    									{replayTaskLang.report("taskNo")}：{other.taskNo}
    								</span>
    								<span className="report-time">
    									{/* lang:报告时间 */}
    									{replayTaskLang.report("taskTime")}：{other.createTime}
    								</span>
    							</p>
    						</div>
    					</div>
    					<div className="is-close-btn-wrapper">
    						<i
    							className="iconfont icon-close is-close-btn"
    							onClick={this.closeModal}
    						/>
    					</div>
    				</div>
    				<div className="report-body">
    					<ReportInfo reportData={other} />
    					{
    						ruleList &&
                            ruleList.length > 0 &&
                            <React.Suspense fallback={null}>
                            	<ReportChart
                            		other={other}
                            		chartData={ruleList}
                            		reportData={other}
                            		idName="lineBarTwo"
                            	/>
                            </React.Suspense>
    					}
    					<React.Suspense fallback={null}>
    						<RuleHitDetail
    							other={other}
    							ruleList={ruleList}
    						/>
    					</React.Suspense>
    				</div>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	replayTaskStore: state.replayTask
}))(ReplayTaskReportModal);
