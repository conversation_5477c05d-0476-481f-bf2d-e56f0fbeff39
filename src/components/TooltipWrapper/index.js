import { useState } from "react";
import { Tooltip } from "antd";

export default (props) => {
	const [title, setTitle] = useState("");
	const { children } = props;
	return (
		<Tooltip
			placement="topLeft"
			onMouseEnter={(e) => {
				let title = "";
				const target = e.currentTarget;
				if (target.localName === "input") {
					title = e.currentTarget.defaultVaue || e.currentTarget.value;
				} else {
					title = e.currentTarget.firstChild.innerText;
				}
				setTitle(title);
			}}
			title={title}
			getPopupContainer={(e) => {
				if (e.localName === "input") {
					return e.parentElement;
				}
				return e;
			}}
		>
			{children}
		</Tooltip>
	);

};
