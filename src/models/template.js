import { message } from "antd";
import { templateAPI } from "@/services";
import { isJSON } from "@/utils/isJSON";

export default {
	namespace: "template",
	state: {
		currentType: 1,
		activeGroupIndex: 0,
		ruleTemplateList: [],
		indexTemplateList: [],
		ruleTemplateListObj: {},
		indexTemplateListObj: {},
		indexOperateTypeMap: [],
		currentStep: 0,
		operatorTemplateItemIndex: 0,
		searchWord: null,
		dialogShow: {
			addGroup: false,
			modifyGroup: false,
			addTemplate: false,
			modifyTemplate: false,
			asyncTemplate: false
		},
		dialogData: {
			addGroupData: {
				name: null,
				displayName: null,
				enDisplayName: null,
				type: null
			},
			modifyGroupData: {
				id: null,
				name: null,
				displayName: null,
				enDisplayName: null,
				type: null
			},
			addTemplateData: {
				name: null,
				displayName: null,
				enDisplayName: null,
				description: null,
				enDescription: null,
				type: null,
				cfgJson: null,
				parentId: null,
				sort: null,
				advanceConfig: {
					noFilterFunction: false,
					demo: "",
					demoPic: ""
				}
			},
			modifyTemplateData: {
				id: null,
				name: null,
				displayName: null,
				enDisplayName: null,
				description: null,
				enDescription: null,
				type: null,
				cfgJson: null,
				parentId: null,
				sort: null,
				demo: "",
				demoPic: "",
				advanceConfig: {
					noFilterFunction: false,
					demo: "",
					demoPic: ""
				}
			},
			asyncTemplateData: {
				templateList: [],
				labelText: ""
			}
		}
	},
	effects: {
		*getTemplateList({ payload }, { call, put }) {
			const response = yield call(templateAPI.getTemplateList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			// console.log(payload);
			// console.log(response);
			yield put({
				type: "initTemplateList",
				payload: {
					type: payload.type,
					response: response
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		initTemplateList(state, { payload }) {
			let { ruleTemplateList, indexTemplateList, ruleTemplateListObj, indexTemplateListObj, indexOperateTypeMap } = state;
			let data = payload.response.data || [];
			if (payload.type === 1) {
				// ruleTemplateList = data;
			} else if (payload.type === 2) {
				// indexTemplateList = data;
				indexOperateTypeMap = [];
			}

			data &&
				data.map((item, index) => {
					if (item["templateDataList"] && item["templateDataList"].length) {
						item["templateDataList"].map((subItem, subIndex) => {

							if (subItem.advanceConfig && isJSON(subItem.advanceConfig)) {
								subItem["advanceConfig"] = JSON.parse(subItem.advanceConfig);
							} else {
								subItem["advanceConfig"] = {};
							}

							if (payload.type === 1) {
								ruleTemplateList.push(subItem);
								ruleTemplateListObj[subItem.name] = subItem;
							} else if (payload.type === 2) {
								indexTemplateListObj[subItem.name] = subItem;
								indexOperateTypeMap.push({
									name: subItem.name,
									dName: subItem.displayName,
									enDName: subItem.enDisplayName
								});
							}
						});
					}
				});

			if (payload.type === 1) {
				ruleTemplateList = data;
			} else if (payload.type === 2) {
				indexTemplateList = data;
			}

			return {
				...state,
				ruleTemplateList: ruleTemplateList,
				indexTemplateList: indexTemplateList,
				ruleTemplateListObj: ruleTemplateListObj,
				indexTemplateListObj: indexTemplateListObj,
				indexOperateTypeMap: indexOperateTypeMap
			};
		}
	}
};
