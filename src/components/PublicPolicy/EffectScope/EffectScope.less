
.txt-wrap{
	span{
		display:block;
		margin-bottom:6px;
		&:last-of-type{
			margin:0;
		}
	}
}
.text-overflow-2-line{
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
}
.grey-txt{
	color:rgba(51,51,51,0.6);
	cursor: pointer;
}
.popover-content{
	max-width:450px;
}
.transform-default{
	transform: rotate(360deg);
}
.transform180{
	transform: rotate(180deg);
	transition: all ease-in .2s;
}
.transform360{
	transform: rotate(360deg);
	transition: all ease-in .2s;
}
