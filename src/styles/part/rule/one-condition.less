@import "../../base/variables";

:global {
    .rule-condition {
        & {
            margin-bottom: 20px;
        }

        .ant-col-4 {
            // text-align: right;
        }
    }

    .rule-content {
        & {
            background: #f7f8f9;
            border: 1px solid #e6e6e6;
            min-height: 300px;
        }

        .one-condition.custom-item {
            & {
                border-bottom: 1px dashed #ccc;
                padding: 15px 0 15px 0;
            }

            .one-condition {
                margin-bottom: 0 !important;
            }

            // 下面的样式是规则免打扰
            &.rule-immuno {
                border-bottom: none;
            }

            &.rule-immuno .one-condition:last-child {
                margin-bottom: 0 !important;
            }

            &.rule-immuno .one-condition {
                margin-bottom: 10px !important;
            }
        }

        .group-condition.custom-item {
            & {
                border-bottom: 1px dashed #ccc;
                padding: 15px 0 15px 0;
            }

            .one-condition {
                & {
                    margin-bottom: 15px;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .oper-icon {
            & {
                font-size: 18px;
                vertical-align: top;
                color: #999;
                margin-left: 10px;
                display: inline-block;
                line-height: 30px !important;
            }

            i {
                margin-right: 10px;
                cursor: pointer;
            }

            i.add:hover {
                color: @blue;
            }

            i.delete:hover {
                color: #f00;
            }
        }

        .rule-ctrl {
            & {
                margin-top: 20px;
                margin-bottom: 20px;
            }

            span {
                color: @blue;
                margin-right: 12px;
                cursor: pointer;
            }

            span:hover {
                text-decoration: underline;
            }
        }

        .custom-item .custom-item {
            border-bottom: 0;
            padding: 0;
            margin-bottom: 15px;
        }
    }
}
