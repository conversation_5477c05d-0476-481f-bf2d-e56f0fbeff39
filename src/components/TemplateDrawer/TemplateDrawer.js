import { PureComponent } from "react";
import { connect } from "dva";
import { Drawer, Input, Tag } from "antd";
import "./TemplateDrawer.less";
import { policyDetailLang } from "@/constants/lang";

class TemplateDrawer extends PureComponent {
	state = {
		activeId: null
	};

	constructor(props) {
		super(props);
		this.openGroup = this.openGroup.bind(this);
		this.addRule = this.addRule.bind(this);
		this.addIndex = this.addIndex.bind(this);
		this.changeSearchWord = this.changeSearchWord.bind(this);
	}

	openGroup(groupId) {
		let { activeId } = this.state;
		if (groupId === activeId) {
			activeId = null;
		} else {
			activeId = groupId;
		}
		this.setState({
			activeId: activeId
		});
	}

	addRule(item) {
		let { policyDetailStore, dispatch } = this.props;
		let { policyRules, policyDetail, addIFChildRuleIndex } = policyDetailStore;

		let timestamp = Date.parse(new Date());
		console.log(item);
		let tempObj = {
			unSave: true,
			hasModify: false,
			template: item.name,
			priority: 0,
			uuid: timestamp.toString(),
			weightRatio: 0,
			weightType: "salaxyZb",
			weightOperator: "add",
			valid: 1,
			baseWeight: 1,
			name: item.displayName,
			fkDealTypeUuid: "",
			id: timestamp,
			operationActions: [],
			conditions: {
				children: [],
				logicOperator: "&&"
			},
			applyStatus: "Approved",
			fkPolicyUuid: policyDetail.uuid,
			operateCode: "Accept"
		};

		let aliasTempObj = {
			logicOperator: "&&",
			property: item.name,
			operator: "==",
			value: "1",
			type: "alias",
			description: "",
			params: [],
			propertyDataType: "",
			children: [],
			describe: item.description
		};

		if (item.name !== "common/custom") {
			tempObj.conditions.children.push(aliasTempObj);
		}

		if (item.name === "common/custom" && item.displayName === "IF") {
			tempObj["ifClause"] = "IF";
			tempObj["displayOrder"] = parseInt(addIFChildRuleIndex + 1, 10);
			tempObj["children"] = [];
		}

		policyRules.push(tempObj);

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				templateDrawer: false
			}
		});
		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				ruleActiveKey: [timestamp.toString()],
				currentRuleUuid: timestamp.toString()
			}
		});
	}

	addIndex(item) {
		let { policyDetailStore, globalStore, indexEditorStore, templateStore, dispatch } = this.props;
		let { personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let timestamp = Date.parse(new Date());

		let tempObj = {
			unSave: true,
			hasModify: false,
			id: timestamp,
			uuid: timestamp.toString(),
			name: lang === "en" ? item.enDisplayName : item.displayName,
			status: "0",
			app: null,
			event: null,
			type: "null",
			calcType: item.name,
			winSize: null,
			winType: null,
			winCount: null,
			dim1: null,
			dimRead1: null,
			dim2: null,
			dim3: null,
			calcField: null,
			filterStr: "[]",
			filterList: [],
			remark: null,
			attachFields: "{}",
			attachFieldsObj: {},
			sceneList: [],
			sceneType: "",
			sceneLogic: "eq",
			maxSize: null
		};
		editIndexMap[timestamp] = tempObj;
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				editIndexMap: editIndexMap
			}
		});
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				templateDrawer: false
			}
		});
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				indexActiveKey: [timestamp.toString()],
				currentIndexId: timestamp.toString()
			}
		});
	}

	changeSearchWord(e) {
		let { policyDetailStore, indexEditorStore, templateStore, dispatch, pageName, templateData } = this.props;
		let { ruleTemplateList, indexTemplateList, searchWord } = templateStore;
		let detailDialogShow = policyDetailStore.dialogShow;
		let value = e.target.value;

		dispatch({
			type: "template/setAttrValue",
			payload: {
				searchWord: value
			}
		});
	}

	render() {
		let { policyDetailStore, indexEditorStore, templateStore, globalStore, dispatch, pageName, templateData } = this.props;
		let { ruleTemplateList, indexTemplateList, searchWord } = templateStore;
		const { personalMode } = globalStore;
		const { lang } = personalMode;
		let detailDialogShow = policyDetailStore.dialogShow;
		let salaxyDialogShow = indexEditorStore.dialogShow;
		let { activeId } = this.state;

		let title = pageName === "policyDetail" ? policyDetailLang.drawer("title1") : policyDetailLang.drawer("title2"); // "规则模板列表" : "指标模板列表"
		let show = pageName === "policyDetail" ? detailDialogShow.templateDrawer : salaxyDialogShow.templateDrawer;
		let placeholder = pageName === "policyDetail" ? policyDetailLang.drawer("inputPlaceholder") : policyDetailLang.drawer("inputPlaceholder2"); // "规则模板搜索" : "指标模板搜索"
		let templateList = pageName === "policyDetail" ? ruleTemplateList : indexTemplateList;

		let templateFilterList = [];
		if (searchWord) {
			templateList && templateList.map((item, index) => {
				if ((item["groupName"] && item["groupName"].toLowerCase().indexOf(searchWord) > -1) || (item["groupDisplayName"] && item["groupDisplayName"].toLowerCase().indexOf(searchWord) > -1)) {
					item["templateDataList"] &&
						item["templateDataList"].map((subItem, subIndex) => {
							templateFilterList.push(subItem);
						});
				} else {
					item["templateDataList"] &&
						item["templateDataList"].map((subItem, subIndex) => {
							if ((subItem.displayName && subItem.displayName.toLowerCase().indexOf(searchWord) > -1) || (subItem.enDisplayName && subItem.enDisplayName.toLowerCase().indexOf(searchWord) > -1) || (subItem.name && subItem.name.toLowerCase().indexOf(searchWord) > -1)) {
								templateFilterList.push(subItem);
							}
						});
				}
			});
		}
		// console.log(indexTemplateList);

		return (
			<Drawer
				title={title}
				width={360}
				className="template-drawer-wrap"
				placement="right"
				closable={true}
				onClose={() => {
					if (pageName === "policyDetail") {
						dispatch({
							type: "policyDetail/setDialogShow",
							payload: {
								templateDrawer: false
							}
						});
					} else {
						dispatch({
							type: "indexEditor/setDialogShow",
							payload: {
								templateDrawer: false
							}
						});
					}
					dispatch({
						type: "template/setAttrValue",
						payload: {
							searchWord: null
						}
					});
				}}
				visible={show}
			>
				<div className="template-detail">
					<div className="template-close" onClick={() => {
						if (pageName === "policyDetail") {
							dispatch({
								type: "policyDetail/setDialogShow",
								payload: {
									templateDrawer: false
								}
							});
						} else {
							dispatch({
								type: "indexEditor/setDialogShow",
								payload: {
									templateDrawer: false
								}
							});
						}
					}}>
						<i className="iconfont icon-close-thin"></i>
					</div>
					<div className="template-search">
						<Input
							type="text"
							placeholder={placeholder}
							value={searchWord || undefined}
							onChange={this.changeSearchWord.bind(this)}
						/>
						<i className="iconfont icon-search"></i>
					</div>
					<div className="template-list">
						{
							searchWord &&
							<div className="template-list-item">
								<ul className="active">
									{
										templateFilterList.map((sItem) => {
											const { advanceConfig = {} } = sItem || {};
											const { tag = {} } = advanceConfig || {};
											return (
												<li
													onClick={() => {
														if (pageName === "policyDetail") {
															this.addRule(sItem);
														} else {
															this.addIndex(sItem);
														}
													}}
												>
													<span>
														{lang === "en" ? sItem.enDisplayName : sItem.displayName}
														{
															tag &&
															Object.keys(tag).length > 0 &&
															<Tag color="blue" className="ml10-important">
																{lang === "en" ? tag.value : tag.name}
															</Tag>
														}
													</span>
													<i className="iconfont icon-plus"></i>
												</li>
											);
										})
									}
								</ul>
							</div>
						}
						{
							!searchWord && templateList && templateList.map((item, index) => {
								return (
									<div className="template-list-item" key={index}>
										<h4
											onClick={this.openGroup.bind(this, item.groupId)}
											className={activeId === item.groupId ? "active" : ""}
										>
											<span>{lang === "en" ? item.groupName : item.groupDisplayName}</span>
											<i className={activeId === item.groupId ? "iconfont icon-arrow-bottom" : "iconfont icon-arrow-right"}></i>
										</h4>
										{
											item["templateDataList"] && item["templateDataList"].length &&
											<ul className={activeId === item.groupId ? "active" : ""}>
												{
													item["templateDataList"].map((subItem, subIndex) => {
														const { advanceConfig = {} } = subItem || {};
														const { tag = {} } = advanceConfig || {};
														return (
															<li
																key={subIndex}
																onClick={() => {
																	if (pageName === "policyDetail") {
																		this.addRule(subItem);
																	} else {
																		this.addIndex(subItem);
																	}
																}}
															>
																<span>
																	{lang === "en" ? subItem.enDisplayName : subItem.displayName}
																	{
																		tag && tag.name &&
																		Object.keys(tag).length > 0 &&
																		<Tag color="blue" className="ml10-important">
																			{lang === "en" ? tag.value : tag.name}
																		</Tag>
																	}
																</span>
																<i className="iconfont icon-plus"></i>
															</li>
														);
													})
												}
											</ul>
										}
									</div>
								);
							})
						}
					</div>
				</div>
			</Drawer>
		);
	}
}

export default connect(state => ({
	templateStore: state.template,
	policyDetailStore: state.policyDetail,
	indexEditorStore: state.indexEditor,
	globalStore: state.global
}))(TemplateDrawer);
