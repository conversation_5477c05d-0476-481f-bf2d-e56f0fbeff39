import { PureComponent } from "react";
import { replayTaskLang } from "@/constants/lang";

export default (props) => {
	const { reportData } = props;

	return (
		<div className="report-panel">
			<div className="report-panel-content-personal">
				<table className="tnt-ant-table">
					<tbody>
						<tr>
							<td>
                                回测开始时间：{reportData.taskStartTime}
							</td>
							<td>
                                回测结束时间：{reportData.taskEndTime}
							</td>
						</tr>
						<tr>
							<td>
                                底层交易量：{reportData.totalCount}
							</td>
							<td>
                                底层交易额：{reportData.totalAmount}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	);
};
