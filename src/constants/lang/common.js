import store from "../../app";

const getRecords = (number) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;

	let cn = "共" + number + "条记录";
	let en = "A total of " + number + " records";
	return personalMode && personalMode.lang === "cn" ? cn : en;
};

const base = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"cancel": {
			"cn": "取消",
			"en": "Cancel"
		},
		"ok": {
			"cn": "确定",
			"en": "Ok"
		},
		"upload": {
			"cn": "上传",
			"en": "upload"
		},
		"close": {
			"cn": "禁用",
			"en": "close"
		},
		"formal": {
			"cn": "启用",
			"en": "formal"
		},
		"simulation": {
			"cn": "模拟",
			"en": "simulation"
		},
		"waitCommit": {
			"cn": "待提交",
			"en": "Wait Commit"
		},
		"waitReview": {
			"cn": "待审批",
			"en": "Wait Review"
		},
		"publish": {
			"cn": "已发布",
			"en": "Publish"
		},
		"back": {
			"cn": "返回",
			"en": "Back"
		},
		"importBtn": {
			"cn": "导入",
			"en": "Import"
		},
		"save": {
			"cn": "保存",
			"en": "Save"
		}
	};
	return params[field][lang];
};

const messageInfo = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"noPermission": {
			"cn": "无权限操作",
			"en": "Permission free operation"
		}
	};
	return params[field][lang];
};

const sourceName = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		field: {
			cn: "字段",
			en: "field"
		},
		realtime: {
			cn: "实时",
			en: "realtime"
		},
		offline: {
			cn: "离线",
			en: "offline"
		}
	};
	return params[field][lang];
};

export default {
	getRecords,
	base,
	messageInfo,
	sourceName
};
