import { PureComponent } from "react";
import { Layout } from "antd";
import { LocaleProvider } from "antd";
import zhCN from "antd/lib/locale-provider/zh_CN";
import enUS from "antd/lib/locale-provider/en_US";
import DocumentTitle from "react-document-title";
import { connect } from "dva";
import { Route, Redirect, Switch } from "dva/router";
import { ContainerQuery } from "react-container-query";
import classNames from "classnames";
import Debounce from "lodash-decorators/debounce";
import Top from "../Common/Top";
import Cookies from "universal-cookie";
import PublishSideMenu from "../Common/PublishSideMenu";
import { isJSON } from "@/utils/isJSON";

const { Content } = Layout;

const query = {
	"screen-xs": {
		maxWidth: 575
	},
	"screen-sm": {
		minWidth: 576,
		maxWidth: 767
	},
	"screen-md": {
		minWidth: 768,
		maxWidth: 991
	},
	"screen-lg": {
		minWidth: 992,
		maxWidth: 1199
	},
	"screen-xl": {
		minWidth: 1200
	}
};

class PublishLayout extends PureComponent {
	constructor(props) {
		super(props);
	}

	componentWillMount() {
		// 检测sessionStorage是否存在token字段，如果存在，将其放到cookie
		// const cookies = new Cookies();
		// if (sessionStorage.getItem("_td_token_")) {
		// 	let tokenStr = sessionStorage.getItem("_td_token_");
		// 	if (tokenStr.length > 10) {
		// 		// 有效token
		// 		cookies.set("_td_token_", tokenStr, { path: "/" });
		// 	} else {
		// 		// 无效token
		// 		sessionStorage.removeItem("_td_token_");
		// 		cookies.remove("_td_token_", { path: "/" });
		// 	}
		// } else {
		// 	if (process.env.SYS_ENV !== "development") {
		// 		// 如果sessionStorage不存在
		// 		cookies.remove("_td_token_", {path: "/"});
		// 		const origin = window.location.origin;
		// 		const pathname = window.location.pathname;
		// 		const search = window.location.search;
		// 		const callbackUrl = origin + pathname + encodeURIComponent(search);
		// 		window.location = "/user/login?callbackUrl=" + callbackUrl;
		// 	}
		// }

		if (!sessionStorage.getItem("_csrf_") && process.env.SYS_ENV !== "development") {
			// 如果sessionStorage不存在
			const {origin, pathname, search} = window.location || {};
			const callbackUrl = origin + pathname + encodeURIComponent(search);
			window.location = "/user/login?callbackUrl=" + callbackUrl;
	   }

		let { dispatch } = this.props;

		// 查看本地localStorage是否存在customTree
		if (localStorage.getItem("customTree")) {
			if (isJSON(localStorage.getItem("customTree"))) {
				let customTreeStr = localStorage.getItem("customTree") || "[]";
				dispatch({
					type: "global/setAttrValue",
					payload: {
						customTree: JSON.parse(customTreeStr),
						menuTreeReady: true
					}
				});
			}
		}

		dispatch({
			type: "global/getUserMenuTree",
			payload: {}
		});
	}

	componentWillUnmount() {
		this.triggerResizeEvent.cancel();
	}

    getMenuData = (data, parentPath) => {
    	let arr = [];
    	data.forEach((item) => {
    		if (item.children) {
    			arr.push({ path: `${parentPath}/${item.path}`, name: item.name });
    			arr = arr.concat(this.getMenuData(item.children, `${parentPath}/${item.path}`));
    		}
    	});
    	return arr;
    };

    getDefaultCollapsedSubMenus(props) {
    	let currentMenuSelectedKeys = [...this.getCurrentMenuSelectedKeys(props)];
    	currentMenuSelectedKeys.splice(-1, 1);
    	if (currentMenuSelectedKeys.length === 0) {
    		return ["dashboard"];
    	}
    	return currentMenuSelectedKeys;
    }

    getCurrentMenuSelectedKeys(props) {
    	let { location: { pathname } } = props || this.props;
    	let keys = pathname.split("/").slice(1);
    	if (keys.length === 1 && keys[0] === "") {
    		return [this.menus[0].key];
    	}
    	return keys;
    }

    getPageTitle() {
    	// 获取页面标题
    	let { location, globalStore } = this.props;
    	let { customTree, personalMode } = globalStore;
    	let lang = personalMode.lang === "cn" ? "cn" : "en";
    	let { pathname } = location;
    	let title;

    	customTree && customTree["menuTree"] && customTree["menuTree"].map(item => {
    		item.children && item.children.map(subItem => {
    			if (pathname.includes(subItem.path)) {
    				title = lang === "en" ? subItem["enName"] : subItem["menuName"];
    			}
    		});
    	});
    	if (pathname.includes("/policy/policyDetail/")) {
    		title = lang === "en" ? "Policy Detail" : "策略详情";
    	}
    	return title ? title : "策略指标";
    }

    handleOpenChange = (openKeys) => {
    	let lastOpenKey = openKeys[openKeys.length - 1];
    	let isMainMenu = this.menus.some(
    		item => lastOpenKey && (item.key === lastOpenKey || item.path === lastOpenKey)
    	);
    	this.setState({
    		openKeys: isMainMenu ? [lastOpenKey] : [...openKeys]
    	});
    };

    @Debounce(600)
    triggerResizeEvent() { // eslint-disable-line
    	let event = document.createEvent("HTMLEvents");
    	event.initEvent("resize", true, false);
    	window.dispatchEvent(event);
    }

    render() {
    	let { globalStore, getRouteData, currentUser, location } = this.props;
    	let { personalMode, sideMenu } = globalStore;
    	let { collapsed } = sideMenu;
    	let topClass = "basic-layout themeS2";
    	if (personalMode.theme) {
    		topClass = "basic-layout " + personalMode.theme;
    	}
    	if (collapsed) {
    		topClass = `${topClass} collapsed-true`;
    	} else {
    		topClass = `${topClass} collapsed-false`;
    	}

    	let layout = (
    		<Layout className={topClass}>
    			<PublishSideMenu
    				menuList={this.menus}
    				location={location}
    				isPublishLayout={true}
    			/>
    			<Layout>
    				<Top
    					currentUser={currentUser}
    					isPublishLayout={true}
    					location={location}
    				/>
    				<Content className="basic-content">
    					<div style={{ minHeight: "calc(100vh - 120px)" }}>
    						<Switch>
    							{
    								getRouteData("PublishLayout").map(item =>
    									(
    										<Route
    											exact={item.exact}
    											key={item.path}
    											path={item.path}
    											component={item.component}
    										/>
    									)
    								)
    							}
    							<Redirect exact to="/index/exception/404" />
    						</Switch>
    					</div>
    				</Content>
    			</Layout>
    		</Layout>
    	);
    	return (
    		<LocaleProvider locale={personalMode.lang === "cn" ? zhCN : enUS}>
    			<DocumentTitle title={this.getPageTitle()}>
    				<ContainerQuery query={query}>
    					{params => <div className={classNames(params)}>{layout}</div>}
    				</ContainerQuery>
    			</DocumentTitle>
    		</LocaleProvider>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	currentUser: state.user.currentUser
}))(PublishLayout);
