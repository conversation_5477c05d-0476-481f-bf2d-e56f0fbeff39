import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Row, Col } from "antd";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";

class IndexBase extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    }

    render() {
    	let { templateStore, globalStore, templateName, indexData } = this.props;
    	let { indexTemplateListObj } = templateStore;
    	let { personalMode } = globalStore;
    	let lang = personalMode.lang === "cn" ? "cn" : "en";

    	let currentTemplate = indexTemplateListObj && templateName ? indexTemplateListObj[templateName] : null;

    	return (
    		<div className="ml-1">
    			<Row className="mb10" gutter={CommonConstants.gutterSpan}>
    				<Col span={4} className="basic-info-title">
    					{/* lang:指标名称 */}
    					{indexListLang.ruleAttr("indexName")}
    				</Col>
    				<Col span={8}>
    					<Input
    						value={indexData ? indexData["name"] : undefined}
    						disabled={true}
    					/>
    				</Col>
    			</Row>
				{/* lang:指标别名 */}
    			{/* <Row className="mb10" gutter={CommonConstants.gutterSpan}>
    				<Col span={4} className="basic-info-title">
    					{indexListLang.ruleAttr("indexAlias")}
    				</Col>
    				<Col span={8}>
    					<Input
    						value={indexData ? indexData["zbId"] : undefined}
    						disabled={true}
    					/>
    				</Col>
    			</Row> */}
    			<Row className="mb10" gutter={CommonConstants.gutterSpan}>
    				<Col span={4} className="basic-info-title">
    					{/* lang:指标描述 */}
    					{indexListLang.ruleAttr("indexDescription")}
    				</Col>
    				<Col span={16}>
    					<p className="index-description">
    						{
    							currentTemplate ? lang === "cn" ? currentTemplate["description"] : currentTemplate["enDescription"] : undefined
    						}
    					</p>
    				</Col>
    			</Row>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	indexRunningStore: state.indexRunning,
	templateStore: state.template
}))(IndexBase);

