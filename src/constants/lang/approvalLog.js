import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		policyTitle1: {
			"cn": "策略审批日志",
			"en": "Policy Approval Log"
		},
		policyTitle2: {
			"cn": "策略审批日志列表",
			"en": "Policy Approval Log List"
		},
		indicatorTitle1: {
			"cn": "指标审批日志",
			"en": "Indicator Approval Log"
		},
		indicatorTitle2: {
			"cn": "指标审批日志列表",
			"en": "Indicator Approval Log List"
		},
		workflowTitle1: {
			"cn": "规则流审批日志",
			"en": "Rule Process Approval Log"
		},
		workflowTitle2: {
			"cn": "规则流审批日志列表",
			"en": "Rule Process Approval Log List"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		taskName: {
			"cn": "任务名称",
			"en": "Task Name"
		},
		search: {
			"cn": "搜索",
			"en": "Search"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"operationType": {
			"cn": "变更类型",
			"en": "Operation Type"
		},
		"taskName": {
			"cn": "任务名称",
			"en": "Task Name"
		},
		"publishDesc": {
			"cn": "发版描述",
			"en": "Publish Description"
		},
		"approvalResult": {
			"cn": "审核结果",
			"en": "Approval Result"
		},
		"approvalDesc": {
			"cn": "审批理由",
			"en": "Approval Description"
		},
		"approvalTime": {
			"cn": "审批时间",
			"en": "Approval Time"
		},
		"createdBy": {
			"cn": "提交人",
			"en": "Created By"
		},
		"submitTime": {
			"cn": "提交时间",
			"en": "Submit Time"
		},
		"delete": {
			"cn": "删除",
			"en": "Delete"
		},
		"publish": {
			"cn": "发布",
			"en": "Publish"
		},
		"offline": {
			"cn": "下线",
			"en": "Offline"
		},
		"pass": {
			"cn": "通过",
			"en": "Pass"
		},
		"reject": {
			"cn": "拒绝",
			"en": "Reject"
		},
		"noReasonForApproval": {
			"cn": "暂无审批理由",
			"en": "No reason for approval"
		}
	};
	return params[field][lang];
};

export default {
	common,
	searchParams,
	table
};
