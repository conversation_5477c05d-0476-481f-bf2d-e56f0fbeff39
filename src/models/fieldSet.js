import cloneDeep from "lodash.clonedeep";
import { message } from "antd";
import { compare, multipleToNull } from "@/utils/modelOperator";
import { fieldSetAPI } from "@/services";

export default {
	namespace: "fieldSet",
	state: {
		loading: true,
		fieldSetList: [],
		searchParams: {
			curPage: 1,
			pageSize: 10,
			total: 0,
			name: null, // 字段集标示
			displayName: null, // 字段集显示名
			type: "all" // 字段集类型
		},
		dialogShow: {
			addFieldSet: false,
			modifyFieldSet: false
		},
		dialogData: {
			operateFieldSetData: {
				id: null,
				name: null,
				displayName: null,
				type: null,
				fields: []
			}
		}
	},
	effects: {
		// 获取字段集列表
		*getFieldSetList({ payload }, { call, put, select }) {
			const searchParams = yield select(state => state.fieldSet.searchParams);
			const queryParams = {
				curPage: searchParams.curPage,
				pageSize: searchParams.pageSize,
				name: searchParams.name,
				displayName: searchParams.displayName,
				type: searchParams.type === "all" ? null : searchParams.type
			};
			let response = yield call(fieldSetAPI.getFieldSetList, queryParams);
			if (!response) {
				yield put({
					type: "setAttrValue",
					payload: {
						loading: false
					}
				});
				return;
			}
			if (!response.success) {
				message.error(response.message);
				yield put({
					type: "setAttrValue",
					payload: {
						loading: false
					}
				});
				return;
			}
			yield put({
				type: "setAttrValue",
				payload: {
					loading: false,
					fieldSetList: response.data.dataList,
					searchParams: {
						curPage: response.data.curPage,
						pageSize: response.data.pageSize,
						total: response.data.total
					}
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = multipleToNull(stateChange[key]);
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		}
	}
};
