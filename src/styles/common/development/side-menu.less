@import "../../base/variables";

:global {
	.side-menu-warp {
		& {
			position: relative;
			top: @header-height;
			margin-top: 16px;
			padding-bottom: 50px;
		}
		.side-menu-section {
			& {
				margin-bottom: 10px;
			}
			h2 {
				font-size: 14px;
				color: #bababa;
				padding-left: 32px;
				font-weight: normal;
				height: @menu-height;
				line-height: @menu-height;
				border-bottom: 1px solid #f0f0f0;
			}
			.side-menu-list {
				& {
					list-style: none;
					padding: 10px 0;
					margin-bottom: 0;
				}
				.side-menu-item {
					& {
						position: relative;
						cursor: pointer;
						height: @menu-height;
						line-height: @menu-height;
					}
					a {
						& {
							color: #7e8081;
							text-decoration: none;
							display: block;
						}
						i {
							padding-left: 30px;
							font-size: 20px;
							margin-right: 15px;
							vertical-align: sub;
							color: #5F6367;
						}
					}
					&:hover {
						color: #323232;
						background: #f5f5f5;
					}
					&.active {
						&:before {
							position: absolute;
							left: 0;
							width: 4px;
							height: @menu-height;
							background: @blue;
							content: "";
						}
						a {
							& {
								background: #e8f0fe;
								color: #2f80f7;
							}
							i {
								color: @blue;
							}
						}
					}
				}
			}
		}
	}
}
