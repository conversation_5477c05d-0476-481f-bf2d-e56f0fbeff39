@import "../base/variables";

@li-height: 40px;

:global {
	.template-drawer-wrap {
		.ant-drawer-header {
			display: none;
		}
		.ant-drawer-close {
			display: none;
		}
		.ant-drawer-body {
			padding: 0;
		}
		.template-close {
			& {
				position: absolute;
				right: 0;
				top: 0;
				width: 56px;
				height: 56px;
				line-height: 56px;
				text-align: center;
				text-decoration: none;
				-webkit-transition: color 0.3s;
				-o-transition: color 0.3s;
				transition: color 0.3s;
				outline: 0;
				padding: 0;
				cursor: pointer;
			}
			i {
				display: block;
				font-size: 16px;
				text-decoration: none;
				cursor: pointer;
				color: #c8c8c8;
				font-weight: 100;
			}
			i:hover {
				color: @red;
			}
		}
		.template-search {
			& {
				position: relative;
				margin-top: 80px;
				margin-left: 20px;
				margin-right: 20px;
				margin-bottom: 35px;
				padding-bottom: 5px;
				padding-right: 30px;
				border-bottom: 1px solid #dcdcdc;
			}
			input {
				border: none;
				outline: none;
				width: 100%;
				line-height: 30px;
				padding-left: 0;
				&:active, &:focus {
					outline: none !important;
					box-shadow: none !important;
				}
			}
			i {
				position: absolute;
				display: inline-block;
				line-height: 30px;
				text-align: center;
				cursor: pointer;
				right: 0;
				top: 3px;
				color: #999;
				font-size: 18px;
			}
		}
		.template-detail {
			& {

			}
			.template-list-item {
				&:last-child {
					h4 {
						border-bottom: 1px solid #e6e6e6;
					}
					ul {
						border-bottom: 1px solid #e6e6e6;
					}
				}
				h4 {
					& {
						font-weight: normal;
						margin: 0;
						padding: 0 20px;
						height: 45px;
						color: #0277bd;
						line-height: 45px;
						font-size: 14px;
						font-variant: normal;
						cursor: pointer;
						border-top: 1px solid #E6E6E6;
					}
					&.active {
						border-top-color: #3498db;
						color: #fff;
						background-color: #3498db;
					}
					i {
						float: right;
						font-size: 14px;
					}
				}
				ul {
					& {
						display: none;
						padding-left: 0;
						margin: 0;
					}
					&.active {
						display: block;
					}
					li {
						& {
							position: relative;
							list-style: none;
							height: @li-height;
							line-height: @li-height;
							color: #666;
							font-size: 14px;
							padding: 0 20px;
						}
						&:hover {
							background-color: #f5f5f5;
							i {
								display: block;
							}
						}
						i {
							& {
								position: absolute;
								right: 0;
								top: 0;
								width: @li-height;
								height: @li-height;
								line-height: @li-height;
								font-size: 16px;
								color: #969696;
								cursor: pointer;
								display: none;
								font-weight: 100;
							}
							&:hover {
								color: @blue;
							}
						}
					}
				}
			}
		}
	}
}
