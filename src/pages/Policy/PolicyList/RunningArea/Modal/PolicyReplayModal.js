import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Modal, Button, Input, Select, Row, Col, Radio, message, Alert, DatePicker } from "antd";
import moment from "moment";
import { policyRunningAPI } from "@/services";
import { CommonConstants } from "@/constants";
import PolicySetFilter from "../PolicySetFilter";
import HistoryPolicyRule from "../HistoryPolicyRule";
import { policyListLang, replayTaskLang } from "@/constants/lang";
import "./PolicyReplayModal.less";

const Option = Select.Option;
const { RangePicker } = DatePicker;

const DataSourceType = [{
	value: "es",
	enDName: "Local event history",
	dName: "本地事件历史"
}
// , {
// 	value: "dc",
// 	enDName: "Big data platform",
// 	dName: "大数据平台"
// }
];

class ReplayTaskReportModal extends PureComponent {
    state = {
    	hasGetReplayRunInfo: false,
    	timeByMinutes: 0,
    	dataTotalCount: 0,
    	postEstimating: false
    }
    constructor(props) {
    	super(props);
    	this.changeTimeDistance = this.changeTimeDistance.bind(this);
    	this.refreshRunningList = this.refreshRunningList.bind(this);
    }

    refreshRunningList() {
    	let { policyRunningStore, globalStore, dispatch } = this.props;
    	let { curPage, pageSize } = policyRunningStore;
    	let { currentApp } = globalStore;
    	if (currentApp && currentApp.name) {
    		dispatch({
    			type: "policyRunning/getPolicySets",
    			payload: {
    				appName: currentApp.name ? currentApp.name : null,
    				curPage: curPage,
    				pageSize: pageSize
    			}
    		});
    	}
    }

    submitModal = () => {
    	let { policyRunningStore } = this.props;
    	let { dialogShow, dialogData } = policyRunningStore;
    	let { policyReplayData } = dialogData;
    	let { addPolicyReplay, modifyPolicyReplay } = dialogShow;
    	let { taskName, taskStartAt, taskFrom, taskTo, taskExecuteType, testTarget: needHandleTestTarget, fieldFilters } = policyReplayData;
    	let testTarget = this.handleTestTarget(needHandleTestTarget);
    	if (!taskName) {
    		// lang:请输入任务名称
    		message.warning(replayTaskLang.operatorModal("replayNamePlaceholder"));
    		return;
    	}
    	if (!taskFrom || !taskTo) {
    		// lang:请选择回测数据区间
    		message.warning(replayTaskLang.operatorModal("timeIntervalPlaceholder"));
    		return;
    	}
    	// lang:请选择回测数据过滤
    	// if (!fieldFilters || fieldFilters.length < 1) {
    	// 	message.warning(replayTaskLang.operatorModal("selectPoliceFilter"));
    	// 	return;
    	// }
    	// if (!taskTarget) {
    	// 	// lang:请选择回测策略集
    	// 	message.warning(replayTaskLang.operatorModal("policySetPlaceholder"));
    	// 	return;
    	// }
    	if (taskExecuteType === "TIMING" && !taskStartAt) {
    		// lang:请选择任务执行时间
    		message.warning(replayTaskLang.operatorModal("taskExecuteTimePleaseholder"));
    		return;
    	}
    	if (!(testTarget.policyList && testTarget.policyList.length > 0)) {
    		// lang:请选择回测目标策略
    		message.warning(replayTaskLang.operatorModal("replayTargetPolice"));
    		return;
    	}

    	// 格式化历史回测策略
    	policyReplayData = Object.assign({}, policyReplayData, {
    		testTarget: JSON.stringify(testTarget, null, 4),
    		fieldFilters: (fieldFilters && fieldFilters.length > 0) ? JSON.stringify(fieldFilters, null, 4) : null
    	});

    	if (addPolicyReplay) {
    		policyRunningAPI.addPolicySetReplay(policyReplayData).then(res => {
    			if (res.success) {
    				// lang:新增回测任务成功
    				message.success(replayTaskLang.operatorModal("addSuccessTip"));
    				// 刷新运行区列表
    				this.refreshRunningList();
    				// 关闭弹窗
    				this.closeModal();
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	}
    	if (modifyPolicyReplay) {
    		policyRunningAPI.modifyPolicySetReplay(policyReplayData).then(res => {
    			if (res.success) {
    				// lang:修改回测任务成功
    				message.success(replayTaskLang.operatorModal("modifySuccessTip"));
    				// 刷新运行区列表
    				this.refreshRunningList();
    				// 关闭弹窗
    				this.closeModal();
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	}
    }

    closeModal = () => {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "policyRunning/setDialogShow",
    		payload: {
    			addPolicyReplay: false,
    			modifyPolicyReplay: false
    		}
    	});

    	setTimeout(() => {
    		dispatch({
    			type: "policyRunning/setDialogData",
    			payload: {
    				policyReplayData: {
    					policySetUuid: null,
    					taskName: null,
    					taskStartAt: null,
    					taskFrom: null,
    					taskTo: null,
    					taskTarget: null,
    					taskExecuteType: "RIGHTNOW",
    					replayTaskStatus: null
    				}
    			}
    		});
    		this.setState({
    			hasGetReplayRunInfo: false,
    			timeByMinutes: 0,
    			dataTotalCount: 0
    		});
    	}, 300);
    }

    changeTimeDistance(timeArr) {
    	let { policyRunningStore, dispatch } = this.props;
    	let { dialogData } = policyRunningStore;
    	let { policyReplayData } = dialogData;

    	let taskFrom, taskTo;
    	if (timeArr.length === 2) {
    		if (timeArr[1].valueOf() < timeArr[0].valueOf()) {
    			message.warning(policyListLang.replay("dataError"));
    			return;
    		}
    		if (timeArr[1].valueOf() - timeArr[0].valueOf() > 7776000000) {
    			message.warning(policyListLang.replay("timeRangeError"));
    			return;
    		}
    		taskFrom = moment(timeArr[0].format("YYYY-MM-DD 00:00:00")).valueOf(); // timeArr[0].valueOf();
    		taskTo = moment(timeArr[1].format("YYYY-MM-DD 23:59:59.999")).valueOf();// timeArr[1].valueOf();
    	}
    	policyReplayData["taskFrom"] = taskFrom;
    	policyReplayData["taskTo"] = taskTo;

    	console.log(taskFrom);
    	console.log(taskTo);
    	dispatch({
    		type: "policyRunning/setDialogData",
    		payload: {
    			policyReplayData: policyReplayData
    		}
    	});
    }

    changeFieldValue = (field, type, e) => {
    	let { policyRunningStore, dispatch } = this.props;
    	let { dialogData } = policyRunningStore;
    	let { policyReplayData: policyReplayDataOld } = dialogData;
    	const policyReplayData = {...policyReplayDataOld};
    	let value;
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	} else if (type === "date") {
    		value = e.valueOf();
    	} else {
    		value = e;
    	}

    	policyReplayData[field] = value;
    	dispatch({
    		type: "policyRunning/setDialogData",
    		payload: {
    			policyReplayData: policyReplayData
    		}
    	});
    }

    // 处理回测数据
    handleTestTarget = ({ ...testTarget }) => {
    	let { policyRunningStore: { dialogData } } = this.props;
    	let { policyRulesSet } = dialogData;
    	let { policyList } = testTarget;
    	const newPolicyList = [];
    	policyRulesSet && policyRulesSet.map((v) => {
    		const { rules = [], policyInfo } = v;
    		let testData = {
    			policyUuid: policyInfo.uuid,
    			policyName: policyInfo.name,
    			ruleList: []
    		};
    		rules.map(rule => {
    			if (policyList.indexOf(rule.uuid) > -1) {
    				testData.ruleList.push({
    					"ruleId": rule.id,
    					"ruleUuid": rule.uuid,
    					"ruleName": rule.name
    				});
    			}
    		});
    		if (testData.ruleList.length > 0) {
    			newPolicyList.push(testData);
    		}
    	});
    	testTarget.policyList = newPolicyList;
    	return testTarget;
    }
    getReplayTaskRunInfo = () => {
    	let { policyRunningStore: { dialogData } } = this.props;
    	let { policyReplayData } = dialogData;
    	let { taskName, taskStartAt, taskFrom, taskTo, taskExecuteType, testTarget: needHandleTestTarget, fieldFilters, dataSourceType } = policyReplayData;
    	let testTarget = this.handleTestTarget(needHandleTestTarget);
    	if (!taskName) {
    		// lang:请输入任务名称
    		message.warning(replayTaskLang.operatorModal("replayNamePlaceholder"));
    		return;
    	}
    	if (!dataSourceType) {
    		// lang:请选择回测数据类型
    		message.warning(replayTaskLang.operatorModal("replayDataTypePlaceholder"));
    		return;
    	}
    	if (!taskFrom || !taskTo) {
    		// lang:请选择回测数据区间
    		message.warning(replayTaskLang.operatorModal("timeIntervalPlaceholder"));
    		return;
    	}
    	// lang:请选择回测数据过滤
    	// if (!fieldFilters || fieldFilters.length < 1) {
    	// 	message.warning(replayTaskLang.operatorModal("selectPoliceFilter"));
    	// 	return;
    	// }
    	// if (!taskTarget) {
    	// 	// lang:请选择回测策略集
    	// 	message.warning(replayTaskLang.operatorModal("policySetPlaceholder"));
    	// 	return;
    	// }
    	if (taskExecuteType === "TIMING" && !taskStartAt) {
    		// lang:请选择任务执行时间
    		message.warning(replayTaskLang.operatorModal("taskExecuteTimePleaseholder"));
    		return;
    	}
    	if (!(testTarget.policyList && testTarget.policyList.length > 0)) {
    		// lang:请选择回测目标策略
    		message.warning(replayTaskLang.operatorModal("replayTargetPolice"));
    		return;
    	}

    	// 格式化历史回测策略
    	policyReplayData = Object.assign({}, policyReplayData, {
    		testTarget: JSON.stringify(testTarget, null, 4),
    		fieldFilters: (fieldFilters && fieldFilters.length > 0) ? JSON.stringify(fieldFilters, null, 4) : null
    	});

    	this.setState({
    		postEstimating: true
    	}, ()=>{
    		policyRunningAPI.getReplayTaskRunInfo(policyReplayData).then(res => {
    			this.setState({
    				postEstimating: false
    			});
    			if (res.success) {
    				let data = res.data || {};
    				this.setState({
    					hasGetReplayRunInfo: true,
    					timeByMinutes: data.timeByMinutes,
    					dataTotalCount: data.dataTotalCount
    				});
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			this.setState({
    				postEstimating: false
    			});
    			console.log(err);
    		});
    	});

    }

    render() {
    	let { hasGetReplayRunInfo, timeByMinutes, dataTotalCount, postEstimating } = this.state;
    	let { policyRunningStore, globalStore } = this.props;
    	let { dialogShow, dialogData, policySetsList = [] } = policyRunningStore;
    	let { allMap } = globalStore;
    	let { addPolicyReplay, modifyPolicyReplay } = dialogShow;
    	let { policyReplayData, policyRulesSet = [] } = dialogData;
    	let { taskName, taskStartAt, taskFrom, taskTo, taskTarget, taskExecuteType, replayTaskStatus, policySetUuid = [], testTarget = {}, fieldFilters = [], dataSourceType } = policyReplayData;
    	fieldFilters = fieldFilters && fieldFilters.length > 0 ? fieldFilters : [{}];
    	// lang：全部策略集
    	let eventSelectOption = [];
    	for (let [key, value] of Object.entries(allMap && allMap["eventIdSelect"] ? allMap["eventIdSelect"] : {})) {
    		eventSelectOption.push(
    			<Option value={key}>{value}</Option>
    		);
    	}

    	let disabled = replayTaskStatus === 5;

    	let { personalMode } = globalStore;
    	let lang = personalMode.lang === "cn" ? "cn" : "en";

    	let footer = [];
    	if (hasGetReplayRunInfo) {
    		footer = [
    			<Button
    				onClick={() => {
    					this.setState({
    						hasGetReplayRunInfo: false,
    						timeByMinutes: 0,
    						dataTotalCount: 0
    					});
    				}}
    			>
                    上一步
    			</Button>,
    			<Button
    				type="primary"
    				onClick={this.submitModal}
    				onClick={dataTotalCount > 0 ? this.submitModal : this.closeModal}
    			>
    				{dataTotalCount > 0 ? "确定" : "取消"}
    			</Button>
    		];
    	} else {
    		footer = [
    			<Button
    				onClick={this.closeModal}
    			>
                    取消
    			</Button>,
    			<Button
    				type="primary"
    				loading={postEstimating}
    				onClick={this.getReplayTaskRunInfo}
    			>
                    下一步
    			</Button>
    		];
    	}

    	return (
    		<Fragment>
    			{
    				(addPolicyReplay || modifyPolicyReplay) &&
                    <Modal
                    	title={addPolicyReplay ? replayTaskLang.operatorModal("addReplayTaskSetting") : replayTaskLang.operatorModal("modifyReplayTaskSetting")} // lang:新增回测任务配置 & 修改回测任务配置
                    	visible={true}
                    	maskClosable={false}
                    	onCancel={this.closeModal}
                    	footer={footer}
                    	width={700}
                    >
                    	{
                    		!hasGetReplayRunInfo &&
                            <div className="basic-form">
                            	{
                            		disabled &&
                                    <Alert
                                    	message={replayTaskLang.operatorModal("runningTaskModifyTip")} // 运行中的任务无法修改
                                    	type="warning"
                                    	showIcon
                                    	style={{ marginBottom: "10px" }}
                                    />
                            	}
                            	<Row gutter={CommonConstants.gutterSpan}>
                            		<Col span={5} className="basic-info-title">
                            			{/* 回测任务名称*/}
                            			{replayTaskLang.operatorModal("replayName")}：
                            		</Col>
                            		<Col span={19}>
                            			<Input
                            				placeholder={replayTaskLang.operatorModal("replayNamePlaceholder")} // 请输入回测任务名称
                            				value={taskName || undefined}
                            				onChange={this.changeFieldValue.bind(this, "taskName", "input")}
                            				disabled={disabled}
                            			/>
                            		</Col>
                            	</Row>
                            	<Row gutter={CommonConstants.gutterSpan}>
                            		<Col span={5} className="basic-info-title">
                            			{/* 回测数据类型*/}
                            			{replayTaskLang.operatorModal("replayDataType")}：
                            		</Col>
                            		<Col span={19}>
                            			<Select
                            				placeholder={replayTaskLang.operatorModal("replayDataTypePlaceholder")} // 请选择回测数据类型
                            				value={dataSourceType || undefined}
                            				onChange={this.changeFieldValue.bind(this, "dataSourceType", "select")}
                            				disabled={disabled}
                            			>
                            				{
                            					DataSourceType.map(v=>{
                            						return (
                            							<Option key={v.value} value={v.value}>
                            								{lang === "cn" ? v.dName : v.enDName}
                            							</Option>
                            						);
                            					})
                            				}
                            			</Select>
                            		</Col>
                            	</Row>
                            	<Row gutter={CommonConstants.gutterSpan} className="basic-info-history-height-auto">
                            		<Col span={5} className="basic-info-title">
                            			{/* lang:回测策略集 */}
                            			{replayTaskLang.operatorModal("policyFilter")}：
                            		</Col>
                            		<Col span={19}>
                            			{/* <Select
                            				placeholder={replayTaskLang.operatorModal("policySetPlaceholder")} // lang：请选择回测策略集
                            				value={taskTarget || undefined}
                            				onChange={this.changeFieldValue.bind(this, "taskTarget", "select")}
                            				disabled={disabled}
                            				showSearch
                            				optionFilterProp="children"
                            				dropdownMatchSelectWidth={false}
                            			>
                            				{eventSelectOption}
                                        </Select> */}
                            			<PolicySetFilter
                            				changeFieldValue={(data) => { this.changeFieldValue("fieldFilters", "", data); }}
                            				value={fieldFilters}
                            				disabled={disabled}
                            			/>
                            		</Col>
                            	</Row>
                            	<Row gutter={CommonConstants.gutterSpan}>
                            		<Col span={5} className="basic-info-title">
                            			{/* lang:回测数据区间 */}
                            			{replayTaskLang.operatorModal("timeInterval")}：
                            		</Col>
                            		<Col span={19}>
                            			<RangePicker
                            				format="YYYY-MM-DD"
                            				value={[taskFrom ? moment(taskFrom) : null, taskTo ? moment(taskTo) : null]}
                            				onChange={this.changeTimeDistance.bind(this)}
                            				style={{ width: "100%" }}
                            				allowClear={false}
                            				ranges={policyListLang.replay("dateMap")}
                            				disabled={disabled}
                            			/>
                            		</Col>
                            	</Row>
                            	{/* {
                            		!disabled &&
                                    <Row gutter={CommonConstants.gutterSpan}>
                                    	<Col span={5} className="basic-info-title">
                                    	</Col>
                                    	<Col span={19}>
                                    		<Alert
                                    			message={replayTaskLang.operatorModal("timeMaxSelect")} // lang:日期区间最长选择3个月，最大支持500W条数据。
                                    			type="warning"
                                    			showIcon
                                    			style={{ marginBottom: "10px" }}
                                    		/>
                                    	</Col>
                                    </Row>
                            	} */}

                            	{/* 选择历史策略集中的策略以及规则 */}
                            	<Row gutter={CommonConstants.gutterSpan} className='basic-info-history-height-auto'>
                            		<Col span={5} className="basic-info-title">
                            			{/* lang:historyReplaySet */}
                            			{replayTaskLang.operatorModal("historyReplaySet")}：
    					            </Col>
                            		<Col span={19}>
                            			{
                            				policyRulesSet &&
                                            policyRulesSet.length > 0 &&
                                            <HistoryPolicyRule
                                            	policyRulesSet={policyRulesSet}
                                            	changeFieldValue={(data) => { this.changeFieldValue("testTarget", "", data); }}
                                            	value={testTarget}
                                            />
                            			}
                            		</Col>
                            	</Row>

                            	<Row gutter={CommonConstants.gutterSpan}>
                            		<Col span={5} className="basic-info-title">
                            			{/* lang:taskExecuteType */}
                            			{replayTaskLang.operatorModal("taskExecuteType")}：
                            		</Col>
                            		<Col span={19}>
                            			<Radio.Group
                            				value={taskExecuteType}
                            				buttonStyle="solid"
                            				onChange={this.changeFieldValue.bind(this, "taskExecuteType", "input")}
                            				disabled={disabled}
                            			>
                            				<Radio.Button value="RIGHTNOW">
                            					{/* lang:立即执行 */}
                            					{replayTaskLang.operatorModal("rightNow")}
                            				</Radio.Button>
                            				<Radio.Button value="TIMING">
                            					{/* lang:定时执行 */}
                            					{replayTaskLang.operatorModal("timing")}
                            				</Radio.Button>
                            			</Radio.Group>
                            		</Col>
                            	</Row>
                            	{
                            		taskExecuteType === "TIMING" &&
                                    <Row gutter={CommonConstants.gutterSpan}>
                                    	<Col span={5} className="basic-info-title">
                                    		{/* lang:任务执行时间 */}
                                    		{replayTaskLang.operatorModal("taskExecuteTime")}：
                                    	</Col>
                                    	<Col span={19}>
                                    		<DatePicker
                                    			style={{ width: "100%" }}
                                    			value={taskStartAt ? moment(taskStartAt) : undefined}
                                    			onChange={this.changeFieldValue.bind(this, "taskStartAt", "date")}
                                    			disabledDate={(current) => {
                                    				return current && current < moment().subtract(1, "days");
                                    			}}
                                    			format="YYYY-MM-DD HH:mm:ss"
                                    			showTime={{ defaultValue: moment("00:00:00", "HH:mm:ss") }}
                                    			disabled={disabled}
                                    		/>
                                    	</Col>
                                    </Row>
                            	}
                            </div>
                    	}
                    	{
                    		hasGetReplayRunInfo &&
                            dataTotalCount > 0 &&
                            <div>当前测试共{dataTotalCount}条数据，预计运行时间{timeByMinutes}分钟，请确认是否需要回测。</div>
                    	}
                    	{
                    		hasGetReplayRunInfo &&
                            dataTotalCount === 0 &&
                            <div>当前测试共{dataTotalCount}条数据，无需添加任务，重新选择回测区间或取消操作。</div>
                    	}
                    </Modal>
    			}
    		</Fragment>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyRunningStore: state.policyRunning
}))(ReplayTaskReportModal);
