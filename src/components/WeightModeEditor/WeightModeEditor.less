@color1: #4ed24d;
@color2: #b9ff6f;
@color3: #ffc465;
@color4: #ffa655;
@color5: #ffb66a;
@color6: #ff6d1f;
@color7: #ff7b53;
@color8: #ff5a37;
@color9: #ee3824;
@color10: #d80000;
@color11: #c20000;

:global {
    .weight-mode-editor-wrap {
        & {
            height: auto;
            overflow: hidden;
            border: 1px dashed #dcdcdc;
			padding: 20px 20px;
			position: relative;
        }

        &.basic-page {
            background: #f0f2f5;
        }

        &:hover {
            //background: #fafafa;
        }

        ul {
            & {
                position: relative;
				padding-left: 0;
				margin-bottom:0;
            }

            // &:before {
            //     position: absolute;
            //     left: 9px;
            //     top: 10px;
            //     width: 1px;
            //     height: 100%;
            //     content: "";
            //     background: #c8c8c8;
            // }

            li {
				position: relative;
				list-style: none;
				margin-bottom: 20px;
				padding: 5px 0;
				&:last-of-type{
					margin-bottom:0;
				}
                //&.color1:before {
                //	border-color: @color1;
                //}
                //&.color2:before {
                //	border-color: @color2;
                //}
                //&.color3:before {
                //	border-color: @color3;
                //}
                //&.color4:before {
                //	border-color: @color4;
                //}
                //&.color5:before {
                //	border-color: @color5;
                //}
                //&.color6:before {
                //	border-color: @color6;
                //}
                //&.color7:before {
                //	border-color: @color7;
                //}
                //&.color8:before {
                //	border-color: @color8;
                //}
                //&.color9:before {
                //	border-color: @color9;
                //}
                //&.color10:before {
                //	border-color: @color10;
                //}
                //&.color11:before {
                //	border-color: @color11;
                //}
                &:before {
                    position: absolute;
                    left: 0;
                    top: 10px;
                    width: 19px;
                    height: 19px;
                    border: 5px solid rgba(54, 93, 120, 0.55);
                    content: "";
                    background: #fff;
                    border-radius: 50%;
				}
				&:not(:last-of-type){
					&:after {
						content: "";
						position: absolute;
						left: 9px;
						top: 30px;
						width: 1px;
						height: 100%;
						background: #c8c8c8;
					}
				}

                .ant-input-group {
                    & {
                        position: relative;
                        top: 30px;
                        padding-left: 40px;
                    }

                    .ant-input {
                        text-align: center;
                        padding: 4px 8px;
                    }

                    .fz-span {
                        line-height: 32px;
                        text-align: center;
                    }
                }

                .info-oper {
                    & {
                        font-size: 18px;
                        line-height: 32px;
                    }

                    i {
                        width: 40%;
                        text-align: center;
                        cursor: pointer;
                        color: #999;
                    }

                    i.add:hover {
                        color: #2e81f7;
                    }

                    i.delete:hover {
                        color: #ff0000;
                    }
                }
            }
        }
    }
    .more-weight-info{
        position: absolute;
        right: 14px;
        top: 10px;
		font-size: 16px;
		z-index: 10;
    }
}
