import { PureComponent, Fragment } from "react";
import { routerRedux } from "dva/router";
import { connect } from "dva";
import { Modal, Table, Pagination, Tooltip, message } from "antd";
import { commonLang } from "@/constants/lang";
import EffectScope from "@/components/PublicPolicy/EffectScope/EffectScope";
import "./index.less";

class PublicPolicyModal extends PureComponent {
	paginationOnChange = (current, pageSize) => {
		this.props.paginationOnChange({ curPage: current, pageSize });
	}
	// 表单内容
	columns = () => {
		let { dispatch, location } = this.props;
		return (
			[
				{
					title: "公共策略名称",
					dataIndex: "name",
					key: "name",
					width: 160,
					ellipsis: true,
					render: (text) => {
						return (
							<Fragment>
								<Tooltip placement="left" title={text}>
									{text}
								</Tooltip>
							</Fragment>
						);
					}
				},
				{
					title: "生效范围",
					dataIndex: "scene",
					key: "scene",
					width: 220,
					render: (scene) => {
						return <EffectScope scene={scene && JSON.parse(scene)} />;
					}
				},
				{
					title: "描述",
					dataIndex: "description",
					key: "description"
				},
				{
					title: "修改时间",
					key: "gmtModified",
					dataIndex: "gmtModified"
				},
				{
					title: "修改人",
					key: "createBy",
					dataIndex: "createBy"
				},
				{
					title: "操作",
					key: "action",
					width: 70,
					render: (e, record) => (
						<div className="table-action">
							<Tooltip title="查看详情">
								<a
									onClick={() => {
										if (true) {
											let pathname = location.pathname;
											let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
											this.props.onCancel(false); // 关闭弹窗 然后去跳转
											dispatch(routerRedux.push(prefix + "/policy/versionPublicPolicyDetail/" + record.uuid + "?tabIndex=1"));
										} else {
											// lang:无权限操作
											message.warning(commonLang.messageInfo("noPermission"));
										}
									}}
								>
									<i className="salaxy-iconfont salaxy-preview"></i>
								</a>
							</Tooltip>
						</div >
					)
				}
			]
		);
	}
	render() {
		let { visible, onCancel, publicPolicyData } = this.props;
		const { dataList, total, curPage } = publicPolicyData;
		return (
			<Modal
				title="公共策略" // lang:公共策略
				visible={visible}
				maskClosable={false}
				onCancel={() => { onCancel(false); }}
				width="850px"
			>
				<div className="page-global-body-main no-padding">
					<Table
						columns={this.columns()}
						dataSource={dataList}
						pagination={false}
						rowKey={(e, ind) => ind}
					/>

					<div className="page-global-body-pagination">
						{/* lang:共x条记录 */}
						<span className="count">{commonLang.getRecords(total)}</span>
						<Pagination
							showSizeChanger
							onChange={this.paginationOnChange}
							onShowSizeChange={this.paginationOnChange}
							defaultCurrent={1}
							total={total}
							current={curPage}
						/>
					</div>
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(PublicPolicyModal);
