import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Form, Icon, message, Row, Col, Select, Popover, Checkbox } from "antd";
import { templateAPI } from "@/services";
import { CommonConstants } from "@/constants";

const Option = Select.Option;

class Step<PERSON>hree extends PureComponent {
	constructor(props) {
		super(props);
		this.changeHandle = this.changeHandle.bind(this);
		this.modifyTemplate = this.modifyTemplate.bind(this);
	}

	changeHandle(field, type, e) {
		let { dispatch, templateStore } = this.props;
		let { modifyTemplateData } = templateStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		}
		modifyTemplateData[field] = value;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	modifyTemplate() {
		let { templateStore, dispatch } = this.props;
		let { currentType, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		templateAPI.modifyTemplate(modifyTemplateData).then((res) => {
			if (res.success) {
				dispatch({
					type: "template/getTemplateList",
					payload: {
						type: currentType
					}
				});
				message.success("更新模板成功");
				dispatch({
					type: "template/setDialogShow",
					payload: {
						modifyTemplate: false
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { templateStore } = this.props;
		let { dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let { cfgJson } = modifyTemplateData;

		return (
			<div className="template-preview">
				<Form>
					<Row gutter={CommonConstants.gutterSpan} className="mb20">
						<Col span={3} className="basic-info-title">描述</Col>
						<Col span={10}>
							<Input
								type="text"
								value={modifyTemplateData.description || undefined}
								disabled={true}
								placeholder="请输入策略名称"
							/>
						</Col>
					</Row>
					{
						cfgJson &&
                        cfgJson.params &&
                        cfgJson.params.map((item, index) => {
                        	let tipContentArr = [];
                        	if (item["tipContent"]) {
                        		tipContentArr = item["tipContent"].split(/\n/);
                        	}
                        	return (
                        		<Row gutter={CommonConstants.gutterSpan} className="mb20" key={index}>
                        			<Col span={3} className="basic-info-title">
                        				{
                        					item.labelText ? item.labelText : "暂无标签"
                        				}
                        			</Col>
                        			<Col span={21}>
                        				<Row>
                        					{
                        						item.children && item.children.map((subItem, subIndex) => {
                        							return (
                        								<Col span={10} key={subIndex} className="mb10">
                        									{
                        										subItem.componentType === "input" &&
                                                                <Input
                                                                	type="text"
                                                                	defaultValue={subItem.defaultValue || undefined}
                                                                	placeholder={subItem.placeholder || undefined}
                                                                />
                        									}
                        									{
                        										subItem.componentType === "select" &&
                                                                <Select
                                                                	defaultValue={subItem.defaultValue || undefined}
                                                                	placeholder={subItem.placeholder || undefined}
                                                                >
                                                                	{
                                                                		subItem.selectType === "self" &&
                                                                        subItem.selectOption.map((optionItem, optionIndex) => {
                                                                        	return (
                                                                        		<Option
                                                                        			value={optionItem.value}
                                                                        			key={optionIndex}
                                                                        		>
                                                                        			{optionItem.name}
                                                                        		</Option>
                                                                        	);
                                                                        })
                                                                	}
                                                                </Select>
                        									}
                        									{
                        										subItem.componentType === "checkbox" &&
                                                                subItem.selectType === "self" &&
                                                                <Checkbox.Group style={{ width: "100%" }}>
                                                                	{
                                                                		subItem.selectOption.map((optionItem, optionIndex) => {
                                                                			return (
                                                                				<Checkbox
                                                                					value={optionItem.value}
                                                                					key={optionIndex}
                                                                				>
                                                                					{optionItem.name}
                                                                				</Checkbox>
                                                                			);
                                                                		})
                                                                	}
                                                                </Checkbox.Group>
                        									}
                        								</Col>
                        							);
                        						})
                        					}
                        					{
                        						item["tipTitle"] && item["tipContent"] &&
                                                <Col span={1} className="mb10">
                                                	<Popover
                                                		placement="right"
                                                		title={item["tipTitle"]}
                                                		content={
                                                			tipContentArr.map((tip, tipIndex) => <div key={tipIndex}>{tip}</div>)
                                                		}
                                                	>
                                                		<Icon
                                                			type="question-circle-o"
                                                			className="param-tip"
                                                		/>
                                                	</Popover>
                                                </Col>
                        					}
                        				</Row>
                        			</Col>
                        		</Row>
                        	);
                        })
					}
				</Form>
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(StepThree);
