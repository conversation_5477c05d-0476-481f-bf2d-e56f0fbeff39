@import "../../base/variables";

:global {
	.template-param-rule-list {
		& {
			margin-left: 20%;
			border: 1px solid #dcdcdc;
		}
		.template-param-rule-section {
			& {
				position: relative;
				padding: 20px;
				border-bottom: 1px dashed #b4b4b4;
			}
			&:last-child {
				border-bottom: none;
			}
			&:hover {
				.remove-rule-item {
					display: block;
				}
			}
			.remove-rule-item {
				& {
					position: absolute;
					left: 0;
					top: 0;
					width: 36px;
					height: 36px;
					line-height: 36px;
					cursor: pointer;
					text-align: center;
					display: none;
				}
				&:hover {
					background: #dcdcdc;
					i {
						color: @blue;
					}
				}
				i {
					font-size: 18px;
				}
			}
		}
		.template-param-rule-item {
			& {
				position: relative;
				height: auto;
				line-height: 32px;
				overflow: hidden;
				margin-bottom: 10px;
			}
			&:last-child {
				margin-bottom: 0;
			}
			.template-param-rule-title {
				width: 30%;
				float: left;
				text-align: right;
			}
			.template-param-rule-field {
				& {
					width: 65%;
					float: right;
				}
				.ant-select {
					width: 100%;
				}
				.add-new-operation {
					& {
						cursor: pointer;
					}
					&:hover {
						text-decoration: underline;
						color: @blue;
					}
					i {
						margin-right: 4px;
					}
				}
			}
		}
		.template-rule-case-list {
			& {
				border: 1px dashed #dcdcdc;
			}
			.template-rule-case-item {
				& {
					position: relative;
					background: #fff;
					margin-bottom: 10px;
					padding: 10px;
				}
				&:last-child {
					margin-bottom: 0;
				}
				&:hover {
					.remove-rule-case-item {
						display: block;
					}
				}
				.remove-rule-case-item {
					& {
						position: absolute;
						left: 0;
						top: 0;
						width: 36px;
						height: 36px;
						line-height: 36px;
						cursor: pointer;
						text-align: center;
						display: none;
						background: rgba(220, 220, 220, 0.65);
						z-index: 999;
					}
					&:hover {
						background: #dcdcdc;
						i {
							color: @blue;
						}
					}
					i {
						font-size: 18px;
					}
				}
			}
		}
	}
}
