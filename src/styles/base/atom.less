// 原子类集合
/*文字排版*/
:global {
    .tl {
        text-align: left;
    }

    .tc {
        text-align: center;
    }

    .tr {
        text-align: right;
    }

    .cb {
        clear: both;
    }

    .cl {
        clear: left;
    }

    .cr {
        clear: right;
    }

    .clearfix:after {
        content: ".";
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
    }

    .clearfix {
        display: inline-block;
    }

    .clearfix {
        display: block;
    }

    .w50 {
        width: 50px;
    }

    .w100 {
        width: 100px;
    }

    .w150 {
        width: 150px;
    }

    .w200 {
        width: 200px;
    }

    .w250 {
        width: 250px;
    }

    /* ================================== margin ================================== */
    // margin top
    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }

    .mt30 {
        margin-top: 30px;
    }

    .mt40 {
        margin-top: 40px;
    }

    .mt50 {
        margin-top: 50px;
    }

	// margin bottom
	.mb5 {
        margin-bottom: 5px;
	}

    .mb10 {
        margin-bottom: 10px;
    }

    .mb20 {
        margin-bottom: 20px;
    }

    .mb30 {
        margin-bottom: 30px;
    }

    .mb40 {
        margin-bottom: 40px;
    }

    .mb50 {
        margin-bottom: 50px;
    }

    // margin left
    .ml10 {
        margin-left: 10px;
    }

    .ml20 {
        margin-left: 20px;
    }

    .ml30 {
        margin-left: 30px;
    }

    .ml40 {
        margin-left: 40px;
    }

    .ml50 {
        margin-left: 50px;
    }

    // margin right
    .mr10 {
        margin-right: 10px;
    }

    .mr20 {
        margin-right: 20px;
    }

    .mr30 {
        margin-right: 30px;
    }

    .mr40 {
        margin-right: 40px;
    }

    .mr50 {
        margin-right: 50px;
    }

    // padding top
    .p20 {
        padding: 20px;
    }

    .pt10 {
        padding-top: 10px;
    }

    .pt20 {
        padding-top: 20px;
    }

    .pt30 {
        padding-top: 30px;
    }

    .pt40 {
        padding-top: 40px;
    }

    .pt50 {
        padding-top: 50px;
    }

    // padding bottom
    .pb10 {
        padding-bottom: 10px;
    }

    .pb20 {
        padding-bottom: 20px;
    }

    .pb30 {
        padding-bottom: 30px;
    }

    .pb40 {
        padding-bottom: 40px;
    }

    .pb50 {
        padding-bottom: 50px;
    }

    // padding left
    .pl10 {
        padding-left: 10px;
    }

    .pl20 {
        padding-left: 20px;
    }

    .pl30 {
        padding-left: 30px;
    }

    .pl40 {
        padding-left: 40px;
    }

    .pl50 {
        padding-left: 50px;
    }

    // padding right
    .pr10 {
        padding-right: 10px;
    }

    .pr20 {
        padding-right: 20px;
    }

    .pr30 {
        padding-right: 30px;
    }

    .pr40 {
        padding-right: 40px;
    }

    .pr50 {
        padding-right: 50px;
    }

    .text-overflow {
        overflow: hidden;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        -webkit-text-overflow: ellipsis;
        -moz-text-overflow: ellipsis;
        white-space: nowrap;
    }
    .wid-100-percent{
        width: 100% !important;
	}
	.fl{
		float:left;
	}
	.fr{
		float:right;
	}
}
