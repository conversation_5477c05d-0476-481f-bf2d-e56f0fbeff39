import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Form, Input, message } from "antd";
import { reCallTaskLang } from "@/constants/lang";
import { reCallTaskAPI } from "@/services";
import "./SearchResultModal.less";

const formItemLayout = {
	labelCol: {
	  xs: { span: 24 },
	  sm: { span: 6 }
	},
	wrapperCol: {
	  xs: { span: 24 },
	  sm: { span: 18 }
	}
};
class SearchResultModal extends PureComponent {
	constructor(props) {
		super(props);
	}

    closeModal = () => {
    	const { dispatch } = this.props;
    	dispatch({
    		type: "reCallTask/setDialogShow",
    		payload: {
    			searchResultModal: false
    		}
    	});
    	dispatch({
    		type: "reCallTask/setAttrValue",
    		payload: {
    			zbInfo: {
    				zbName: "",
    				zbFillBackUuid: "",
    				dim1Value: "",
    				dim1: "",
    				slaveDimsForWeb: []
    			}
    		}
    	});
    }

	changeFieldValue = (e, name, type, changeType) => {
		const { dispatch, reCallTaskStore } = this.props;
		const { zbInfo: zbInfoOld } = reCallTaskStore;
		const zbInfo = {...zbInfoOld};
		const { slaveDimsForWeb = [] } = zbInfo || {};
		let value = e;
		if (type === "input") {
			value = e.target.value;
		}
		if (changeType === "salveDim") {
			slaveDimsForWeb.map(v=>{
				if (v.slaveField === name) {
					v.slaveValue = value;
				}
				return v;
			});
		} else {
			zbInfo[name] = value;
		}
		dispatch({
    		type: "reCallTask/setAttrValue",
    		payload: {
    			zbInfo
    		}
    	});
	}

	submit = () => {
		const { reCallTaskStore } = this.props;
		const { zbInfo = {} } = reCallTaskStore;
		const { zbFillBackUuid, dim1, dim1Value, calcFieldValue, calcField, slaveDimsForWeb = [], calcType } = zbInfo || {};
		let params = {
			zbFillBackUuid,
			dim1,
			dim1Value
		};
		if (calcType === "topn") {
			params = {
				...params,
				calcFieldValue,
				calcField
			};
		} else {
			params = {
				...params,
				slaveDimsForWeb: JSON.stringify(slaveDimsForWeb)
			};
		}
		reCallTaskAPI.queryResult(params).then(response=>{
			const { success, data, message } = response;
			if (success) {
				Modal[data ? "success" : "warning"]({
					title: zbInfo.zbName,
					content: data ? `${reCallTaskLang.reCallTaskModal("backVal")} ${data}` : reCallTaskLang.reCallTaskModal("noResult"),
					okText: reCallTaskLang.reCallTaskModal("confirm") // "确定"
				  });
			} else {
				message.error(message || reCallTaskLang.reCallTaskModal("searchSuc")); // 查询成功
			}
		}).catch(e=>{
			message.error(e.message || reCallTaskLang.reCallTaskModal("searchFail")); // 查询失败
		});
	};

	render() {
    	const { reCallTaskStore, globalStore } = this.props;
    	const { dialogShow, zbInfo } = reCallTaskStore;
    	const { searchResultModal } = dialogShow;
    	const { dim1 = "", dim1Value, slaveDimsForWeb = [], calcType, calcField, calcFieldValue } = zbInfo || {};
    	const { allMap } = globalStore;
		const ruleFieldList = allMap["ruleFieldList"] || [];
    	const dim1Name = (ruleFieldList.find(v=>{
    		return v.name === dim1;
		}) || {}).dName;
		const calcFieldName = (ruleFieldList.find(v=>{
    		return v.name === calcField;
		}) || {}).dName;
    	return (
    		<Modal
    			title={reCallTaskLang.reCallTaskModal("queryResult")} // 查询执行结果
    			visible={searchResultModal }
    			maskClosable={true}
    			width={650}
				onCancel={this.closeModal}
				onOk={this.submit.bind(this)}
    		>
    			<Form {...formItemLayout}>
    				{
    					dim1Name &&
						<div className="mb20">
							<div className="recall-task-result-title">
								{/* 主属性 */}
								{reCallTaskLang.reCallTaskModal("mainAttribute")}
							</div>
							<Form.Item label={dim1Name}>
								<Input
									value={dim1Value || ""}
									onChange={(v)=>{this.changeFieldValue(v, "dim1Value", "input");}}
								/>
							</Form.Item>
						</div>
    				}
    				{
						calcType !== "topn" &&
    					slaveDimsForWeb &&
						slaveDimsForWeb.length > 0 &&
						<div className="mb20">
							<div className="recall-task-result-title">
								{/* 从属性 */}
								{reCallTaskLang.reCallTaskModal("subAttribute")}
							</div>
							{
								slaveDimsForWeb.map(slaveDim=>{
									const slaveDimName = (ruleFieldList.find(v=>{
										return v.name === slaveDim.slaveField;
									}) || {}).dName;
									if (!slaveDimName) {
										return null;
									}
									return (
										<Form.Item label={slaveDimName}>
											<Input
												value={slaveDim.slaveValue || ""}
												onChange={(v)=>{this.changeFieldValue(v, slaveDim.slaveField, "input", "salveDim");}}
											/>
										</Form.Item>
									);
								})
							}
						</div>
    				}
					{
						calcType === "topn" &&
						<div className="mb20">
							<div className="recall-task-result-title">
								{/* 字段排行 */}
								{reCallTaskLang.reCallTaskModal("fieldRanking")}
							</div>
							<Form.Item label={calcFieldName}>
								<Input
									value={calcFieldValue || ""}
									onChange={(v)=>{this.changeFieldValue(v, "calcFieldValue", "input");}}
								/>
							</Form.Item>
						</div>
					}
    			</Form>
    		</Modal>
    	);
	}
}

export default connect(state => ({
	globalStore: state.global,
	reCallTaskStore: state.reCallTask
}))(SearchResultModal);
