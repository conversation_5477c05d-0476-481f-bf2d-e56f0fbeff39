import { PureComponent } from "react";
import { connect } from "dva";
import { isJSON } from "@/utils/isJSON";
import { Input, Select, Row, Col } from "antd";
import { CommonConstants } from "@/constants";

const InputGroup = Input.Group;
const Option = Select.Option;

class FieldList extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { globalStore, indexData } = this.props;
		let { allMap } = globalStore;
		let { fieldSetSelect } = allMap;

		let currentIndexObj = indexData;

		// 如果字段集没有数据，给他初始化一段默认数据
		if (currentIndexObj && currentIndexObj.attachFieldsObj) {
			let matchSets = currentIndexObj.attachFieldsObj.matchSets;
			let emptyObj = {
				fieldSet: null,
				blackListSet: null,
				similarityGrade: null
			};
			if (matchSets && isJSON(matchSets)) {
				let matchSetsList = JSON.parse(matchSets);
				if (matchSetsList.length === 0) {
					matchSetsList = [emptyObj];
				}
				currentIndexObj.attachFieldsObj["matchSets"] = JSON.stringify(matchSetsList);
			} else {
				let matchSetsList = [emptyObj];
				matchSets = JSON.stringify(matchSetsList);
				currentIndexObj.attachFieldsObj["matchSets"] = matchSets;
			}
		}

		let matchSetsList = currentIndexObj && currentIndexObj.attachFieldsObj && currentIndexObj.attachFieldsObj.matchSets ? JSON.parse(currentIndexObj.attachFieldsObj.matchSets) : [];

		return (
			<div className="ml-1">
				{
					matchSetsList &&
                    matchSetsList.map((item, index) => {
                    	let fieldSetType;
                    	if (item.fieldSet) {
                    		let fieldSetSelectObj = fieldSetSelect.find(fItem => fItem.name === item.fieldSet);
                    		if (fieldSetSelectObj) {
                    			fieldSetType = fieldSetSelectObj.type;
                    		}
                    	}

                    	return (
                    		<Row
                    			className="mb10"
                    			gutter={CommonConstants.gutterSpan}
                    			key={index}
                    		>
                    			<Col span={14}>
                    				<InputGroup compact>
                    					<Select
                    						value={item.fieldSet || undefined}
                    						style={{ width: "35%" }}
                    						placeholder="请选择字段集"
                    						disabled={true}
                    					>
                    						{
                    							fieldSetSelect &&
                                                fieldSetSelect.map((fItem, fIndex) => {
                                                	return (
                                                		<Option
                                                			value={fItem.name}
                                                			key={fIndex}
                                                		>
                                                			{fItem.dName}
                                                		</Option>
                                                	);
                                                })
                    						}
                    					</Select>
                    					<Input
                    						style={{ width: "30%" }}
                    						defaultValue="对比黑名单字段集"
                    						disabled={true}
                    					/>
                    					<Select
                    						value={item.blackListSet || undefined}
                    						style={{ width: "35%" }}
                    						placeholder="请选择字段集"
                    						disabled={true}
                    					>
                    						{
                    							fieldSetSelect &&
                                                fieldSetSelect.map((fItem, fIndex) => {
                                                	return (
                                                		<Option
                                                			value={fItem.name}
                                                			key={fIndex}
                                                			disabled={fItem.type !== fieldSetType}
                                                		>
                                                			{fItem.dName}
                                                		</Option>
                                                	);
                                                })
                    						}
                    					</Select>
                    				</InputGroup>
                    			</Col>
                    			{
                    				fieldSetType &&
                                    fieldSetType === "addressSet" &&
                                    <Col span={4}>
                                    	<Input
                                    		addonBefore="相似度"
                                    		addonAfter="%"
                                    		value={item.similarityGrade}
                                    		disabled={true}
                                    	/>
                                    </Col>
                    			}
                    		</Row>
                    	);
                    })
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(FieldList);

