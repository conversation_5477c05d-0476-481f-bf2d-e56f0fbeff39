import { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Drawer, message, Modal, Tooltip } from "antd";
import "./PolicyVersionDrawer.less";
import { policyDetailAPI } from "@/services";
import { policyDetailLang } from "@/constants/lang";
import { isPublicVersionPolicy } from "@/utils/utils";

const confirm = Modal.confirm;

class PolicyVersionDrawer extends PureComponent {
	state = {
		activeId: null
	};

	constructor(props) {
		super(props);
		this.handleClose = this.handleClose.bind(this);
		this.viewPolicyVersion = this.viewPolicyVersion.bind(this);
		this.policyVersionSwitch = this.policyVersionSwitch.bind(this);
	}

	viewPolicyVersion(item) {
		let { dispatch, isEditor } = this.props;

		dispatch({
			type: "policyDetail/getPolicyVersionRules",
			payload: {
				policyUuid: item.policyUuid,
				policyVersion: item.policyVersion,
				status: item.status
			}
		});
		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				policyVersionDrawer: false
			}
		});
		if (isEditor) {
			dispatch({
				type: "policyDetail/setAttrValue",
				payload: {
					ruleActiveKey: [],
					currentRuleUuid: ""
				}
			});
		}
	}

	policyVersionSwitch(item, e) {
		e.stopPropagation();
		let { dispatch, isEditor } = this.props;
		let _this = this;
		let params = {
			policyUuid: item.policyUuid,
			policyVersion: item.policyVersion
		};
		let methodType = "policyVersionSwitch";
		if (isPublicVersionPolicy()) {
			methodType = "publicPolicyVersionSwitch";
		}
		confirm({
			title: policyDetailLang.policyVersion("policySwitchTipTitle"),
			content: policyDetailLang.policyVersion("overwritePopConfirmTitle"),
			onOk() {
				policyDetailAPI[methodType](params).then(res => {
					if (res.success) {
						// lang:策略版本切换成功！
						message.success(policyDetailLang.policyVersion("policySwitchSuccess"));
						// 关闭抽屉
						dispatch({
							type: "policyDetail/setDialogShow",
							payload: {
								policyVersionDrawer: false
							}
						});
						// 当前策略编辑区内容已切换为当前版本，是否跳转编辑区查看。
						if (!isEditor) {
							confirm({
								title: policyDetailLang.policyVersion("policySwitchTipTitle"),		// lang:覆盖编辑区提醒
								content: policyDetailLang.policyVersion("policySwitchTipContent"),
								onOk() {
									let { dispatch, location } = _this.props;
									let pathname = location.pathname;
									let search = location.search;
									let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
									const isPublic = isPublicVersionPolicy();
									let plicyPath = "policyDetail";
									if (isPublic) {
										plicyPath = "publicPolicyDetail";
									}
									dispatch(routerRedux.push(prefix + "/policy/" + plicyPath + "/" + item.policyUuid + search));
								},
								onCancel() {
									console.log("Cancel");
								}
							});
						} else {
							dispatch({
								type: "policyDetail/setAttrValue",
								payload: {
									ruleActiveKey: [],
									currentRuleUuid: ""
								}
							});
							dispatch({
								type: "policyDetail/getPolicyRules",
								payload: {
									policyUuid: item.policyUuid
								}
							});
						}
					} else {
						message.error(res.message);
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel() {
				// console.log('Cancel');
			}
		});
	}

	handleClose() {
		let { dispatch } = this.props;

		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				policyVersionDrawer: false
			}
		});
		dispatch({
			type: "policyDetail/setDialogData",
			payload: {
				policyVersionData: {
					versionList: []
				}
			}
		});
	}

	render() {
		let { policyDetailStore } = this.props;
		let { dialogShow, dialogData, policyVersionInfo } = policyDetailStore;
		let { policyVersionDrawer } = dialogShow;
		let { policyVersionDrawerData } = dialogData;
		let { versionList } = policyVersionDrawerData;
		let { currentVersion } = policyVersionInfo;

		return (
			<Drawer
				title={policyDetailLang.policyVersion("policyHistoryVersion")} // lang:策略历史版本
				width={360}
				className="policy-version-drawer-wrap"
				placement="right"
				closable={true}
				onClose={this.handleClose.bind(this)}
				visible={policyVersionDrawer}
			>
				{
					versionList && versionList.length > 0 &&
					<ul className="policy-version-list">
						{
							versionList.map((item, index) => {
								return (
									<li
										className="policy-version-item"
										onClick={() => {
											if (currentVersion !== item.policyVersion) {
												this.viewPolicyVersion(item);
											} else if (item.status !== "online") {
												this.viewPolicyVersion(item);
											}
										}}
										key={index}
									>
										<div className="policy-version-item-box">
											<div className="line1">
												<Tooltip title={item.policyName || undefined}>
													<h3
														className="text-overflow"
														style={{ width: "160px" }}
													>
														{item.policyName}
													</h3>
												</Tooltip>
												<div className="more-operator">
													<a onClick={this.policyVersionSwitch.bind(this, item)}>
														{/* lang: 覆盖回编辑区 */}
														{policyDetailLang.policyVersion("overwritten")}
													</a>
												</div>
											</div>
											<div className="version-detail-list">
												<div className="version-detail-item">
													<label>
														{/* lang:版本编号 */}
														{policyDetailLang.policyVersion("versionId")}
													</label>
													<span>
														{
															item.policyVersion ? "V" + item.policyVersion : "V1"
														}
													</span>
												</div>
												<div className="version-detail-item">
													<label>
														{/* lang:版本状态 */}
														{policyDetailLang.policyVersion("versionStatus")}
													</label>
													<span>
														{
															// lang:正式版本、历史版本
															item.status && item.status === "online" ? policyDetailLang.policyVersion("officialVersion") : policyDetailLang.policyVersion("historyVersion")
														}
													</span>
												</div>
												<div className="version-detail-item">
													<label>
														{/* lang:发版描述 */}
														{policyDetailLang.policyVersion("description")}
													</label>
													<span>
														{
															item.desc ? item.desc : <em>-</em>
														}
													</span>
												</div>

												<div className="version-detail-item">
													<label>
														{/* lang:发版时间 */}
														{policyDetailLang.policyVersion("date")}
													</label>
													<span>
														{item.gmtCreate}
													</span>
												</div>
											</div>
										</div>
									</li>
								);
							})
						}
					</ul>
				}
			</Drawer>
		);
	}
}

export default connect(state => ({
	templateStore: state.template,
	policyDetailStore: state.policyDetail,
	salaxyStore: state.salaxy
}))(PolicyVersionDrawer);
