import { message } from "antd";
import moment from "moment";
import { policyRunningAPI, policyDetailAPI, publicPolicyRunningAPI } from "@/services";

const addModifyImmuneDataInit = () => ({
	"policyType": "normalPolicy", // 策略类型
	"appName": "", // 应用名
	"policySetUuid": "", // 策略集
	"policyUuid": "", // 策略
	"ruleName": "", // 规则名
	"ruleUuid": "", // 规则
	"conditionJson": { // 免疫条件
		logicOperator: "&&",
		priority: "1",
		defaultNode: false,
		children: []
	},
	"effectFrom": "", // 生效时间
	"effectTo": moment("2099-12-30 23:59:59"), // 失效时间
	"publicPolicyUuid": "",
	"publicRuleName": "",
	"publicRuleUuid": "",
	"effectRange": "", // 生效范围
	"remarks": "" // 备注
});
export default {
	namespace: "immuneConfig",
	state: {
		addModifyImmune: false,
		addModifyImmuneData: addModifyImmuneDataInit(),
		openType: "create", // 打开方式
		createdBy: "", // 操作人
		gmtCreate: "", // 操作时间
		updateType: "", // 更新类型
		// 普通策略规则
		// 策略集
		policySetListLoad: false,
		policySetList: [],
		policySetParams: {
			curPage: 1,
			name: "",
			pageSize: 300,
			tag: ""
		},
		policyList: [], // 策略列表
		ruleList: [], // 规则列表

		// 公共策略规则
		// 策略
		publicPolicyListLoad: false,
		publicPolicyList: [], // 策略列表
		publicPolicyParams: {
			policyName: "",
			curPage: 1,
			pageSize: 300
		},
		publicRuleList: [], // 规则列表
		sceneListSource: [] // 场景
	},
	effects: {
		// 获取普通策略集
		*fetchPolicySet({}, {call, put, select}) {
			const immuneConfigState = yield select(state=>state.immuneConfig);
			const { policySetParams, addModifyImmuneData } = immuneConfigState;
			yield put({
				type: "setAttrValue",
				payload: {
					policySetListLoad: true
				}
			});
			let response = yield call(policyRunningAPI.getPolicySets, {
				...policySetParams,
				appName: addModifyImmuneData.appName
			});
			yield put({
				type: "setAttrValue",
				payload: {
					policySetListLoad: false
				}
			});
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const { data = {} } = response;
			const { dataList = [] } = data || {};
			yield put({
				type: "setAttrValue",
				payload: {
					policySetList: dataList || []
				}
			});
		},
		// 获取策略集下的规则
		*getPolicyRules({ payload }, { call, put, select }) {
			const { openType } = yield select(state=>state.immuneConfig);
			let response = yield call(policyDetailAPI.getPolicyVersionRules, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const { data = {} } = response;
			let { rules = [] } = data || {};
			if (rules && rules.length > 0 && openType === "create") {
				rules = rules.filter((v)=>{
					return v.valid === 1;
				});
			}
			yield put({
				type: "setAttrValue",
				payload: {
					ruleList: rules
				}
			});
		},
		// 获取公共策略
		*fetchPolicy({ }, { call, put, select }) {
			yield put({
				type: "setAttrValue",
				payload: {
					publicPolicyListLoad: true
				}
			});
			const {publicPolicyParams} = yield select(state=>state.immuneConfig);
			let response = yield call(publicPolicyRunningAPI.publicRunningList, publicPolicyParams);
			yield put({
				type: "setAttrValue",
				payload: {
					publicPolicyListLoad: false
				}
			});
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const { data = {} } = response;
			const { dataList = [] } = data || {};
			yield put({
				type: "setAttrValue",
				payload: {
					publicPolicyList: dataList || []
				}
			});
		},
		// 获取公共策略下的规则
		*getPublicPolicyRules({ payload }, { call, put, select }) {
			const { openType } = yield select(state=>state.immuneConfig);
			let response = yield call(policyDetailAPI.getPublicPolicyVersionRules, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const { data = {} } = response;
			let { rules = [] } = data || {};
			if (rules && rules.length > 0 && openType === "create") {
				rules = rules.filter((v)=>{
					return v.valid === 1;
				});
			}
			yield put({
				type: "setAttrValue",
				payload: {
					publicRuleList: rules
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}
			return {
				...state
			};
		},
		setSearchData(state, action) {
			let { payload } = action;
			let { searchData } = state;
			for (let key in payload) {
				if (payload[key] !== undefined) {
					searchData[key] = payload[key];
				}
			}
			return {
				...state,
				searchData
			};
		},
		initAddModifyImmuneData(state) {
			let { addModifyImmuneData } = state;
			addModifyImmuneData = {
				...addModifyImmuneData,
				addModifyImmuneData: addModifyImmuneDataInit()
			};
			return {
				...state,
				...addModifyImmuneData
			};
		},
		setAddModifyImmuneData(state, action) {
			let { payload } = action;
			let { addModifyImmuneData } = state;
			for (let key in payload) {
				if (payload[key] !== undefined) {
					addModifyImmuneData[key] = payload[key];
				}
			}
			return {
				...state,
				addModifyImmuneData
			};
		}
	}
};
