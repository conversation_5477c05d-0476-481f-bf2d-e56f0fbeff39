import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Row, Col, Checkbox, Popover } from "antd";
import { isJSON } from "@/utils/isJSON";
import { CommonConstants, PolicyConstants } from "@/constants";
import { commonLang, policyDetailLang } from "@/constants/lang";
import { getRuleCfgList, getHandleType } from "@/utils/salaxy";
import CustomRuleConfig from "@/components/CustomRuleConfig";
// import FormulaScriptEditor from "@/components/PolicyDetail/FormulaScriptEditor";

const { excludeRuleTemplate } = PolicyConstants;
const Option = Select.Option;
const InputGroup = Input.Group;

class RuleConditonTemplate extends PureComponent {
	constructor(props) {
		super(props);
		this.changeTemplateDescription = this.changeTemplateDescription.bind(this);
		this.modifyTemplateForm = this.modifyTemplateForm.bind(this);
		this.deleteConditionTemplate = this.deleteConditionTemplate.bind(this);
		this.expandTemplate = this.expandTemplate.bind(this);
		this.modifyRightVariable = this.modifyRightVariable.bind(this);
		this.initRuleConfig();
	}

	initRuleConfig = () => {
		let { policyDetailStore, templateStore, ruleIndexArr, conditionIndex, property, dispatch } = this.props;
		let { policyRules } = policyDetailStore;
		let { ruleTemplateListObj } = templateStore;

		let currentRule = null;
		if (ruleIndexArr && ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
		} else if (ruleIndexArr && ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
		}

		let currentCondition = currentRule && currentRule["conditions"] ? currentRule["conditions"] : null;
		let templateName = property;
		let currentTemplate = ruleTemplateListObj && templateName ? ruleTemplateListObj[templateName] : null;
		let cfgJson = currentTemplate && currentTemplate.cfgJson && isJSON(currentTemplate.cfgJson) ? JSON.parse(currentTemplate.cfgJson) : null;
		let params = currentCondition && currentCondition.children && currentCondition.children[conditionIndex] ? currentCondition.children[conditionIndex]["params"] : [];
		// params = unionBy(params, "name");
		// 这里处理规则
		let simpleCfgList = getRuleCfgList(cfgJson, params);
		// 如何规则模板存在默认值，将默认值回传到params
		// 这里需要排除name等于value和operator的选项，因为他们不是存在params里面的
		// subItem.name === "value" || subItem.name === "operator"
		simpleCfgList.forEach((scItem, scIndex) => {
			if (scItem.name !== "value" && scItem.name !== "operator" && scItem.value) {
				if (!params.find(pItem => pItem.name === scItem)) {
					params.push({
						name: scItem.name,
						type: scItem.type,
						value: scItem.value
					});
				}
			}
		});

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	changeTemplateDescription(e) {
		let { policyDetailStore, dispatch, ruleIndexArr, conditionIndex } = this.props;
		let { policyRules } = policyDetailStore;
		let value = e.target.value;

		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["conditions"].children[conditionIndex]["describe"] = value;
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"].children[conditionIndex]["describe"] = value;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}
	modifyTemplate = (field, e) => {
		let { policyDetailStore, dispatch, ruleIndexArr } = this.props;
		let { policyRules } = policyDetailStore;
		let value = e;
		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]][field] = value;
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]][field] = value;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}
		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}
	modifyTemplateForm(field, componentType, dataType, typeFinal, e) {
		let { policyDetailStore, globalStore, dispatch, ruleIndexArr, conditionIndex } = this.props;
		let { policyRules } = policyDetailStore;
		let { allMap } = globalStore;
		let value = "";
		if (componentType === "input") {
			value = e.target.value;
		} else if (componentType === "select") {
			value = e;
		} else if (componentType === "checkbox") {
			value = e ? e.toString() : "";
		}
		let params = null;
		let currentChild = null;
		if (ruleIndexArr.length === 1) {
			currentChild = policyRules[ruleIndexArr[0]]["conditions"].children[conditionIndex];
			params = currentChild["params"];
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			currentChild = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"].children[conditionIndex];
			params = currentChild["params"];
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		// 去重
		// params = uniqBy(params, "name");

		dataType = typeFinal ? typeFinal : dataType;
		if (field === "operator" || field === "value" || field === "script") {
			currentChild[field] = value;
		} else {
			let filterObj = params.find(item => item.name === field);
			if (filterObj) {
				// 如果是模糊匹配规则
				// 匹配字段和生日字段百分比相加等于100
				if (field === "nameWeight" || field === "birthWeight") {
					value = parseInt(value, 10);
					if (!value || value === 0) {
						value = 0;
					}
					if (value > 99) {
						value = 99;
					}
					if (value < 0) {
						value = 1;
					}
					let otherField = field === "nameWeight" ? "birthWeight" : "nameWeight";
					filterObj["value"] = value.toString();
					filterObj["type"] = dataType ? dataType : "string";

					let otherFieldObj = params.find(item => item.name === otherField);
					if (otherFieldObj) {
						let finalNum = 100 - (value || 0);
						otherFieldObj["value"] = finalNum.toString();
						otherFieldObj["type"] = "int";
					}
				} else {
					// 这里判断value类型，现在存在了array类型了
					if (dataType === "array") {
						filterObj["value"] = value;
						filterObj["type"] = "string";
					} else if (dataType === "int") {
						filterObj["value"] = value;
						filterObj["type"] = "int";
					} else {
						filterObj["value"] = value.toString();
						filterObj["type"] = dataType ? dataType : "string";
					}
				}
			} else {
				let tempObj = {
					name: field,
					type: dataType ? dataType : "string",
					value: value
				};
				params.push(
					tempObj
				);
			}
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}
	modifyRightVariable(field, type, e) {
		let { policyDetailStore, globalStore, dispatch, ruleIndexArr, conditionIndex } = this.props;
		let { policyRules } = policyDetailStore;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		let params = null;
		let currentChild = null;
		if (ruleIndexArr.length === 1) {
			currentChild = policyRules[ruleIndexArr[0]]["conditions"].children[conditionIndex];
			params = currentChild["params"];
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			currentChild = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"].children[conditionIndex];
			params = currentChild["params"];
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		let filterObj = params.find(item => item.name === "rightVariable");
		if (filterObj) {
			filterObj[field] = value;
			if (field === "type") {
				filterObj["value"] = null;
			}
		} else {
			let tempObj = {
				"name": "rightVariable",
				"type": field === "type" ? value : "string",
				"value": field === "type" ? null : value
			};
			params.push(
				tempObj
			);
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	deleteConditionTemplate() {
		let { policyDetailStore, dispatch, ruleIndexArr, conditionIndex } = this.props;
		let { policyRules } = policyDetailStore;
		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["conditions"]["children"].splice(conditionIndex, 1);
			policyRules[ruleIndexArr[0]]["hasModify"] = true;

		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"].splice(conditionIndex, 1);
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	expandTemplate() {
		let { policyDetailStore, dispatch, ruleIndexArr, conditionIndex } = this.props;
		let { expandTemplate, policyRules } = policyDetailStore;

		let currentRule = null;
		if (ruleIndexArr && ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
		} else if (ruleIndexArr && ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
		}

		if (expandTemplate[currentRule.uuid]) {
			expandTemplate[currentRule.uuid];
			if (expandTemplate[currentRule.uuid].indexOf(conditionIndex) > -1) {
				let index = expandTemplate[currentRule.uuid].indexOf(conditionIndex);
				expandTemplate[currentRule.uuid].splice(index, 1);
			} else {
				expandTemplate[currentRule.uuid].push(conditionIndex);
			}
		} else {
			expandTemplate[currentRule.uuid] = [conditionIndex];
		}

		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				expandTemplate: expandTemplate
			}
		});
	}

	render() {
		let { globalStore, policyDetailStore, templateStore, ruleIndexArr, conditionIndex, property } = this.props;
		let { policyDetail, expandTemplate, policyRules } = policyDetailStore;
		let { ruleTemplateListObj } = templateStore;
		let { allMap, personalMode } = globalStore;
		let { lang } = personalMode;

		let currentRule = null;
		if (ruleIndexArr && ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
		} else if (ruleIndexArr && ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
		}

		let currentCondition = currentRule && currentRule["conditions"] ? currentRule["conditions"] : null;
		let templateName = property;
		let currentTemplate = ruleTemplateListObj && templateName ? ruleTemplateListObj[templateName] : null;
		let cfgJson = currentTemplate && currentTemplate.cfgJson && isJSON(currentTemplate.cfgJson) ? JSON.parse(currentTemplate.cfgJson) : null;
		let currentParamInfo = currentCondition && currentCondition.children && currentCondition.children[conditionIndex] ? currentCondition.children[conditionIndex] : {};
		let params = currentParamInfo ? currentParamInfo["params"] : [];
		// console.log("======================>>>>>>>>>>>>");
		let expandIndexList = expandTemplate[currentRule.uuid] ? expandTemplate[currentRule.uuid] : [];

		// 这里处理规则
		let simpleCfgList = getRuleCfgList(cfgJson, params);
		// 如果规则模板存在默认值，将默认值回传到params
		// simpleCfgList.forEach((scItem, scIndex) => {
		// 	if (scItem.value) {
		// 		if (!params.find(pItem => pItem.name === scItem)) {
		// 			params.push({
		// 				name: scItem.name,
		// 				type: scItem.type,
		// 				value: scItem.value
		// 			});
		// 		}
		// 	}
		// });

		console.log(cfgJson);
		// console.log(simpleCfgList);
		// console.log(currentParamInfo);
		console.log(policyRules);
		return (
			<div className="custom-condition-item template-condition">
				{
					currentTemplate && currentTemplate.description &&
					<Row gutter={CommonConstants.gutterSpan} className="template-condition-row">
						<Col span={4} className="basic-info-title">
							{/* 规则描述 */}
							{policyDetailLang.ruleConditionTemplate("ruleDescription")}：
                    	</Col>
						<Col span={6}>
							<p className="basic-info-description">
								{lang === "en" ? currentTemplate.enDescription : currentTemplate.description}
							</p>
						</Col>
						<Col span={6}></Col>
						{
							currentRule && currentRule["template"] === "common/custom" &&
							<Col span={3} className="basic-info-oper">
								{/* 收起/展开条件 */}
								<Tooltip title={policyDetailLang.ruleConditionTemplate("upDown")} placement="top">
									<Icon
										className={expandIndexList.indexOf(conditionIndex) > -1 ? "show-more" : "show-more hide"}
										type="double-right"
										onClick={this.expandTemplate.bind(this)}
									/>
								</Tooltip>
								{/* 删除条件 */}
								<Tooltip title={policyDetailLang.ruleConditionTemplate("deleteCodition")}
									placement="top">
									<Icon
										className="delete"
										type="delete"
										onClick={this.deleteConditionTemplate.bind(this)}
									/>
								</Tooltip>
							</Col>
						}
					</Row>
				}
				{
					// 如果是逻辑是添加规则排除，有些规则模板无法配置出来，这就需要
					currentParamInfo &&
					currentParamInfo.property &&
					excludeRuleTemplate.indexOf(currentParamInfo.property) > -1 &&
					<CustomRuleConfig
						currentParamInfo={currentParamInfo}
						simpleCfgList={simpleCfgList}
						onChange={(field, value, type) => {
							this.modifyTemplateForm(field, "select", type, type, value);
						}}
						disabled={false}
					/>
				}
				{
					// 判断当前指标模板不在排除列表
					currentParamInfo &&
					currentParamInfo.property &&
					excludeRuleTemplate.indexOf(currentParamInfo.property) === -1 &&
					currentRule &&
					(currentRule["template"] !== "common/custom" ? true : expandIndexList.indexOf(conditionIndex) > -1) &&
					cfgJson.params &&
					cfgJson.params.map((item, index) => {
						let tipTitle = lang === "cn" ? item["tipTitle"] : item["enTipTitle"];
						let tipContent = lang === "cn" ? item["tipContent"] : item["enTipContent"];
						let tipContentArr = [];
						if (tipContent) {
							tipContentArr = tipContent.split(/\n/);
						}

						let RowDom = (
							<Row gutter={CommonConstants.gutterSpan} className="template-condition-row" key={index}>
								<Col span={4} className="basic-info-title">
									{lang === "en" ? item.enLabelText : item.labelText}：
                    			</Col>
								{
									item.children &&
									item.children.map((subItem, subIndex) => {
										let param = params.filter(item => item.name === subItem.name)[0] ? params.filter(item => item.name === subItem.name)[0] : undefined;
										let type = param ? param["type"] : undefined;
										let value = param ? param["value"] : undefined;

										let serviceMapName = null;
										if (subItem.componentType === "select" && subItem.selectName) {
											if (subItem.mapType === "static") {
												serviceMapName = subItem.selectName;
											} else if (subItem.mapType === "dynamic") {
												if (subItem.selectName === "${policySetUuid}_policys") {
													serviceMapName = policyDetail.fkPolicySetUuid + "_policys";
												}
											}
										}
										let isRuleFieldList = subItem.selectName === "ruleFieldList";
										let filterType = subItem.filterType && subItem.filterType.length ? subItem.filterType : null;

										let placeholder;
										if (subItem.componentType === "input") {
											if (lang === "en") {
												placeholder = subItem.enPlaceholder;
											} else {
												placeholder = subItem.placeholder;
											}
										} else if (subItem.componentType === "select") {
											if (lang === "en") {
												placeholder = subItem.enPlaceholder ? subItem.enPlaceholder : policyDetailLang.ruleConditionTemplate("select");
											} else {
												placeholder = subItem.placeholder ? subItem.placeholder : "请选择";
											}
										}

                                    	/*
										* 从这里处理changeRuleForOther规则
										* handle：指的是改变当前参数的那个值
										* */
										let currentSimpleObj = simpleCfgList.find(fItem => fItem.name === subItem.name);
										// 得到willChangeSelf
										let willChangeSelf = currentSimpleObj && currentSimpleObj.willChangeSelf ? currentSimpleObj.willChangeSelf : null;

										// 获取handle 名称
										let changeHandleName = willChangeSelf && willChangeSelf.name ? willChangeSelf.name : null;
										// 获取handle 实体
										let changeHandleObj = changeHandleName ? simpleCfgList.find(fItem => fItem.name === changeHandleName) : null;
										// 获取handle value
										let changeHandleValue = changeHandleObj ? changeHandleObj.value : null;
                                    	/*
										* 预先设置如下几个变量
										* ruleDisabled
										* ruleHidden
										* ruleSelectMap
										* ruleMapLocation
										* */
										let ruleDisabled = false;
										let ruleHidden = false;
										let ruleMapName = null;
										let ruleMapLocation = null;

										if (willChangeSelf) {
											// console.log(changeHandleValue);
											if (willChangeSelf.changeMode && willChangeSelf.changeMode === "whenSomeValue") {
												// 当为具体值的时候
												willChangeSelf["caseList"] && willChangeSelf["caseList"].map((caseItem, caseIndex) => {
													if (caseItem["modeValueList"] && caseItem["modeValueList"].find(mvItem => mvItem === changeHandleValue)) {
														// 如果modeValueList列表中确实有handle value，则进行如下操作
														if (caseItem.changeType && caseItem.changeType === "disabled") {
															ruleDisabled = true;
														} else if (caseItem.changeType && caseItem.changeType === "hidden") {
															ruleHidden = true;
														} else if (caseItem.changeType && caseItem.changeType === "selectMap") {
															// 如果是改变select map
															ruleMapName = caseItem.mapName;
															ruleMapLocation = caseItem.mapLocation;
														}
													}
												});
											} else if (willChangeSelf.changeMode && willChangeSelf.changeMode === "whenSomeType") {
												// 当为具体类型的时候
												let type = getHandleType(changeHandleObj, allMap);
												willChangeSelf["caseList"] &&
													willChangeSelf["caseList"].map((caseItem, caseIndex) => {
														if (caseItem["modeValueList"] && caseItem["modeValueList"].find(mvItem => mvItem.toLowerCase() === type.toLowerCase())) {
															// 如果modeValueList列表中确实有handle value，则进行如下操作
															if (caseItem.changeType && caseItem.changeType === "disabled") {
																ruleDisabled = true;
															} else if (caseItem.changeType && caseItem.changeType === "hidden") {
																ruleHidden = true;
															} else if (caseItem.changeType && caseItem.changeType === "selectMap") {
																// 如果是改变select map
																ruleMapName = caseItem.mapName;
																ruleMapLocation = caseItem.mapLocation;
															}
														}
													});
											}
										}

										// if (willChangeSelf) {
										// 	console.log("=========================willChangeSelf");
										// 	console.log(willChangeSelf);
										// 	console.log("ruleDisabled：" + ruleDisabled);
										// 	console.log("ruleHidden：" + ruleHidden);
										// 	console.log("ruleMapName：" + ruleMapName);
										// 	console.log("ruleMapLocation：" + ruleMapLocation);
										// }

										if (ruleHidden) {
											return;
										}

										// console.log(subItem.name);
										let span = 8;
										if (subItem.componentType === "script") {
											span = 16;
										}
										return (
											<Col span={span || 8} key={subIndex}>
												{
													subItem.componentType === "input" &&
													<Input
														value={(subItem.name === "value" || subItem.name === "operator") ? currentCondition["children"][conditionIndex][subItem.name] : value}
														onChange={this.modifyTemplateForm.bind(this, subItem.name, "input", subItem.type, subItem.typeFinal)}
														placeholder={placeholder}
														addonAfter={subItem.addonAfter}
														addonBefore={subItem.addonBefore}
													/>
												}
												{/* {
													subItem.componentType === "script" &&
													<FormulaScriptEditor
														value={value}
														onChange={this.modifyTemplateForm.bind(this, "script", "select", subItem.type, subItem.typeFinal)}
													/>
												} */}
												{
													subItem.componentType === "select" &&
													<Select
														value={(subItem.name === "value" || subItem.name === "operator") ? currentCondition["children"][conditionIndex][subItem.name] : value}
														placeholder={placeholder} // 请选择
														onChange={this.modifyTemplateForm.bind(this, subItem.name, "select", subItem.type, subItem.typeFinal)}
														showSearch
														optionFilterProp="children"
														dropdownMatchSelectWidth={false}
													>
														{
															subItem.selectType === "self" &&
															subItem.selectOption &&
															subItem.selectOption.map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.value}
																		key={optionIndex}
																		title={optionItem.name}
																	>
																		{lang === "en" ? optionItem.enName : optionItem.name}
																	</Option>
																);
															})
														}
														{
															subItem.selectType === "service" &&
															subItem.selectName &&
															allMap[serviceMapName] &&
															allMap[serviceMapName].filter(item => {
																return item.type && filterType ? filterType.indexOf(item.type.toLowerCase()) > -1 : true;
															}).filter(fItem => {
																let appName = policyDetail && policyDetail.appName ? policyDetail.appName : null;
																let filterByFieldType = subItem["filterByField"] && subItem["filterByFieldName"] && subItem["filterByFieldType"] ? subItem["filterByFieldType"] : null;
																let result = true;
																if (filterByFieldType) {
																	if (filterByFieldType === "policyApp") {
																		result = fItem[subItem["filterByFieldName"]] === appName;
																	} else if (filterByFieldType === "policyAppAndCustom") {
																		result = fItem[subItem["filterByFieldName"]] === appName || subItem["filterByFieldValue"] === fItem[subItem["filterByFieldName"]];
																	} else {
																		result = subItem["filterByFieldValue"] === fItem[subItem["filterByFieldName"]];
																	}
																}
																return result;
															}).map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.name}
																		key={optionIndex}
																		title={optionItem.dName}
																	>

																		{
																			serviceMapName === "ruleAndIndexFieldList"
																				? `[${commonLang.sourceName(optionItem.sourceName)}] ` + optionItem.dName
																				: optionItem.dName
																		}
																	</Option>
																);
															})
														}
													</Select>
												}
												{
													subItem.componentType === "checkbox" &&
													<Checkbox.Group
														value={value ? value.toString().split(",") : undefined}
														onChange={this.modifyTemplateForm.bind(this, subItem.name, "checkbox", subItem.type, subItem.typeFinal)}
													>
														{
															subItem.selectType === "self" && subItem.selectOption &&
															subItem.selectOption.map((optionItem, optionIndex) => {
																return (
																	<Checkbox
																		value={optionItem.value}
																		key={optionIndex}
																	>
																		{lang === "en" ? optionItem.enName : optionItem.name}
																	</Checkbox>
																);
															})
														}
														{
															subItem.selectType === "service" && subItem.selectName &&
															allMap[subItem.selectName] && allMap[subItem.selectName].map((optionItem, optionIndex) => {
																return (
																	<Checkbox
																		value={optionItem.name}
																		key={optionIndex}
																	>
																		{lang === "en" ? optionItem.enName : optionItem.name}
																	</Checkbox>
																);
															})
														}
													</Checkbox.Group>
												}
												{
													subItem.componentType === "variable" &&
													<InputGroup compact>
														<Select
															value={type}
															style={{ width: "30%" }}
															onChange={this.modifyRightVariable.bind(this, "type", "select")}
															dropdownMatchSelectWidth={false}
														>
															{
																(subItem.type === "all" || subItem.type === "input") &&
																<Option value="input">
																	{/* 常量 */}
																	{policyDetailLang.ruleConditionTemplate("constant")}
																</Option>
															}
															{
																(subItem.type === "all" || subItem.type === "context") &&
																<Option value="context">
																	{/* 变量 */}
																	{policyDetailLang.ruleConditionTemplate("variable")}
																</Option>
															}
														</Select>
														{
															type && type === "input" &&
															<Input
																style={{ width: "70%" }}
																value={value}
																onChange={this.modifyRightVariable.bind(this, "value", "input")}
																placeholder={policyDetailLang.ruleConditionTemplate("enter")} // 请输入
															/>
														}
														{
															type && type === "context" &&
															<Select
																style={{ width: "70%" }}
																value={value || undefined}
																placeholder={policyDetailLang.ruleConditionTemplate("select")} // 请选择
																onChange={this.modifyRightVariable.bind(this, "value", "select")}
																showSearch
																optionFilterProp="children"
																dropdownMatchSelectWidth={false}
															>
																{
																	allMap &&
																	allMap["ruleFieldList"] &&
																	!subItem["includeIndex"] &&
																	allMap["ruleFieldList"].filter(item => item.type && filterType ? filterType.indexOf(item.type.toLowerCase()) > -1 : true).map((item, index) => {
																		return (
																			<Option
																				value={item.name}
																				key={index}
																				title={item.dName}
																			>
																				{
																					item.dName
																				}
																			</Option>
																		);
																	})
																}
																{
																	allMap &&
																	allMap["ruleAndIndexFieldList"] &&
																	subItem["includeIndex"] &&
																	allMap["ruleAndIndexFieldList"].filter(item => item.type && filterType ? filterType.indexOf(item.type.toLowerCase()) > -1 : true).map((item, index) => {
																		return (
																			<Option value={item.name}
																				key={index}
																				title={item.dName}
																			>
																				[{commonLang.sourceName(item.sourceName)}]&nbsp;
                                			                                    {item.dName}
																			</Option>
																		);
																	})
																}
															</Select>
														}
													</InputGroup>
												}
											</Col>
										);
									})
								}
								{
									tipTitle &&
									tipContent &&
									<Col span={1}>
										<Popover
											popupClassName="rule-param-pop-tip"
											placement="right"
											title={tipTitle}
											content={
												tipContentArr.map((tip, tipIndex) => {
													return (
														<div key={tipIndex}>{tip}</div>
													);
												})
											}
										>
											<Icon
												type="question-circle-o"
												className="param-tip-icon"
											/>
										</Popover>
									</Col>
								}
							</Row>
						);
						// 在这里处理行规则willChangeLine
						let showThisLine = false;
						let willChangeLine = JSON.stringify(item["willChangeLine"]) !== "{}" ? item["willChangeLine"] : undefined;

						if (willChangeLine) {
							let currentSimpleObj = simpleCfgList.find(fItem => fItem.name === willChangeLine.name);
							// 是否隐藏当前行
							showThisLine = currentSimpleObj && currentSimpleObj.value === willChangeLine.changeValue;
						}

						// 如果没有行控制规则
						if (!willChangeLine) {
							return RowDom;
						}

						// 如果有行控制规则，那么判断showThisLine是否为true
						if (willChangeLine && showThisLine) {
							return RowDom;
						}
					})
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	templateStore: state.template,
	policyDetailStore: state.policyDetail
}))(RuleConditonTemplate);
