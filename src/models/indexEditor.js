import { message } from "antd";
import { indexEditorAPI } from "@/services";
import { isJSON } from "@/utils/isJSON";
import key from "keymaster";

export default {
	namespace: "indexEditor",
	state: {
		listLoad: true,
		indexList: [],
		indexListReady: false,
		curPage: 1,
		pageSize: 20,
		total: 0,
		pages: 0,
		searchParams: {
			sceneType: null,
			event: null,
			policyUuid: null,
			ruleUuid: null,
			calcType: null,
			name: null
		},
		indexActiveKey: [],
		currentIndexId: null,
		indexActiveKeyLoading: false,
		currentLoadingId: null,
		indexActiveIndex: null,
		editIndexMap: {},
		dialogShow: {
			addSalaxy: false,
			modifySalaxy: false,
			templateDrawer: false,
			indexCiteDrawer: false,
			indexImport: false,
			indexVersionSubmit: false,
			batchVersionSubmit: false,
			batchRes: false,
			authList: false,
			runningAuthList: false, // 运行区的授权弹窗先记录在编辑区，因为只有一个字段的控制
			reCall: false
		},
		dialogData: {
			addSalaxyData: {
				partner: null
			},
			modifySalaxyData: {
				id: null,
				detail: null
			},
			indexCiteDrawerData: {
				indexId: null,
				item: null,
				dataList: {}
			},
			indexImportData: {
				importMode: "SKIP",
				file: null,
				replaceScene: "",
				zbMode: ""
			},
			indexVersionSubmitData: {
				id: null,
				description: null
			},
			batchVersionSubmitData: {
				zbs: null,
				description: null
			},
			batchResData: [],
			authListData: {
				zbUuid: "",
				selectedKeys: [],
				targetKeys: [],
				authorizedLoad: false,
				isRunning: false
			},
			reCallData: {
				operaType: "",
				timeType: "",
				zbUuid: "",
				zbName: "",
				zbCalcType: "",
				executeType: "RIGHTNOW",
				deadline: "",
				taskStartAt: ""
			}
		}
	},
	effects: {
		*getSalaxyList({ payload }, { call, put }) {
			yield put({
				type: "setAttrValue",
				payload: {
					listLoad: true
				}
			});
			let response = yield call(indexEditorAPI.getSalaxyList, payload);
			yield put({
				type: "setAttrValue",
				payload: {
					listLoad: false
				}
			});
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initSalaxyList",
				payload: response
			});
		},
		*changeIndexStatus({ payload }, { call, put, select }) {
			let { id } = payload;
			let indexList = yield select(state => state.indexEditor.indexList);
			let editIndexMap = yield select(state => state.indexEditor.editIndexMap);
			let response = yield call(indexEditorAPI.viewIndexItemDetail, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let obj = indexList.find(item => item.id === parseInt(id, 10));
			const { data } = response || {};
			let publishStatus = (data && data.publishStatus) || null;
			let hasCommited = (data && data.hasCommited) || null;
			const {fillBackResultStatus, fillBackProgress, fillBackStatus} = data || {};

			if (obj && publishStatus) {
				obj["publishStatus"] = publishStatus;
			}
			if (obj && hasCommited) {
				obj["hasCommited"] = hasCommited;
			}
			if (obj) {
				obj["fillBackResultStatus"] = fillBackResultStatus;
				obj["fillBackProgress"] = fillBackProgress;
				obj["fillBackStatus"] = fillBackStatus;
			}
			if (editIndexMap[id]) {
				editIndexMap[id]["publishStatus"] = publishStatus;
				editIndexMap[id]["hasCommited"] = hasCommited;

				editIndexMap[id]["fillBackResultStatus"] = fillBackResultStatus;
				editIndexMap[id]["fillBackProgress"] = fillBackProgress;
				editIndexMap[id]["fillBackStatus"] = fillBackStatus;
			}
			yield put({
				type: "setAttrValue",
				payload: {
					indexList: indexList,
					editIndexMap: editIndexMap
				}
			});
		},
		// switch当前指标版本
		*switchVersionDetail({ payload }, { call, put, select }) {
			let { id } = payload;
			const {indexList = [] } = yield select(state => state.indexEditor);
			let response = yield call(indexEditorAPI.viewIndexItemDetail, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const { data } = response || {};
			const newIndexList = [...indexList].map(item => {
				if (item.id === parseInt(id, 10)) {
					item = data;
				}
				return item;
			});

			yield put({
				type: "setAttrValue",
				payload: {
					indexList: newIndexList
				}
			});
		},
		*fetchLicenseApps({ payload }, { call, put }) {
			yield put({
				type: "setDialogData",
				payload: {
					authListData: {
						...payload,
						zbUuid: "",
						targetKeys: [],
						selectedKeys: [],
						authorizedLoad: true
					}
				}
			});
			let response = yield call(indexEditorAPI.licenseApps, payload) || {};
			const data = (response && response.data) || [];
			yield put({
				type: "setDialogData",
				payload: {
					authListData: {
						...payload,
						targetKeys: data || [],
						selectedKeys: payload.isRunning ? data : [],
						authorizedLoad: false
					}
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		initSalaxyList(state, action) {
			let { indexList, curPage, pageSize, total, pages, indexListReady } = state;
			let data = action.payload.data || null;
			let list = data["dataList"] || [];
			curPage = data.curPage;
			pageSize = data.pageSize;
			total = data.total;
			pages = data.pages;
			indexList = list || [];
			indexList && indexList.map((item, index) => {
				indexList[index]["filterList"] = item["filterStr"] && isJSON(item["filterStr"]) ? JSON.parse(item["filterStr"]) : [];
				indexList[index]["attachFieldsObj"] = item["attachFields"] && isJSON(item["attachFields"]) ? JSON.parse(item["attachFields"]) : {};
				indexList[index]["sceneList"] = item["scene"] && isJSON(item["scene"]) ? JSON.parse(item["scene"]) : [];
			});
			indexListReady = true;

			return {
				...state,
				indexList: indexList,
				curPage: curPage,
				pageSize: pageSize,
				total: total,
				pages: pages,
				indexListReady: indexListReady
			};
		},
		switchTemplateDrawer(state, action) {
			let { dialogShow } = state;
			let { templateDrawer } = dialogShow;

			dialogShow.templateDrawer = true;

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		initSalaxyExpandDetail(state) {
			return {
				...state,
				indexActiveKey: [],
				currentIndexId: null,
				indexActiveKeyLoading: false,
				currentLoadingId: null,
				indexActiveIndex: null,
				editIndexMap: {}
			};
		}
	},
	subscriptions: {
		// 监听地址，如果地址含有app则跳转到登陆页
		setup({ dispatch, history }) {
			history.listen(location => {
				if (location.pathname.includes("/policy/salaxy")) {
					key("c", () => {
						dispatch({
							type: "switchTemplateDrawer"
						});
					});
				}
			});
		}
	}
};
