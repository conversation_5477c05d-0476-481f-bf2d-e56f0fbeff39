import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Button } from "antd";
import RuleAttr from "./Inner/RuleAttr";
import RuleCondition from "./Inner/RuleConditon";
import RuleOperation from "./Inner/RuleOperation";
import { policyDetailLang } from "@/constants/lang";

class RuleConfig extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { currentRule, ruleIndexArr, mode } = this.props;
		return (
			<div className="rule-config-wrap">
				<Form>
					<div className="rule-detail rule-attr">
						<h4>
							{/* 基本设置 */}
							{policyDetailLang.ruleConfig("basicSetup")}
						</h4>
						<div className="rule-detail-content">
							<RuleAttr mode={mode} currentRule={currentRule} ruleIndexArr={ruleIndexArr} />
						</div>
					</div>

					<div className="rule-detail rule-condition" style={{ paddingBottom: "0" }}>
						<h4>
							{/* 条件配置 */}
							{policyDetailLang.ruleConfig("conditionsConfiguration")}
						</h4>
						<div className="rule-detail-content">
							<RuleCondition currentRule={currentRule} ruleIndexArr={ruleIndexArr} />
						</div>
					</div>
					{
						currentRule.operationActions &&
                        currentRule.operationActions.length > 0 &&
                        <div className="rule-detail rule-operation">
                        	<h4>
                        		{/* 操作配置 */}
                        		{policyDetailLang.ruleConfig("operatingConfiguration")}
                        	</h4>
                        	<div className="rule-detail-content">
                        		<RuleOperation currentRule={currentRule} ruleIndexArr={ruleIndexArr} />
                        	</div>
                        </div>
					}
					<div className="rule-btns">
						<Button
							type="default"
							onClick={() => {
								let { changeActiveKey } = this.props;
								changeActiveKey([]);
							}}
						>
							{/* 收起 */}
							{policyDetailLang.ruleConfig("packUp")}
						</Button>
					</div>
				</Form>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail,
	templateStore: state.template
}))(RuleConfig);
