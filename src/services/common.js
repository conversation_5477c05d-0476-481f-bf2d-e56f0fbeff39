import queryString from "query-string";
import Store from "store";
import Config from "../common/config";

const getUrl = (url, query) => {
	return url + "?" + queryString.stringify(query);
};
const getHeader = () => {
	let headers = {};
	headers["X-User-Token"] = Store.get(Config.X_USER_TOKEN);
	headers["X-Cf-Random"] = sessionStorage.getItem("_csrf_");
	return headers;
};
const getApiDetailHeader = () => {
	let headers = {};
	headers["X-User-Token"] = Store.get(Config.X_USER_TOKEN);
	headers["X-Cf-Random"] = sessionStorage.getItem("_csrf_");
	headers["DETAILRESPONSE"] = 1;
	return headers;
};
const deleteEmptyObjItem = (obj) => {
	for (let i in obj) {
		let value = obj[i];
		if (value === null || value === undefined || value === "" || !value && value !== 0) {
			delete obj[i];
		}
	}
	return obj;
};
export {
	getUrl,
	getHeader,
	getApiDetailHeader,
	deleteEmptyObjItem
};
