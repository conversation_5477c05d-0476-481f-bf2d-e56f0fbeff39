import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { approvalTaskAPI, policyEditorAPI } from "@/services";
import { isJSON } from "@/utils/isJSON";
import { Button, Input, Table, Pagination, message, DatePicker } from "antd";
import moment from "moment";
import WorkflowApprovalModal from "../TaskModal/WorkflowApprovalModal";
import { approvalTaskLang, commonLang } from "@/constants/lang";
import { approvalTaskConstants } from "@/constants";
import { checkFunctionHasPermission } from "@/utils/permission";

const { RangePicker } = DatePicker;
const { operationTypeMap } = approvalTaskConstants;
class WorkflowTask extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.startSearch = this.startSearch.bind(this);
    	this.changePagination = this.changePagination.bind(this);
    	this.changeSearchParamField = this.changeSearchParamField.bind(this);
    	this.workflowApprovalHandle = this.workflowApprovalHandle.bind(this);
    }

    componentDidMount() {
    	// 页面初始化数据
    	this.timer = setInterval(() => {
    		const { globalStore } = this.props;
    		const { menuTreeReady } = globalStore;
    		if (menuTreeReady) {
    			clearInterval(this.timer);
    			if (checkFunctionHasPermission("ZB0401", "workflowApprovalSearch")) {
    				this.startSearch();
    			}
    		}
    	}, 100);
    }

    startSearch() {
    	let { approvalTaskStore, globalStore, dispatch } = this.props;
    	let { workflowPage } = approvalTaskStore;
    	let { curPage, pageSize, searchParams } = workflowPage;
    	let { currentApp } = globalStore;
    	if (currentApp.name !== "all") {
    		searchParams["appName"] = currentApp.name;
    	} else {
    		searchParams["appName"] = "";
    	}

    	dispatch({
    		type: "approvalTask/getWorkflowApprovalTaskList",
    		payload: {
    			curPage: curPage,
    			pageSize: pageSize,
    			...searchParams
    		}
    	});
    }

    componentWillReceiveProps(nextProps) {
    	let { approvalTaskStore, dispatch, globalStore } = this.props;
    	let { menuTreeReady } = globalStore;
    	let { workflowPage } = approvalTaskStore;
    	let { curPage, pageSize, searchParams } = workflowPage;
    	let preCurrentApp = this.props.globalStore.currentApp;
    	let nextCurrentApp = nextProps.globalStore.currentApp;
    	if (!menuTreeReady || !true) {
    		// 这里放权限
    		return;
    	}
    	if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
    		if (nextCurrentApp.name !== "all") {
    			searchParams["appName"] = nextCurrentApp.name;
    		} else {
    			searchParams["appName"] = "";
    		}

    		dispatch({
    			type: "approvalTask/getWorkflowApprovalTaskList",
    			payload: {
    				curPage: curPage,
    				pageSize: pageSize,
    				...searchParams
    			}
    		});
    	}
    }

    changePagination(curPage, pageSize) {
    	let { approvalTaskStore, dispatch } = this.props;
    	let { workflowPage } = approvalTaskStore;
    	let { searchParams } = workflowPage;

    	dispatch({
    		type: "approvalTask/getWorkflowApprovalTaskList",
    		payload: {
    			curPage: curPage,
    			pageSize: pageSize,
    			...searchParams
    		}
    	});
    }

    changeSearchParamField(field, type, e) {
    	let { approvalTaskStore, dispatch } = this.props;
    	let { workflowPage } = approvalTaskStore;
    	let { searchParams } = workflowPage;

    	let value;
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "date") {
    		value = e;
    	}
    	if (field === "targetName") {
    		searchParams[field] = value;
    	} else if (field === "selectTs") {
			let startStr = moment(value[0]).format("YYYY-MM-DD") + " 00:00:00";
			let endStr = moment(value[1]).format("YYYY-MM-DD") + " 23:59:59";
    		searchParams["startTs"] = value.length === 2 ? Date.parse(startStr) : null;
    		searchParams["endTs"] = value.length === 2 ? Date.parse(endStr) : null;
    	}
    	dispatch({
    		type: "approvalTask/setAttrValue",
    		payload: {
    			workflowPage: workflowPage
    		}
    	});
    }

    workflowApprovalHandle(item) {
    	let { approvalTaskStore, dispatch } = this.props;
    	let { dialogData } = approvalTaskStore;
    	let { workflowApprovalData } = dialogData;

    	dispatch({
    		type: "approvalTask/setDialogShow",
    		payload: {
    			workflowApproval: true
    		}
    	});

    	workflowApprovalData.uuid = item.uuid;
    	dispatch({
    		type: "approvalTask/setDialogData",
    		payload: {
    			workflowApprovalData: workflowApprovalData
    		}
    	});

    	let params = {
    		uuid: item.uuid
    	};
    	approvalTaskAPI.getWorkflowApprovalTaskDiff(params).then(res => {
    		if (res.success) {
    			let flowDataTemp = {
    				nodes: [],
    				edges: []
    			};
    			let onlineVersion = res.data && res.data.onlineVersion ? res.data.onlineVersion : null;
    			let pendVersion = res.data && res.data.pendVersion ? res.data.pendVersion : null;

    			workflowApprovalData["diffDetail"]["onlineVersion"] = onlineVersion && isJSON(onlineVersion) ? JSON.parse(onlineVersion) : flowDataTemp;
    			if (item.operationType !== "DEL") {
    				workflowApprovalData["diffDetail"]["pendVersion"] = pendVersion && isJSON(pendVersion) ? JSON.parse(pendVersion) : flowDataTemp;
    			}

    			dispatch({
    				type: "approvalTask/setDialogData",
    				payload: {
    					workflowApprovalData: workflowApprovalData
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});

    	// 根据策略集uuid查询出策略集详情，然后回显到审核结果中
    	let policySetParams = {
    		uuid: item.targetUuid
    	};
    	policyEditorAPI.getPolicySetDetail(policySetParams).then(res => {
    		if (res.success) {
    			console.log(res);
    			dispatch({
    				type: "workflow/setAttrValue",
    				payload: {
    					policySetItem: res.data
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    render() {
    	let { approvalTaskStore } = this.props;
    	let { workflowPage } = approvalTaskStore;
    	let { taskList, total, curPage, searchParams } = workflowPage;
    	let { startTs, endTs } = searchParams;
    	let columns = [
    		{
    			title: approvalTaskLang.table("operationType"),		// lang:变更类型
    			render: (record) => {
    				return (
    					<div>
    						{
    							record["operationType"] &&
								operationTypeMap[record["operationType"]] &&
								operationTypeMap[record["operationType"]]()["text"]
    						}
    					</div>
    				);
    			}
    		},
    		{
    			title: approvalTaskLang.table("policySetName"),		// lang:策略集
    			dataIndex: "targetName"
    		},
    		{
    			title: approvalTaskLang.table("publishDesc"),		// lang:发版描述
    			dataIndex: "description"
    		},
    		{
    			title: approvalTaskLang.table("createdBy"),		// lang:提交人
    			dataIndex: "createdBy"
    		},
    		{
    			title: approvalTaskLang.table("submitTime"),		// lang:提交时间
    			dataIndex: "gmtCreate"
    		},
    		{
    			title: approvalTaskLang.table("operation"),		// lang:操作
    			width: 180,
    			render: (record) => {
    				return (
    					<div className="table-action">
    						<a
    							className={checkFunctionHasPermission("ZB0401", "changeWorkflowApproval") ? null : "a-disabled"}
    							onClick={() => {
    								if (checkFunctionHasPermission("ZB0401", "changeWorkflowApproval")) {
    									this.workflowApprovalHandle(record);
    								} else {
    									// lang:没有权限
    									message.info(commonLang.messageInfo("noPermission"));
    								}
    							}}
    						>
    							{/* lang:审批 */}
    							{approvalTaskLang.table("approval")}
    						</a>
    					</div>
    				);
    			}
    		}
    	];
    	return (
    		<Fragment>
    			<div className="page-global-body">
    				<div className="page-global-body-search">
    					<div className="left-info">
    						<h2>
    							{/* lang:规则流审批任务列表 */}
    							{approvalTaskLang.common("workflowTitle2")}
    						</h2>
    					</div>
    					<div className="right-info">
    						<div className="right-info-item">
    							<RangePicker
    								onChange={this.changeSearchParamField.bind(this, "selectTs", "date")}
    								style={{ width: "270px" }}
    								value={startTs && endTs ? [moment(startTs), moment(endTs)] : []}
    								format="YYYY-MM-DD"
    								disabled={!checkFunctionHasPermission("ZB0401", "workflowApprovalSearch")}
    							/>
    						</div>
    						<div className="right-info-item">
    							<Input
    								placeholder={approvalTaskLang.searchParams("workflowNamePlaceholder")}
    								value={searchParams.targetName || undefined}
    								onChange={this.changeSearchParamField.bind(this, "targetName", "input")}
    								onPressEnter={this.startSearch.bind(this)}
    								disabled={!checkFunctionHasPermission("ZB0401", "workflowApprovalSearch")}
    							/>
    						</div>
    						<div className="right-info-item">
    							<Button
    								type="primary"
    								className="search-button"
    								onClick={this.startSearch.bind(this)}
    								disabled={!checkFunctionHasPermission("ZB0401", "workflowApprovalSearch")}
    							>
    								{/* lang:搜索 */}
    								{approvalTaskLang.searchParams("search")}
    							</Button>
    						</div>
    					</div>
    				</div>
    				<div className="page-global-body-main">
    					<Table
    						className="table-card-body"
    						columns={columns}
    						dataSource={taskList}
    						pagination={false}
    						rowKey="id"
    					/>
    					<div className="page-global-body-pagination">
    						<span className="count">
    							{commonLang.getRecords(total)}
    						</span>
    						<Pagination
    							showQuickJumper
    							showSizeChanger
    							onChange={this.changePagination.bind(this)}
    							onShowSizeChange={this.changePagination.bind(this)}
    							current={curPage}
    							total={total}
    						/>
    					</div>
    				</div>
    			</div>
    			<WorkflowApprovalModal />
    		</Fragment>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(WorkflowTask);
