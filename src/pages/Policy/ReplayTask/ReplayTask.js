import React, { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { replayTaskAPI } from "@/services";
import { Table, Pagination, Tag, message, Icon, Tooltip, Modal, Popover, Select, DatePicker, Button, Progress } from "antd";
import { checkFunctionHasPermission } from "@/utils/permission";
import { PolicyConstants } from "@/constants";
import { replayTaskLang, commonLang } from "@/constants/lang";
import NoPermission from "@/components/NoPermission";
import moment from "moment";

const ReplayTaskReportModal = React.lazy(() => import("./Modal/ReplayTaskReportModal"));
const Option = Select.Option;
const confirm = Modal.confirm;
const { RangePicker } = DatePicker;

class TriggerManage extends PureComponent {

    state = {
    	replayTaskInterval: null
    };

    constructor(props) {
    	super(props);
    	this.changePagination = this.changePagination.bind(this);
    	this.initSearch = this.initSearch.bind(this);
    	this.startSearch = this.startSearch.bind(this);
    	this.resetSearch = this.resetSearch.bind(this);
    	this.changeTimeDistance = this.changeTimeDistance.bind(this);
    	this.terminateReplayTask = this.terminateReplayTask.bind(this);
    }

    componentDidMount() {
    	this.timer = setInterval(() => {
    		const { globalStore } = this.props;
    		const { menuTreeReady } = globalStore;
    		if (menuTreeReady) {
    			clearInterval(this.timer);
    			if (checkFunctionHasPermission("ZB0103", "ListPsReplayTask")) {
    				this.initSearch();
    			}
    		}
    	}, 100);
    }

    async componentWillReceiveProps(nextProps) {
    	let { replayTaskStore, dispatch, globalStore } = this.props;
    	const { menuTreeReady } = globalStore;
    	if (menuTreeReady && checkFunctionHasPermission("ZB0103", "ListPsReplayTask")) {
    		let preCurrentApp = this.props.globalStore.currentApp;
    		let nextCurrentApp = nextProps.globalStore.currentApp;
    		if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
    			// 检测到切换应用，开始刷新列表
    			// 首先清空搜索项目
    			let { searchParams } = replayTaskStore;

    			searchParams["curPage"] = 1;
    			await dispatch({
    				type: "replayTask/setAttrValue",
    				payload: searchParams
    			});
    			await this.initSearch();
    		}
    	}
    }

    initSearch() {
    	let { replayTaskStore, globalStore, dispatch } = this.props;
    	let { searchParams } = replayTaskStore;
    	let { currentApp } = globalStore;

    	dispatch({
    		type: "replayTask/getReplayTaskList",
    		payload: {
    			...searchParams,
    			appName: currentApp && currentApp.name ? currentApp.name : null
    		}
    	});
    	this.startSearch();
    }

    btnInitSearch() {
    	let { replayTaskStore, globalStore, dispatch } = this.props;
    	let { searchParams } = replayTaskStore;
    	let { currentApp } = globalStore;

        dispatch({
    		type: "replayTask/setAttrValue",
    		payload: {
    			searchParams: {
					...searchParams,
					curPage: 1
				}
    		}
        });

    	dispatch({
    		type: "replayTask/getReplayTaskList",
    		payload: {
                ...searchParams,
                curPage: 1,
    			appName: currentApp && currentApp.name ? currentApp.name : null
    		}
    	});
    	this.startSearch();
    }


    startSearch() {
    	let { replayTaskInterval } = this.state;
    	clearInterval(replayTaskInterval);
    	this.setState({
    		replayTaskInterval: setInterval(() => {
    			let { replayTaskStore, globalStore, dispatch } = this.props;
    			let { searchParams } = replayTaskStore;
    			let { currentApp } = globalStore;

    			dispatch({
    				type: "replayTask/getReplayTaskList",
    				payload: {
    					...searchParams,
    					appName: currentApp && currentApp.name ? currentApp.name : null
    				}
    			});
    		}, 5000)
    	});
    }

    componentWillUnmount() {
    	// 销毁页面做的事情
    	let { replayTaskInterval } = this.state;
    	clearInterval(replayTaskInterval);
    	replayTaskInterval = null;

    	this.setState({
    		replayTaskInterval
    	});
    }

    async changePagination(current, pageSize) {
    	let { replayTaskStore, globalStore, dispatch } = this.props;
    	let { searchParams } = replayTaskStore;
    	let { currentApp } = globalStore;

    	searchParams["curPage"] = current;
    	searchParams["pageSize"] = pageSize;
    	searchParams["pageSize"] = pageSize;
    	await dispatch({
    		type: "replayTask/setAttrValue",
    		payload: searchParams
    	});
    	await dispatch({
    		type: "replayTask/getReplayTaskList",
    		payload: {
    			...searchParams,
    			appName: currentApp && currentApp.name ? currentApp.name : null
    		}
    	});
    }

    resetSearch() {
    	let { replayTaskStore, dispatch } = this.props;
    	let { searchParams } = replayTaskStore;

        searchParams["curPage"] = 1;
    	searchParams["eventId"] = null;
    	searchParams["taskStartAt"] = null;
    	searchParams["taskEndAt"] = null;
    	searchParams["taskStatus"] = null;
    	dispatch({
    		type: "replayTask/setAttrValue",
    		payload: {
    			searchParams: searchParams
    		}
    	});
    }

    changeTimeDistance(timeArr) {
    	let { replayTaskStore, dispatch } = this.props;
    	let { searchParams } = replayTaskStore;

    	let taskStartAt, taskEndAt;
    	if (timeArr.length === 2) {
    		if (timeArr[1].valueOf() < timeArr[0].valueOf()) {
    			message.warning(replayTaskLang.date("dataError"));
    			return;
    		}
    		if (timeArr[1].valueOf() - timeArr[0].valueOf() > 7776000000) {
    			message.warning(replayTaskLang.date("timeRangeError"));
    			return;
    		}
    		taskStartAt = timeArr[0].valueOf();
    		taskEndAt = timeArr[1].valueOf();
    	}

    	searchParams["taskStartAt"] = taskStartAt;
    	searchParams["taskEndAt"] = taskEndAt;

    	dispatch({
    		type: "replayTask/setAttrValue",
    		payload: {
    			searchParams: searchParams
    		}
    	});
    }

    changeSearchParamValue(field, type, e) {
    	let { replayTaskStore, dispatch } = this.props;
    	let { searchParams } = replayTaskStore;

    	let value;
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}

    	searchParams[field] = value;
    	dispatch({
    		type: "replayTask/setAttrValue",
    		payload: {
    			searchParams: searchParams
    		}
    	});
    }

    viewReportHandle(item) {
    	let { dispatch } = this.props;

    	let params = {
    		taskUuid: item.taskUuid
    	};
    	replayTaskAPI.getReplayTaskDetail(params).then(res => {
    		if (res.success) {
    			let data = res.data || {};
    			let { contents = [], ...other } = data;
    			console.log(other);
    			dispatch({
    				type: "replayTask/setDialogShow",
    				payload: {
    					replayTaskReportModal: true
    				}
    			});
    			dispatch({
    				type: "replayTask/setDialogData",
    				payload: {
    					replayTaskReportData: {
    						ruleList: data.contents || [],
    						...other
    					}
    				}
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    exportReport(item) {
    	let params = {
    		taskUuid: item.taskUuid
    	};
    	replayTaskAPI.exportReport(params, item.taskNo, "xls").then(res => {
    		if (res.success) {
    			message.success(res.message);
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    terminateReplayTask(item) {
    	let _this = this;
    	let params = {
    		taskUuid: item.taskUuid
    	};

    	confirm({
    		title: replayTaskLang.table("terminateTaskTip"),		// lang:终止回测任务提醒
    		content: replayTaskLang.terminateTaskTipText(`[${item.taskName}]`),		// lang："确定要终止回测任务" + item.taskName + "吗？"
    		onOk() {
    			replayTaskAPI.terminateReplayTask(params).then(res => {
    				if (res.success) {
    					message.success("终止回测任务成功");
    					_this.initSearch();
    				} else {
    					message.error(res.message);
    				}
    			}).catch(err => {
    				console.log(err);
    			});
    		},
    		onCancel() {
    			console.log("Cancel");
    		}
    	});
    }

    render() {
    	let { replayTaskStore, globalStore } = this.props;
    	let { allMap, personalMode, menuTreeReady } = globalStore;
    	let { searchParams, total, replayTaskList } = replayTaskStore;
    	let { eventId, taskStartAt, taskEndAt, taskStatus, pageSize } = searchParams;
    	let lang = personalMode.lang === "cn" ? "cn" : "en";

    	let columns = [
    		{
    			title: replayTaskLang.table("taskNo"),		// lang:任务编号
    			dataIndex: "taskNo",
    			fixed: "left",
    			width: 120
    		},
    		{
    			title: replayTaskLang.table("taskName"),		// lang:任务名称
    			dataIndex: "taskName",
    			width: 160,
    			render: (taskName, record) => {
    				let timeArrText;
    				if (record.taskFrom) {
    					timeArrText = "&startTs=" + Date.parse(record.taskStartAt);
    				}
    				if (record.taskEndAt) {
    					if (record.taskExecuteType !== "RIGHTNOW") {
    						timeArrText += "&endTs=" + Date.parse(record.taskEndAt);
    					}
    				}
    				let link = "/trade/event/replay?policySetUuid=" + record.policySetUuid + "&taskUuid=" + record.taskUuid + timeArrText;
    				return (
    					<a href={link} target="_blank">
    						<Icon
    							type="link"
    							style={{ marginRight: "5px" }}
    						/>
    						<Tooltip title={taskName && taskName.length > 10 ? taskName : undefined}>
    							<span>
    								{taskName && taskName.length > 10 ? taskName.substr(0, 9) + "..." : taskName}
    							</span>
    						</Tooltip>
    					</a>
    				);
    			}
    		},
    		{
    			title: replayTaskLang.table("policySetName"),		// lang:策略集名称
    			dataIndex: "policySetName",
    			width: 200,
    			render: (text) => {
    				return (
    					<Tooltip title={text && text.length > 15 ? text : undefined}>
    						<div
    							className="text-overflow"
    							style={{ width: "180px" }}
    						>
    							{text}
    						</div>
    					</Tooltip>
    				);
    			}
    		},
    		// {
    		// 	title: replayTaskLang.table("totalCount"),		// lang:总数
    		// 	dataIndex: "totalCount",
    		// 	width: 60
    		// },
    		{
    			title: replayTaskLang.table("taskProgress"),		// lang:任务进度
    			width: 160,
    			render: (record) => {
    				// 如果后端没有返回则默认赋值0
    				if (!record.hasOwnProperty("completeCount")) {
    					record.completeCount = 0;
    				}
    				let percent;
    				if (record.totalCount === 0) {
    					percent = 100;
    				} else if (record.completeCount === 0) {
    					percent = 0;
    				} else {
    					percent = (record.completeCount / record.totalCount * 100).toFixed(2);
    				}

    				return (
    					<Popover
    						content={
    							<div>
    								<div>
    									{/* lang:回测总数*/}
    									{replayTaskLang.table("replayCount")}：{record.totalCount || record.totalCount === 0 ? record.totalCount : "-"}
    								</div>
    								<div>
    									{/* lang:已完成 */}
    									{replayTaskLang.table("hasCompleted")}：{record.completeCount || record.completeCount === 0 ? record.completeCount : "-"}
    								</div>
    							</div>
    						}
    						title={replayTaskLang.table("taskProgress")} // lang:任务进度
    					>
    						<Progress
    							strokeColor={{
    								"0%": "#108ee9",
    								"100%": "#87d068"
    							}}
    							percent={percent}
    							status="active"
    						/>
    					</Popover>
    				);
    			}
    		},
    		{
    			title: replayTaskLang.table("taskStatus"),		// lang:任务状态
    			dataIndex: "taskStatus",
    			width: 100,
    			render: (taskStatus) => {
    				let taskStatusObj;
    				if ((taskStatus === 0 || taskStatus) && PolicyConstants.replayTaskStatusMap[taskStatus.toString()]) {
    					taskStatusObj = PolicyConstants.replayTaskStatusMap[taskStatus.toString()];
    					return (
    						<div>
    							<Tag
    								color={taskStatusObj.color}
    							>
    								{lang === "cn" ? taskStatusObj.dName : taskStatusObj.enDName}
    							</Tag>
    						</div>
    					);
    				}
    			}
    		},
    		{
    			title: replayTaskLang.table("taskStartTime"),		// lang:执行开始时间
    			dataIndex: "taskStartAt",
    			width: 200
    		},
    		{
    			title: replayTaskLang.table("taskEndTime"),		// lang:执行结束时间
    			dataIndex: "taskEndAt",
    			width: 200
    		},
    		{
    			title: replayTaskLang.table("startTime"),		// lang:回测数据开始时间
    			dataIndex: "taskFrom",
    			width: 200
    		},
    		{
    			title: replayTaskLang.table("endTime"),		// lang:回测数据结束时间
    			dataIndex: "taskTo",
    			width: 200
    		},
    		{
    			title: replayTaskLang.table("operation"),		// lang:操作
    			width: 200,
    			fixed: "right",
    			render: (record) => {
    				return (
    					<div className="table-action">
    						{/* 查看报告 */}
    						{
    							record.taskStatus !== 10 &&
                                <a className="a-disabled">
                                	{/* lang:暂无报告 */}
                                	{replayTaskLang.table("noReport")}
                                </a>
    						}
    						{
    							checkFunctionHasPermission("ZB0103", "QueryPsReplayTaskReport") &&
                                record.taskStatus === 10 &&
                                <a onClick={this.viewReportHandle.bind(this, record)}>
                                	{/* lang:查看报告 */}
                                	{replayTaskLang.table("viewReport")}
                                </a>
    						}
    						{
    							!checkFunctionHasPermission("ZB0103", "QueryPsReplayTaskReport") && record.taskStatus === 10 &&
                                <a
                                	className="a-disabled"
                                	onClick={() => {
                                		// lang:无权限操作
                                		message.warning(commonLang.messageInfo("noPermission"));
                                	}}
                                >
                                	{/* lang:查看报告 */}
                                	{replayTaskLang.table("viewReport")}
                                </a>
    						}
    						{
    							checkFunctionHasPermission("ZB0103", "queryPsReplayTaskReportExport") &&
                                record.taskStatus === 10 &&
                                <a onClick={this.exportReport.bind(this, record)}>
                                	{/* lang: 导出报告 */}
                                	{replayTaskLang.table("exportReport")}
                                </a>
    						}
    						{
    							!checkFunctionHasPermission("ZB0103", "queryPsReplayTaskReportExport") &&
                                record.taskStatus === 10 &&
                                <a
                                	className="a-disabled"
                                	onClick={() => {
                                		// lang:无权限操作
                                		message.warning(commonLang.messageInfo("noPermission"));
                                	}}
                                >
                                	{/* lang: 导出报告 */}
                                	{replayTaskLang.table("exportReport")}
                                </a>
    						}
    						{/* 终止操作 */}
    						{
    							(record.taskStatus === 0 || record.taskStatus === 5 || record.taskStatus === 3) &&
                                checkFunctionHasPermission("ZB0103", "TerminatePsReplayTask") &&
                                <a onClick={this.terminateReplayTask.bind(this, record)}>
                                	{/* lang:终止回测 */}
                                	{replayTaskLang.table("terminateTask")}
                                </a>
    						}
    					</div>
    				);
    			}
    		}
    	];

    	// lang：全部策略集
    	let policySetListOption = [];
    	for (let [key, value] of Object.entries(allMap && allMap["eventIdSelect"] ? allMap["eventIdSelect"] : {})) {
    		policySetListOption.push(
    			<Option value={key}>{value}</Option>
    		);
    	}

    	return (
    		<Fragment>
    			<div className="page-global-header">
    				<div className="left-info">
    					<h2>
    						{/* lang:回测任务列表 */}
    						{replayTaskLang.common("title")}
    					</h2>
    				</div>
    				{
    					menuTreeReady &&
                        checkFunctionHasPermission("ZB0103", "ListPsReplayTask") &&
                        <div className="right-info">
                        	<div className="right-info-item">
                        		<RangePicker
                        			showTime
                        			format="YYYY-MM-DD HH:mm:ss"
                        			value={[taskStartAt ? moment(taskStartAt) : null, taskEndAt ? moment(taskEndAt) : null]}
                        			onChange={this.changeTimeDistance.bind(this)}
                        			allowClear={false}
                        			ranges={replayTaskLang.date("dateMap")}
                        		/>
                        	</div>
                        	<div className="right-info-item">
                        		<Select
                        			style={{ width: "150px" }}
                        			value={taskStatus || ""}
                        			onChange={this.changeSearchParamValue.bind(this, "taskStatus", "select")}
                        			placeholder={replayTaskLang.searchParams("taskStatusPleaseholder")} // 请选择任务状态
                        		>
                        			<Option value="">
                        				{/* lang:全部状态 */}
                        				{replayTaskLang.searchParams("allStatus")}
                        			</Option>
                        			{
                        				Object.keys(PolicyConstants.replayTaskStatusMap).map((status, index) => {
                        					let statusObj = PolicyConstants.replayTaskStatusMap[status];
                        					return (
                        						<Option value={status}>
                        							{lang === "cn" ? statusObj.dName : statusObj.enDName}
                        						</Option>
                        					);
                        				})
                        			}
                        		</Select>
                        	</div>
                        	<div className="right-info-item">
                        		<Select
                        			style={{ width: "150px" }}
                        			value={eventId || ""}
                        			onChange={this.changeSearchParamValue.bind(this, "eventId", "select")}
                        		>
                        			<Option value="">
                        				{/* lang:全部策略集 */}
                        				{replayTaskLang.searchParams("allPolicySet")}
                        			</Option>
                        			{policySetListOption}
                        		</Select>
                        	</div>
                        	<div className="right-info-item">
                        		<Button
                        			onClick={this.resetSearch.bind(this)}
                        		>
                        			{/* lang:清空 */}
                        			{replayTaskLang.searchParams("clear")}
                        		</Button>
                        	</div>
                        	<div className="right-info-item">
                        		<Button
                        			type="primary"
                        			onClick={this.btnInitSearch.bind(this)}
                        		>
                        			{/* lang:搜索 */}
                        			{replayTaskLang.searchParams("search")}
                        		</Button>
                        	</div>
                        </div>
    				}
    			</div>
    			{
    				menuTreeReady &&
                    <div className="page-global-body">
                    	{
                    		checkFunctionHasPermission("ZB0103", "ListPsReplayTask")
                    			? <div className="page-global-body-main has-table-border">
                    				<Table
                    					columns={columns}
                    					pagination={false}
                    					dataSource={replayTaskList}
                    					rowKey="taskUuid"
                    					scroll={{ x: 1700 }}
                    				/>
                    				<div className="page-global-body-pagination">
                    					<span className="count">
                    						{commonLang.getRecords(total)}
                    					</span>
                    					<Pagination
                                            showSizeChanger
                    						onChange={this.changePagination.bind(this)}
                    						onShowSizeChange={this.changePagination.bind(this)}
                    						defaultCurrent={1}
                    						total={total}
                                            current={searchParams.curPage || 1}
                                            pageSize={pageSize}
                    					/>
                    				</div>
                    			</div>
                    			: <NoPermission />
                    	}
                    </div>
    			}
    			<React.Suspense fallback={null}>
    				<ReplayTaskReportModal />
    			</React.Suspense>
    		</Fragment>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	replayTaskStore: state.replayTask
}))(TriggerManage);

