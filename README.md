# 规则引擎
### 本地开发dll打包问题 

如果你引入的新的第三方公共模块，重新build:dll之后出现dev开发环境下无法正常打开页面，那么你可以尝试以下操作：
- 「⭐️推荐」将`contentBase: [path.join(__dirname, "../dist"), path.join(__dirname, "../public")]` 改成  `contentBase: [path.join(__dirname, "../public")]`
- 「💡笨方法」npm run build 或者 public下的vendor手动复制到dist下
- 「❌不建议」将DllReferencePlugin路径强制指向到public下


<br/>
<br/>
#### ⚠️ 字段标准版本与非标准版本
存在如下场景：
银河：非标准版本
天策：标准版

为了两种同时共存，我们通过编译打包命令来区分两种场景: `npm run build`, `npm run build:unstand`。默认为标准模式

```javascript
const StandField = {
	"S_DT_VS_PARTNERCODE": "S_S_PARTNERCODE",
	"S_DT_VS_EVENTID":"S_S_EVENTID",
	"S_DT_VS_APPNAME":"S_S_APPNAME",
	"D_T_VB_EVENTOCCURTIME":"S_D_EVENTOCCURTIME"
}
export default (field) => {
	// 标准化转换为标准字段
	if(process.env.UNSTAND){
		return field;
	}
	return StandField[field];
}
```

