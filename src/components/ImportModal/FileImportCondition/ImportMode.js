import { PureComponent, Fragment } from "react";
import { Row, Col, Radio } from "antd";
import { CommonConstants, ImportConstants } from "@/constants";
import { imExportModeLang } from "@/constants/lang";
import "./ImportMode.less";
const RadioGroup = Radio.Group;

const { showModeList } = ImportConstants;

export default class ImportMode extends PureComponent {
	renderExceptTip = (checkResult, importType) => {
		const exceptTip = [];
		if (importType === "policySet") {
			const { eventTypeUnMatch = [] } = checkResult || {};
			if (eventTypeUnMatch && eventTypeUnMatch.length > 0) {
				// 事件类型不一致默认导入环境数据
				exceptTip.push(<div>{imExportModeLang.importPolicyModal("eventTypeNotSame")}</div>);
			}
			// 事件标识不一致默认导入环境数据
			exceptTip.push(<div>{imExportModeLang.importPolicyModal("eventNotSame")}</div>);
		}
		if (importType === "rules") {
			const { policyModeUnMatch = [] } = checkResult || {};
			if (policyModeUnMatch && policyModeUnMatch.length > 0) {
				// 策略模式不一致默认导入环境数据
				exceptTip.push(<div>{imExportModeLang.importPolicyModal("policyModeUnMatch")}</div>);
			}
		}
		return exceptTip;
	}
	render() {
		const { changeImport, data, importType, checkResult = {} } = this.props;
		const { policyMode, zbMode, ruleMode } = data || {};
		const showMode = showModeList[importType];
		let tips = null;
		if (importType === "policySet" || importType === "rules") {
			tips = this.renderExceptTip(checkResult, importType);
		}
		return (
			<Row gutter={CommonConstants.gutterSpan} className="import-mode-wrap">
				<Fragment>
					{/* 策略 */}
					{
						showMode["policyMode"] &&
						<div className="mode-item-wrap">
							<Col span={5} className="basic-info-title">
								{/* 策略 */}{/* lang:导入模式 */}
								{imExportModeLang.importPolicyModal("policy")}{imExportModeLang.importPolicyModal("importMode")}：
							</Col>
							<Col span={19} className="import-mode">
								<RadioGroup
									value={policyMode || undefined}
									onChange={(e) => changeImport(e, "policyMode")}
								>
									<Radio value="SKIP">
										{/* lang:相同策略跳过*/}
										{imExportModeLang.importPolicyModal("samePolicySkipping")}
									</Radio>
									<Radio value="OVERRIDE">
										{/* lang:相同策略覆盖 */}
										{imExportModeLang.importPolicyModal("samePolicyCoverage")}
									</Radio>
									<Radio value="TERMINATE">
										{/* lang:相同策略中断 */}
										{imExportModeLang.importPolicyModal("samePolicyInterrupt")}
									</Radio>
								</RadioGroup>
							</Col>
						</div>
					}
					{/* 规则 */}
					{
						showMode["ruleMode"] &&
						<div className="mode-item-wrap">
							<Col span={5} className="basic-info-title">
								{/* 规则 */}{/* lang:导入模式 */}
								{imExportModeLang.importPolicyModal("rule")}{imExportModeLang.importPolicyModal("importMode")}：
							</Col>
							<Col span={19} className="import-mode">
								<RadioGroup
									value={ruleMode || undefined}
									onChange={(e) => changeImport(e, "ruleMode")}
								>
									<Radio value="SKIP">
										{/* lang:相同规则跳过*/}
										{imExportModeLang.importPolicyModal("sameRuleSkipping")}
									</Radio>
									<Radio value="OVERRIDE">
										{/* lang:相同规则覆盖 */}
										{imExportModeLang.importPolicyModal("sameRuleCoverage")}
									</Radio>
									<Radio value="TERMINATE">
										{/* lang:相同规则中断 */}
										{imExportModeLang.importPolicyModal("sameRuleInterrupt")}
									</Radio>
								</RadioGroup>
							</Col>
						</div>
					}
					{/* 指标 */}
					{
						showMode["zbMode"] &&
						<div className="mode-item-wrap">
							<Col span={5} className="basic-info-title">
								{/* 指标 */}{/* lang:导入模式 */}
								{imExportModeLang.importPolicyModal("indicator")}{imExportModeLang.importPolicyModal("importMode")}：
							</Col>
							<Col span={19} className="import-mode">
								<RadioGroup
									value={zbMode || undefined}
									onChange={(e) => changeImport(e, "zbMode")}
								>
									<Radio value="SKIP">
										{/* lang:相同指标跳过*/}
										{imExportModeLang.importPolicyModal("sameIndicatorSkipping")}
									</Radio>
									<Radio value="OVERRIDE">
										{/* lang:相同指标覆盖 */}
										{imExportModeLang.importPolicyModal("sameIndicatorCoverage")}
									</Radio>
									<Radio value="TERMINATE">
										{/* lang:相同指标中断 */}
										{imExportModeLang.importPolicyModal("sameIndicatorInterrupt")}
									</Radio>
								</RadioGroup>
							</Col>
							{
								importType === "index" &&
								<div className="mode-item-wrap-child">{this.props.children}</div>
							}
						</div>
					}
					{/* 规则流 */}
					{
						showMode["workflowMode"] &&
						<div>
							<Col span={5} className="basic-info-title">
								{/* 规则流 */}{/* lang:导入模式 */}
								{imExportModeLang.importPolicyModal("workflow")}{imExportModeLang.importPolicyModal("importMode")}：
							</Col>
							<Col span={19}>
								{/* 覆盖已有规则流 */}
								<div className="red">{imExportModeLang.importPolicyModal("overWorkflow")}</div>
							</Col>
						</div>
					}
					{
						tips &&
						tips.length > 0 &&
						<div>
							<Col span={5} className="basic-info-title">
								{/* lang:异常处理模式 */}
								{imExportModeLang.importPolicyModal("exceptDealMode")}：
							</Col>
							<Col span={19}>
								{tips}
							</Col>
						</div>
					}
				</Fragment>
			</Row>
		);
	}
}
