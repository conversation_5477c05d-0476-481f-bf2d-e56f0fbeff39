import React, { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { uniqBy, cloneDeep } from "lodash";
import { indexEditorAPI, reCallTaskAPI } from "@/services";
import { isJSON } from "@/utils/isJSON";
import { Input, Button, Icon, message, Tooltip, Select, Modal, Checkbox, Collapse, Pagination, Tag, Spin, Row, Col, Popover } from "antd";
import { PolicyConstants } from "@/constants";
import { indexListLang, commonLang, reCallTaskLang } from "@/constants/lang";
import { checkFunctionHasPermission } from "@/utils/permission";
import NoPermission from "@/components/NoPermission";
import ExportIndex from "../Common/ExportIndex";
import CircleProcess from "./Inner/CircleProcess";
import "../Salaxy.less";

const IndexConfig = React.lazy(() => import("./Inner/IndexConfig"));
const SalaxyModal = React.lazy(() => import("@/components/Salaxy/SalaxyModal"));
const TemplateDrawer = React.lazy(() => import("@/components/TemplateDrawer"));
const IndexCiteDrawer = React.lazy(() => import("@/components/Salaxy/IndexCiteDrawer"));
const IndexImportModal = React.lazy(() => import("@/components/ImportModal/IndexImportModal"));
const IndexVersionModal = React.lazy(() => import("./Modal/IndexVersionModal"));
const BatchVersionSubmitModal = React.lazy(() => import("./Modal/BatchVersionSubmitModal"));
const AuthListModal = React.lazy(() => import("../Common/AuthListModal"));
const ReCallTaskModal = React.lazy(() => import("./Modal/ReCallTaskModal"));

const InputGroup = Input.Group;
const { confirm } = Modal;
const { Panel } = Collapse;
const { Option, OptGroup } = Select;

class Salaxy extends PureComponent {

	state = {
		ruleNameWidth: 330,
		circleProcessVisible: false, // 回溯进度圆
		reCallProcessVisible: false, // 回溯进度查看
		reCallProcessUuid: "" // 当前查看回溯进度的指标id
	}

	constructor(props) {
		super(props);

	}

	componentWillUnmount() {
		let { indexEditorStore, dispatch } = this.props;
		let { dialogShow } = indexEditorStore;

		dialogShow["indexCiteDrawer"] = false;

		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				dialogShow: dialogShow
			}
		});
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				indexCiteDrawerData: {
					indexId: null,
					item: null,
					dataList: []
				}
			}
		});
	}

	componentDidMount() {
		this.initPage();
		window.addEventListener("resize", this.initPage);
		// 如果当前有展开的指标详情取消打开模式 例如审批通过跳转回来的场景，会出现状态未更新
		this.props.dispatch({
			type: "indexEditor/initSalaxyExpandDetail"
		});
		this.timer = setInterval(() => {
			const { globalStore } = this.props;
			const { menuTreeReady } = globalStore;
			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkFunctionHasPermission("ZB0102", "editorSearch")) {
					this.search();
				}
			}
		}, 100);
	}

	initPage = () => {
		let clientWidth = document.querySelector("body").offsetWidth;

		let ruleNameWidth = clientWidth - 850;
		if (ruleNameWidth < 330) {
			ruleNameWidth = 330;
		}
		this.setState({
			ruleNameWidth: ruleNameWidth
		});
	}

	search = () => {
		let { indexEditorStore, dispatch, globalStore } = this.props;
		let { currentApp } = globalStore;
		let { curPage, pageSize, searchParams } = indexEditorStore;

		let appName;
		if (currentApp.name) {
			appName = currentApp.name;
		}
		searchParams["app"] = appName;
		dispatch({
			type: "indexEditor/getSalaxyList",
			payload: {
				curPage: curPage,
				pageSize: pageSize,
				...searchParams
			}
		});
	}

	componentWillReceiveProps(nextProps) {
		let { indexEditorStore, dispatch, globalStore } = this.props;
		const { menuTreeReady } = globalStore;
		let { pageSize, searchParams } = indexEditorStore;
		let preCurrentApp = this.props.globalStore.currentApp;
		let nextCurrentApp = nextProps.globalStore.currentApp;
		if (!menuTreeReady || !checkFunctionHasPermission("ZB0101", "editorSearch")) {
			return;
		}
		if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
			if (nextCurrentApp.name !== "all") {
				searchParams["app"] = nextCurrentApp.name;
			} else {
				searchParams["app"] = "";
			}

			searchParams["event"] = null;
			searchParams["calcType"] = null;
			searchParams["sceneType"] = null;
			searchParams["event"] = null;

			dispatch({
				type: "indexEditor/setAttrValue",
				payload: {
					curPage: 1,
					searchParams: searchParams
				}
			});

			dispatch({
				type: "indexEditor/getSalaxyList",
				payload: {
					curPage: 1,
					pageSize: pageSize,
					...searchParams
				}
			});
		}
	}

	addSalaxyHandle = () => {
		let { dispatch } = this.props;

		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				templateDrawer: true
			}
		});
	}

	viewIndexItemDetail = (idList) => {
		let { indexEditorStore, dispatch } = this.props;
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				currentLoadingId: idList[0],
				indexActiveKeyLoading: true,
				indexActiveKey: []
			}
		});
		let { editIndexMap } = indexEditorStore;

		let id = idList[0];

		let params = {
			id: id
		};
		indexEditorAPI.viewIndexItemDetail(params).then(res => {
			if (res.success) {
				let obj = res.data;
				obj["filterList"] = obj["filterStr"] && isJSON(obj["filterStr"]) ? JSON.parse(obj["filterStr"]) : [];
				obj["attachFieldsObj"] = obj["attachFields"] && isJSON(obj["attachFields"]) ? JSON.parse(obj["attachFields"]) : {};
				obj["sceneList"] = obj["scene"] && isJSON(obj["scene"]) ? JSON.parse(obj["scene"]) : [];
				editIndexMap[id] = res.data;
				dispatch({
					type: "indexEditor/setAttrValue",
					payload: {
						editIndexMap: editIndexMap
					}
				});
				dispatch({
					type: "indexEditor/setAttrValue",
					payload: {
						currentIndexId: idList[0]
					}
				});
				setTimeout(() => {
					dispatch({
						type: "indexEditor/setAttrValue",
						payload: {
							indexActiveKey: idList,
							indexActiveKeyLoading: false
						}
					});
				}, 300);

			} else {
				message.error(res.message);
				dispatch({
					type: "indexEditor/setAttrValue",
					payload: {
						indexActiveKeyLoading: false
					}
				});
			}
		}).catch(err => {
			console.log(err);
			dispatch({
				type: "indexEditor/setAttrValue",
				payload: {
					indexActiveKeyLoading: false
				}
			});
		});
	}

	deleteIndexHandle = async (obj, e) => {
		e.stopPropagation();
		const { dispatch, indexEditorStore } = this.props;
		let fillBackStatus = "";
		if (!obj.unSave) {
			// 删除指标时判断是否有回溯状态
			await dispatch({
				type: "indexEditor/changeIndexStatus",
				payload: {
					id: obj.id
				}
			});
			let { indexList } = indexEditorStore;
			const curObj = indexList.find(v => v.id === obj.id) || {};
			fillBackStatus = (curObj || {}).fillBackStatus;
		}
		let msg = (
			<div>
				{indexListLang.operator("deleteIndexDesc1") + obj.name + indexListLang.operator("deleteIndexDesc2")}
				{/* 删除指标将一并删除回溯任务 */}
				{fillBackStatus && <div className="red mt5">{indexListLang.operator("delIndexReCall")}</div>}
			</div>
		);
		confirm({
			title: indexListLang.operator("deleteIndexTip"),	// lang:删除指标提醒
			content: <Fragment>{msg}</Fragment>,
			onOk: () => {
				this.deleteIndex(obj);
			},
			onCancel: () => {
				console.log("Cancel");
			}
		});
	}

	deleteIndex = (item) => {
		let { indexEditorStore, dispatch } = this.props;
		let { editIndexMap } = indexEditorStore;

		if (item.unSave) {
			// 未保存
			delete editIndexMap[item.id];
			dispatch({
				type: "indexEditor/setAttrValue",
				payload: {
					editIndexMap: editIndexMap
				}
			});
		} else {
			// 已经保存过了
			let params = {
				id: item.id
			};
			indexEditorAPI.deleteSalaxy(params).then(res => {
				if (res.success) {
					// lang:操作成功
					message.success(indexListLang.operator("operateSuccessfully"));
					// 刷新列表前将待刷新的移除
					let newEditMap = {};
					Object.values(editIndexMap).map((editItem) => {
						if (!editItem.needRefresh) {
							newEditMap[editItem["id"]] = editItem;
						}
					});
					console.log(newEditMap);
					dispatch({
						type: "indexEditor/setAttrValue",
						payload: {
							editIndexMap: newEditMap
						}
					});

					// 刷新列表，这里的逻辑是有可能需要审核，删除后变成待审批
					this.search();

					// 重新刷新allMap列表
					dispatch({
						type: "global/getAllMap",
						payload: {}
					});
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		}
	}

	changeSalaxyStatus = (item, status) => {
		let { indexEditorStore, dispatch } = this.props;
		let { indexList, editIndexMap } = indexEditorStore;
		let params = {
			id: item.id,
			status: status ? "1" : "0"
		};
		// return;
		indexEditorAPI.changeSalaxyStatus(params).then(res => {
			if (res.success) {
				// lang:修改指标状态成功
				message.success(indexListLang.operator("modifyIndexStatusSuccess"));
				if (item.needRefresh) {
					// 如果新增指标成功但还没有刷新页面
					editIndexMap[item.id]["status"] = status ? "1" : "0";
					dispatch({
						type: "indexEditor/setAttrValue",
						payload: {
							editIndexMap: editIndexMap
						}
					});
				} else {
					let indexObj = indexList.find(fItem => fItem.id === item.id);
					if (indexObj) {
						indexObj["status"] = status ? "1" : "0";
					}
					dispatch({
						type: "indexEditor/setAttrValue",
						payload: {
							indexList: indexList
						}
					});
					dispatch({
						type: "global/getAllMap",
						payload: {}
					});
				}
				// TODO 变更指标状态是否需要变更publishStatus
				dispatch({
					type: "indexEditor/changeIndexStatus",
					payload: {
						id: item.id
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	paginationOnChange = (current, pageSize) => {
		let { indexEditorStore, dispatch } = this.props;
		let { searchParams } = indexEditorStore;
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				curPage: current,
				pageSize: pageSize
			}
		});

		dispatch({
			type: "indexEditor/getSalaxyList",
			payload: {
				curPage: current,
				pageSize: pageSize,
				...searchParams
			}
		});
	}

	copyCurrentIndex = (item, e) => {
		e.stopPropagation();

		if (item.unSave) {
			// lang:请保存指标后再复制
			message.error(indexListLang.operator("saveBeforeCopy"));
			return;
		}

		let { indexEditorStore, dispatch } = this.props;
		let { editIndexMap } = indexEditorStore;
		let timestamp = Date.parse(new Date());

		let cloneItem = cloneDeep(item);
		if (editIndexMap[item.id]) {
			cloneItem = cloneDeep(editIndexMap[item.id]);
		}

		cloneItem["unSave"] = true;
		cloneItem["hasModify"] = false;
		cloneItem["id"] = timestamp;
		cloneItem["uuid"] = timestamp.toString();
		cloneItem["publishStatus"] = "wait_commit";
		cloneItem["hasCommited"] = "0";
		// cloneItem["sceneList"] = [];
		// cloneItem["sceneType"] = "";
		// cloneItem["sceneLogic"] = "eq";

		editIndexMap[timestamp] = cloneItem;
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				editIndexMap: editIndexMap
			}
		});
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				templateDrawer: false
			}
		});
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				indexActiveKey: [timestamp.toString()],
				currentIndexId: timestamp.toString()
			}
		});
		// lang:复制指标成功
		message.success(indexListLang.operator("copyIndexSuccess"));
	}

	openIndexCiteDrawer = (item, e) => {
		e.stopPropagation();
		let { dispatch, indexEditorStore } = this.props;
		let { dialogData } = indexEditorStore;
		let { indexCiteDrawerData } = dialogData;
		indexCiteDrawerData["item"] = {
			...item,
			sceneList: item.scene
		};
		indexCiteDrawerData["indexId"] = item.id;
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				indexCiteDrawer: true
			}
		});
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				indexCiteDrawerData: indexCiteDrawerData
			}
		});
		this.getIndexCite(item.id);
	}

	getIndexCite = (id) => {
		let { dispatch, indexEditorStore } = this.props;
		let { dialogData } = indexEditorStore;
		let { indexCiteDrawerData } = dialogData;
		let params = {
			id: id,
			curPage: 1,
			pageSize: 50
		};
		indexEditorAPI.getIndexCite(params).then(res => {
			if (res.success) {
				indexCiteDrawerData["dataList"] = res.data ? res.data : [];
				dispatch({
					type: "indexEditor/setDialogData",
					payload: {
						indexCiteDrawerData: indexCiteDrawerData
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	onSearch = () => {
		let { indexEditorStore, dispatch, globalStore } = this.props;
		let { pageSize, searchParams } = indexEditorStore;
		let { currentApp } = globalStore;
		let appName = currentApp.name;
		if (appName !== "all") {
			searchParams["app"] = appName;
		} else {
			searchParams["app"] = "";
		}

		dispatch({
			type: "indexEditor/getSalaxyList",
			payload: {
				curPage: 1,
				pageSize: pageSize,
				...searchParams
			}
		});
	}

	changeSearchParams = (field, type, e, toSearch) => {
		let { indexEditorStore, dispatch } = this.props;
		let { searchParams } = indexEditorStore;

		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e === "all" ? null : e;
		}
		searchParams[field] = value;
		console.log(searchParams);
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				searchParams: searchParams
			}
		});

		if (toSearch) {
			this.onSearch();
		}
	}

	// 批量处理
	batchSubmit = (checkedList) => {
		let { dispatch } = this.props;
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				batchVersionSubmitData: {
					zbs: checkedList,
					description: null
				}
			}
		});
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				batchVersionSubmit: true
			}
		});
	}

	// 刷新运行区列表
	cancelRefreshRunning = () => {
		const { dispatch, globalStore, indexRunningStore } = this.props;
		// 1.清空每个策略详情
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				indexActiveKey: [],
				currentIndexId: null,
				indexActiveKeyLoading: false,
				currentLoadingId: null,
				indexActiveIndex: null,
				editIndexMap: {}
			}
		});

		// 2. 重新获取运行区列表数据
		let { currentApp } = globalStore;
		let { curPage: runCurPage, pageSize: runPageSize, searchParams: runSearchParams } = indexRunningStore;
		let appName;
		if (currentApp.name) {
			appName = currentApp.name;
		}
		runSearchParams["app"] = appName;
		dispatch({
			type: "indexRunning/getSalaxyList",
			payload: {
				curPage: runCurPage,
				pageSize: runPageSize,
				...runSearchParams
			}
		});
		this.onSearch();
	}

	// 打开授权列表
	openAuthListModal = (item) => {
		if (item.unSave) {
			// lang:请保存指标后再授权
			message.error(indexListLang.operator("saveBeforeAuth"));
			return;
		}
		const { dispatch } = this.props;
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				authList: true
			}
		});
		dispatch({
			type: "indexEditor/fetchLicenseApps",
			payload: {
				zbUuid: item.uuid,
				isRunning: false
			}
		});
	}

	// 回溯新增弹窗
	openRecallTaskModal = (item, type) => {
		if (item.unSave) {
			// lang:请保存指标后再添加回溯
			message.error(indexListLang.operator("saveBeforeRecall"));
			return;
		}
		const { dispatch } = this.props;
		const { winSize, name, uuid, calcType } = item || {};
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				reCallData: {
					operaType: type || "create",
					timeType: winSize,
					zbUuid: uuid,
					zbName: name,
					zbCalcType: calcType,
					executeType: "RIGHTNOW",
					deadline: "",
					taskStartAt: ""
				}
			}
		});
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				reCall: true
			}
		});
	}

	// 回溯编辑弹窗
	editRecallTaskModal = async (item) => {
		const { uuid } = item;
		const { dispatch } = this.props;
		reCallTaskAPI.backDetail({
			zbUuid: uuid
		}).then(res => {
			let { data = {}, success } = res;
			if (success) {
				data = data.data || {};
				const { uuid, zbUuid, zbName, zbCalcType, executeType, deadline, taskStartAt } = data || {};
				dispatch({
					type: "indexEditor/setDialogData",
					payload: {
						reCallData: {
							operaType: "edit",
							uuid, zbUuid, zbName, zbCalcType, executeType, deadline,
							taskStartAt: executeType === "RIGHTNOW" ? null : taskStartAt
						}
					}
				});
				dispatch({
					type: "indexEditor/setDialogShow",
					payload: {
						reCall: true
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(e => {
			message.error(e.message);
		});
	}

	// 查看回溯进度
	handleReCallProcess = async (item) => {
		const { dispatch } = this.props;
		const { reCallProcessVisible } = this.state;
		if (!reCallProcessVisible) {
			await dispatch({
				type: "indexEditor/changeIndexStatus",
				payload: {
					id: item.id
				}
			});
			this.setState({
				reCallProcessVisible: true,
				reCallProcessUuid: item.uuid
			}, () => {
				this.setState({
					circleProcessVisible: true
				});
			});
		} else {
			this.setState({
				reCallProcessVisible: false,
				reCallProcessUuid: ""
			}, () => {
				this.setState({
					circleProcessVisible: false
				});
			});
		}
	}

	// 回溯按钮展示逻辑
	reCallBtnShow = (obj, supportOffline, lang) => {
		const { reCallProcessVisible, reCallProcessUuid, circleProcessVisible } = this.state;
		const { fillBackProgress, fillBackStatus, fillBackResultStatus } = obj || {};
		const { location } = this.props;
		let reCallBtn = null;
		if (supportOffline) {
			// 待运行 可编辑
			if (["PEND_RUN"].indexOf(fillBackStatus) > -1) {
				if (checkFunctionHasPermission("ZB0107", "ModifyZBFillBack")) {
					reCallBtn = (
						<Tooltip title={reCallTaskLang.operatorModal("editReCall")} placement="left">
							<i
								className={`salaxy-iconfont salaxy-recall-edit ${obj.unSave ? "disabled" : ""}`}
								onClick={(e) => {
									e.stopPropagation();
									this.editRecallTaskModal(obj);
								}}
							></i>
						</Tooltip>
					);
				} else {
					reCallBtn = (
						<i
							className="salaxy-iconfont salaxy-recall-edit icon-disabled"
							onClick={(e) => {
								e.stopPropagation();
								// lang:无权限操作
								message.warning(commonLang.messageInfo("noPermission"));
							}}
						></i>
					);
				}
			} else if (["RUNNING"].indexOf(fillBackStatus) > -1 || (fillBackResultStatus === "PEND" && fillBackStatus === "COMPLETED")) { // 运行中的可以查看 或者已完成且待生效
				if (checkFunctionHasPermission("ZB0102", "editorViewCite")) {
					const showPopover = reCallProcessVisible && obj.uuid === reCallProcessUuid;
					reCallBtn = (
						<Popover
							placement="left"
							title={reCallTaskLang.operatorModal("reCallProcessView")} // lang:查看回溯进度
							content={
								circleProcessVisible
									? <CircleProcess
										uuid={obj.uuid}
										percent={fillBackProgress}
										lang={lang}
										fillBackResultStatus={fillBackResultStatus}
										location={location}
									/>
									: null
							}
							onVisibleChange={this.handleReCallProcess.bind(this, obj)}
							visible={showPopover}
						>
							<i
								className={`salaxy-iconfont salaxy-view-recall ${obj.unSave ? "disabled" : ""}`}
								onClick={(e) => {
									e.stopPropagation();
								}}
							></i>
						</Popover>
					);
				} else {
					reCallBtn = (
						<i
							className="salaxy-iconfont salaxy-view-recall icon-disabled"
							onClick={(e) => {
								e.stopPropagation();
								// lang:无权限操作
								message.warning(commonLang.messageInfo("noPermission"));
							}}
						></i>
					);
				}
			} else {
				if (checkFunctionHasPermission("ZB0107", "AddZBFillBack")) {
					reCallBtn = (
						<Tooltip title={reCallTaskLang.operatorModal("addRecall")} placement="left">
							<i
								className={`salaxy-iconfont salaxy-recall ${obj.unSave ? "disabled" : ""}`}
								onClick={(e) => {
									e.stopPropagation();
									this.openRecallTaskModal(obj, "create");
								}}
							></i>
						</Tooltip>
					);
				} else {
					reCallBtn = (
						<i
							className="salaxy-iconfont salaxy-recall icon-disabled"
							onClick={(e) => {
								e.stopPropagation();
								// lang:无权限操作
								message.warning(commonLang.messageInfo("noPermission"));
							}}
						></i>
					);
				}
			}
		}
		return reCallBtn;
	}
	render() {
		const { ruleNameWidth } = this.state;
		let { indexEditorStore, globalStore, templateStore, dispatch, location } = this.props;
		const { indexTemplateListObj = {} } = templateStore || {};
		const { pathname } = location;
		let { allMap, personalMode, menuTreeReady } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		let { indexList, indexListReady, indexActiveKey, editIndexMap, currentIndexId, indexActiveKeyLoading, currentLoadingId, curPage, pageSize, total, dialogShow, dialogData, searchParams, listLoad } = indexEditorStore;
		let { indexCiteDrawerData } = dialogData;
		let { indexCiteDrawer } = dialogShow;
		indexList = indexList.filter(item => !item.unSave);

		// 将未保存和待刷新的加入列表
		for (let i in editIndexMap) {
			if (editIndexMap[i]["unSave"]) {
				indexList.splice(0, 0, editIndexMap[i]);
			}
			if (editIndexMap[i]["needRefresh"]) {
				let hasObjInList = indexList.find(fItem => String(fItem.id) === String(i));
				if (!hasObjInList) {
					indexList.splice(0, 0, editIndexMap[i]);
				}
			}
		}
		let indexListBackup = cloneDeep(indexList);
		// 准备checkbox all的数据
		let checkedList = indexList.filter(item => item.checked);
		let indeterminate = !!checkedList.length && (checkedList.length < indexList.length);
		let checkAll = checkedList.length === indexList.length && indexList.length !== 0;

		// 去重
		indexList = uniqBy(indexList, "id");

		let publishStatusMap = lang === "cn" ? PolicyConstants.policyStatusMap : PolicyConstants.policyStatusMap2;

		let collapseList = indexList.map((item, index) => {
			let title = (obj) => {
				obj = obj || {};
				let detailObj = editIndexMap[obj.id] ? editIndexMap[obj.id] : null;
				let headerClass = "rule-manage-header collapse-header";
				if (obj.unSave) {
					headerClass = headerClass + " un-save";
				}
				// 这里设置查看指标引用标识对应的指标行
				if (indexCiteDrawer && indexCiteDrawerData.indexId && item.id && indexCiteDrawerData.indexId.toString() === item.id.toString()) {
					headerClass = headerClass + " show-cite-drawer";
				}
				// 转指标类型为中文
				let currentCalcTypeObj = templateStore.indexOperateTypeMap && templateStore.indexOperateTypeMap.find((v) => {
					if (v.name === obj.calcType) {
						return v;
					}
				}) || {};

				// 判断是否支持回溯计算
				if (indexTemplateListObj && !(indexTemplateListObj[item.calcType])) {
					console.log(item.calcType);
				}
				const { advanceConfig } = indexTemplateListObj ? (indexTemplateListObj[item.calcType] || {}) : {};
				const { supportOffline } = advanceConfig || {};

				return (
					<Spin spinning={currentLoadingId && currentLoadingId.toString() === item.id.toString() && indexActiveKeyLoading}>
						<div
							className={headerClass}
							key={index}
						>
							<div className="checkbox">
								<Checkbox
									checked={obj.checked}
									onClick={(e) => {
										e.stopPropagation();
										let status = e.target.checked;
										(indexListBackup.find(fItem => fItem.id === obj.id) || {})["checked"] = status;
										dispatch({
											type: "indexEditor/setAttrValue",
											payload: {
												indexList: indexListBackup
											}
										});
									}}
								></Checkbox>
							</div>
							<div
								className="rule-name"
								style={{ maxWidth: ruleNameWidth + "px" }}
							>
								<Icon
									className={indexActiveKey.length && indexActiveKey[0].toString() === obj.id.toString() ? "row-collapsed active" : "row-collapsed"}
									type={indexActiveKey.length && indexActiveKey[0].toString() === obj.id.toString() ? "minus-square" : "plus-square"}
								/>
								{
									publishStatusMap[obj["publishStatus"]] &&
									<Tag color={publishStatusMap[obj["publishStatus"]]["color"]}>
										{publishStatusMap[obj["publishStatus"]]["text"]}
									</Tag>
								}
								{
									detailObj ? detailObj.name : obj.name
								}
							</div>
							<div className="rule-st">
								{
									detailObj &&
									detailObj.unSave &&
									// lang:新添加，未保存
									<span className="un-save">
										{/* lang:新添加，未保存 */}
										{indexListLang.table("addNotSave")}
									</span>
								}
								{
									detailObj &&
									detailObj.hasModify &&
									!detailObj.unSave &&
									// lang:有修改
									<span className="has-modify">
										{/* lang:有修改 */}
										{indexListLang.table("hasModify")}
									</span>
								}
								{
									detailObj &&
									detailObj.needRefresh &&
									!detailObj.unSave &&
									// lang:已保存，页面待刷新
									<span className="need-refresh">
										{/* lang:已保存，页面待刷新 */}
										{indexListLang.table("hasModifyNeedRefresh")}
									</span>
								}
							</div>
							<div className="rule-action rule-action-large">
								{
									checkFunctionHasPermission("ZB0102", "editorViewHistoryVersion")
										// lang:查看历史版本
										? <Tooltip title={indexListLang.tooltip("viewHistoryVersion")}>
											<i
												className="iconfont icon-version"
												onClick={(e) => {
													e.stopPropagation();
													dispatch({
														type: "indexRunning/setDialogShow",
														payload: {
															indexVersion: true
														}
													});
													dispatch({
														type: "indexRunning/setDialogData",
														payload: {
															indexVersion: {
																switchType: "Editor",
																id: obj.id
															}
														}
													});
													dispatch({
														type: "indexRunning/getIndexVersionList",
														payload: {
															uuid: obj.uuid
														}
													});
												}}
											></i>
										</Tooltip>
										: <i
											className="iconfont icon-version icon-disabled"
											onClick={(e) => {
												e.stopPropagation();
												// lang:无权限操作
												message.warning(commonLang.messageInfo("noPermission"));
											}}></i>
								}
								{
									checkFunctionHasPermission("ZB0102", "editorViewCite")
										// lang:查看指标引用
										? <Tooltip title={indexListLang.tooltip("viewIndexCite")}>
											<i className="iconfont icon-yinyong"
												onClick={(e) => {
													e.stopPropagation();
													this.openIndexCiteDrawer(obj, e);
												}}></i>
										</Tooltip>
										: <i
											className="iconfont icon-yinyong icon-disabled"
											onClick={(e) => {
												e.stopPropagation();
												// lang:无权限操作
												message.warning(commonLang.messageInfo("noPermission"));
											}}></i>
								}
								{
									checkFunctionHasPermission("ZB0102", "editorCopyIndex")
										// lang:拷贝当前指标
										? <Tooltip title={indexListLang.tooltip("copyIndex")}>
											<i
												className={obj.unSave ? "iconfont icon-fuzhi1 disabled" : "iconfont icon-fuzhi1"}
												onClick={(e) => {
													e.stopPropagation();
													this.copyCurrentIndex(obj, e);
												}}
											></i>
										</Tooltip>
										: <i className="iconfont icon-fuzhi1 icon-disabled" onClick={(e) => {
											e.stopPropagation();
											// lang:无权限操作
											message.warning(commonLang.messageInfo("noPermission"));
										}}></i>
								}
								{
									checkFunctionHasPermission("ZB0102", "editorDeleteIndex")
										// lang:删除当前指标
										? <Tooltip title={indexListLang.tooltip("deleteIndex")}>
											<i className="iconfont icon-delete"
												onClick={(e) => {
													this.deleteIndexHandle(obj, e);
												}}></i>
										</Tooltip>
										: <i
											className="iconfont icon-delete icon-disabled"
											onClick={(e) => {
												e.stopPropagation();
												// lang:无权限操作
												message.warning(commonLang.messageInfo("noPermission"));
											}}></i>
								}
								{/* 授权 */}
								{
									checkFunctionHasPermission("ZB0102", "editorViewCite")
										// lang:授权
										? <Tooltip title={indexListLang.tooltip("authorization")}>
											<i
												className={obj.unSave ? "salaxy-iconfont salaxy-user-follow-line disabled" : "salaxy-iconfont salaxy-user-follow-line"}
												onClick={(e) => {
													e.stopPropagation();
													this.openAuthListModal(obj, e);
												}}
											></i>
										</Tooltip>
										: <i
											className="salaxy-iconfont salaxy-user-follow-line icon-disabled"
											onClick={(e) => {
												e.stopPropagation();
												// lang:无权限操作
												message.warning(commonLang.messageInfo("noPermission"));
											}}></i>
								}
								{/* 回溯 */}
								{this.reCallBtnShow(obj, supportOffline, lang)}
							</div>
							<div className="rule-number">
								{
									obj["unSave"] ? <span className="none-data">
										{/* lang:暂无指标类型 */}
										{indexListLang.table("noCalcType")}
									</span> : currentCalcTypeObj.dName
								}
							</div>
						</div>
					</Spin>
				);
			};
			let renderDom = [];
			let itemObj = (
				<Panel
					header={title(item, [index])}
					key={item.id}
					showArrow={false}
					forceRender={true}
				>
					{
						currentIndexId &&
						currentIndexId.toString() === item.id.toString() &&
						!indexActiveKeyLoading &&
						<React.Suspense fallback={null}>
							<IndexConfig templateName={item.calcType} />
						</React.Suspense>
					}
				</Panel>
			);
			renderDom.push(itemObj);
			return renderDom;
		});

		// 获取筛选参数
		const { sceneType, event } = searchParams;

		// 获取场景条件
		const { sceneTypeSelect, sceneSelect } = allMap;
		const sceneSelectList = sceneSelect ? sceneSelect[sceneType || "ALL"] : [];

		return (
			<div className="page-global-body">
				<div className="page-global-body-search">
					{
						menuTreeReady &&
						checkFunctionHasPermission("ZB0102", "editorSearch") &&
						<Row gutter={10}>
							<Col span={8}>
								<InputGroup compact>
									<Select
										style={{ "width": "35%" }}
										placeholder={indexListLang.searchParams("selectSceneType")} // lang:选择场景类型
										value={sceneType || "ALL"}
										onChange={(e) => {
											this.changeSearchParams("event", "select", null); // 联动场景
											this.changeSearchParams("sceneType", "select", e === "ALL" ? null : e, true);
										}}
									>
										{
											sceneTypeSelect &&
											sceneTypeSelect.length > 0 &&
											sceneTypeSelect.map(v => {
												return (
													<Option value={v.name} key={v.name}>{v.dName}</Option>
												);
											})
										}
									</Select>
									<Select
										placeholder={indexListLang.searchParams("selectScene")} // lang:选择场景
										value={event || (!sceneType ? "ALL" : "")}
										style={{ "width": "65%" }}
										showSearch
										optionFilterProp="children"
										dropdownMatchSelectWidth={false}
										onChange={(e) => {
											this.changeSearchParams("event", "select", !sceneType ? null : e, true);
										}}
									>
										{
											sceneType &&
											sceneType !== "ALL" &&
											<Option value="">
												{/* 全部 */}
												{indexListLang.searchParams("all")}
											</Option>
										}
										{
											sceneSelectList &&
											sceneSelectList.length > 0 &&
											sceneSelectList.map((v) => {
												if (sceneType !== "EVENT") {
													return (
														<Option key={v.name} value={v.name}>{v.dName}</Option>
													);
												} else {
													const { eventList = [], dName, name } = v;
													return (
														<OptGroup label={dName} key={name}>
															{
																eventList &&
																eventList.length > 0 &&
																eventList.map(eventItem => {
																	return <Option key={eventItem.name} value={eventItem.name}>{eventItem.dName}</Option>;
																})
															}
														</OptGroup>
													);
												}
											})
										}
									</Select>
								</InputGroup>
							</Col>
							<Col span={4}>
								<Select
									className="wid-100-percent"
									placeholder={indexListLang.searchParams("selectIndexTypes")} // lang：选择指标类型
									value={searchParams.calcType ? searchParams.calcType : "all"}
									onChange={(e) => {
										this.changeSearchParams("calcType", "select", e, true);
									}}
									showSearch
									optionFilterProp="children"
									dropdownMatchSelectWidth={false}
								>
									{/* lang：全部指标类型*/}
									<Option value="all">
										{indexListLang.searchParams("allIndexTypes")}
									</Option>
									{
										templateStore &&
										templateStore.indexOperateTypeMap &&
										templateStore.indexOperateTypeMap.map((item, index) => {
											return (
												<Option value={item.name} key={index}>
													{lang === "en" ? item.enDName : item.dName}
												</Option>
											);
										})
									}
								</Select>
							</Col>
							<Col span={5}>
								<Input
									className="wid-100-percent"
									placeholder={indexListLang.searchParams("indexName")} // lang：指标名称
									value={searchParams["name"] || undefined}
									onPressEnter={this.onSearch}
									onChange={(e) => {
										this.changeSearchParams("name", "input", e);
									}}
								/>
							</Col>
							<Col span={7}>
								<Row type="flex" justify="space-between">
									<Button
										style={{ "marginRight": "10px" }}
										type="primary"
										onClick={() => {
											this.onSearch();
										}}
									>
										{/* lang：搜索 */}
										{indexListLang.searchParams("search")}
									</Button>
									<Button.Group>
										<Button
											onClick={() => {
												dispatch({
													type: "indexEditor/setDialogShow",
													payload: {
														indexImport: true
													}
												});
											}}
											disabled={!checkFunctionHasPermission("ZB0102", "editorImport")}
										>
											{indexListLang.common("import")}
											{/* lang:导入 */}
										</Button>
										{/* 导出指标 */}
										<ExportIndex
											disabled={!(checkedList && checkedList.length > 0)}
											checkedList={checkedList}
											isEditorArea={true}
										/>

										{/* 批量上线 */}
										<Button
											disabled={!(checkedList && checkedList.length > 0)}
											onClick={() => {
												if (checkFunctionHasPermission("ZB0102", "indicesBatchOnline")) {
													this.batchSubmit(checkedList);
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}}
										>
											{indexListLang.common("batchSubmit")}
										</Button>
									</Button.Group>
								</Row>
							</Col>
						</Row>
					}
					{
						menuTreeReady &&
						checkFunctionHasPermission("ZB0102", "editorSearch") &&
						<Row className="mt10">
							<Col span={5}>
								{/* lang:新建指标*/}
								<Button
									type="primary"
									onClick={this.addSalaxyHandle}
									disabled={!checkFunctionHasPermission("ZB0102", "editorAddIndex")}
								>{indexListLang.common("addNewIndex")}</Button>
							</Col>
						</Row>
					}
				</div>

				{
					menuTreeReady &&
					<div className="policy-detail-wrap zb-detail-wrap" >
						{
							checkFunctionHasPermission("ZB0102", "editorSearch")
								? <Spin spinning={listLoad} >
									<div
										className="page-global-body-main has-table-border"
										style={{
											paddingBottom: indexList.length > 0 ? "20px" : 0,
											minHeight: indexList.length < 1 ? "100px" : 0,
											borderRadius: "4px"
										}}
									>

										<div className="card-section-body">
											<div className="rule-manage">
												{
													indexList &&
													indexList.length > 0 &&
													<div className="rule-manage-header rule-th">
														<div className="checkbox pad-left-6">
															<Checkbox
																onClick={(e) => {
																	let status = e.target.checked;
																	indexListBackup.map((item, index) => {
																		item.checked = status;
																	});
																	dispatch({
																		type: "indexEditor/setAttrValue",
																		payload: {
																			indexList: indexListBackup
																		}
																	});
																}}
																checked={checkAll}
																indeterminate={indeterminate}
															></Checkbox>
														</div>
														<div className="rule-name">
															{indexListLang.table("indexName")}
															{/* lang:指标名称 */}
														</div>
														<div className="rule-operator rule-operator-large">
															{indexListLang.table("operator")}
															{/* lang:操作 */}
														</div>
														<div className="rule-number">
															{indexListLang.table("calcType")}
															{/* lang:指标类型 */}
														</div>
													</div>
												}
												<div className="rule-manage-body">
													<Collapse
														className="rule-manage-collapse"
														activeKey={indexActiveKey}
														onChange={(value) => {
															let { indexCiteDrawer } = dialogShow;
															let { indexCiteDrawerData } = dialogData;
															let idList = value.length > 1 ? [value[value.length - 1]] : value;
															if (indexCiteDrawer) {
																// 判断查看指标引用的抽屉有没有打开，如果打开，则点击请求指标引用接口
																if ((indexCiteDrawerData.indexId && indexCiteDrawerData.indexId.toString() === idList[0]) || idList.length < 1) {
																	dispatch({
																		type: "indexEditor/setDialogShow",
																		payload: {
																			indexCiteDrawer: false
																		}
																	});
																} else {
																	let currentIndexObj = indexList.filter(item => item.id.toString() === idList[0])[0];
																	indexCiteDrawerData["item"] = currentIndexObj;
																	indexCiteDrawerData["indexId"] = idList[0];

																	dispatch({
																		type: "indexEditor/setDialogData",
																		payload: {
																			indexCiteDrawerData: indexCiteDrawerData
																		}
																	});
																	this.getIndexCite(idList[0]);
																}
															}

															if (!indexCiteDrawer) {
																if (idList.length) {
																	if (editIndexMap[idList[0]]) {
																		// 如果当前记录在editIndexMap
																		dispatch({
																			type: "indexEditor/setAttrValue",
																			payload: {
																				indexActiveKey: idList,
																				currentIndexId: idList[0],
																				indexActiveKeyLoading: false
																			}
																		});
																	} else {
																		// 如果当前记录不在editIndexMap
																		this.viewIndexItemDetail(idList);
																	}
																} else {
																	// 收起
																	dispatch({
																		type: "indexEditor/setAttrValue",
																		payload: {
																			indexActiveKey: idList,
																			currentIndexId: idList[0],
																			indexActiveKeyLoading: false
																		}
																	});
																}
															}
														}}
													>
														{
															collapseList
														}
													</Collapse>
													{
														indexListReady &&
														indexList.length === 0 &&
														<div className="no-rule-tip"></div>
													}
												</div>
											</div>
										</div>
										{
											total !== 0 &&
											<div className="page-global-body-pagination">
												{/* lang:共x条记录 */}
												<span className="count">{commonLang.getRecords(total)}</span>
												<Pagination
													showSizeChanger
													onChange={this.paginationOnChange}
													onShowSizeChange={this.paginationOnChange}
													defaultCurrent={1}
													total={total}
													current={curPage}
													pageSize={pageSize}
												/>
											</div>
										}
										{
											!(indexList && indexList.length > 0) &&
											indexListReady &&
											<div className="none-data index-none-data">
												<i className="iconfont icon-empty"></i>
												<p>
													{/* lang:暂无指标信息 */}
													{indexListLang.table("noIndexInfo")}
												</p>
											</div>
										}

										<React.Suspense fallback={null}>
											<SalaxyModal />
										</React.Suspense>
										<React.Suspense fallback={null}>
											<TemplateDrawer pageName="salaxy" />
										</React.Suspense>
										<React.Suspense fallback={null}>
											<IndexCiteDrawer
												viewIndexItemDetail={this.viewIndexItemDetail.bind(this)}
												pathname={pathname}
											/>
										</React.Suspense>
										<React.Suspense fallback={null}>
											<IndexImportModal />
										</React.Suspense>
										<React.Suspense fallback={null}>
											<IndexVersionModal />
										</React.Suspense>
										<React.Suspense fallback={null}>
											<BatchVersionSubmitModal handleOk={this.cancelRefreshRunning} />
										</React.Suspense>
										<React.Suspense fallback={null}>
											<AuthListModal
												refresh={this.cancelRefreshRunning}
												disabled={false}
											/>
										</React.Suspense>
										<React.Suspense fallback={null}>
											<ReCallTaskModal
												refresh={this.cancelRefreshRunning}
											/>
										</React.Suspense>
									</div>
								</Spin>
								: <NoPermission />
						}
					</div>
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	indexRunningStore: state.indexRunning,
	templateStore: state.template
}))(Salaxy);

