import {isJ<PERSON><PERSON>} from "./isJSON";
import {uniqBy} from "lodash";

export function formatPolicyRules(policyRules) {
	policyRules && policyRules.map((item, index) => {
		policyRules[index]["operationActions"] = item["operationActions"] && isJSON(item["operationActions"]) ? JSON.parse(item["operationActions"]) : [];
		policyRules[index]["triggers"] = item["triggers"] && isJSON(item["triggers"]) ? JSON.parse(item["triggers"]) : [];
		if (item["conditions"]) {
			policyRules[index]["conditions"]["params"] = item["conditions"]["params"] && isJSON(item["conditions"]["params"]) ? JSON.parse(item["conditions"]["params"]) : [];

			policyRules[index]["conditions"]["children"] &&
			policyRules[index]["conditions"]["children"].map((conditionItem, conditionIndex) => {
				if (conditionItem.type === "alias") {
					let aliasItem = policyRules[index]["conditions"]["children"][conditionIndex];
					let params = [];
					if (conditionItem["params"] && isJSON(conditionItem["params"])) {
						params = JSON.parse(conditionItem["params"]);
					}
					aliasItem["params"] = uniqBy(params, "name");
				}
			});
		}

		if (item["reminder"] && isJSON(item["reminder"])) {
			let arr = JSON.parse(item["reminder"]);
			let obj = {};
			arr.forEach(c=>{
				obj[c.key] = c.code;
			})
			item["reminder"] = obj;
		}

		// 如果是if规则，有子集
		item.children && item.children.map((subItem, subIndex) => {
			policyRules[index]["children"][subIndex]["operationActions"] = subItem["operationActions"] && isJSON(subItem["operationActions"]) ? JSON.parse(subItem["operationActions"]) : [];
			policyRules[index]["children"][subIndex]["triggers"] = subItem["triggers"] && isJSON(subItem["triggers"]) ? JSON.parse(subItem["triggers"]) : [];
			if (subItem["reminder"] && isJSON(subItem["reminder"])) {
				let arr = JSON.parse(subItem["reminder"]);
				let obj = {};
				arr.forEach(c=>{
					obj[c.key] = c.code;
				})
				subItem["reminder"] = obj;
			}
			subItem["conditions"]["children"] && subItem["conditions"]["children"].map((conditionItem, conditionIndex) => {
				if (conditionItem.type === "alias") {
					let aliasItem2 = subItem["conditions"]["children"][conditionIndex];

					let params = [];
					if (conditionItem["params"] && isJSON(conditionItem["params"])) {
						params = JSON.parse(conditionItem["params"]);
					}
					aliasItem2["params"] = uniqBy(params, "name");
				}
			});
		});
	});
	return policyRules;
}
