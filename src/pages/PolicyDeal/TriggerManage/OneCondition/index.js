import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import OneCondition from "./OneCondition";
import { policyDetailLang } from "@/constants/lang";
import { Select, Radio, Row, Col } from "antd";

const RadioGroup = Radio.Group;

class ConditionSetting extends PureComponent {
	// 添加
	addCondition(type) {
		const { policyDealStore, dispatch } = this.props;
		const { modifyImmuno } = policyDealStore.dialogData;
		const { immunoConditions = [] } = modifyImmuno || {};

		let conditionTemp = {};

		if (type === "single") {
			conditionTemp = {
				operator: "==",
				leftValueType: "STRING",
				leftVarType: "context",
				leftVar: null,
				rightVarType: "input",
				rightVar: null
			};
		} else if (type === "group") {
			conditionTemp = {
				logicOperator: "&&",
				children: [
					{
						operator: "==",
						leftValueType: "STRING",
						leftVarType: "context",
						property: null,
						rightVarType: "input",
						rightVar: null
					}
				]
			};
		}
		immunoConditions["children"].push(conditionTemp);
		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				immunoConditions
			}
		});
	};

    // 删除
    deleteCondition = (i) => {
    	const { policyDealStore, dispatch } = this.props;
    	const { modifyImmuno } = policyDealStore.dialogData;
    	const { immunoConditions = [] } = modifyImmuno || {};

    	immunoConditions["children"].splice(i, 1);
    	dispatch({
    		type: "policyDeal/setDialogData",
    		payload: {
    			immunoConditions: immunoConditions
    		}
    	});
    };

    // 修改组的条件
    changeGroupConditionType = (index, value) => {
    	const { policyDealStore, dispatch } = this.props;
    	const { modifyImmuno } = policyDealStore.dialogData;
    	const { immunoConditions = [] } = modifyImmuno || {};
    	immunoConditions["children"][index]["logicOperator"] = value;
    	dispatch({
    		type: "policyDeal/setDialogData",
    		payload: {
    			immunoConditions: immunoConditions
    		}
    	});
    }

    // 修改前提条件
    changeAllConditionType(e) {
    	const { policyDealStore, dispatch } = this.props;
    	const { modifyImmuno } = policyDealStore.dialogData;
    	const { immunoConditions = [] } = modifyImmuno || {};

    	let value = e.target.value;
    	immunoConditions["logicOperator"] = value;
    	dispatch({
    		type: "policyDeal/setDialogData",
    		payload: immunoConditions
    	});

    }

    renderRuleTemplate = (item, index) => {
    	let ConditionDom = [];
    	let ruleType = item.hasOwnProperty("children") ? "group" : "single";
    	if (ruleType === "group") {
    		ConditionDom = item["children"].map((obj, index2) => {
    			let key = item.ruleType + "_" + index[0] + "_" + index2;
    			return (
    				<OneCondition
    					key={key}
    					item={obj}
    					type="group"
    					indexArr={[index, index2]}
    				/>
    			);
    		});
    	} else {
    		let key = item.ruleType + "_" + index;
    		ConditionDom = (
    			<OneCondition
    				key={key}
    				item={item}
    				type="single"
    				indexArr={[index]}
    			/>
    		);
    	}
    	console.log(item);
    	return (
    		<div
    			className={ruleType === "single" ? "one-condition custom-item" : "group-condition custom-item"}
    			key={index}
    		>
    			<Row gutter={8}>
    				<Col span={3} push={1}>
    					{
    						ruleType === "group" &&
                            <Select
                            	value={item.logicOperator}
                            	onChange={(e) => { this.changeGroupConditionType(index, e); }}
                            >
                            	<Option value="&&">
                            		{/* 与 */}
                            		{policyDetailLang.oneCondition("and")}
                            	</Option>
                            	<Option value="||">
                            		{/* 或 */}
                            		{policyDetailLang.oneCondition("or")}
                            	</Option>
                            </Select>
    					}
    				</Col>
    				<Col span={21} push={1}>{ConditionDom}</Col>
    			</Row>
    		</div>
    	);
    }
    render() {
    	let { policyDealStore } = this.props;
    	let { modifyImmuno } = policyDealStore.dialogData;
    	const { immunoConditions = [] } = modifyImmuno || {};
    	return (
    		<Fragment>
    			<Row gutter={8} className="rule-condition">
    				<Col className="gutter-row" span={4}>
    					<div
    						className="gutter-box"
    						style={{ textAlign: "right" }}
    					>
    						{/* lang:执行条件 */}
    						{policyDetailLang.ruleCondition("performsConditional")}：
    					</div>
    				</Col>
    				<Col className="gutter-row" span={20}>
    					<div className="gutter-box">
    						<RadioGroup
    							onChange={this.changeAllConditionType.bind(this)}
    							value={immunoConditions.logicOperator}
    						>
    							<Radio value="&&">
    								{/* lang:满足以下所有条件 */}
    								{policyDetailLang.ruleCondition("mztj1")}
    							</Radio>
    							<Radio value="||">
    								{/* lang:满足以下任意条件 */}
    								{policyDetailLang.ruleCondition("mztj2")}
    							</Radio>
    							{/* lang:以下条件均不满足 */}
    							{/* <Radio value="!&&">
    								{policyDetailLang.ruleCondition("mztj3")}
    							</Radio> */}
    						</RadioGroup>
    					</div>
    				</Col>
    			</Row>
    			<div className="rule-content">
    				{
    					immunoConditions.children &&
                        immunoConditions.children.map((item, index) => {
                        	return this.renderRuleTemplate(item, index);
                        })
    				}
    				<Row gutter={8} className="rule-ctrl">
    					<Col className="gutter-row" span={24} push={4}>
    						<span onClick={this.addCondition.bind(this, "single")}>
    							{/* lang:添加单条条件 */}
    							{policyDetailLang.ruleCondition("add2")}
    						</span>
    						<span onClick={this.addCondition.bind(this, "group")}>
    							{/* lang:添加条件组 */}
    							{policyDetailLang.ruleCondition("add3")}
    						</span>
    					</Col>
    				</Row>
    			</div>
    		</Fragment>
    	);
    }
}
export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(ConditionSetting);
