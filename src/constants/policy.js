const conditionTypeMap = {
	"&&": "满足所有条件",
	"||": "满足任意条件",
	"!&&": "条件均不满足"
};

const policyMode = {
	"FirstMatch": "首次匹配",
	"WorstMatch": "最坏匹配",
	"Weighted": "权重匹配"
};

const conditionOperator = {
	"STRING": [
		{
			name: "==",
			dName: "等于",
			enDName: "equal"
		},
		{
			name: "include",
			dName: "包含",
			enDName: "include"
		},
		{
			name: "prefix",
			dName: "前缀",
			enDName: "prefix"
		},
		{
			name: "exclude",
			dName: "不包含",
			enDName: "exclude"
		},
		{
			name: "!=",
			dName: "不等于",
			enDName: "unequal"
		},
		{
			name: "suffix",
			dName: "后缀",
			enDName: "suffix"
		},
		{
			name: "isnull",
			dName: "为空",
			enDName: "isnull"
		},
		{
			name: "notnull",
			dName: "不为空",
			enDName: "notnull"
		},
		{
			name: "in",
			dName: "存在于",
			enDName: "in"
		},
		{
			name: "notin",
			dName: "不存在于",
			enDName: "notin"
		}
		// {
		// 	name: "belong",
		// 	dName: "属于",
		// 	enDName: "belong"
		// }, {
		// 	name: "notbelong",
		// 	dName: "不属于",
		// 	enDName: "notbelong"
		// }
	],
	"DOUBLE": [
		{
			name: "==",
			dName: "等于",
			enDName: "equal"
		},
		{
			name: "<=",
			dName: "小于等于",
			enDName: "equal or less than"
		},
		{
			name: "!=",
			dName: "不等于",
			enDName: "unequal"
		},
		{
			name: "<",
			dName: "小于",
			enDName: "less than"
		},
		{
			name: ">",
			dName: "大于",
			enDName: "greater than"
		},
		{
			name: ">=",
			dName: "大于等于",
			enDName: "equal or greater than"
		},
		{
			name: "isnull",
			dName: "为空",
			enDName: "isnull"
		},
		{
			name: "notnull",
			dName: "不为空",
			enDName: "notnull"
		},
		{
			name: "in",
			dName: "存在于",
			enDName: "in"
		},
		{
			name: "notin",
			dName: "不存在于",
			enDName: "notin"
		}
		// {
		// 	name: "belong",
		// 	dName: "属于",
		// 	enDName: "belong"
		// }, {
		// 	name: "notbelong",
		// 	dName: "不属于",
		// 	enDName: "notbelong"
		// }
	],
	"INT": [
		{
			name: "==",
			dName: "等于",
			enDName: "equal"
		},
		{
			name: "<=",
			dName: "小于等于",
			enDName: "equal or less than"
		},
		{
			name: "!=",
			dName: "不等于",
			enDName: "unequal"
		},
		{
			name: "<",
			dName: "小于",
			enDName: "less than"
		},
		{
			name: ">",
			dName: "大于",
			enDName: "greater than"
		},
		{
			name: ">=",
			dName: "大于等于",
			enDName: "equal or greater than"
		},
		{
			name: "isnull",
			dName: "为空",
			enDName: "isnull"
		},
		{
			name: "notnull",
			dName: "不为空",
			enDName: "notnull"
		},
		{
			name: "in",
			dName: "存在于",
			enDName: "in"
		},
		{
			name: "notin",
			dName: "不存在于",
			enDName: "notin"
		}
		// {
		// 	name: "belong",
		// 	dName: "属于",
		// 	enDName: "belong"
		// }, {
		// 	name: "notbelong",
		// 	dName: "不属于",
		// 	enDName: "notbelong"
		// }
	],
	"LONG": [
		{
			name: "==",
			dName: "等于",
			enDName: "equal"
		},
		{
			name: "<=",
			dName: "小于等于",
			enDName: "equal or less than"
		},
		{
			name: "!=",
			dName: "不等于",
			enDName: "unequal"
		},
		{
			name: "<",
			dName: "小于",
			enDName: "less than"
		},
		{
			name: ">",
			dName: "大于",
			enDName: "greater than"
		},
		{
			name: ">=",
			dName: "大于等于",
			enDName: "equal or greater than"
		},
		{
			name: "isnull",
			dName: "为空",
			enDName: "isnull"
		},
		{
			name: "notnull",
			dName: "不为空",
			enDName: "notnull"
		},
		{
			name: "in",
			dName: "存在于",
			enDName: "in"
		},
		{
			name: "notin",
			dName: "不存在于",
			enDName: "notin"
		}
		// {
		// 	name: "belong",
		// 	dName: "属于",
		// 	enDName: "belong"
		// }, {
		// 	name: "notbelong",
		// 	dName: "不属于",
		// 	enDName: "notbelong"
		// }
	],
	"DATETIME": [
		{
			name: "==",
			dName: "等于",
			enDName: "equal"
		},
		{
			name: "!=",
			dName: "不等于",
			enDName: "unequal"
		},
		{
			name: "isnull",
			dName: "为空",
			enDName: "isnull"
		},
		{
			name: "notnull",
			dName: "不为空",
			enDName: "notnull"
		},
		{
			name: "in",
			dName: "存在于",
			enDName: "in"
		},
		{
			name: "notin",
			dName: "不存在于",
			enDName: "notin"
		}
	],
	"BOOLEAN": [
		{
			name: "==",
			dName: "等于",
			enDName: "equal"
		},
		{
			name: "!=",
			dName: "不等于",
			enDName: "unequal"
		},
		{
			name: "isnull",
			dName: "为空",
			enDName: "isnull"
		},
		{
			name: "notnull",
			dName: "不为空",
			enDName: "notnull"
		},
		{
			name: "in",
			dName: "存在于",
			enDName: "in"
		},
		{
			name: "notin",
			dName: "不存在于",
			enDName: "notin"
		}
	],
	"ENUM": [
		{
			name: "==",
			dName: "等于",
			enDName: "equal"
		},
		{
			name: "!=",
			dName: "不等于",
			enDName: "unequal"
		},
		{
			name: "isnull",
			dName: "为空",
			enDName: "isnull"
		},
		{
			name: "notnull",
			dName: "不为空",
			enDName: "notnull"
		},
		{
			name: "in",
			dName: "存在于",
			enDName: "in"
		},
		{
			name: "notin",
			dName: "不存在于",
			enDName: "notin"
		}
	],
	"POLICY": [
		{
			name: "==",
			dName: "等于",
			enDName: "equal"
		},
		{
			name: "!=",
			dName: "不等于",
			enDName: "unequal"
		},
		{
			name: "isnull",
			dName: "为空",
			enDName: "isnull"
		},
		{
			name: "notnull",
			dName: "不为空",
			enDName: "notnull"
		},
		{
			name: "in",
			dName: "存在于",
			enDName: "in"
		},
		{
			name: "notin",
			dName: "不存在于",
			enDName: "notin"
		}
	]
};

const inputRightValueType = [
	{
		name: "input",
		dName: "常量"
	},
	{
		name: "context",
		dName: "变量"
	}
];

// continuationCount连续次数指标用的
const countOperatorStringMap = [
	{
		name: "==",
		dName: "等于",
		enDName: "equal"
	},
	{
		name: "include",
		dName: "包含",
		enDName: "include"
	},
	{
		name: "prefix",
		dName: "前缀",
		enDName: "prefix"
	},
	{
		name: "exclude",
		dName: "不包含",
		enDName: "exclude"
	},
	{
		name: "!=",
		dName: "不等于",
		enDName: "unequal"
	},
	{
		name: "suffix",
		dName: "后缀",
		enDName: "suffix"
	}
];
const countOperatorIntMap = [
	{
		name: "==",
		dName: "等于",
		enDName: "equal"
	},
	{
		name: "<=",
		dName: "小于等于",
		enDName: "equal or less than"
	},
	{
		name: "!=",
		dName: "不等于",
		enDName: "unequal"
	},
	{
		name: "<",
		dName: "小于",
		enDName: "less than"
	},
	{
		name: ">",
		dName: "大于",
		enDName: "greater than"
	},
	{
		name: ">=",
		dName: "大于等于",
		enDName: "equal or greater than"
	}
];
const countOperatorBoolMap = [
	{
		name: "==",
		dName: "等于",
		enDName: "equal"
	},
	{
		name: "!=",
		dName: "不等于",
		enDName: "unequal"
	}
];
const countOperatorDateMap = [
	{
		name: "==",
		dName: "等于",
		enDName: "equal"
	},
	{
		name: "!=",
		dName: "不等于",
		enDName: "unequal"
	}
];
const countOperatorEnumMap = [
	{
		name: "==",
		dName: "等于",
		enDName: "equal"
	},
	{
		name: "!=",
		dName: "不等于",
		enDName: "unequal"
	}
];
const policyStatusMap = {
	"wait_commit": {
		text: "待提交",
		color: "blue"
	},
	"import_commit": {
		text: "导入待提交",
		color: "blue"
	},
	"wait_review": {
		text: "待审批",
		color: "red"
	},
	"publish": {
		text: "已上线",
		color: "purple"
	},
	"offline": {
		text: "已下线",
		color: ""
	}
};

const policyStatusMap2 = {
	"wait_commit": {
		text: "Wait Submit",
		color: "blue"
	},
	"import_commit": {
		text: "Import to be submitted",
		color: "blue"
	},
	"wait_review": {
		text: "Wait Check",
		color: "red"
	},
	"publish": {
		text: "Published",
		color: "purple"
	}
};

const replayTaskStatusMap = {
	"0": {
		dName: "待运行",
		enDName: "wait",
		dName2: "回测待运行",
		enDName2: "replay wait",
		color: "blue",
		bgColor: "#1890ff"
	},
	"3": {
		dName: "启动中",
		enDName: "start up",
		dName2: "回测启动中",
		enDName2: "start ing",
		color: "cyan",
		bgColor: "#13c2c2"
	},
	"5": {
		dName: "运行中",
		enDName: "ing",
		dName2: "回测运行中",
		enDName2: "replay ing",
		color: "geekblue",
		bgColor: "#2f54eb"
	},
	"10": {
		dName: "完成",
		enDName: "done",
		dName2: "回测完成",
		enDName2: "replay done",
		color: "green",
		bgColor: "#52c41a"
	},
	"15": {
		dName: "失败",
		enDName: "fail",
		dName2: "回测失败",
		enDName2: "replay fail",
		color: "volcano",
		bgColor: "#f18c6b"
	},
	"20": {
		dName: "终止",
		enDName: "terminal",
		dName2: "回测终止",
		enDName2: "replay terminal",
		color: "red",
		bgColor: "#f5222d"
	}
};

const excludeRuleTemplate = ["multiDimList/customList"];
// const excludeRuleTemplate = ["watchlist/customList", "multiDimList/customList"];

export default {
	policyMode,
	conditionTypeMap,
	conditionOperator,
	inputRightValueType,
	countOperatorStringMap,
	countOperatorIntMap,
	countOperatorBoolMap,
	countOperatorDateMap,
	countOperatorEnumMap,
	policyStatusMap,
	policyStatusMap2,
	replayTaskStatusMap,
	excludeRuleTemplate
};
