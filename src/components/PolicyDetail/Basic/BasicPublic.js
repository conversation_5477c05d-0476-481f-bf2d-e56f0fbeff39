import React from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Input, Select, Form, Row, Col, Radio, message, Button } from "antd";
import { publicPolicyEditorAPI } from "@/services";
import { CommonConstants, PolicyConstants } from "@/constants";
import { publicPolicyListLang, policyDetailLang } from "@/constants/lang";
import { checkFunctionHasPermission } from "@/utils/permission";
import SelectEffectScope from "@/components/PublicPolicy/SelectEffectScope/SelectEffectScope";
import WeightModeEditor from "../../WeightModeEditor";

const Option = Select.Option;
const { TextArea } = Input;
const InputGroup = Input.Group;
const formItemLayout = {
	labelCol: {
		xs: { span: 24 },
		sm: { span: 5 }
	},
	wrapperCol: {
		xs: { span: 24 },
		sm: { span: 18 }
	}
};
const { countOperatorIntMap } = PolicyConstants;

class BasicPublic extends React.PureComponent {
	state = {
		global_app: null // 全局应用
	}
	constructor(props) {
		super(props);
	}
	componentWillMount() {
		let { globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		const global_app = publicPolicyScene && publicPolicyScene.length > 0 && publicPolicyScene.find((v) => v.name === "GLOBAL_APP") || {}; // 全局应用
		this.setState({
			global_app
		});
	}

	changeDataHandle = (field, e, type) => {
		let { dispatch, policyDetailStore } = this.props;
		let { policyDetail } = policyDetailStore;
		let value = e;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		policyDetail[field] = value;
		dispatch({
			type: "policyDetail/setPolicyDetail",
			payload: {
				policyDetail: policyDetail
			}
		});
	}

	// 保存编辑
	savePolicyHandle = () => {
		this.props.form.validateFields((err) => {
			if (!err) {
				const { global_app } = this.state;
				let { policyDetailStore, dispatch } = this.props;
				let { policyDetail } = policyDetailStore;
				const { uuid, scene, effectScope, name, riskType, mode, terminateOperator, terminate, terminateThreshold, description } = policyDetail;
				let params = {
					uuid,
					name,
					mode,
					riskType,
					terminateOperator,
					terminate: String(terminate) === "1",
					terminateThreshold,
					description
				};
				if (mode === "Weighted") {
					params.dealTypeMappings = policyDetail.dealTypeMappings ? JSON.stringify(policyDetail.dealTypeMappings, null, 4) : null;
					if (policyDetail.dealTypeMappings && policyDetail.dealTypeMappings.length > 0) {
						let mapHasEmpty = false;
						for (let i = 0; i < policyDetail.dealTypeMappings.length; i++) {
							let mapItem = policyDetail.dealTypeMappings[i];
							if (!mapItem.dealType || !mapItem.score) {
								mapHasEmpty = true;
								break;
							}
						}
						if (mapHasEmpty) {
							message.warning(publicPolicyListLang.publicPolicyModal("riskThresholdEmptyTip")); // 风险阈值配置存在空值，请补充完整！
							return;
						}
					}
				};

				let sceneNew = [];
				if (effectScope === "set") {
					sceneNew = scene;
					if (typeof sceneNew === "string") {
						sceneNew = JSON.parse(sceneNew);
					}
				} else {
					sceneNew = [{
						appName: global_app.name,
						eventList: [global_app.eventList && global_app.eventList[0].name]
					}];
				};
				params.scene = JSON.stringify(sceneNew);

				publicPolicyEditorAPI.modifyPublicPolicy(params).then(res => {
					if (res.success) {
						dispatch({
							type: "policyDetail/changePolicyStatus",
							payload: {
								uuid: policyDetail.uuid
							}
						});
						message.success(publicPolicyListLang.publicPolicyModal("modifyPolicySetSuccessTip")); // 修改公共策略成功
						dispatch({
							type: "global/getAllMap",
							payload: {}
						});
					} else {
						message.error(res.message);
					}

				}).catch(err => {
					console.log(err);
				});
			}
		});
	}

	changeWeightMode = (field, value, index) => {
		let { dispatch, policyDetailStore } = this.props;
		let { policyDetail } = policyDetailStore;

		policyDetail["dealTypeMappings"][index][field] = value;
		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				policyDetail: policyDetail
			}
		});
	}

	addDeleteItem = (type, index) => {
		let { dispatch, policyDetailStore } = this.props;
		let { policyDetail } = policyDetailStore;

		let temp = {
			"score": null,
			"dealType": null
		};

		if (type === "add") {
			policyDetail["dealTypeMappings"].splice(index + 1, 0, temp);
		} else if (type === "delete") {
			policyDetail["dealTypeMappings"].splice(index, 1);
		}

		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				policyDetail: policyDetail
			}
		});
	}

	render() {
		let { policyDetailStore, globalStore, form } = this.props;
		const { getFieldDecorator } = form;
		let { allMap, policyModel, personalMode } = globalStore;
		let { policyDetail } = policyDetailStore;
		const { publicPolicyRiskSelect = [], dealTypeList = [] } = allMap || {};
		let { lang } = personalMode;
		const { effectScope, name, riskType, mode, dealTypeMappings, terminateOperator, terminate, terminateThreshold, description } = policyDetail;
		let { scene } = { ...policyDetail };
		const { global_app } = this.state;
		if (typeof scene === "string") {
			scene = JSON.parse(scene);
		}
		return (
			<Form {...formItemLayout} className="modify-public-policy basic-form">
				<Row style={{ "height": "auto", "overflow": "inherit" }}>
					<Col span="24">
						{/* 生效范围 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("effectScope")}>
							{
								getFieldDecorator("effectScope", {
									initialValue: effectScope || undefined,
									rules: [
										{
											required: true,
											message: publicPolicyListLang.publicPolicyModal("enterEffectScope") // 请选择生效范围
										}
									]
								})(
									<Select
										placeholder={publicPolicyListLang.publicPolicyModal("enterEffectScope")} // 请选择生效范围
										onChange={(e) => { this.changeDataHandle("effectScope", e, "select"); }}
										value={effectScope || undefined}
									>
										{
											global_app &&
											<Option value={global_app.name}>{global_app.dName}</Option>
										}
										{/* 自定义 */}
										<Option value="set">{publicPolicyListLang.publicPolicyModal("custom")}</Option>
									</Select>
								)
							}
						</Form.Item>
						{
							effectScope === "set" &&
							// 自定义生效范围
							<Form.Item label={publicPolicyListLang.publicPolicyModal("customEffectScope")}>
								{
									getFieldDecorator("scene", {
										initialValue: scene || undefined,
										rules: [
											{
												required: true,
												message: publicPolicyListLang.publicPolicyModal("enterCustomEffectScope") // 请选择自定义生效范围
											}
										]
									})(
										<SelectEffectScope
											scene={scene || []}
											changeField={this.changeDataHandle.bind(this)}
										/>
									)
								}
							</Form.Item>
						}
					</Col>
					<Col span="24">
						{/* 策略名称 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("policyName")}>
							{
								getFieldDecorator("name", {
									initialValue: name || undefined,
									rules: [
										{
											required: true,
											message: publicPolicyListLang.publicPolicyModal("enterPolicyName")// "请输入策略名称"
										},
										{
											max: 72,
											message: publicPolicyListLang.publicPolicyModal("max72")// "长度控制在72个字符长度"
										},
										{
											pattern: /^[A-Za-z0-9\u4E00-\u9FA5\_]{1,72}$/,
											message: publicPolicyListLang.publicPolicyModal("policyRegTip") // "请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合"
										}
									]
								})(
									<Input
										value={name || undefined}
										placeholder={publicPolicyListLang.publicPolicyModal("enterPolicyName")} // "请输入策略名称"
										onChange={(e) => { this.changeDataHandle("name", e, "input"); }}
									/>
								)
							}
						</Form.Item>
					</Col>
					<Col span="24">
						{/* 风险类型 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("riskType")}>
							{
								getFieldDecorator("riskType", {
									initialValue: riskType || undefined,
									rules: [
										{
											required: true,
											message: publicPolicyListLang.publicPolicyModal("enterRiskType")// "请选择风险类型"
										}
									]
								})(
									<Select
										name="riskType"
										placeholder={publicPolicyListLang.publicPolicyModal("enterRiskType")} // 请选择风险类型
										onChange={(e) => { this.changeDataHandle("riskType", e, "select"); }}
										value={riskType || undefined}
									>
										{
											publicPolicyRiskSelect &&
											publicPolicyRiskSelect.map((item, index) => {
												return (
													<Option value={item.name} key={index}>
														{item.dName}
													</Option>
												);
											})
										}
									</Select>
								)
							}
						</Form.Item>
					</Col>
					<Col span="24">
						{/* 策略模式 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("policyMode")}>
							{
								getFieldDecorator("mode", {
									initialValue: mode || undefined,
									rules: [
										{
											required: true,
											message: publicPolicyListLang.publicPolicyModal("selectPolicyMode") // "请选择策略模式"
										}
									]
								})(
									<Select
										disabled
										name="mode"
										value={mode || undefined}
										placeholder={publicPolicyListLang.publicPolicyModal("selectPolicyMode")} // "请选择策略模式"
										onChange={(e) => { this.changeDataHandle("mode", e, "select"); }}
									>
										{
											policyModel && policyModel.map((item, index) => {
												return (
													<Option value={item.name} key={index}>
														{item.dName}
													</Option>
												);
											})
										}
									</Select>
								)}
						</Form.Item>
					</Col>
					{
						mode === "Weighted" &&
						<Col span="24">
							{/* lang:风险阈值 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("riskThreshold")}>
								{
									getFieldDecorator("dealTypeMappings", {
										initialValue: dealTypeMappings || undefined,
										rules: [
											{
												required: true,
												message: publicPolicyListLang.publicPolicyModal("enterRiskThreshold") // "请设置风险阈值"
											}
										]
									})(
										<WeightModeEditor
											dealTypeMappings={dealTypeMappings}
											onChange={this.changeWeightMode.bind(this)}
											operateItem={this.addDeleteItem.bind(this)}
										/>
									)
								}
							</Form.Item>
						</Col>
					}
					<Col span="24">
						{/* 是否中断 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("interrupted")}>
							{
								getFieldDecorator("terminate", {
									initialValue: terminate || undefined,
									rules: [
										{
											required: true,
											message: publicPolicyListLang.publicPolicyModal("enterBreakCon") // 请选择中断条件
										}
									]
								})(
									<Radio.Group
										value={String(terminate)}
										onChange={(e) => { this.changeDataHandle("terminate", e, "input"); }}
									>
										{/* 不中断 */}
										<Radio value="0">{publicPolicyListLang.publicPolicyModal("noInterruption")}</Radio>
										{/* 中断 */}
										<Radio value="1">{publicPolicyListLang.publicPolicyModal("interruption")}</Radio>
									</Radio.Group>
								)}
						</Form.Item>
					</Col>
					{
						String(terminate) === "1" &&
						<Col span="24">
							{/* 中断条件 */}
							<Form.Item label={publicPolicyListLang.publicPolicyModal("interruptCondition")}>
								<InputGroup compact>
									{/* 风险决策结果 */}
									<Input value={publicPolicyListLang.publicPolicyModal("riskResult")} style={{ "width": "25%" }} disabled />
									<Select
										style={{ "width": "35%" }}
										placeholder={publicPolicyListLang.publicPolicyModal("enterOperator")} // 请选择中断操作符
										value={terminateOperator || undefined}
										onChange={(e) => { this.changeDataHandle("terminateOperator", e, "select"); }}
									>
										{
											countOperatorIntMap.map((v) => {
												return <Option value={v.name}>{lang === "en" ? v.enDName : v.dName}</Option>;
											})
										}
									</Select>
									{
										getFieldDecorator("terminateThreshold", {
											initialValue: terminateThreshold || undefined,
											rules: [
												{
													required: true,
													message: publicPolicyListLang.publicPolicyModal("enterRes") // 请设置决策结果
												}
											]
										})(
											<Select
												style={{ "width": "40%", "display": terminateOperator ? "inherit" : "none" }}
												placeholder={publicPolicyListLang.publicPolicyModal("enterBreakCon")} // 请选择中断条件
												value={terminateThreshold || undefined}
												onChange={(e) => { this.changeDataHandle("terminateThreshold", e, "select"); }}
											>
												{
													dealTypeList &&
													dealTypeList.length > 0 &&
													dealTypeList.map((v) => {
														return <Option value={v.name}>{lang === "en" ? v.enDName : v.dName}</Option>;
													})
												}
											</Select>
										)
									}
								</InputGroup>
							</Form.Item>
						</Col>
					}
					<Col span="24">
						{/* 描述 */}
						<Form.Item label={publicPolicyListLang.publicPolicyModal("description")}>
							{
								getFieldDecorator("description", {
									initialValue: description || undefined,
									rules: [
										{
											max: 200,
											message: publicPolicyListLang.publicPolicyModal("max200") // 字符不超过200
										}
									]
								})(
									<TextArea
										value={description || undefined}
										onChange={(e) => { this.changeDataHandle("description", e, "input"); }}
										placeholder={publicPolicyListLang.publicPolicyModal("enterPolicyDescription")} // 请输入描述
										rows="3"
									/>
								)
							}
						</Form.Item>
					</Col>

				</Row>
				<Row gutter={CommonConstants.gutterSpan} className="mt10" style={{ clear: "both" }}>
					<Col span={5}></Col>
					<Col span={18} className="basic-info-btns">
						<Button type="primary" onClick={() => {
							if (checkFunctionHasPermission("ZB0101", "editorModifyPolicy")) {
								this.savePolicyHandle();
							} else {
								message.warning(policyDetailLang.basicSetup("noPermission")); // 无权限操作
							}
						}}>
							{/* 保存 */}
							{policyDetailLang.basicSetup("save")}
						</Button>
						<Button onClick={() => {
							let { dispatch } = this.props;

							if (process.env.SYS_ENV === "development") {
								dispatch(routerRedux.push("/policy/publicPolicyList?currentTab=2"));
							} else {
								dispatch(routerRedux.push("/index/policy/publicPolicyList?currentTab=2"));
							}
						}}>
							{/* 返回 */}
							{policyDetailLang.basicSetup("goBack")}
						</Button>
					</Col>
				</Row>
			</Form >
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(Form.create({ name: "edit-public-policy" })(BasicPublic));
