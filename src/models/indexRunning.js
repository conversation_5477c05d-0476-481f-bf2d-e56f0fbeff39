import { message } from "antd";
import { indexRunningAPI, policyDetailAPI } from "@/services";
import { isJSON } from "@/utils/isJSON";

export default {
	namespace: "indexRunning",
	state: {
		listLoad: true,
		indexList: [],
		indexListReady: false,
		curPage: 1,
		pageSize: 20,
		total: 0,
		pages: 0,
		indexPages: {
			// 多个页面共同使用运行区只读，需要配置不同的数据源
			runningArea: {
				indexList: [],
				indexListReady: false,
				curPage: 1,
				pageSize: 20,
				total: 0,
				pages: 0
			},
			versionModal: {
				indexList: [],
				indexListReady: false,
				curPage: 1,
				pageSize: 20,
				total: 0,
				pages: 0
			},
			policyDetail: {
				indexList: [],
				indexListReady: false,
				curPage: 1,
				pageSize: 20,
				total: 0,
				pages: 0
			},
			approvalTask: {
				indexList: [],
				indexListReady: false,
				curPage: 1,
				pageSize: 20,
				total: 0,
				pages: 0
			}
		},
		searchParams: {
			sceneType: null,
			event: null,
			policyUuid: null,
			ruleUuid: null,
			calcType: null,
			name: null
		},
		indexActiveKey: [],
		currentIndexId: null,
		indexActiveIndex: null,
		editIndexMap: {},
		dialogShow: {
			indexCiteDrawer: false,
			indexVersion: false
		},
		dialogData: {
			indexCiteDrawerData: {
				indexId: null,
				item: null,
				dataList: []
			},
			indexVersion: {
				switchType: "",
				id: null
			}
		},
		closeZbRes: [],
		closeZbDialogShow: false
	},
	effects: {
		*getSalaxyList({ payload }, { call, put }) {
			yield put({
				type: "setAttrValue",
				payload: {
					listLoad: true
				}
			});
			let response = yield call(indexRunningAPI.getSalaxyList, payload);
			yield put({
				type: "setAttrValue",
				payload: {
					listLoad: false
				}
			});
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initSalaxyList",
				payload: {
					response: response,
					pageName: "runningArea"
				}
			});
		},
		*getIndexVersionList({ payload }, { call, put }) {
			let response = yield call(indexRunningAPI.getIndexVersionList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initSalaxyList",
				payload: {
					response: response,
					pageName: "versionModal"
				}
			});
		},
		*getPolicyCiteIndexList({ payload }, { call, put }) {
			let response = yield call(policyDetailAPI.getPolicyCiteIndexList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initSalaxyList",
				payload: {
					response: response,
					pageName: "policyDetail"
				}
			});
		},
		// 批量删除
		*closeOnlineZb({ payload }, { call, put }) {
			let response = yield call(indexRunningAPI.batchCloseOnlineZb, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "setAttrValue",
				payload: {
					closeZbRes: response.data || [],
					closeZbDialogShow: true
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}
			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		initSalaxyList(state, { payload }) {
			let { indexPages, indexList, curPage, pageSize, total, pages, indexListReady } = state;
			let { response, pageName } = payload;
			let data = response.data || null;
			let list = data["dataList"] || [];

			curPage = data.curPage;
			pageSize = data.pageSize;
			total = data.total;
			pages = data.pages;
			indexList = list || [];
			indexList && indexList.map((item, index) => {
				indexList[index]["filterList"] = item["filterStr"] && isJSON(item["filterStr"]) ? JSON.parse(item["filterStr"]) : [];
				indexList[index]["attachFieldsObj"] = item["attachFields"] && isJSON(item["attachFields"]) ? JSON.parse(item["attachFields"]) : {};
				indexList[index]["sceneList"] = item["scene"] && isJSON(item["scene"]) ? JSON.parse(item["scene"]) : [];
			});
			indexListReady = true;
			let currentPage = indexPages[pageName];
			currentPage["indexList"] = indexList;
			currentPage["curPage"] = curPage;
			currentPage["pageSize"] = pageSize;
			currentPage["total"] = total;
			currentPage["pages"] = pages;
			currentPage["indexListReady"] = indexListReady;

			return {
				...state,
				indexPages: indexPages
				// indexList: indexList,
				// curPage: curPage,
				// pageSize: pageSize,
				// total: total,
				// pages: pages,
				// indexListReady: indexListReady,
			};
		},
		initCheckedSalaxyList(state, { payload }) {
			const { indexList, pageName } = payload;
			const newIndexPages = Object.assign({}, state.indexPages, {
				[pageName]: Object.assign({}, state.indexPages[pageName], {
					indexList
				})
			});
			return {
				...state,
				indexPages: newIndexPages
			};
		}
	}
};
