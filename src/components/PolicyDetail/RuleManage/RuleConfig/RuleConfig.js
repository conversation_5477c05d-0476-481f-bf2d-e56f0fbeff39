import {PureComponent} from "react";
import {connect} from "dva";
import {Form, Button, message} from "antd";
import {policyDetailAPI} from "@/services";
import RuleAttr from "./Inner/RuleAttr";
import RuleCondition from "./Inner/RuleConditon";
import RuleOperation from "./Inner/RuleOperation";
import {isJSON} from "@/utils/isJSON";
import {checkFunctionHasPermission} from "@/utils/permission";
import {policyDetailLang} from "@/constants/lang";
import {trim, uniqBy, get, isArray} from "lodash";

class RuleConfig extends PureComponent {

	constructor(props) {
		super(props);
		this.savePolicyRule = this.savePolicyRule.bind(this);
		this.modifyRuleName = this.modifyRuleName.bind(this);
		this.getTemplateFormatObj = this.getTemplateFormatObj.bind(this);
	}

	savePolicyRule() {
		let {policyDetailStore, globalStore, dispatch, ruleIndexArr} = this.props;
		let {allMap} = globalStore;
		let {policyDetail, policyRules, upLimit, downLimit} = policyDetailStore;
		let currentRule = null;
		let acceptDealType = allMap.dealTypeList && allMap.dealTypeList.find(item => item.name === "Accept");
		let defaultDealType = acceptDealType ? acceptDealType.uuid : null;
		console.log(policyDetail, "policyDetail");
		if (ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
		} else if (ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
		}

		// 检测操作配置是否有空值
		if (currentRule.operationActions && currentRule.operationActions.length > 0) {
			let operationActionsHasEmpty = false;
			for (let i = 0; i < currentRule.operationActions.length; i++) {
				let operationActionsItem = currentRule.operationActions[i];
				if (!operationActionsItem.left || !operationActionsItem.right) {
					operationActionsHasEmpty = true;
					break;
				}
			}
			if (operationActionsHasEmpty) {
				message.error(policyDetailLang.ruleConfig("tip1")); // 操作配置存在空值，请补充完整！
				return;
			}
		}
		// 检测规则名称是否为空
		if (!trim(currentRule.name)) {
			message.error(policyDetailLang.ruleConfig("nameEmptyTip")); // 规则名称不能为空
			return;
		}
		// 规则名称长度最大80个字符
		if (trim(currentRule.name) && trim(currentRule.name).length > 80) {
			message.error(policyDetailLang.ruleConfig("nameLengthTip")); // 规则名称长度不能超过80个字符
			return;
		}
		// 检测条件配置是否有空值
		if (currentRule.conditions && currentRule.conditions.children && currentRule.conditions.children.length > 0) {
			let conditionsHasEmpty = false;
			let customTemplateMsg = null;
			let conditionsChildren = currentRule.conditions.children;
			for (let i = 0; i < conditionsChildren.length; i++) {
				let singleCondition = conditionsChildren[i];
				if (singleCondition["type"] && singleCondition["type"] === "alias") {
					// 如果是规则模板
					if (singleCondition["property"]) {
						// TODO 规则模板暂时隐藏空校验
						// let customTemplateParams = this.getTemplateFormatObj(singleCondition);
						// if (customTemplateParams && customTemplateParams.length > 0) {
						// 	for (let c = 0; c < customTemplateParams.length; c++) {
						// 		if (!customTemplateParams[c]["value"]) {
						// 			conditionsHasEmpty = true;
						// 			let componentType = customTemplateParams[c]["componentType"];
						// 			if (componentType === "input") {
						// 				customTemplateMsg = customTemplateParams[c]["labelText"] + policyDetailLang.ruleConfig("tip2"); // 输入框为空，请补充完整！
						// 			} else if (componentType === "select" || componentType === "radio" || componentType === "checkbox") {
						// 				customTemplateMsg = customTemplateParams[c]["labelText"] + policyDetailLang.ruleConfig("tip3"); // 选项为空，请选择！
						// 			} else {
						// 				customTemplateMsg = customTemplateParams[c]["labelText"] + policyDetailLang.ruleConfig("tip4"); // 存在空值，请补充完整！
						// 			}
						// 			break;
						// 		}
						// 	}
						// }
					}
				} else {
					// 如果是单条添加或条件组
					if (singleCondition["children"] && singleCondition["children"].length > 0) {
						// 如果是条件组
						for (let j = 0; j < singleCondition["children"].length; j++) {
							let currentLineObj = singleCondition["children"][j];
							if (currentLineObj["operator"] && currentLineObj["operator"] !== "isnull" && currentLineObj["operator"] !== "notnull") {
								if (!currentLineObj["property"] || !currentLineObj["operator"] || !currentLineObj["value"]) {
									conditionsHasEmpty = true;
									break;
								}
							}
							if (!currentLineObj["operator"]) {
								conditionsHasEmpty = true;
								break;
							}
						}
					}
					if (!singleCondition["children"] || (singleCondition["children"] && singleCondition["children"].length === 0)) {
						// 如果不是条件组规则导
						if (singleCondition["operator"] && singleCondition["operator"] !== "isnull" && singleCondition["operator"] !== "notnull") {
							if (!singleCondition["property"] || !singleCondition["operator"] || !singleCondition["value"]) {
								conditionsHasEmpty = true;
								break;
							}
						}
						if (!singleCondition["operator"]) {
							conditionsHasEmpty = true;
							break;
						}
					}
				}
			}
			if (conditionsHasEmpty) {
				if (customTemplateMsg) {
					message.error(customTemplateMsg);
					return;
				} else {
					message.error(policyDetailLang.ruleConfig("tip5")); // 条件配置存在空值，请补充完整！
					return;
				}
			}
		}

		// 检测规则脚本是否为空
		if (currentRule.template === "common/script" && !currentRule.script) {
			message.error(policyDetailLang.ruleConfig("scriptTip")); // 规则脚本不能为空
			return;
		}

		// 如果存在上下限场景做校验
		const {weightUpperLimit, weightLowerLimit} = currentRule || {};
		if (upLimit && !weightUpperLimit) {
			message.error(policyDetailLang.ruleConfig("limitUpPlaceHolder")); // 请输入上限
			return;
		}
		if (downLimit && !weightLowerLimit) {
			message.error(policyDetailLang.ruleConfig("limitDownPlaceHolder")); // 请输入下限
			return;
		}
		if (weightUpperLimit && weightLowerLimit && Number(weightLowerLimit) > Number(weightUpperLimit)) {
			message.error(policyDetailLang.ruleConfig("lowLessUp")); // 上限不能小于下限
			return;
		}

		const conditions = get(currentRule, "conditions") || {};

		if (currentRule.template === "common/custom" && !(conditions && conditions.children && conditions.children.length > 0)) {
			message.error(policyDetailLang.ruleConfig("setCondition")); // "请设置执行条件"
			return;
		}

		if (conditions.children) {
			conditions.children = conditions.children.map(({params = [], ...rest}) => ({
				...rest,
				params: isArray(params) && rest.type === "alias" ? uniqBy(params || [], "name") : ""
			}));

			// 时间点比较
			const timePointComparsion = conditions.children.filter((v) => {
				return v.property === "time/timePointComparison";
			}) || [];
			if (timePointComparsion && timePointComparsion.length > 0) {
				let lowerLimit = 0;
				let upperLimit = 0;
				(timePointComparsion[0].params || []).map((params) => {
					if (params.name === "lowerLimit") {
						lowerLimit = params.value;
					}
					if (params.name === "upperLimit") {
						upperLimit = params.value;
					}
				});
				if (Number(lowerLimit) > Number(upperLimit)) {
					message.error(policyDetailLang.ruleConfig("lowLessUp")); // 上限不能小于下限
					return;
				}
			}
		}

		let params = {
			name: currentRule.name,
			displayOrder: ruleIndexArr.length === 2 ? ruleIndexArr[1] : ruleIndexArr[0],
			condition: JSON.stringify(conditions, null, 4),
			priority: currentRule.priority || "",
			operationActions: JSON.stringify(currentRule.operationActions, null, 4),
			customId: ""
		};

		// 脚本规则
		if (currentRule.script) {
			params.script = currentRule.script;
		}
		if (weightUpperLimit) {
			params.weightUpperLimit = weightUpperLimit;
		}
		if (weightLowerLimit) {
			params.weightLowerLimit = weightLowerLimit;
		}
		if (currentRule.unSave) {
			// 新增规则
			params["policyUuid"] = currentRule.fkPolicyUuid;
			params["template"] = currentRule.template;
			if (currentRule["ifClause"] && currentRule["ifClause"] === "IF") {
				params["dataFlow"] = "IF";
			}
			if (ruleIndexArr.length === 2) {
				let parentRule = policyRules[ruleIndexArr[0]];
				params["dataFlowParentUuid"] = parentRule.uuid;
			}
		} else {
			// 修改规则
			params["uuid"] = currentRule.uuid;
		}

		if (policyDetail.mode === "Weighted") {
			params["baseWeight"] = currentRule.baseWeight;
			params["weightRatio"] = currentRule.weightRatio;
			params["weightIndex"] = currentRule.weightIndex;
			params["weightType"] = currentRule.weightType;
			params["weightOperator"] = currentRule.weightOperator;
		} else {
			params["fkDealTypeUuid"] = currentRule.fkDealTypeUuid;
		}

		if (currentRule["ifClause"] && currentRule["ifClause"] === "IF") {
			params["fkDealTypeUuid"] = defaultDealType;
		}

		let {reminder = {}} = currentRule;
		let arr = [];
		if (reminder && Object.prototype.toString.call(reminder) === "[object Object]") {
			for (let key in reminder) {
				if (reminder[key]) {
					arr.push({
						"key": key,
						"code": reminder[key]
					});
				}
			}
		}

		reminder = arr;
		//
		params["reminder"] = JSON.stringify(reminder);

		console.log(params, currentRule, "params");
		// return;
		if (currentRule.unSave) {
			// 新增规则
			// 给规则操作loading添加状态
			dispatch({
				type: "policyDetail/setAttrValue",
				payload: {
					ruleOperatorLoading: true
				}
			});
			policyDetailAPI.addPolicyRule(params).then(res => {
				if (res.success) {
					message.success(policyDetailLang.ruleConfig("tip6")); // 新增规则成功
					let ruleData = res.data;
					ruleData["operationActions"] = ruleData["operationActions"] && isJSON(ruleData["operationActions"]) ? JSON.parse(ruleData["operationActions"]) : [];
					ruleData["triggers"] = ruleData["triggers"] && isJSON(ruleData["triggers"]) ? JSON.parse(ruleData["triggers"]) : [];
					if (ruleData["conditions"]) {
						ruleData["conditions"]["params"] = ruleData["conditions"]["params"] && isJSON(ruleData["conditions"]["params"]) ? JSON.parse(ruleData["conditions"]["params"]) : [];
						ruleData["conditions"]["children"] && ruleData["conditions"]["children"].map((conditionItem, conditionIndex) => {
							// 变量children
							if (conditionItem.type === "alias") {
								conditionItem["params"] = conditionItem["params"] && isJSON(conditionItem["params"]) ? JSON.parse(conditionItem["params"]) : [];
							}
						});
					}

					let a = ruleData["reminder"] ? JSON.parse(ruleData["reminder"]) : [];
					let obj = {};
					a.forEach(c=>{
						obj[c.key] = c.code;
					})
					ruleData["reminder"] = obj;

					if (ruleIndexArr.length === 1) {
						policyRules.splice(ruleIndexArr[0], 1, ruleData);
					} else if (ruleIndexArr.length === 2) {
						policyRules[ruleIndexArr[0]]["children"].splice(ruleIndexArr[1], 1, ruleData);
					}
					// 设置依然展开当前规则状态
					dispatch({
						type: "policyDetail/setAttrValue",
						payload: {
							ruleActiveKey: [ruleData.uuid],
							currentRuleUuid: ruleData.uuid
						}
					});
					dispatch({
						type: "policyDetail/setPolicyRule",
						payload: {
							policyRules: policyRules
						}
					});
					// 更新规则成功后刷新策略审批状态
					dispatch({
						type: "policyDetail/changePolicyStatus",
						payload: {
							uuid: policyDetail.uuid
						}
					});
				} else {
					if (String(res.code) === "400007") {
						dispatch({
							type: "policyDetail/setAttrValue",
							payload: {
								ruleErrMsg: res.message
							}
						});
					} else {
						message.error(res.message);
					}

				}
				// 给规则操作loading添加状态
				dispatch({
					type: "policyDetail/setAttrValue",
					payload: {
						ruleOperatorLoading: false
					}
				});
			}).catch(err => {
				// 给规则操作loading添加状态
				dispatch({
					type: "policyDetail/setAttrValue",
					payload: {
						ruleOperatorLoading: false
					}
				});
				console.log(err);
			});
		} else {
			// 更新规则
			// 给规则操作loading添加状态
			dispatch({
				type: "policyDetail/setAttrValue",
				payload: {
					ruleOperatorLoading: true
				}
			});
			policyDetailAPI.modifyPolicyRule(params).then(res => {
				if (res.success) {
					message.success(policyDetailLang.ruleConfig("tip7")); // 更新规则成功
					currentRule.hasModify = false;
					dispatch({
						type: "policyDetail/setPolicyRule",
						payload: {
							policyRules: policyRules
						}
					});
					// 更新规则成功后刷新策略审批状态
					dispatch({
						type: "policyDetail/changePolicyStatus",
						payload: {
							uuid: policyDetail.uuid
						}
					});
				} else {
					console.log(res.message);
					if (String(res.code) === "400007") {
						dispatch({
							type: "policyDetail/setAttrValue",
							payload: {
								ruleErrMsg: res.message
							}
						});
					} else {
						message.error(res.message);
					}
				}
				// 给规则操作loading添加状态
				dispatch({
					type: "policyDetail/setAttrValue",
					payload: {
						ruleOperatorLoading: false
					}
				});
			}).catch(err => {
				console.log(err);
				// 给规则操作loading添加状态
				dispatch({
					type: "policyDetail/setAttrValue",
					payload: {
						ruleOperatorLoading: false
					}
				});
			});
		}
	}

	modifyRuleName(e) {
		let {policyDetailStore, dispatch, ruleData, ruleIndexArr} = this.props;
		let {policyDetail, policyRules} = policyDetailStore;
		let value = e.target.value;

		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["name"] = value;
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["name"] = value;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	getTemplateFormatObj(singleCondition) {
		let {policyDetailStore, templateStore, ruleIndexArr} = this.props;
		let {policyRules} = policyDetailStore;
		let {ruleTemplateListObj} = templateStore;

		let currentRule = null;
		if (ruleIndexArr && ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
		} else if (ruleIndexArr && ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
		}

		let templateName = singleCondition.property;
		let currentTemplate = ruleTemplateListObj && templateName ? ruleTemplateListObj[templateName] : null;
		let cfgJson = currentTemplate && currentTemplate.cfgJson && isJSON(currentTemplate.cfgJson) ? JSON.parse(currentTemplate.cfgJson) : null;

		let list = [];
		cfgJson &&
		cfgJson["params"] &&
		cfgJson["params"].map((item, index) => {
			item.children && item.children.map((subItem, subIndex) => {
				let value;
				let type = "string";

				if (subItem.name === "operator" || subItem.name === "value") {
					value = singleCondition[subItem.name];
				} else {
					let paramObj = singleCondition["params"] && singleCondition["params"].find(fItem => fItem.name === subItem.name);
					value = paramObj ? paramObj.value : null;
					type = paramObj ? paramObj.type : null;
				}

				let obj = {
					...subItem,
					labelText: item.labelText,
					value: value,
					type: type
				};
				list.push(obj);
			});
		});
		return list;
	}

	render() {
		let {policyDetailStore, dispatch, ruleData, ruleIndexArr} = this.props;
		let {policyRules, ruleOperatorLoading} = policyDetailStore;
		let currentRule = null;
		let hasOperationActions = true;
		if (ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
		} else if (ruleIndexArr.length === 2) {
			hasOperationActions = false;
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
		}
		return (
			<div className="rule-config-wrap">
				<Form>
					<div className="rule-detail rule-attr">
						<h4>
							{/* 基本设置 */}
							{policyDetailLang.ruleConfig("basicSetup")}
						</h4>
						<div className="rule-detail-content">
							<RuleAttr
								ruleData={ruleData}
								ruleIndexArr={ruleIndexArr}
							/>
						</div>
					</div>

					<div className="rule-detail rule-condition">
						<h4>
							{/* 条件配置 */}
							{policyDetailLang.ruleConfig("conditionsConfiguration")}
						</h4>
						<div className="rule-detail-content">
							<RuleCondition
								ruleData={ruleData}
								ruleIndexArr={ruleIndexArr}
							/>
						</div>
					</div>
					{/* { hasOperationActions && }*/}
					<div className="rule-detail rule-operation">
						<h4>
							{/* 操作配置 */}
							{policyDetailLang.ruleConfig("operatingConfiguration")}
						</h4>
						<div className="rule-detail-content">
							<RuleOperation ruleIndexArr={ruleIndexArr}/>
						</div>
					</div>
					<div className="rule-btns">
						<Button
							type="default"
							onClick={() => {
								dispatch({
									type: "policyDetail/setAttrValue",
									payload: {
										ruleActiveKey: []
										// currentRuleUuid: null
									}
								});
								setTimeout(() => {
									dispatch({
										type: "policyDetail/setAttrValue",
										payload: {
											currentRuleUuid: null
										}
									});
								}, 500);
							}}
						>
							{/* 收起 */}
							{policyDetailLang.ruleConfig("packUp")}
						</Button>
						<Button
							disabled={ruleOperatorLoading}
							type="primary"
							loading={ruleOperatorLoading}
							onClick={() => {
								if (currentRule.unSave && checkFunctionHasPermission("ZB0101", "addRule")) {
									this.savePolicyRule();
								} else if (!currentRule.unSave && checkFunctionHasPermission("ZB0101", "updateRule")) {
									this.savePolicyRule();
								} else {
									message.warning(policyDetailLang.ruleConfig("noPermission")); // 无权限操作
								}
							}}
						>
							{
								currentRule.unSave ? policyDetailLang.ruleConfig("add") : policyDetailLang.ruleConfig("update") // "添加" : "更新"
							}
						</Button>
					</div>
				</Form>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail,
	templateStore: state.template
}))(RuleConfig);
