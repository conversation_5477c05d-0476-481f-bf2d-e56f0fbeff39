@header-height: 60px;

:global {
    .policy-drawer-wrap {
        & {
            z-index: 99 !important;
        }

        .ant-drawer-content-wrapper {
            -webkit-box-shadow: 0 2px 16px rgba(0, 0, 0, .2);
            box-shadow: 0 2px 16px rgba(0, 0, 0, .2);
            background-clip: padding-box;
            margin-top: @header-height;
        }

        .ant-drawer-header {
            display: none;
        }

        .policy-detail {
            & {
                top: @header-height;
                bottom: 0;
                right: 0;
				z-index: 99;
				padding-bottom:30px;
                background-color: #fff;
                overflow-x: hidden;
                overflow-y: auto;
            }

            .policy-detail-title {
                & {
                    font-size: 20px;
                    margin-bottom: 10px;
                }

                .title-pre {
                    font-size: 26px;
                    color: #A0A0A0;
                    margin-right: 10px;
                }

                .title-close {
                    float: right;
                    position: relative;
                    top: 7px;
                    font-size: 14px;
                    cursor: pointer;
                    -webkit-transition: all 0.2s ease-out;
                    -moz-transition: all 0.2s ease-out;
                    -ms-transition: all 0.2s ease-out;
                    -o-transition: all 0.2s ease-out;
                }

                .title-close:hover {
                    font-size: 15px;
                    color: #c90000;
                    font-weight: bold;
                }

                .policy-status {
                    position: relative;
                    vertical-align: text-bottom;
                    margin-left: 10px;
                }
            }

        }

        .policy-detail-base {
            border-top: 1px solid #CDCDCD;
            padding-top: 10px;
            padding-bottom: 10px;
        }

        .policy-detail-group {
            & {
                padding: 3px 0;
                font-size: 14px;
            }

            label {
                color: #b4b4b4;
                display: inline-block;
                min-width: 80px;
                min-height: 21px;
                margin-right: 20px;
            }

            span {
                white-space: nowrap;
                width: 400px;
                overflow: hidden;
                text-overflow: ellipsis;
                -o-text-overflow: ellipsis;
                -webkit-text-overflow: ellipsis;
                -moz-text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                vertical-align: bottom;
            }
        }

        .policy-detail-sep {
            height: 5px;
            margin: 0;
            padding: 0;
            background: -moz-linear-gradient(left, #3498DB, #71C195);
            background: -webkit-linear-gradient(left, #3498DB, #71C195);
            background: -o-linear-gradient(left, #3498DB, #71C195);
            background: -ms-linear-gradient(left, #3498DB, #71C195);
            background-color: #CDCDCD;
        }

        .policy-detail-rule {
            & {
                padding-top: 10px;
                padding-bottom: 10px;
            }

            .policy-detail-status {
                font-size: 15px;
                margin-bottom: 10px;
            }

            h4 {
                margin-bottom: 12px;
                margin-top: 16px;
                font-size: 17.5px;
            }
        }

        .policy-detail-ruleList {
            & {
                margin: 0;
                padding: 5px 0;
                border-top: 1px solid #eaeaea;
                padding-top: 10px;
            }

            >li {
                & {
                    line-height: 28px;
                }

                &.rule-status-0 a {
                    color: #b4b4b4;
                }
            }

            a {
                color: #555;
            }

            a:hover {
                color: #1890ff !important;
                text-decoration: underline !important;
            }

            a[disabled] {
                color: #A6A6A6;
            }

            a[disabled]:hover {
                color: #1890ff;
            }
        }
    }

    .ant-drawer-right.ant-drawer-open.policy-drawer-wrap {
        width: auto;
    }
}
