import { PureComponent } from "react";
import { message, Tooltip, Icon, notification } from "antd";
import { connect } from "dva";
import { policyEditorAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";
import { policyListLang, commonLang, imExportModeLang } from "@/constants/lang";

class ExportRules extends PureComponent {
    state = {
    	exportLoading: false
    }
    // 导出规则流
    exportRules = async(item, e) => {
    	e.stopPropagation();
    	// 系统默认将策略集下的策略/规则及引用到的指标一并导出
    	notification.open({
    		message: imExportModeLang.exportInfo("warn"),
    		description: imExportModeLang.exportInfo("tipPolicy")
    	});
    	const { isEditorArea } = this.props;
    	const params = {
    		uuid: item.uuid,
    		fileName: policyListLang.table("policyA") + item.name,
    		fileType: "ply",
    		isEditorArea
    	};
    	this.setState({
    		exportLoading: true
    	});
    	const testResult = await policyEditorAPI.exportRulesTest(params);
    	if (testResult && testResult.hasOwnProperty("success") && !testResult.success) {
    		message.warning(testResult.message);
    		this.setState({
    			exportLoading: false
    		});
    	} else {
    		policyEditorAPI.exportRules(params).then(() => {
    			this.setState({
    				exportLoading: false
    			});
    		}).catch(err => {
    			console.log(err);
    			this.setState({
    				exportLoading: false
    			});
    		});
    	}
    }
    render() {
    	const { record } = this.props;
    	const { exportLoading } = this.state;
    	return (
    		<Tooltip title={policyListLang.tooltip("exportRules")} >
    			<a onClick={(e) => {
    				e.stopPropagation();
    				if (checkFunctionHasPermission("ZB0101", "editorExportRules")) {
    					!exportLoading && this.exportRules(record, e);
    				} else {
    					// lang:无权限操作
    					message.warning(commonLang.messageInfo("noPermission"));
    				}
    			}}>
    				{
    					exportLoading
    						? <Icon type="loading" />
    						: <i className="iconfont icon-upload"></i>
    				}
    			</a>
    		</Tooltip >
    	);
    }
}

export default connect(state => ({
	globalStore: state.global
}))(ExportRules);
