import { Tooltip } from "antd";
import { imExportModeLang } from "@/constants/lang";
import { Fragment } from "react";
export default (props) => {
	const { data = [] } = props;
	const nameDesc = {
		"name": imExportModeLang.importInfo("identification"), // 标识
		"dName": imExportModeLang.importInfo("name"), // 标识名称
		"version": imExportModeLang.importInfo("version"), // 版本
		"app": imExportModeLang.importInfo("app"), // 应用标识
		"appName": imExportModeLang.importInfo("appName"), // 应用标识名称
		"eventId": imExportModeLang.importInfo("eventId"), // 策略标识
		"policyName": imExportModeLang.importInfo("policyName"), // 策略标识名称
		"policySetName": imExportModeLang.importInfo("policySetName"), // 策略集标识名称
		"importScene": imExportModeLang.importInfo("importScene"), // 导入环境
		"exportScene": imExportModeLang.importInfo("exportScene")// 导出环境
	};
	console.log(props);
	return (
		<ul className={`import-field-ul ${name}`}>
			{
				data.map((v, i) => {
					const keys = v ? Object.keys(v) : [];
					if (keys && keys.length > 0) {
						return (
							<Fragment>
								{
									i === 0 &&
									<li>
										{
											keys.map((key) => {
												return (
													<div>{nameDesc[key]}</div>
												);
											})
										}
									</li>
								}
								<li>
									{
										keys.map((key) => {
											const name = ["FirstMatch", "WorstMatch"].indexOf(v[key]) > -1
												? imExportModeLang.importInfo(v[key])
												: v[key];
											return (
												<Tooltip placement="left" title={name}>
													<div>
														{ name }
													</div>
												</Tooltip>
											);
										})
									}
								</li>
							</Fragment>
						);
					}
				})
			}
		</ul>
	);
};
