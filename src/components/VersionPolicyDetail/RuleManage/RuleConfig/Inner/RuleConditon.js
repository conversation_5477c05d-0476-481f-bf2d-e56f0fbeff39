import { PureComponent } from "react";
import { connect } from "dva";
import { Radio, Row, Col, Icon, Popover } from "antd";
import OneCondition from "../../OneCondition";
import RuleConditonTemplate from "./RuleConditonTemplate";
import { CommonConstants } from "@/constants";
import { policyDetailLang } from "@/constants/lang";
import FormulaScriptEditor from "@/components/PolicyDetail/FormulaScriptEditor";

const RadioGroup = Radio.Group;

class RuleConditon extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { currentRule, ruleIndexArr } = this.props;
		let currentCondition = currentRule && currentRule.conditions ? currentRule.conditions : null;

		return (
			<div>
				{
					currentRule &&
					currentRule.template &&
					currentRule.template === "common/custom" &&
					<Row gutter={CommonConstants.gutterSpan} className="mb10">
						<Col span={1} className="basic-info-title"></Col>
						<Col span={3} className="basic-info-title">
							{/* 执行条件： */}
							{policyDetailLang.ruleCondition("performsConditional")}
						</Col>
						<Col span={18}>
							<RadioGroup
								value={currentCondition &&
									currentCondition["logicOperator"] ? currentCondition["logicOperator"] : null}
								disabled={true}
							>
								<Radio value="&&">
									{/* 满足以下所有条件 */}
									{policyDetailLang.ruleCondition("mztj1")}
								</Radio>
								<Radio value="||">
									{/* 满足以下任意条件 */}
									{policyDetailLang.ruleCondition("mztj2")}
								</Radio>
								{
									currentRule["ifClause"] && currentRule["ifClause"] === "IF" &&

									<Radio value="!||">
										{/* 以下条件均不满足 */}
										{policyDetailLang.ruleCondition("mztj3")}
									</Radio>

								}
								{
									currentRule["ifClause"] && currentRule["ifClause"] === "IF" &&
									<Radio value="!&&">
										{/* 以下条件至少一条不满足 */}
										{policyDetailLang.ruleCondition("mztj4")}
									</Radio>
								}
							</RadioGroup>
						</Col>
						<Col span={2}></Col>
					</Row>
				}
				{
					currentRule &&
					currentRule.template &&
					currentRule.template !== "common/script" &&
					<Row gutter={CommonConstants.gutterSpan}>
						<div className="custom-condition">
							{
								currentCondition &&
								currentCondition.type !== "alias" &&
								currentCondition.children &&
								currentCondition.children.map((item, index) => {
									if (item.children && item.children.length) {
										return (
											<div className="custom-condition-item group-condition has-line" key={index}>
												{
													item.children.map((groupItem, groupIndex) => {
														return (
															<OneCondition
																conditionData={item}
																conditionSingleData={groupItem}
																conditionType="group"
																ruleIndexArr={ruleIndexArr}
																conditionArr={[index, groupIndex]}
																key={groupIndex}
															/>
														);
													})
												}
											</div>
										);
									} else if (item.type !== "alias") {
										return (
											<div className="custom-condition-item one-condition has-line" key={index}>
												<OneCondition
													conditionData={null}
													conditionSingleData={item}
													ruleIndexArr={ruleIndexArr}
													conditionType="single"
													conditionArr={[index]}
												/>
											</div>
										);
									} else if (item.type === "alias") {
										return (
											<RuleConditonTemplate
												property={item.property}
												ruleIndexArr={ruleIndexArr}
												conditionIndex={index}
												currentRule={currentRule}
												key={index}
											/>
										);
									}
								})
							}
						</div>
					</Row>
				}

				{
					currentRule && currentRule.template && currentRule.template === "common/script" &&
					<Row gutter={CommonConstants.gutterSpan} className="mb10">
						<Col span={4} className="basic-info-title">
							{/* 脚本规则 */}
							{policyDetailLang.ruleConfig("scriptRule")}
						</Col>
						<Col span={16}>
							<FormulaScriptEditor
								disabled={true}
								value={currentRule.script}
							/>
						</Col>
						<Col span={1}>
							<Popover
								placement="right"
								title={policyDetailLang.ruleAttr("scriptRule")} // 脚本规则
								content={
									<pre>
										{ policyDetailLang.ruleAttr("scriptDemo") }
									</pre>
								} // 脚本规则介绍
							>
								<Icon
									type="question-circle-o"
									className="param-tip-icon"
								/>
							</Popover>
						</Col>
					</Row>
				}
			</div>
		);
	}
}

export default connect(state => ({
	policyDetailStore: state.policyDetail
}))(RuleConditon);
