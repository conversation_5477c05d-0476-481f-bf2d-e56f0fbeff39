@import "../base/variables";

:global {
	.main-area-wrap {
		height: ~"calc(100vh - 62px)";
		overflow-x: auto;
		overflow-y: hidden;
	}
	.page-global-header {
		& {
			overflow: hidden;
			background: #fff;
			padding: 0 20px;
			border-bottom: 1px solid #e8e8e8;
			height: @header-height;
			line-height: @header-height;
		}
		.left-info {
			& {
				float: left;
			}
			h2 {
				display: inline-block;
				font-size: 18px;
				font-weight: normal;
				color: rgba(0, 0, 0, .85);
				margin-right: 12px;
				float: left;
				margin-bottom: 0;
			}
			.sub-info {
				float: left;
				position: relative;
				top: 30px;
				line-height: normal;
			}
		}
		.right-info {
			& {
				float: right;
			}
			.right-info-item {
				& {
					float: left;
					margin-left: 12px;
				}
				.quick-search {
					margin-top: 14px;
				}
			}
		}
	}
	.page-global-body-search {
		& {
			overflow: hidden;
			margin-bottom: 15px;
		}
		.left-info {
			& {
				float: left;
			}
			h2 {
				display: inline-block;
				font-size: 18px;
				font-weight: normal;
				color: rgba(0, 0, 0, .85);
				margin-right: 12px;
				float: left;
				margin-bottom: 0;
				line-height: 32px;
			}
			.sub-info {
				float: left;
				position: relative;
				top: 30px;
				line-height: normal;
			}
			.left-info-item {
				& {
					float: left;
					margin-right: 12px;
				}
				.quick-search {
					margin-top: 16px;
				}
			}
		}
		.right-info {
			& {
				float: right;
			}
			.right-info-item {
				& {
					float: left;
					margin-left: 12px;
				}
				.quick-search {
					margin-top: 16px;
				}
			}
		}
	}
	.page-global-body {
		& {
			padding: 20px;
			height: ~"calc(100vh - 120px)";
			overflow: auto;
		}
		.table-top-button {
			& {
				margin-bottom: 10px;
			}
			.ant-btn {
				margin-right: 10px;
			}
		}
		.ant-table {
			background: #fff;
		}
	}
	.page-global-body-main {
		& {
			background: #fff;
			padding-bottom: 20px;
		}
		&.no-pagination {
			padding-bottom: 0;
		}
		.page-global-body-pagination {
			& {
				margin-top: 20px;
				height: 32px;
				line-height: 32px;
			}
			.count {
				float: left;
				margin-left: 16px;
			}
			.ant-pagination {
				float: right;
				margin-right: 16px;
			}
			.list-pagination {
				position: relative;
				left: -40%;
			}
		}
	}
	.collapsed-true {
		.page-global-body {
			min-width: 1024px;
			width: ~"calc(100vw - 80px)";
		}
		.page-global-tab {
			.ant-tabs {
				width: ~"calc(100vw - 80px)";
				min-width: 1024px;
			}
		}
	}
	.collapsed-false {
		.page-global-body {
			min-width: 1024px;
			width: ~"calc(100vw - 220px)";
		}
		.page-global-tab {
			.ant-tabs {
				width: ~"calc(100vw - 220px)";
				min-width: 1024px;
			}
		}
	}
	.page-global-tab {
		// 处理tab样式
		.ant-tabs-bar {
			& {
				height: @header-height;
				padding: 0 20px;
				margin-bottom: 0;
				background: #fff;
			}
			.ant-tabs-tab {
				height: @header-height;
			}
			.ant-tabs-nav .ant-tabs-tab {
				margin: 0 32px 0 0;
				height: @header-height;
				line-height: @header-height;
				padding: 0px 16px;
			}
		}
	}
	.divider {
		height: 1px;
		margin: 20px 0;
		overflow: hidden;
		border-top: 1px dashed #e1e1e1;
		clear: both;
	}
}
