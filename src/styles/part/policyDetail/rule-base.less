@import "../../base/variables";

@un-save-color: #f0ad4e;
@has-modify-color: rgba(240, 153, 41, 0.78);
@need-refresh-color: rgba(74, 152, 240, 0.78);
@online: rgba(106, 185, 65, 0.78);

:global {
    .policy-detail-wrap {
        .rule-manage {
            & {
                position: relative;
                min-width: 960px;
                overflow-x: scroll;
            }

            .rule-manage-header {
                &.collapse-header {
                    & {
                        height: 40px;
                        line-height: 40px;
                        background: #fff;
                    }

                    .rule-name {
                        & {
                            padding-left: 20px;
                        }

                        .row-collapsed {
                            & {
                                margin-right: 10px;
                                font-size: 18px;
                                color: #86d16b;
                                vertical-align: text-bottom;
                            }

                            &.active {
                                color: #f17063;
                            }
                        }
                    }
                }

                &.rule-th {
                    height: 38px;
                    line-height: 38px;
                    text-indent: 0;
                    border: 1px solid #ededed;
                    background: #f6f6f6;
                    // border-bottom: 2px solid #ddd;
                    // border-bottom: 1px solid #ddd;
                    font-weight: 600;
                    border-radius: 3px 3px 0 0;
                }

                &.un-save {
                    //background: #ffd79e;
                }

                .checkbox,
                .rule-name,
                .rule-st {
                    float: left;
                }

                .checkbox {
                    margin-left: 15px;
                    position: relative;
                    top:-1px;
                }

                .rule-st {
                    margin-left: 15px;

                    span {
                        & {
                            position: relative;
                            background: #86d16b;
                            padding: 0 10px;
                            line-height: 24px;
                            display: inline-block;
                            border-radius: 3px;
                            color: #fff;
                            font-size: 12px;
                            font-weight: normal;
                        }

                        &:before {
                            position: absolute;
                            left: -9px;
                            top: 6px;
                            width: 0;
                            height: 0;
                            border-left: 4px solid transparent;
                            border-bottom: 5px solid transparent;
                            border-top: 5px solid transparent;
                            border-right: 5px solid @un-save-color;
                            content: "";
                        }

                        &.need-refresh:before {
                            border-right: 5px solid @need-refresh-color;
                        }

                        &.online:before {
                            border-right: 5px solid @online;
                        }
                    }

                    span.un-save {
                        background: @un-save-color;
                    }

                    span.has-modify {
                        background: @has-modify-color;
                    }

                    span.need-refresh {
                        background: @need-refresh-color;
                    }

                    span.online {
                        background: @online;
                    }
                }

                .rule-name {
                    // padding-left: 51px;
                    padding-left:48px;
                    max-width: 330px;
                    overflow: hidden;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                    -webkit-text-overflow: ellipsis;
                    -moz-text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .rule-empty,
                .rule-number,
                .rule-immuno,
                .rule-status,
                .rule-status2,
                .rule-action,
                .rule-operator,
                .rule-policySet {
					float: right;
					min-height: 28px;
                }


                .rule-number.edit-area {
                    .rule-id {
                        i {
                            color: #999;
                            margin-left: 8px;
                        }

                        &:hover {
                            i {
                                color: #009fff;
                            }
                        }
                    }
                }

                .rule-policySet {
                    width: 200px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    color: #666;
                }

                .rule-empty {
                    width: 120px;
                    height: 10px;
                }

                &:hover .rule-extra {
                    a {
                        display: inline-block;
                        opacity: 1;
                        color: #b4b4b4;
                        border-color: #b4b4b4;
                    }
                }

                .rule-extra {
                    float: right;

                    a {
                        padding: 0px 10px;
                        line-height: 24px;
                        border: 1px dashed #999;
                        border-radius: 12px;
                        margin-right: 20px;
                        color: #999;
                        display: inline-block;
                        opacity: .3;
                        cursor: pointer;
                    }

                    a:hover {
                        color: @blue;
                        border: 1px dashed @blue;
                    }
                }

                .rule-action {
					&.rule-action-large{
						width: 165px;
					}
                    & {
                        width: 125px;
                    }

                    & i.blue {
                        color: @blue2;
                    }

                    i {
                        font-size: 18px;
                        margin-right: 10px;
                        color: #999;
                    }

                    i:hover {
                        color: #3498db;
                    }

                    i.un-save {
                        color: #c8c8c8;
                        cursor: not-allowed;
                    }

                    i.disabled {
                        color: #c8c8c8;
                        cursor: not-allowed;
                    }
                }

                .rule-operator {
					width: 125px;
					&.rule-operator-large{
						width:165px;
					}
                }

                .rule-number {
                    width: 205px;

                    .none-data {
                        color: #c8c8c8;
                    }
                }

                .rule-immuno {
                    width: 100px;

                    .none-data {
                        color: #c8c8c8;
                    }
                }

                .rule-status2 {
                    width: 120px;

                    .rule-status-switch {
                        position: relative;
                        top: -1px;
                    }
                }

                .rule-status {
                    width: 200px;

                    .rule-status-switch {
                        position: relative;
                        top: -1px;
                    }

                    .ant-radio-button-wrapper {
                        position: relative;
                        top: 0;
                        font-size: 14px;
                    }

                    .ant-radio-button-wrapper:first-child {
                        border-radius: 12px 0 0 12px;
                    }

                    .ant-radio-button-wrapper:last-child {
                        border-radius: 0 12px 12px 0;
                    }
                }
            }

            .rule-manage-collapse {
                & {
                    border-radius: 0;
                    border-top: none;
                    border-bottom-left-radius: 4px;
                    border-bottom-right-radius: 4px;
                }

                .ant-collapse-item.hidden {
                    display: none;
                }

                .ant-collapse-item>.ant-collapse-header {
                    // padding: 1px 0 0 3px;
                    padding: 1px 3px 0 3px;
                }

                .ant-collapse-item.ant-collapse-item-active {
                    & {
                        position: relative;
                        background: #f6f6f6;
                    }

                    &:before {}
                }

                .ant-collapse-content-box {
                    border-radius: 0;
                }

                .ant-collapse>.ant-collapse-item:last-child,
                .ant-collapse>.ant-collapse-item:last-child>.ant-collapse-header {
                    border-radius: 0;
                }
            }
        }

        .ant-collapse-item {
            & {
                background: #fff;
                // border-left: 3px solid #f6f6f6;
                border-left:3px solid transparent;
                border-bottom: 1px solid #d9d9d9;
                clear: both;
            }

            &.rule-children {
                margin-left: 15px;
            }

            .ant-collapse-content {
                & {
                    background: #fbfbfb;
                    border-radius: 0;
                    clear: both;
                    padding: 0 30px;
                    box-shadow: inset 0 0 10px 4px #dcdcdc;
                }

                >.ant-collapse-content-box {
                    margin-top: 16px;
                    margin-bottom: 16px;
                    padding: 10px 0;
                }
            }

            &.ant-collapse-item-active {
                & {
                    border-left: 3px solid #009fff;
                    background: #f6f6f6;
                }

                .rule-manage-header.collapse-header {
                    background: #f6f6f6;
                }
            }

            .rule-manage-header.show-cite-drawer {
                background: #f6f6f6 !important;
            }
        }
    }
    .pad-left-6{
        padding-left: 6px;
    }
}
