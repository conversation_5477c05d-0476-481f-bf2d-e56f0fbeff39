import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Select, Table, Icon, message } from "antd";
import "../style/model-setting-modal.less";
import { workflowAPI } from "@/services";
import { commonLang, workflowLang } from "@/constants/lang";
import { filterAvailableFieldList } from "@/utils/filterFieldList";

const Option = Select.Option;

class ModelSetting extends PureComponent {
    state = {
    	modelId: null,
    	modelVersion: null,
    	modelType: null,
    	modelInput: [],
    	modelOutput: []
    };

    constructor(props) {
    	super(props);
    	this.settingSubmit = this.settingSubmit.bind(this);
    	this.initPage = this.initPage.bind(this);
    	this.changeSystemFieldValue = this.changeSystemFieldValue.bind(this);
    }

    componentDidMount() {
    	let { nodeConfig } = this.props;
    	let modelId = nodeConfig && nodeConfig.modelId ? nodeConfig.modelId : null;
    	let modelVersion = nodeConfig && nodeConfig.modelVersion ? nodeConfig.modelVersion : null;
    	let modelType = nodeConfig && nodeConfig.modelType ? nodeConfig.modelType : null;
    	let modelInput = nodeConfig && nodeConfig.modelInput ? nodeConfig.modelInput : [];
    	let modelOutput = nodeConfig && nodeConfig.modelOutput ? nodeConfig.modelOutput : [];

    	this.setState({
    		modelId: modelId,
    		modelVersion: modelVersion,
    		modelType: modelType,
    		modelInput: modelInput,
    		modelOutput: modelOutput
    	});

    	if (modelId && modelVersion && modelType) {
    		this.initPage(modelId, modelVersion, modelType);
    	}
    }

    initPage(modelId, modelVersion, modelType) {
    	let params = {
    		modelUuid: modelId,
    		modelVersion: modelVersion,
    		modelType: modelType
    	};
    	workflowAPI.getModelParamsByUuid(params).then(res => {
    		if (res.success) {
    			let { modelInput, modelOutput } = this.state;
    			let serviceModelInput = res.data && res.data["modelInput"] ? res.data["modelInput"] : [];
    			let serviceModelOutput = res.data && res.data["modelOutput"] ? res.data["modelOutput"] : [];
    			serviceModelInput.map(item => {
    				let inStateObj = modelInput.find(fItem => fItem.modelVar === item.name);
    				if (inStateObj) {
    					item["systemField"] = inStateObj["systemField"];
    				}
    				item["modelVar"] = item.name;
    				delete item.name;
    			});
    			serviceModelOutput.map(item => {
    				let inStateObj = modelOutput.find(fItem => fItem.modelVar === item.name);
    				if (inStateObj) {
    					item["systemField"] = inStateObj["systemField"];
    				}
    				item["modelVar"] = item.name;
    				delete item.name;
    			});
    			this.setState({
    				modelInput: serviceModelInput,
    				modelOutput: serviceModelOutput
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    getModelParams(modelId, modelVersion, modelType) {
    	let params = {
    		modelUuid: modelId,
    		modelVersion: modelVersion,
    		modelType: modelType
    	};
    	workflowAPI.getModelParamsByUuid(params).then(res => {
    		if (res.success) {
    			let modelInput = res.data && res.data["modelInput"] ? res.data["modelInput"] : [];
    			let modelOutput = res.data && res.data["modelOutput"] ? res.data["modelOutput"] : [];

    			modelInput.map(item => {
    				item["systemField"] = null;
    				item["modelVar"] = item.name;
    				delete item.name;
    			});
    			modelOutput.map(item => {
    				item["systemField"] = null;
    				item["modelVar"] = item.name;
    				delete item.name;
    			});

    			this.setState({
    				modelInput: modelInput,
    				modelOutput: modelOutput
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    changeSystemFieldValue(type, index, value) {
    	let { modelInput, modelOutput } = this.state;
    	let { globalStore } = this.props;
    	let { allMap } = globalStore;
    	let { ruleAndIndexFieldList } = allMap;

    	let itemObj = ruleAndIndexFieldList && ruleAndIndexFieldList.find(item => item.name === value);
    	if (type === "input") {
    		modelInput[index]["systemField"] = value;
    		modelInput[index]["systemFieldDName"] = itemObj.dName;
    		this.setState({
    			modelInput: JSON.parse(JSON.stringify(modelInput))
    		});
    	} else if (type === "output") {
    		modelOutput[index]["systemField"] = value;
    		modelOutput[index]["systemFieldDName"] = itemObj.dName;
    		this.setState({
    			modelOutput: JSON.parse(JSON.stringify(modelOutput))
    		});
    	}
    }

    settingSubmit() {
    	let { modelId, modelVersion, modelType, modelInput, modelOutput } = this.state;
    	let { onChange, dispatch } = this.props;

    	if (!modelId) {
    		message.warning(workflowLang.modelSettingModal("selectModel")); // lang:请选择模型
    		return;
    	}
    	// 判断input，output参数是否有空值
    	let inputHasEmptyField = false;
    	let outputHasEmptyField = false;

    	modelInput.forEach(item => {
    		if (!item.systemField) {
    			inputHasEmptyField = true;
    		}
    	});
    	modelOutput.forEach(item => {
    		if (!item.systemField) {
    			outputHasEmptyField = true;
    		}
    	});

    	if (inputHasEmptyField) {
    		message.warning(workflowLang.modelSettingModal("inputFieldEmptyMsg")); // lang:"模型入参选择字段存在空值，请补充完整。");
    		return;
    	}
    	if (outputHasEmptyField) {
    		message.warning(workflowLang.modelSettingModal("outputFieldEmptyMsg")); // lang:"模型出参选择字段存在空值，请补充完整。");
    		return;
    	}

    	let nodeConfig = {
    		modelId: modelId,
    		modelVersion: modelVersion,
    		modelType: modelType,
    		modelInput: modelInput,
    		modelOutput: modelOutput
    	};

    	onChange(nodeConfig);

    	dispatch({
    		type: "workflow/setDialogShow",
    		payload: {
    			modelSetting: false
    		}
    	});

    	// 延迟300ms初始化state
    	setTimeout(() => {
    		this.setState({
    			modelId: null,
    			modelVersion: null,
    			modelType: null,
    			modelInput: [],
    			modelOutput: []
    		});
    	}, 300);
    }

    render() {
    	let { workflowStore, globalStore, dispatch, disabled } = this.props;
    	let { modelId, modelVersion, modelInput, modelOutput } = this.state;
    	let { dialogShow, policySetItem } = workflowStore;
    	let { allMap } = globalStore;
    	let { ruleFieldList = [], salaxyFieldList = [] } = allMap;

    	const { appName } = policySetItem || {};
    	const ruleAndIndexFieldList = filterAvailableFieldList({allMap, appName});

    	let inputColumns = [
    		{
    			title: workflowLang.modelSettingModal("serviceInput"), // lang:"服务入参",
    			dataIndex: "systemField",
    			width: 200,
    			render: (text, record, index) => {
    				let type = record.type ? record.type.toUpperCase() : "STRING";
    				let fieldList;
    				if (type === "FIELD") {
    					fieldList = ruleAndIndexFieldList;
    				} else if (type === "INDEX") {
    					fieldList = salaxyFieldList;
    				} else {
    					fieldList = ruleAndIndexFieldList.filter(fItem => fItem.type === type);
    				}

    				return (
    					<Select
    						placeholder={workflowLang.modelSettingModal("serviceInvolvementPlaceholder")} // lang:"请选择入参字段"
    						value={text || undefined}
    						onChange={this.changeSystemFieldValue.bind(this, "input", index)}
    						showSearch
    						optionFilterProp="children"
    						style={{ width: "200px" }}
    						disabled={disabled}
    					>
    						{
    							fieldList.map((ruleItem, ruleIndex) => {
    								return (
    									<Option
    										value={ruleItem.name}
    										key={ruleIndex}
    									>
                                            [{ruleItem.sourceName ? commonLang.sourceName(ruleItem.sourceName) : commonLang.sourceName("realtime")}]&nbsp;
    										{ruleItem.dName}
    									</Option>
    								);
    							})
    						}
    					</Select>
    				);
    			}
    		},
    		{
    			title: workflowLang.modelSettingModal("assignmentDirection"), // lang:"赋值方向",
    			width: 100,
    			render: (text, record, index) => {
    				return (
    					<Icon type="arrow-right" />
    				);
    			}
    		},
    		{
    			title: workflowLang.modelSettingModal("elementName"), // lang:"要素名",
    			dataIndex: "dName",
    			width: 100
    		},
    		{
    			title: workflowLang.modelSettingModal("elementId"), // lang:"要素标识",
    			dataIndex: "modelVar"
    		},
    		{
    			title: workflowLang.modelSettingModal("valueType"), // lang"值类型",
    			dataIndex: "type",
    			width: 100
    		}
    	];
    	let outputColumns = [
    		{
    			title: workflowLang.modelSettingModal("serviceOutput"), // lang"服务出参",
    			dataIndex: "systemField",
    			width: 200,
    			render: (text, record, index) => {
    				let type = record.type ? record.type.toUpperCase() : "STRING";
    				let fieldList;

    				if (type === "STRING") {
    					fieldList = ruleFieldList.filter(fItem => fItem.type === type);
    				} else {
    					fieldList = ruleFieldList;
    				}

    				return (
    					<Select
    						placeholder={workflowLang.modelSettingModal("serviceOutputPlaceholder")} // lang:"请选择出参字段"
    						value={text || undefined}
    						onChange={this.changeSystemFieldValue.bind(this, "output", index)}
    						showSearch
    						optionFilterProp="children"
    						style={{ width: "200px" }}
    						disabled={disabled}
    					>
    						{
    							fieldList.map((ruleItem, ruleIndex) => {
    								return (
    									<Option
    										value={ruleItem.name}
    										key={ruleIndex}
    									>
    										{ruleItem.dName}
    									</Option>
    								);
    							})
    						}
    					</Select>
    				);
    			}
    		},
    		{
    			title: workflowLang.modelSettingModal("assignmentDirection"), // lang:"赋值方向",
    			width: 100,
    			render: (text, record, index) => {
    				return (
    					<Icon type="arrow-left" />
    				);
    			}
    		},
    		{
    			title: workflowLang.modelSettingModal("elementName"), // lang:"要素名",
    			dataIndex: "dName",
    			width: 100
    		},
    		{
    			title: workflowLang.modelSettingModal("elementId"), // lang:"要素标识",
    			dataIndex: "modelVar"
    		},
    		{
    			title: workflowLang.modelSettingModal("valueType"), // lang"值类型",
    			dataIndex: "type",
    			width: 100
    		}
    	];
    	return (
    		<Modal
    			title={workflowLang.modelSettingModal("title")} // lang:"模型设置"
    			width={850}
    			className="model-setting-modal"
    			visible={dialogShow.modelSetting}
    			maskClosable={false}
    			onOk={this.settingSubmit.bind(this)}
    			onCancel={() => {
    				dispatch({
    					type: "workflow/setDialogShow",
    					payload: {
    						modelSetting: false
    					}
    				});
    			}}
    		>
    			<div className="model-select">
    				<Select
    					placeholder={workflowLang.modelSettingModal("selectModel")} // lang:"请选择模型"
    					value={modelId && modelVersion ? modelId + "_" + modelVersion : undefined}
    					onChange={(modelIdAndVersion) => {
    						let modelId = modelIdAndVersion.split("_")[0];
    						let modelVersion = modelIdAndVersion.split("_")[1];
    						let modelObj = allMap["modelSelect"] && allMap["modelSelect"].find(fItem => fItem.uuid === modelId && fItem.version === modelVersion);

    						if (modelObj) {
    							this.setState({
    								modelId: modelId,
    								modelVersion: modelObj.version,
    								modelType: modelObj.modelType
    							});
    							this.getModelParams(modelId, modelObj.version, modelObj.modelType);
    						}
    					}}
    					showSearch
    					optionFilterProp="children"
    					disabled={disabled}
    				>
    					{
    						allMap &&
                            allMap["modelSelect"] &&
                            allMap["modelSelect"].map((item, index) => {
                            	return (
                            		<Option
                            			value={item.uuid + "_" + item.version}
                            			key={index}
                            		>
                            			{item.name + "_" + item.version}
                            		</Option>
                            	);
                            })
    					}
    				</Select>
    			</div>
    			<div className="model-section-wrap">
    				<div className="model-section-header">
    					<h3>
    						{/* lang:入参配置*/}
    						{workflowLang.modelSettingModal("inputConfiguration")}
    					</h3>
    				</div>
    				<div className="model-section-body">
    					<Table
    						className="table-card-body"
    						columns={inputColumns}
    						dataSource={modelInput}
    						pagination={false}
    						rowKey="name"
    						size="middle"
    					/>
    				</div>
    			</div>
    			<div className="model-section-wrap">
    				<div className="model-section-header">
    					<h3>
    						{/* lang:出参配置*/}
    						{workflowLang.modelSettingModal("outputConfiguration")}
    					</h3>
    				</div>
    				<div className="model-section-body">
    					<Table
    						className="table-card-body"
    						columns={outputColumns}
    						dataSource={modelOutput}
    						pagination={false}
    						rowKey="name"
    						size="middle"
    					/>
    				</div>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	workflowStore: state.workflow
}))(ModelSetting);
