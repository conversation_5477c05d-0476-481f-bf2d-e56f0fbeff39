import { message } from "antd";
import { approvalLogAPI } from "@/services";
import cloneDeep from "lodash.clonedeep";

export default {
	namespace: "approvalLog",
	state: {
		policyPage: {
			taskList: [],
			total: 0,
			pages: 0,
			curPage: 1,
			pageSize: 10,
			searchParams: {
				targetName: null,
				startTs: null,
				endTs: null
			}
		},
		salaxyPage: {
			taskList: [],
			total: 0,
			pages: 0,
			curPage: 1,
			pageSize: 10,
			searchParams: {
				targetName: null,
				startTs: null,
				endTs: null
			}
		},
		workflowPage: {
			taskList: [],
			total: 0,
			pages: 0,
			curPage: 1,
			pageSize: 10,
			searchParams: {
				targetName: null,
				startTs: null,
				endTs: null
			}
		}
	},
	effects: {
		*getPolicyApprovalLogList({ payload }, { call, put }) {
			let response = yield call(approvalLogAPI.getPolicyApprovalLogList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let data = response.data || null;
			yield put({
				type: "setAttrValue",
				payload: {
					policyPage: {
						taskList: data && data.dataList ? data.dataList : [],
						total: data && data.total ? data.total : 0,
						pages: data && data.pages ? data.pages : 0,
						curPage: data && data.curPage ? data.curPage : 1,
						pageSize: data && data.pageSize ? data.pageSize : 10
					}
				}
			});
		},
		*getSalaxyApprovalLogList({ payload }, { call, put }) {
			let response = yield call(approvalLogAPI.getSalaxyApprovalLogList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let data = response.data || null;
			yield put({
				type: "setAttrValue",
				payload: {
					salaxyPage: {
						taskList: data && data.dataList ? data.dataList : [],
						total: data && data.total ? data.total : 0,
						pages: data && data.pages ? data.pages : 0,
						curPage: data && data.curPage ? data.curPage : 1,
						pageSize: data && data.pageSize ? data.pageSize : 10
					}
				}
			});
		},
		*getWorkflowApprovalLogList({ payload }, { call, put }) {
			let response = yield call(approvalLogAPI.getWorkflowApprovalLogList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let data = response.data || null;
			yield put({
				type: "setAttrValue",
				payload: {
					workflowPage: {
						taskList: data && data.dataList ? data.dataList : [],
						total: data && data.total ? data.total : 0,
						pages: data && data.pages ? data.pages : 0,
						curPage: data && data.curPage ? data.curPage : 1,
						pageSize: data && data.pageSize ? data.pageSize : 10
					}
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined) {
							stateChange[key] = newState[key];
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		}
	}
};

