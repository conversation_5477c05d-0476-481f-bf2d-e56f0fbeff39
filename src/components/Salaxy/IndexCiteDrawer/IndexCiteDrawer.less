:global {
    .index-cite-drawer-wrap {
        & {
            z-index: 99 !important;
        }

        .ant-drawer-content-wrapper {
            -webkit-box-shadow: 0 2px 16px rgba(0, 0, 0, .2);
            box-shadow: 0 2px 16px rgba(0, 0, 0, .2);
            background-clip: padding-box;
            margin-top: 60px;
        }

        .ant-drawer-header {
            display: none;
        }

        .index-cite-detail {
            & {
                top: 60px;
                bottom: 0;
                right: 0;
                z-index: 99;
                background-color: #fff;
                overflow-x: hidden;
				overflow-y: auto;
				padding-bottom:14px;
            }

            .index-cite-detail-title {
                & {
                    font-size: 20px;
                    margin-bottom: 10px;
                }

                .title-pre {
                    font-size: 26px;
                    color: #A0A0A0;
                    margin-right: 10px;
                }

                .index-cite-title-text {
                    display: inline-block;
                    max-width: 86%;
                    .tip {
                        position: relative;
                        top: -2px;
                        padding: 5px 10px;
                        background: #1d8bd8;
                        border-radius: 5px;
                        margin-left: 10px;
                        font-size: 16px;
                        color: #fff;
                    }
                }

                .title-close {
                    float: right;
                    position: relative;
                    top: 7px;
                    font-size: 14px;
                    cursor: pointer;
                    -webkit-transition: all 0.2s ease-out;
                    -moz-transition: all 0.2s ease-out;
                    -ms-transition: all 0.2s ease-out;
                    -o-transition: all 0.2s ease-out;
                }

                .title-close:hover {
                    font-size: 15px;
                    color: #c90000;
                    font-weight: bold;
                }
            }
        }

        .index-cite-detail-base {
            border-top: 1px solid #CDCDCD;
            padding-top: 10px;
            padding-bottom: 10px;
        }

        .index-cite-detail-group {
            & {
                padding: 3px 0;
                font-size: 14px;
            }

            label {
                // color: #b4b4b4;
                display: inline-block;
                min-width: 64px;
                min-height: 21px;
                margin-right: 20px;
            }

            span {
                white-space: nowrap;
            }
        }

        .index-cite-detail-sep {
            height: 5px;
            margin: 0;
            padding: 0;
            background: -moz-linear-gradient(left, #3498DB, #71C195);
            background: -webkit-linear-gradient(left, #3498DB, #71C195);
            background: -o-linear-gradient(left, #3498DB, #71C195);
            background: -ms-linear-gradient(left, #3498DB, #71C195);
            background-color: #CDCDCD;
        }

        .index-cite-list {
            & {
                padding-top: 20px;
            }

            dl {
                & {
                    margin-bottom: 20px;
                }

                dt {
                    & {
                        font-size: 16px;
                        min-height: 38px;
                        //line-height: 48px;
                        border-bottom: 1px dashed #dcdcdc;
                    }

                    span {
                        margin-right: 12px;
                    }

                    .ant-tag {
                        position: relative;
                        top: -2px;
                    }
                }

                dd {
                    & {
                        margin-bottom: 0;
                        // height: 32px;
						// line-height: 32px;
						height:auto;
						line-height: 22px;
						padding: 5px 0;
                        border-bottom: 1px dashed #dcdcdc;
                    }
                }
            }
            .index-cite-list-item-title{
                font-size:16px;
                margin-bottom:12px;
                font-weight: 500;
                position: relative;
                color:#333;
                padding-left:10px;
                &::before{
                    content:'';
                    left:0;
                    top:50%;
                    transform: translateY(-50%);
                    width:3px;
                    height:14px;
                    background: #1890ff;
                    position: absolute;
                }
            }
            .none-data {
                & {
                    text-align: center;
                    margin-top: 40px;
                }

                i {
                    font-size: 40px;
                }

                p {}
            }
        }
    }

    .ant-drawer-right.ant-drawer-open.index-cite-drawer-wrap {
        width: auto;
    }

    .index-cite-drawer-wrap .index-cite-list .sub-item dl dt{
        font-size:15px;
        min-height:32px;
        padding-bottom:4px;
    }
    .sub-item{
        margin-bottom:30px;
        .sub-item-title{
            font-size:16px;
            font-weight: bold;
            margin-bottom: 6px;
        }
    }
    .td-no-bg.scene-table-wrap table tbody tr td{
        background:#fff;
    }
}
