import { PureComponent } from "react";
import { connect } from "dva";
// import FormulaEdit from "formula-edit-react";
import FormulaEdit from "@/components/FormulaEdit";
import { commonLang } from "@/constants/lang";
import { cloneDeep } from "lodash";
import { formulaMethodList } from "@/constants/common";
import "./DefineScript";

const exceptZb = ["bizChain", "topn", "focusDaytime"];

class FormulaEditor extends PureComponent {

    state = {
    	defaultCnCode: "",
    	originCode: "",
    	errorMsg: ""
    };

    constructor(props) {
    	super(props);
    }

    componentDidMount() {
    	let { indexEditorStore } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;

    	let formulaCode = "";

    	if (currentIndexObj && currentIndexObj.attachFieldsObj && currentIndexObj.attachFieldsObj.formula) {
    		formulaCode = currentIndexObj.attachFieldsObj.formula;
    	}
    	let cnCode = this.enCodeToCn(formulaCode);

    	this.setState({
    		defaultCnCode: cnCode,
    		originCode: formulaCode
    	});
    }

    componentWillReceiveProps(nextProps) {
    	let { defaultCnCode } = this.state;
    	let { indexEditorStore } = this.props;
    	let preCurrentIndexObj = indexEditorStore.editIndexMap[indexEditorStore.currentIndexId] ? indexEditorStore.editIndexMap[indexEditorStore.currentIndexId] : null;
    	let nextCurrentIndexObj = nextProps.indexEditorStore.editIndexMap[nextProps.indexEditorStore.currentIndexId] ? nextProps.indexEditorStore.editIndexMap[nextProps.indexEditorStore.currentIndexId] : null;

    	let preFormulaCode = "";
    	let nextFormulaCode = "";

    	if (preCurrentIndexObj && preCurrentIndexObj.attachFieldsObj && preCurrentIndexObj.attachFieldsObj.formula) {
    		preFormulaCode = preCurrentIndexObj.attachFieldsObj.formula;
    	}
    	if (nextCurrentIndexObj && nextCurrentIndexObj.attachFieldsObj && nextCurrentIndexObj.attachFieldsObj.formula) {
    		nextFormulaCode = nextCurrentIndexObj.attachFieldsObj.formula;
    	}

    	if (!defaultCnCode && nextFormulaCode) {
    		let cnCode = this.enCodeToCn(nextFormulaCode);

    		this.setState({
    			// defaultCnCode: cnCode,
    			originCode: nextFormulaCode
    		});
    	}
    }

    getCode = (code, obj) => {
    	const { errorMsg } = obj || {};
    	this.setState({
    		errorMsg
    	});
    	let { originCode } = this.state;
    	// 移除字符串中的所有[]括号（包括其内容）
    	code = code.replace(/\[.*?\]/g, "");
    	let { indexEditorStore, globalStore, dispatch } = this.props;
    	let { allMap } = globalStore;
    	let { ruleAndIndexFieldList = [] } = allMap;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObjOld = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
    	let currentIndexObj = cloneDeep(currentIndexObjOld);

    	let newFieldList = [];
    	let keywords = [];
    	ruleAndIndexFieldList &&
            ruleAndIndexFieldList.length > 0 &&
            ruleAndIndexFieldList.forEach(item => {
            	if (item.name && item.dName) {
            		if (item.type === "INT" || item.type === "DOUBLE") {
            			// 计算公式指标不能引用 字段排行类、业务链类指标
            			if (!item.calcType || (item.calcType && !(exceptZb.indexOf(item.calcType) > -1))) {
            				keywords.push(`@${item.dName}`);

            				newFieldList.push({
            					name: `@${item.dName}`,
            					value: item.name,
            					type: item.type
            				});
            			}
            		}
            	}
            });

    	// const invalid = /[°"§%()\[\]{}=\\?´`'#<>,;.:]+/g;
    	const invalid = /[°"\(\)\[\]{}=\\?´`'<>,;.:\+]{1,1}/g; // 对字符串中的特殊字符进行转义

    	// 正则替换函数名
    	let formulaKeywords = formulaMethodList.reduce((pre, cur) => {
    		pre.push(`#${cur.name}`);
    		return pre;
    	}, []);

    	keywords = keywords.sort((a, b) => b.length - a.length);
    	formulaKeywords = formulaKeywords.sort((a, b) => b.length - a.length);

    	// 正则替换关键词
    	let newCode = code.replace(
    		new RegExp(`(${keywords.join("|")})`.replace(invalid, (v) => { return "\\" + v; }), "g"),
    		function(match) {
    			let turnStr = match;
    			newFieldList.forEach(item => {
    				if (item.name === match) {
    					turnStr = `@${item.value}`;
    				}
    			});
    			return turnStr;
    		}
    	);
    	// 替换方法名
    	newCode = newCode.replace(
    		new RegExp(`(${formulaKeywords.join("|")})`.replace(invalid, ""), "g"),
    		function(match) {
    			let turnStr = match;
    			formulaMethodList.forEach(item => {
    				if (`#${item.name}` === match) {
    					turnStr = `#${item.realValue}`;
    				}
    			});
    			return turnStr;
    		}
    	);
    	currentIndexObj.attachFieldsObj.formula = newCode;

    	if (originCode && originCode !== newCode) {
    		currentIndexObj["hasModify"] = true;
    	}

    	dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			editIndexMap: {
    				...editIndexMap,
    				[currentIndexId]: currentIndexObj
    			}
    		}
    	});
    };

    enCodeToCn = (code) => {
    	let { globalStore } = this.props;
    	let { allMap } = globalStore;
    	let { ruleAndIndexFieldList = [] } = allMap;

    	let newFieldList = [];
    	let keywords = [];
    	ruleAndIndexFieldList &&
            ruleAndIndexFieldList.length > 0 &&
            ruleAndIndexFieldList.forEach(item => {
            	if (item.name && item.dName) {
            		if (item.type === "INT" || item.type === "DOUBLE") {
            			// 计算公式指标不能引用 字段排行类、业务链类指标
            			if (!item.calcType || (item.calcType && !(exceptZb.indexOf(item.calcType) > -1))) {
            				keywords.push(`@${item.name}`);

            				newFieldList.push({
            					name: `@[${commonLang.sourceName(item.sourceName)}]${item.dName}`,
            					value: `@${item.name}`
            				});
            			}
            		}
            	}
            });

    	// 正则替换函数名
    	keywords = formulaMethodList.reduce((pre, cur) => {
    		pre.push(cur.realValue);
    		return pre;
    	}, keywords || []);

    	// 正则替换关键词
    	let newCode = code.replace(
    		new RegExp(`(${keywords.join("|")})`, "g"),
    		function(match) {
    			let turnStr = match;
    			let hasFind = false;
    			newFieldList.forEach(item => {
    				if (item.value === match) {
    					hasFind = true;
    					turnStr = item.name;
    				}
    			});
    			if (!hasFind) {
    				formulaMethodList.forEach(item => {
    					if (item.realValue === match) {
    						turnStr = item.name;
    					}
    				});
    			}
    			return turnStr;
    		}
    	);
    	return newCode;
    };

    render() {
    	let { defaultCnCode, errorMsg } = this.state;
    	let { globalStore } = this.props;
    	let { allMap } = globalStore;
    	let { ruleAndIndexFieldList = [] } = allMap;
    	console.log(ruleAndIndexFieldList);

    	let newFieldList = [];
    	ruleAndIndexFieldList &&
            ruleAndIndexFieldList.length > 0 &&
            ruleAndIndexFieldList.forEach(item => {
            	if (item.name && item.dName) {
            		if (item.type === "INT" || item.type === "DOUBLE") {
            			// 计算公式指标不能引用 字段排行类、业务链类指标
            			if (!item.calcType || (item.calcType && !(exceptZb.indexOf(item.calcType) > -1))) {
            				newFieldList.push({
            					name: `[${commonLang.sourceName(item.sourceName)}]${item.dName}`,
            					value: item.name
            				});
            			}
            		}
            	}
            });
    	return (
    		<div className="ml-1">
    			<FormulaEdit
    				theme="night"
    				height={200}
    				defaultValue={defaultCnCode}
    				fieldList={newFieldList}
    				readOnly={false}
    				id={"formulaEditor"}
    				lineNumber={false}
    				onChange={(code, obj) => this.getCode(code, obj)}
    				methodList={formulaMethodList}
    			/>
    			{
    				errorMsg && <div className="red">{errorMsg}</div>
    			}
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(FormulaEditor);

