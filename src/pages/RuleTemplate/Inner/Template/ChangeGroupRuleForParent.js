import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Select } from "antd";

const { Option, OptGroup } = Select;

class ChangeGroupRuleForParent extends PureComponent {
	constructor(props) {
		super(props);
	}

	changeRuleValue = (index, field, type, e) => {
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		let { templateStore, childIndex, dispatch, groupItemIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam = currentParam.children[groupItemIndex];

		currentParam["willChangeParent"][index][field] = value;

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	// 删除改变规则
	removeChangeRuleItem = (i) => {
		let { templateStore, childIndex, dispatch, groupItemIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam = currentParam.children[groupItemIndex];

		currentParam.willChangeParent.splice(i, 1);

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	// 添加改变规则
	addChangeRuleItem = () => {
		let { templateStore, childIndex, groupItemIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam = currentParam.children[groupItemIndex];

		if (!currentParam["willChangeParent"]) {
			currentParam["willChangeParent"] = [];
		}
		let ruleItemTemp = {
			name: null,
			changeMode: null,
			modeValueList: [],
			changeType: null,
			mapName: null,
			mapLocation: null
		};
		currentParam["willChangeParent"].push(ruleItemTemp);
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}
	render() {
		let { templateStore, childIndex, groupItemIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children[childIndex].children[groupItemIndex];
		return (
			<div>
				<div className="single-param-item">
					<div className="param-title">
						<span>改变父节点规则：</span>
					</div>
					<div className="param-field">
						<span
							className="add-new-operation"
							onClick={this.addChangeRuleItem.bind(this)}
						>
							<Icon type="plus-square-o" />新增
						</span>
					</div>
				</div>
				{
					currentParam["willChangeParent"] &&
                    currentParam["willChangeParent"].length > 0 &&
                    <div className="template-param-rule-list">
                    	{
                    		currentParam["willChangeParent"].map((ruleItem, ruleIndex) => {
                    			return (
                    				<div className="template-param-rule-section">
                    					<div
                    						className="remove-rule-item"
                    						onClick={this.removeChangeRuleItem.bind(this, ruleIndex)}
                    					>
                    						<Icon type="delete" />
                    					</div>

                    					<div className="template-param-rule-item">
                    						<div className="template-param-rule-title">
                    							<span>改变形式：</span>
                    						</div>
                    						<div className="template-param-rule-field">
                    							<Select
                    								placeholder="选择被改变节点类型"
                    								value={ruleItem.changeMode || undefined}
                    								onChange={this.changeRuleValue.bind(this, ruleIndex, "changeMode", "select")}
                    							>
                    								<Option value="whenSomeValue">特定值时改变</Option>
                    								<Option value="whenSomeType">特定类型时改变</Option>
                    							</Select>
                    						</div>
                    					</div>
                    					{
                    						ruleItem.changeMode &&
                                            ruleItem.changeMode === "whenSomeValue" &&
                                            <div className="template-param-rule-item">
                                            	<div className="template-param-rule-title">
                                            		<span>改变形式value：</span>
                                            	</div>
                                            	<div className="template-param-rule-field">
                                            		<Select
                                            			mode="tags"
                                            			placeholder="输入特定值，可以输入多个"
                                            			value={ruleItem.modeValueList || undefined}
                                            			onChange={this.changeRuleValue.bind(this, ruleIndex, "modeValueList", "select")}
                                            		>
                                            		</Select>
                                            	</div>
                                            </div>
                    					}
                    					{
                    						ruleItem.changeMode &&
                                            ruleItem.changeMode === "whenSomeType" &&
                                            <div className="template-param-rule-item">
                                            	<div className="template-param-rule-title">
                                            		<span>改变形式value：</span>
                                            	</div>
                                            	<div className="template-param-rule-field">
                                            		<Select
                                            			mode="tags"
                                            			placeholder="选择特定类型，可以多选"
                                            			value={ruleItem.modeValueList || undefined}
                                            			onChange={this.changeRuleValue.bind(this, ruleIndex, "modeValueList", "select")}
                                            		>
                                            			<Option value="string">string</Option>
                                            			<Option value="int">int</Option>
                                            			<Option value="double">double</Option>
                                            			<Option value="datetime">datetime</Option>
                                            			<Option value="enum">enum</Option>
                                            			<Option value="boolean">boolean</Option>
                                            		</Select>
                                            	</div>
                                            </div>
                    					}

                    					<div className="template-param-rule-item">
                    						<div className="template-param-rule-title">
                    							<span>被改变节点：</span>
                    						</div>
                    						<div className="template-param-rule-field">
                    							<Select
                    								placeholder="选择节点handle"
                    								value={ruleItem.name || undefined}
                    								onChange={this.changeRuleValue.bind(this, ruleIndex, "name", "select")}
                    							>
                    								{
                    									params &&
														params.map((paramItem, paramIndex) => {
															return (
																<OptGroup
																	label={paramItem.labelText || "暂无标题"}
																	key={paramIndex}
																>
																	{
																		paramItem.children &&
																		paramItem.children.filter(fItem => fItem.name).map((subItem, subIndex) => {
																			return (
																				<Option
																					value={subItem.name || undefined}
																					disabled={subItem["name"] === children[childIndex].name}
																					key={subIndex}
																				>
																					[{subItem.componentType}] {subItem.name}
																				</Option>
																			);
																		})
																	}
																</OptGroup>
															);
														})
                    								}
                    							</Select>
                    						</div>
                    					</div>
                    					<div className="template-param-rule-item">
                    						<div className="template-param-rule-title">
                    							<span>改变类型：</span>
                    						</div>
                    						<div className="template-param-rule-field">
                    							<Select
                    								placeholder="选择被改变节点类型"
                    								value={ruleItem.changeType || undefined}
                    								onChange={this.changeRuleValue.bind(this, ruleIndex, "changeType", "select")}
                    							>
                    								<Option value="emptyValue">清空被改变节点当的值</Option>
                    								<Option value="changeValue">改变值</Option>
                    							</Select>
                    						</div>
                    					</div>
                    					{
                    						ruleItem.changeType &&
                                            ruleItem.changeType === "changeValue" &&
                                            <div className="template-param-rule-item">
                                            	<div className="template-param-rule-title">
                                            		<span>将值改变为：</span>
                                            	</div>
                                            	<div className="template-param-rule-field">
                                            		<Input
                                            			value={ruleItem.valueChangeTo || undefined}
                                            			onChange={this.changeRuleValue.bind(this, ruleIndex, "valueChangeTo", "input")}
                                            			placeholder="选择节点改变后的值"
                                            		/>
                                            	</div>
                                            </div>
                    					}
                    				</div>
                    			);
                    		})
                    	}
                    </div>
				}
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(ChangeGroupRuleForParent);
