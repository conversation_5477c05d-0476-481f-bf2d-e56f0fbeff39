// 导入策略模式
const showModeList = {
	"policySet": { "zbMode": true, "policyMode": true },
	"rules": { "zbMode": true, "ruleMode": true },
	"publicRules": { "zbMode": true, "ruleMode": true },
	"workflow": { "zbMode": true, "policyMode": true, "workflowMode": true },
	"index": { "zbMode": true }
};

// 校验导入文件后缀
const importFileType = {
	"policySet": "pls",
	"rules": "ply",
	"publicRules": "ply",
	"workflow": "pld",
	"index": "zb"
};

// 展示导入数目
const showNumType = {
	"policySet": { "zbMode": true, "policyMode": true, "ruleMode": true },
	"rules": { "zbMode": true, "ruleMode": true },
	"publicRules": { "zbMode": true, "ruleMode": true },
	"workflow": { "zbMode": true, "policyMode": true, "ruleMode": true },
	"index": { "zbMode": true }
};

export default {
	showModeList,
	importFileType,
	showNumType
};
