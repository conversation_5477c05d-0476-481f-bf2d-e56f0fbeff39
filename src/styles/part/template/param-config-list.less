:global {
    .param-config-list {
        & {
            list-style: none;
            height: ~"calc(100vh - 222px)";
            overflow: scroll;
        }

        .param-config-item {
            & {
                clear: both;
                margin-bottom: 10px;
                overflow: hidden;
            }

            &.mb20 {
                margin-bottom: 20px;
            }

            .param-title {
                float: left;
                width: 20%;
                text-align: right;
                height: 32px;
                line-height: 32px;

                em {
                    position: relative;
                    top: 10px;
                    color: red;
                    font-size: 26px;
                    margin-left: 6px;
                    line-height: 14px;
                }
            }

            .param-field {
                & {
                    float: left;
                    width: 80%;
                    font-size: 14px;
                    min-height: 32px;
                    line-height: 32px;
                }

                textarea {
                    font-size: 14px;
                    height: auto;
                    overflow: hidden;
                }
            }
        }
    }

    .single-param {
        & {
            list-style: none;
            border: 1px solid #dcdcdc;
            padding: 0 20px 20px;
            height: auto;
            overflow: hidden;
            border-radius: 5px;
            margin-bottom: 20px;
            background: #f0f0f0;
        }

        &:last-child {
            margin-bottom: 0;
        }

        .single-param-header {
            & {
                position: relative;
                height: 44px;
                line-height: 44px;
                border-bottom: 1px solid #dcdcdc;
                margin-bottom: 20px;
                font-size: 16px;
            }

            h2 {
                font-size: 16px;
            }

            i {
                & {
                    position: absolute;
                    right: 0;
                    top: 0;
                    height: 44px;
                    line-height: 44px;
                    cursor: pointer;
                }

                i:hover {
                    color: @red;
                }
            }
        }

        .single-param-item {
            & {
                height: auto;
                line-height: 48px;
                clear: both;
                margin-bottom: 10px;
                overflow: hidden;
            }

            .param-title {
                float: left;
                width: 20%;
                text-align: right;
                height: 32px;
                line-height: 32px;

                em {
                    position: relative;
                    top: 10px;
                    color: red;
                    font-size: 26px;
                    margin-left: 6px;
                    line-height: 14px;
                }
            }

            .param-field {
                & {
                    float: left;
                    width: 80%;
                    font-size: 14px;
                    min-height: 32px;
                    line-height: 32px;
                }

                textarea {
                    font-size: 14px;
                    height: auto;
                    overflow: hidden;
                    width: 100%;
                }

                select {
                    width: 100%;
                }

                .add-select-option {
                    & {
                        color: #48a0e3;
                        cursor: pointer;
                    }

                    &:hover {
                        text-decoration: underline;
                    }
                }

                .add-new-operation {
                    & {
                        cursor: pointer;
                    }

                    &:hover {
                        text-decoration: underline;
                        color: @blue;
                    }

                    i {
                        margin-right: 4px;
                    }
                }
            }

            .param-select-option {
                & {
                    //margin-left: 20%;
                    margin-left: 0;
                    clear: both;
                }

                ul {
                    & {
                        list-style: none;
                        margin: 0;
                        padding: 0;
                    }

                    li {
                        & {
                            position: relative;
                            margin-bottom: 10px;
                        }

                        .param-select-option-delete {
                            & {
                                position: absolute;
                                right: 0px;
                                bottom: 4px;
                                font-size: 24px;
                                opacity: 0.45;
                                z-index: 999;
                                cursor: pointer;
                            }

                            &:hover {
                                color: #e95e4c;
                                opacity: 0.85;
                            }
                        }
                    }
                }
            }
        }
    }

    .group-child-list {
        .group-child-item {
            & {
                border: 1px solid #e6e6e6;
                background: #f3f4f5;
                height: auto;
                overflow: hidden;
                padding: 0 20px 20px 20px;
                margin-bottom: 10px;
                font-size: 12px;
            }

            &:last-child {
                margin-bottom: 0;
            }

            .child-item-header {
                & {
                    height: 32px;
                    border-bottom: 1px solid #e6e6e6;
                    margin-bottom: 20px;
                }

                span {
                    display: inline-block;
                }

                .title {
                    float: left;
                    font-size: 14px;
                }

                .delete {
                    float: right;
                    cursor: pointer;

                    &:hover {
                        color: red;
                    }
                }
            }

            .param-item {
                & {
                    height: auto;
                    overflow: hidden;
                    clear: both;
                }
            }
        }
    }

    .react-ace-area {
        & {
            position: relative;
        }

        .read-only-tip {
            position: absolute;
            right: 20px;
            top: 20px;
            z-index: 10;
            font-size: 24px;
            color: #b7aa9a;
        }
    }

    .add-single-param-handle {
        & {
            width: 100%;
            height: 36px;
            line-height: 35px;
            background: #fff;
            border: 1px solid #dcdcdc;
            display: flex;
        }

        span {
            & {
                flex: 1;
                // width: 14.28%;
                display: inline-block;
                text-align: center;
                border-right: 1px solid #e6e6e6;
                cursor: pointer;
                line-height: 36px;
            }

            &:hover {
                background: #1d8bd8;
                color: #fff;
            }

            &:last-child {
                border-right: none;
            }
        }

    }
}
