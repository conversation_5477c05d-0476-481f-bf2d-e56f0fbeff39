import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import Store from "store";
import request, { downloadReport } from "../utils/request";

const getReplayTaskList = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/policySet/replay/list", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getReplayTaskDetail = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/policySet/replay/hitDetail", params), {
		method: "GET",
		headers: getHeader()
	});
};

const terminateReplayTask = async(params) => {
	params = deleteEmptyObjItem(params);
	return request("/admin/policySet/replay/terminate", {
		method: "PUT",
		headers: getHeader(),
		body: params
	});
};

/**
 * @description: 导出报告-导出excel
 */
const exportReport = async(params, name, fileType) => {
	return downloadReport(getUrl("/admin/policySet/replay/hitDetail/export", params), {
		method: "GET",
		headers: getHeader()
	}, name, fileType);
};

export default {
	getReplayTaskList,
	getReplayTaskDetail,
	terminateReplayTask,
	exportReport
};
