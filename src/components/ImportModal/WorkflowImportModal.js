import { PureComponent } from "react";
import { connect } from "dva";
import { workflowAPI } from "@/services";
import { workflowLang } from "@/constants/lang";
import ImportCommon from "./ImportCommon";

class WorkflowImportModal extends PureComponent {
	state = {
		file: null,
		replaceScene: "",
		policyMode: "",
		zbMode: ""
	};
	// 变更值的时候
	changeImport = (e, fileName, type = "input") => {
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		this.setState({
			[fileName]: value
		});
	}
	// 导入成功回调
	importCallback = () => {
		let { policyEditorStore, refreshWorkflow, dispatch, globalStore } = this.props;
		this.setState({
			file: null
		});
		// 导入成功后
		// 1. 刷新工作流流程
		refreshWorkflow();
		// 2. 刷新编辑区策略列表
		let { currentApp } = globalStore;
		if (currentApp.name) {
			let { curPage, pageSize } = policyEditorStore;
			dispatch({
				type: "policyEditor/getPolicySets",
				payload: {
					appName: currentApp.name ? currentApp.name : null,
					curPage: curPage,
					pageSize: pageSize
				}
			});
		}
	}
	// 关闭弹窗
	closeModal() {
		let { dispatch } = this.props;
		console.log(12);
		dispatch({
			type: "workflow/setDialogShow",
			payload: {
				WorkflowImport: false
			}
		});
		this.setState({
			file: null,
			replaceScene: "",
			policyMode: "",
			zbMode: ""
		});
	}

	render() {
		const { state } = this;
		let { workflowStore } = this.props;
		let { dialogShow, policySetItem } = workflowStore;
		console.log(state);
		return (
			<ImportCommon
				title={workflowLang.workflowImportModal("title")} // lang:"规则流导入"
				visible={dialogShow.WorkflowImport}
				modalData={{ ...state, policySetUuid: policySetItem.uuid }}
				importType="workflow"
				changeField={this.changeImport.bind(this)}
				onCancel={this.closeModal.bind(this)}
				importCallback={this.importCallback.bind(this)}
				fetchMethod={workflowAPI.importWorkflow}
			/>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor,
	workflowStore: state.workflow
}))(WorkflowImportModal);
