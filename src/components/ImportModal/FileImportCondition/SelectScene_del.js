import { PureComponent, Fragment } from "react";
import { Row, Col, Radio, Icon, Tooltip } from "antd";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import Scene from "./Scene";

export default class SelectScene extends PureComponent {
	render() {
		const { changeImport, data, type } = this.props;
		const { replaceScene } = data || {};
		let defaultMsg = "";
		switch (type) {
			case "policySet": defaultMsg = indexListLang.ruleConfig("scenePolicySet"); break; // 默认选择导入策略作为指标场景
			case "rules": defaultMsg = indexListLang.ruleConfig("sceneRules"); break; // 默认选择导入规则作为指标场景
			case "workflow": defaultMsg = indexListLang.ruleConfig("sceneWorkFlow"); break; // 默认选择导入规则流作为指标场景
			case "index": defaultMsg = indexListLang.ruleConfig("sceneIndex"); break; //  忽略异常不处理
		}
		return (
			<Fragment>
				<Row gutter={CommonConstants.gutterSpan} className="import-mode-wrap select-scene">
					<Col span={5} className="basic-info-title">
						{/* lang:场景 */}
						{indexListLang.ruleConfig("allMissScene")}
					</Col>
					<Col span={19}>
						<Radio.Group
							onChange={(e) => {
								changeImport(e.target.value === "1" ? [] : "", "replaceScene", "scene");
							}}
							value={replaceScene ? "1" : "0"}
						>
							<Radio value="0">
								<span style={{ "whiteSpace": "pre-wrap" }}>
									{defaultMsg}
									{
										type === "index" &&
										// 指标导入无场景，指标列表可能无法查询到此导入指标，手动创建场景后，可显示
										<Tooltip title={indexListLang.ruleConfig("indexMissWarnTip")}>
											<Icon className="ml10" type="question-circle" />
										</Tooltip>
									}
								</span>
							</Radio>
							<Radio value="1">
								{/* 批量设置场景 */}
								{indexListLang.ruleConfig("batchSetScene")}
							</Radio>
						</Radio.Group>
					</Col>
				</Row>
				{
					replaceScene &&
					<Row className="select-scene">
						<Col span={5} className="basic-info-title"></Col>
						<Col span={19}>
							<Scene
								replaceScene={replaceScene}
								changeImport={changeImport}
							/>
						</Col>
					</Row>
				}
			</Fragment>
		);
	}
}
