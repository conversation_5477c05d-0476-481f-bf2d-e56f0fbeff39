import { getHeader, getUrl } from "./common";
import { downloadFileHandle } from "../utils/request";
import request, { PostForm } from "../utils/request";

const getSalaxyList = async(params) => {
	return request(getUrl("/admin/salaxy", params), {
		method: "GET",
		headers: getHeader()
	});
};
const addSalaxy = async(params) => {
	return request("/admin/salaxy", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const modifySalaxy = async(params) => {
	return request("/admin/salaxy", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const deleteSalaxy = async(params) => {
	return request(getUrl("/admin/salaxy", params), {
		method: "DELETE",
		headers: getHeader()
	});
};
const changeSalaxyStatus = async(params) => {
	return request("/admin/salaxy/status", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const viewIndexItemDetail = async(params) => {
	return request(getUrl("/admin/salaxy/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};
const getIndexCite = async(params) => {
	return request(getUrl("/admin/salaxy/refinfo", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 测试导出指标
const exportIndexListTest = async(params) => {
	return request(getUrl("/admin/salaxy/export", params), {
		method: "GET",
		headers: getHeader()
	});
};
// 导出所选指标列表
const exportIndexList = async(params) => {
	return downloadFileHandle(getUrl("/admin/salaxy/export", params), {
		method: "GET",
		headers: getHeader()
	}, params.fileName, params.fileType);
};

/*
* 导入指标
* @nichunlong
* */
const importIndex = async(params) => {
	const res = await PostForm("/admin/salaxy/import", "post", { ...params });
	return res;
};

/*
* 指标提交版本
* @nichunlong
* */
const indexVersionSubmit = async(params) => {
	return request("/admin/salaxyVersion/submit", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

/*
* 判断是否确实需要离线计算
* @nichunlong
* */
const judgeNeedOffline = async(params) => {
	return request(getUrl("/admin/salaxyVersion/offlineCheck", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 批量提交
const batchSubmit = async(params) => {
	return request("/admin/salaxyVersion/batchSubmit", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

// 查看授权列表
const licenseApps = async(params) => {
	return request(getUrl("/admin/salaxy/licenseApps", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 修改授权列表
const modifyLicenseApps = async(params) => {
	return request("/admin/salaxy/licenseApps", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

export default {
	getSalaxyList,
	addSalaxy,
	modifySalaxy,
	deleteSalaxy,
	changeSalaxyStatus,
	viewIndexItemDetail,
	getIndexCite,
	exportIndexListTest,
	exportIndexList,
	importIndex,
	indexVersionSubmit,
	judgeNeedOffline,
	batchSubmit,
	licenseApps,
	modifyLicenseApps
};
