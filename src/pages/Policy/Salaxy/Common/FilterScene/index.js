// 场景过滤
import { PureComponent } from "react";
import { connect } from "dva";
import { CommonConstants } from "@/constants";
import { Input, Select, Row, Col } from "antd";
import { indexListLang } from "@/constants/lang";
import AppScene from "@/components/SelectScene/AppScene";
import SingleScene from "@/components/SelectScene/SingleScene";
import ShowAppScene from "@/components/SelectScene/ShowAppScene";
import ShowSingleScene from "@/components/SelectScene/ShowSingleScene";

const InputGroup = Input.Group;
const { Option } = Select;

const Logic = [
	{
		"name": "eq",
		"dName": "等于",
		"enDName": "Be equal to"
	}
// , {
// 	"name": "partof",
// 	"dName": "属于",
// 	"enDName": "Belong to"
// }
];
class FilterScene extends PureComponent {
	constructor(props) {
		super(props);
	}
	render() {
		let { globalStore, indexData, onChange, disabled, colSpanLeft, colSpanRight, type, width } = this.props;
		let { allMap, personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		const { sceneType, sceneList } = indexData || {};
		// 获取场景条件
		const { sceneTypeSelect, sceneSelect } = allMap;
		const sceneSelectList = sceneSelect && sceneSelect[sceneType] ? sceneSelect[sceneType] : [];

		return (
			<Row
				gutter={CommonConstants.gutterSpan}
				className="mb10"
			>
				<Col span={colSpanLeft || 4} className="basic-info-title">
					{/* lang:场景 */}
					{indexListLang.ruleConfig("scene")}
				</Col>
				<Col span={colSpanRight || 10}>
					<div className="mb10">
						<InputGroup compact type={type}>
							<Select
								disabled={disabled}
								style={{"width": width || "50%" }}
								placeholder={indexListLang.searchParams("selectSceneType")} // lang:选择场景类型
								value={sceneType || undefined}
								showSearch
								optionFilterProp="children"
								dropdownMatchSelectWidth={false}
								onChange={(e) => {
									onChange("sceneList", null, "select"); // 联动场景
									onChange("sceneType", e, "select");
								}}
							>
								{
									sceneTypeSelect &&
									sceneTypeSelect.length > 0 &&
									sceneTypeSelect.map(v=>{
										if (v.name !== "ALL") {
											return (
												<Option value={v.name} key={v.name}>{v.dName}</Option>
											);
										}
									})
								}
							</Select>
							<Select
								disabled
								placeholder={indexListLang.ruleConfig("selectLogic")} // lang:选择运算符
								value="eq"
								style={{"width": width ? `calc(100% - ${width})` : "50%"}}
								onChange={(e) => {
									onChange("sceneLogic", e, "select");
								}}
							>
								{
									Logic.map((v)=> (
										<Option key={v.name} value={v.name}>
											{lang === "cn" ? v.dName : v.enDName}
										</Option>
									))
								}
							</Select>
						</InputGroup>
					</div>
					{
						sceneType ? (
							!disabled && (
								sceneType === "EVENT"
									? <AppScene
										disabled={disabled}
										sceneListSource = {sceneSelectList}
										scene={sceneList}
										changeField={onChange}
										name="sceneList"
									/>
									: <SingleScene
										disabled={disabled}
										sceneListSource = {sceneSelectList}
										scene={sceneList}
										changeField={onChange}
										name="sceneList"
									/>
							)
						) : null
					}
					{
						sceneType ? (
							disabled && (
								sceneType === "EVENT"
									? <ShowAppScene
										sceneListSource = {sceneSelectList}
										scene={sceneList}
									/>
									: <ShowSingleScene
										sceneListSource = {sceneSelectList}
										scene={sceneList}
									/>
							)
						) : null
					}

				</Col>
			</Row>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(FilterScene);

