import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"basicSet": {
			"cn": "基本设置",
			"en": "Basic"
		},
		"ruleManagement": {
			"cn": "规则管理",
			"en": "Rules"
		},
		"select": {
			"cn": "请选择",
			"en": "select"
		}
	};
	return params[field][lang];
};

const tipInfo = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"addSuccess": {
			"cn": "添加规则成功！",
			"en": "Add the rules for success!"
		},
		"removeStag": {
			"cn": "从暂存区移除",
			"en": "Remove from the staging area"
		},
		"addRuleToPolicy": {
			"cn": "添加此规则到当前策略",
			"en": "This rule is added to the current policy"
		},
		"goBack": {
			"cn": "返回策略集列表",
			"en": "Return policy set list"
		},
		"goBackPublic": {
			"cn": "返回",
			"en": "Go back"
		},
		"toEditArea": {
			"cn": "跳转到编辑区",
			"en": "To edit area"
		},
		"toRunArea": {
			"cn": "跳转到运行区",
			"en": "To run-time area"
		},
		"stratVersionSub": {
			"cn": "策略版本提交",
			"en": "Policy version submit"
		},
		"noPermission": {
			"cn": "无权限操作",
			"en": "No permission to operate"
		},
		"lookStrat": {
			"cn": "查看本策略指标",
			"en": "View cited indicators"
		},
		"hasCopy": {
			"cn": "已复制规则暂存区",
			"en": "Copied rules area"
		},
		"hasCopy2": {
			"cn": "管理已复制规则",
			"en": "Copied rules management"
		},
		"quickAdd": {
			"cn": "快捷键C快速添加规则",
			"en": "Press C to add a rule"
		},
		"searchRule": {
			"cn": "搜索规则",
			"en": "Search rules"
		},
		"sortRule": {
			"cn": "规则排序",
			"en": "Rule sort"
		},
		"inputKey": {
			"cn": "请输入关键词搜索",
			"en": "Please enter a keyword"
		},
		"source": {
			"cn": "来源",
			"en": "source"
		},
		"cantPaste": {
			"cn": "当前规则已被删除，无法粘贴！",
			"en": "Current rule has been deleted, can't paste!"
		},
		"stagNoRules": {
			"cn": "暂存区暂无规则",
			"en": "No rule in temporary area"
		},
		"hasCopyStag": {
			"cn": "已复制规则暂存区",
			"en": "Copied rule area"
		},
		"sureClear": {
			"cn": "您确认要清空暂存区吗？",
			"en": "Are you sure to empty the temporary area?"
		},
		"clear": {
			"cn": "清空",
			"en": "clear"
		},
		"viewHistoryVersion": {
			"cn": "查看历史版本",
			"en": "View history version"
		}
	};
	return params[field][lang];
};

const drawer = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"title1": {
			"cn": "规则模板列表",
			"en": "Rule template list"
		},
		"title2": {
			"cn": "指标模板列表",
			"en": "Indicator template list"
		},
		"inputPlaceholder": {
			"cn": "规则模板搜索",
			"en": "Search rule template"
		},
		"inputPlaceholder2": {
			"cn": "指标模板搜索",
			"en": "Search indicator template"
		}
	};
	return params[field][lang];
};
const policyVersion = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		//	策略历史版本
		"policyHistoryVersion": {
			"cn": "策略历史版本",
			"en": "Policy history version"
		},
		"policy": {
			"cn": "策略",
			"en": "Policy"
		},
		"overwritten": {
			"cn": "覆盖回编辑区",
			"en": "Overwritten to the edit area?"
		},
		"versionId": {
			"cn": "版本编号",
			"en": "ID"
		},
		"versionStatus": {
			"cn": "版本状态",
			"en": "Status"
		},
		"description": {
			"cn": "发版描述",
			"en": "Description"
		},
		"date": {
			"cn": "发版时间",
			"en": "Date"
		},
		"officialVersion": {
			"cn": "正式版本",
			"en": "Official Version"
		},
		"historyVersion": {
			"cn": "历史版本",
			"en": "History Version"
		},
		"overwritePopConfirmTitle": {
			"cn": "您真的要将此版本覆盖回编辑区吗？",
			"en": "Do you want to overwrite this version back into the editing area?"
		},
		"policySwitchSuccess": {
			"cn": "策略版本切换成功！",
			"en": "Policy version switch successfully"
		},
		"policySwitchTipTitle": {
			"cn": "覆盖编辑区提醒",
			"en": "Overwrite edit area reminders"
		},
		"policySwitchTipContent": {
			"cn": "当前策略版本已覆盖至编辑区，是否跳转编辑区查看？",
			"en": "Current policy has overwrite to editing area. Jump to view?"
		},
		"yes": {
			"cn": "是",
			"en": "Yes"
		},
		"no": {
			"cn": "否",
			"en": "No"
		},
		"currentViewVersion": {
			"cn": "当前查看的策略版本：",
			"en": "Current policy version:"
		},
		"returnCurrentVersion": {
			"cn": "点击返回当前版本",
			"en": "Click to return to the current version"
		}
	};
	return params[field][lang];
};

const policyCommitModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"title": {
			"cn": "策略版本提交",
			"en": "Strategy version submitted"
		},
		"policyName": {
			"cn": "策略名称",
			"en": "Policy Name"
		},
		"inputPolicyName": {
			"cn": "请输入策略名称",
			"en": "Please enter the name strategy"
		},
		"releaseDes": {
			"cn": "发版描述",
			"en": "Release described"
		},
		"inputReleaseDes": {
			"cn": "请输入发版描述",
			"en": "Please enter the release"
		}
	};
	return params[field][lang];
};
const ruleSortModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"title": {
			"cn": "规则排序",
			"en": "Rule Sort"
		},
		"alert": {
			"cn": "拖拽列表项可以改变规则排序",
			"en": "Drag and drop list items to change the order"
		},
		"noSaveRule": {
			"cn": "暂无已保存规则",
			"en": "No saved rules yet"
		}
	};
	return params[field][lang];
};

const basicSetup = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"tip1": {
			"cn": "请输入策略名称",
			"en": "Please enter policy name"
		},
		"tip2": {
			"cn": "策略集名称长度应大于1,小于50",
			"en": "The policy set name length should be greater than 1 and less than 50"
		},
		"tip3": {
			"cn": "策略名称：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合",
			"en": "Policy name: please do not enter illegal characters such as complete Angle character, crossed, suggested that the combination of input Chinese, English, Numbers, and underscores"
		},
		"tip4": {
			"cn": "风险阈值配置存在空值，请补充完整！",
			"en": "Risk threshold configuration is null, please complete!"
		},
		"tip5": {
			"cn": "保存策略成功",
			"en": "Save policy successfully"
		},
		"policyName": {
			"cn": "策略名称",
			"en": "Policy Name"
		},
		"inputPolicyName": {
			"cn": "请输入策略名称",
			"en": "Please enter the policy name"
		},
		"applyName": {
			"cn": "应用名",
			"en": "Apply Name"
		},
		"eventType": {
			"cn": "事件类型",
			"en": "Event Type"
		},
		"eventFlag": {
			"cn": "事件标识",
			"en": "Event Flag"
		},
		"inputEventFlag": {
			"cn": "请输入事件标识",
			"en": "Please enter the event id"
		},
		"riskType": {
			"cn": "风险类型",
			"en": "Risk Type"
		},
		"strategyPattern": {
			"cn": "策略模式",
			"en": "Strategy Pattern"
		},
		"riskThreshold": {
			"cn": "风险阈值",
			"en": "Risk Threshold"
		},
		"tags": {
			"cn": "策略标签",
			"en": "Tags"
		},
		"describe": {
			"cn": "描述",
			"en": "Describe"
		},
		"noPermission": {
			"cn": "无权限操作",
			"en": "No permission operation"
		},
		"save": {
			"cn": "保存",
			"en": "Save"
		},
		"goBack": {
			"cn": "返回",
			"en": "Go Back"
		},
		"weightModeInfo": {
			"cn": "权重模式规则，设置权重分映射风险决策结果，权重区间段左侧分数开区间，右侧区间段闭区间",
			"en": "Weight mode rule, set the risk decision result of weight score mapping, left score open interval and right interval closed interval of weight interval segment"
		}
	};
	return params[field][lang];
};

const ruleList = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"tip1": {
			"cn": "更改规则状态成功！",
			"en": "Change the rules of the state success!"
		},
		"tip2": {
			"cn": "删除规则提醒",
			"en": "Delete rules to remind"
		},
		"tip3": {
			"cn": "您真的要删除《",
			"en": "Do you really want to delete《"
		},
		"tip4": {
			"cn": "》吗？",
			"en": "》？"
		},
		"tip5": {
			"cn": "删除规则成功！",
			"en": "Rule was removed successfully!"
		},
		"tip6": {
			"cn": "暂存区已存在此规则，无需重复复制！",
			"en": "Existing registers the rules, without having to repeat copy!"
		},
		"tip7": {
			"cn": "规则已经复制到暂存区",
			"en": "Rules have been copied to the staging area"
		},
		"tip8": {
			"cn": "当前IF规则未保存，无法添加子规则！",
			"en": "The current rules IF not saved, can't add a subrule!"
		},
		"saveFirst": {
			"cn": "如需复制请先保存",
			"en": "If you want to copy please save first"
		},
		"copyRule": {
			"cn": "拷贝规则",
			"en": "Copy the rule"
		},
		"ifNoSupportCopy": {
			"cn": "IF规则不支持复制",
			"en": "IF the rules do not support copy"
		},
		"ifsubNoSupportCopy": {
			"cn": "IF子规则不支持复制",
			"en": "IF the sub-rule does not support replication"
		},
		"newNoSave": {
			"cn": "新添加，未保存",
			"en": "The newly added, not saved"
		},
		"hasChange": {
			"cn": "有修改",
			"en": "There are changes"
		},
		"addList": {
			"cn": "添加到名单",
			"en": "Add to the list"
		},
		"noPermission": {
			"cn": "无权限操作",
			"en": "No permission operation"
		},
		"deleteRules": {
			"cn": "删除规则",
			"en": "Delete rule"
		},
		"close": {
			"cn": "关闭",
			"en": "close"
		},
		"simulation": {
			"cn": "模拟",
			"en": "simulation"
		},
		"formal": {
			"cn": "正式",
			"en": "formal"
		},
		"noId": {
			"cn": "暂无ID",
			"en": "None ID"
		},
		"addSubrule": {
			"cn": "添加子规则",
			"en": "Add a subrule"
		},
		"ruleName": {
			"cn": "规则名称",
			"en": "Rule name"
		},
		"operation": {
			"cn": "操作",
			"en": "Operation"
		},
		"ruleState": {
			"cn": "规则状态",
			"en": "Status"
		},
		"ruleImmuno": {
			"cn": "规则免疫",
			"en": "Immuno"
		},
		"ruleCode": {
			"cn": "规则编号",
			"en": "Rule ID"
		},
		"viewImmuno": {
			"cn": "查看规则免疫信息",
			"en": "View rule immunity info"
		}
	};
	return params[field][lang];
};

const ruleConfig = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"tip1": {
			"cn": "操作配置存在空值，请补充完整！",
			"en": "Operating configuration is null, please complete!"
		},
		"tip2": {
			"cn": "输入框为空，请补充完整！",
			"en": "Input box is empty, please complete!"
		},
		"tip3": {
			"cn": "选项为空，请选择！",
			"en": "Option is empty, please choose!"
		},
		"tip4": {
			"cn": "存在空值，请补充完整！",
			"en": "Is null, please complete!"
		},
		"tip5": {
			"cn": "条件配置存在空值，请补充完整！",
			"en": "Condition configuration is null, please complete!"
		},
		"tip6": {
			"cn": "新增规则成功",
			"en": "New rules for success"
		},
		"tip7": {
			"cn": "更新规则成功",
			"en": "Update rules successful"
		},
		"nameEmptyTip": {
			"cn": "规则名称不能为空",
			"en": "Rule name can not empty"
		},
		"nameLengthTip": {
			"cn": "规则名称长度不能超过80个字符",
			"en": "Rule name cannot be longer than 80 characters"
		},
		"basicSetup": {
			"cn": "基本设置",
			"en": "Basic setup"
		},
		"conditionsConfiguration": {
			"cn": "条件配置",
			"en": "Conditions for configuration"
		},
		"operatingConfiguration": {
			"cn": "操作配置",
			"en": "Operating configuration"
		},
		"packUp": {
			"cn": "收起",
			"en": "Pack up"
		},
		"update": {
			"cn": "更新",
			"en": "Update"
		},
		"add": {
			"cn": "添加",
			"en": "Add"
		},
		"noPermission": {
			"cn": "无权限操作",
			"en": "No permission operation"
		},
		"scriptTip": {
			"cn": "规则脚本不能为空",
			"en": "Rule script cannot be empty"
		},
		"limitUpPlaceHolder": {
			"cn": "请输入上限",
			"en": "Please enter the upper limit"
		},
		"limitDownPlaceHolder": {
			"cn": "请输入下限",
			"en": "Please enter the lower limit"
		},
		"lowLessUp": {
			"cn": "上限不能小于下限",
			"en": "Upper limit cannot be less than lower limit"
		},
		"scriptRule": {
			"cn": "脚本规则",
			"en": "Script rules"
		},
		"setCondition": {
			"cn": "请设置执行条件",
			"en": "Please set the execution conditions"
		}
	};
	return params[field][lang];
};

const ruleAttr = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"ruleName": {
			"cn": "规则名称",
			"en": "Rule name"
		},
		"inputStrat": {
			"cn": "请输入策略名称",
			"en": "Please enter the name strategy"
		},
		"riskWeighting": {
			"cn": "风险权重",
			"en": "Risk weighting"
		},
		"defaultOption": {
			"cn": "默认选项",
			"en": "The default option"
		},
		"riskDecision": {
			"cn": "风险决策",
			"en": "Risk decision"
		},
		"riskIntroduced": {
			"cn": "命中规则配置对应的处置方式",
			"en": "Handling method corresponding to hit rule configuration"
		},
		"addition": {
			"cn": "加",
			"en": "addition"
		},
		"substruction": {
			"cn": "减",
			"en": "substruction"
		},
		"multiplication": {
			"cn": "乘",
			"en": "multiplication"
		},
		"division": {
			"cn": "除",
			"en": "division"
		},
		"indicator": {
			"cn": "指标",
			"en": "Indicator"
		},
		"systemField": {
			"cn": "系统字段",
			"en": "System Field"
		},
		"upLimit": {
			"cn": "上限",
			"en": "Upper limit"
		},
		"downLimit": {
			"cn": "下限",
			"en": "Down limit"
		},
		"scriptRule": {
			"cn": "脚本规则",
			"en": "Script rules"
		},
		"rulesOfTheTip": {
			"cn": "规则提示",
			"en": "Rules of the tip"
		},
		"packUp": {
			"cn": "收起",
			"en": "pack up"
		},
		"packUpContent": {
			"cn": "收起所有的规则提示",
			"en": "Unpack all rule prompts"
		},
		"unfold": {
			"cn": "展开",
			"en": "unfold"
		},
		"unfoldContent": {
			"cn": "展开所有的规则提示",
			"en": "Expand all the rule hints"
		},
		"scriptDemo": {
			"cn": "String tradeAmountField = @F_DT_VS_AMOUNT ;\r\n" +
				  "// 注意使用 对象类型 否则可能会空指针\r\n" +
			      "Double tradeAmount = context.getDouble(tradeAmountField);\r\n" +
			      "// 注意空指针判断\r\n" +
			      "if(tradeAmount!=null && tradeAmount >10000){\r\n" +
			      "  return true;\r\n" +
			      "}\r\n" +
			      "return false;",
			"en": "String tradeAmountField = @F_DT_VS_AMOUNT ;\r\n" +
			      "// Use object type or null pointer may occur\r\n" +
			      "Double tradeAmount = context.getDouble(tradeAmountField);\r\n" +
			      "// Pay attention to null pointer judgment\r\n" +
			      "if(tradeAmount!=null && tradeAmount >10000){\r\n" +
			      "  return true;\r\n" +
			      "}\r\n" +
			      "return false;"
		}
	};
	return params[field][lang];
};

const ruleCondition = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"performsConditional": {
			"cn": "执行条件",
			"en": "Performs conditional"
		},
		"mztj1": {
			"cn": "满足以下所有条件",
			"en": "Satisfy all of the following conditions"
		},
		"mztj2": {
			"cn": "满足以下任意条件",
			"en": "Satisfy the following conditions"
		},
		"mztj3": {
			"cn": "以下条件均不满足",
			"en": "The following conditions are not met"
		},
		"mztj4": {
			"cn": "以下条件至少一条不满足",
			"en": "At least one does not meet the following conditions"
		},
		"add1": {
			"cn": "添加规则模板",
			"en": "Add a rule template"
		},
		"add2": {
			"cn": "添加单条条件",
			"en": "Add a single condition"
		},
		"add3": {
			"cn": "添加条件组",
			"en": "Add the conditions set"
		}
	};
	return params[field][lang];
};

const oneCondition = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"and": {
			"cn": "与",
			"en": "and"
		},
		"or": {
			"cn": "或",
			"en": "or"
		},
		"select": {
			"cn": "请选择",
			"en": "select"
		},
		"constant": {
			"cn": "常量",
			"en": "constant"
		},
		"variable": {
			"cn": "变量",
			"en": "variable"
		},
		"inputConstant": {
			"cn": "请输入常量内容",
			"en": "Please enter the constant content"
		},
		"system": {
			"cn": "系统",
			"en": "system"
		},
		"current": {
			"cn": "[当前] ",
			"en": "[current] "
		},
		"index": {
			"cn": "[指标] ",
			"en": "[index] "
		},
		"addOne": {
			"cn": "添加一项",
			"en": "To add a"
		},
		"deleteCur": {
			"cn": "删除当前行",
			"en": "Delete the current line"
		}
	};
	return params[field][lang];
};

const ruleConditionTemplate = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"ruleDescription": {
			"cn": "规则描述",
			"en": "Rule Description"
		},
		"upDown": {
			"cn": "收起/展开条件",
			"en": "Pack up/conditions"
		},
		"deleteCodition": {
			"cn": "删除条件",
			"en": "Delete the conditions of"
		},
		"select": {
			"cn": "请选择",
			"en": "select"
		},
		"system": {
			"cn": "系统",
			"en": "system"
		},
		"index": {
			"cn": "指标 ",
			"en": "index "
		},
		"index2": {
			"cn": "[指标] ",
			"en": "[index] "
		},
		"system2": {
			"cn": "[系统] ",
			"en": "[system] "
		},
		"enter": {
			"cn": "请输入",
			"en": "enter"
		},
		"constant": {
			"cn": "常量",
			"en": "constant"
		},
		"variable": {
			"cn": "变量",
			"en": "variable"
		}
	};
	return params[field][lang];
};

const ruleOperation = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"insertOperation": {
			"cn": "新增操作",
			"en": "Inert Operation"
		},
		"setting": {
			"cn": "设置",
			"en": "setting"
		},
		"select": {
			"cn": "请选择",
			"en": "select"
		},
		"equal": {
			"cn": "等于",
			"en": "equal"
		},
		"constant": {
			"cn": "常量",
			"en": "constant"
		},
		"variable": {
			"cn": "变量",
			"en": "variable"
		},
		"inputConstant": {
			"cn": "请填写常量内容",
			"en": "Please fill out the constant content"
		},
		"current": {
			"cn": "[当前]",
			"en": "[current]"
		},
		"deleteLine": {
			"cn": "删除当前行",
			"en": "Delete the current line"
		}
	};
	return params[field][lang];
};

const addTemplateModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"title1": {
			"cn": "添加内置模板",
			"en": "Add rule template"
		},
		"title2": {
			"cn": "添加子规则",
			"en": "Add a sub-rule"
		},
		"ruleName": {
			"cn": "规则名称",
			"en": "Rule Name"
		},
		"operation": {
			"cn": "操作",
			"en": "Operation"
		},
		"selectTemplate": {
			"cn": "选择当前模板",
			"en": "Select the current template"
		}
	};
	return params[field][lang];
};

const addToCustomListModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"tip1": {
			"cn": "发现参数为空，请填写完整",
			"en": "Found that the parameter is empty, please fill out the complete"
		},
		"tip2": {
			"cn": "不能选择相同的系统字段",
			"en": "Cannot choose the same system field"
		},
		"tip3": {
			"cn": "更新添加到名单成功",
			"en": "Update operation successful"
		},
		"tip4": {
			"cn": "发现多维度名单缺少主键",
			"en": "Missing primary key found in multiple list"
		},
		"preview": {
			"cn": "预览名单配置",
			"en": "Preview list configuration"
		},
		"title": {
			"cn": "添加到名单",
			"en": "Added to the list"
		},
		"cancel": {
			"cn": "取消",
			"en": "Cancel"
		},
		"ok": {
			"cn": "确定",
			"en": "Ok"
		},
		"autoAdd": {
			"cn": "配置自动添加到名单的选项",
			"en": "Configuration is automatically added to the list of options"
		},
		"autoAdd2": {
			"cn": "请配置自动添加到名单的选项",
			"en": "Please configure automatically added to the list of options"
		},
		"new": {
			"cn": "新增一项",
			"en": "A new"
		},
		"select": {
			"cn": "请选择",
			"en": "select"
		},
		"add": {
			"cn": "添加一项",
			"en": "To add a"
		},
		"deleteLine": {
			"cn": "删除当前行",
			"en": "Delete the current line"
		},
		"deleteCurrentConfig": {
			"cn": "删除当前名单配置",
			"en": "Delete the current list config"
		},
		forever: {
			cn: "永久",
			en: "Forever"
		},
		byDay: {
			cn: "日",
			en: "Day"
		},
		byTs: {
			cn: "日期",
			en: "Date"
		},
		byHour: {
			cn: "小时",
			en: "Hour"
		},
		byMin: {
			cn: "分钟",
			en: "Minute"
		}
	};
	return params[field][lang];
};

const policyCiteIndexModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"title": {
			"cn": "当前策略引用指标列表",
			"en": "The current policy reference index list"
		},
		"ruleManagement": {
			"cn": "规则管理",
			"en": "Rule Management"
		}
	};
	return params[field][lang];
};

const immuneListModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"immuneListTitle": {
			"cn": "查看规则免疫列表",
			"en": "View rule immune list"
		},
		"close": {
			"cn": "关闭",
			"en": "Close"
		}
	};
	return params[field][lang];
};

export default {
	common,
	tipInfo,
	drawer,
	policyVersion,
	policyCommitModal,
	ruleSortModal,
	basicSetup,
	ruleList,
	ruleConfig,
	ruleAttr,
	ruleCondition,
	oneCondition,
	ruleConditionTemplate,
	ruleOperation,
	addTemplateModal,
	addToCustomListModal,
	policyCiteIndexModal,
	immuneListModal
};
