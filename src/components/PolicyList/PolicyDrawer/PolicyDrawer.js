import React from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Drawer, Tag } from "antd";
import "./PolicyDrawer.less";
import { CommonConstants, PolicyConstants } from "@/constants";
import { policyListLang, commonLang } from "@/constants/lang";
import { searchToObject } from "@/utils/utils";

class BasicLayout extends React.PureComponent {

	constructor(props) {
		super(props);
		this.closeDrawerHandle = this.closeDrawerHandle.bind(this);
	}

	closeDrawerHandle() {
		let { dispatch } = this.props;
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policyDrawer: false
			}
		});
	}

	render() {
		let { policyEditorStore, globalStore, isRunning, location: { search } } = this.props;
		let { dialogShow, dialogData } = policyEditorStore;
		let { allMap, policyModel, personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";

		let { policyDetail } = dialogData.policyDrawer;

		let statusMap = lang === "cn" ? PolicyConstants.policyStatusMap : PolicyConstants.policyStatusMap2;

		let total = policyDetail.rules ? policyDetail.rules.length : 0;
		let x = policyDetail.rules && policyDetail.rules.filter(item => item.valid === 1).length;
		let y = policyDetail.rules && policyDetail.rules.filter(item => item.valid === 0).length;
		let z = policyDetail.rules && policyDetail.rules.filter(item => item.valid === 2).length;

		// 这块后续要优化
		const searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		const currentTab = searchObj && searchObj.currentTab ? parseInt(searchObj.currentTab, 10) : 1;
		isRunning = currentTab !== 2;

		return (
			<Drawer
				title="Basic Drawer"
				width={550}
				className="policy-drawer-wrap"
				placement="right"
				closable={false}
				onClose={() => {
				}}
				visible={dialogShow.policyDrawer}
				mask={false}
			>
				<div className="policy-detail">
					<div className="policy-detail-title">
						<i className="iconfont icon-celve title-pre"></i>
						<span
							className="policy-detail-title-text text-overflow"
							style={{
								maxWidth: "300px",
								display: "inline-block",
								verticalAlign: "bottom"
							}}
						>
							{policyDetail.name}
						</span>
						{
							statusMap[policyDetail.status] &&
                            <Tag color={statusMap[policyDetail.status]["color"]} className="policy-status">
                            	{statusMap[policyDetail.status]["text"]}
                            </Tag>
						}
						<i className="iconfont icon-close title-close" onClick={this.closeDrawerHandle.bind(this)}></i>
					</div>
					<div className="policy-detail-base">
						<div className="policy-detail-group">
							<label>
								{/* lang:所属应用 */}
								{policyListLang.detailDrawer("appName")}
							</label>
							<span>
								{
									allMap && allMap["appNames"] && allMap["appNames"].map((item, index) => {
										if (item.name === policyDetail.appName) {
											return item.dName;
										}
									})
								}
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:事件类型 */}
								{policyListLang.detailDrawer("eventType")}
							</label>
							<span>
								{
									allMap && allMap["EventType"] && allMap["EventType"].map((item, index) => {
										if (item.name === policyDetail.riskEventType) { return item.dName; }
									})
								}
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:事件标识 */}
								{policyListLang.detailDrawer("eventId")}
							</label>
							<span>{policyDetail.riskEventId}</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:风险类型 */}
								{policyListLang.detailDrawer("riskType")}
							</label>
							<span>
								{
									allMap &&
                                    allMap[policyDetail.riskEventType] &&
                                    allMap[policyDetail.riskEventType].map((item, index) => {
                                    	if (item.name === policyDetail.riskType) {
                                    		return item.dName;
                                    	}
                                    })
								}
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:策略模式 */}
								{policyListLang.detailDrawer("policyMode")}
							</label>
							<span>
								{
									policyModel && policyModel.map((item, index) => {
										if (item.name === policyDetail.mode) {
											return item.dName;
										}
									})
								}
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:策略标签 */}
								{policyListLang.detailDrawer("tag")}
							</label>
							<span>
								{/* lang:暂无描述*/}
								{
									!policyDetail.tags && "-"
								}
								{
									policyDetail.tags &&
                                    policyDetail.tags.split(",") &&
                                    policyDetail.tags.split(",").length > 0 &&
                                    policyDetail.tags.split(",").map((tag, index) => {
                                    	let tagObj = allMap.policyTags && allMap.policyTags.find(policyTag => policyTag.name === tag);
                                    	if (tagObj) {
                                    		return (
                                    			<Tag
                                    				key={index}
                                    				color={CommonConstants.tagColor[index] ? CommonConstants.tagColor[index] : "blue"}
                                    				style={{ width: "auto" }}
                                    			>
                                    				{tagObj.dName}
                                    			</Tag>
                                    		);
                                    	}
                                    })
								}
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:策略描述 */}
								{policyListLang.detailDrawer("policyDescription")}
							</label>
							<span>
								{/* lang:暂无描述*/}
								{policyDetail.description === "" ? policyListLang.detailDrawer("noDescription") : policyDetail.description}
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:最后修改 */}
								{policyListLang.detailDrawer("lastModify")}
							</label>
							<span>{policyDetail.updatedBy || policyDetail.createBy}</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:修改时间 */}
								{policyListLang.detailDrawer("modifyTime")}
							</label>
							<span>{policyDetail.gmtModified || policyDetail.gmtCreate}</span>
						</div>
					</div>
					<div className="policy-detail-sep"></div>
					<div className="policy-detail-rule">
						<h4>
							{/* lang:规则列表 */}
							{policyListLang.detailDrawer("ruleList")}
						</h4>
						<div className="policy-detail-status">
							{/* lang:当前策略规则总计：6条，启用：3条，禁用：0条，模拟：3条 */}
							{policyListLang.ruleRecords(total, x, y, z)}
						</div>
						<ul className="policy-detail-ruleList">
							{
								policyDetail.rules && policyDetail.rules.map((item, index) => {
									return (
										<li
											key={index}
											className={"rule-status-" + item.valid}
											onClick={() => {
												let { dispatch, location } = this.props;
												let pathname = location.pathname;
												let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
												let baseUrl;
												if (isRunning) {
													baseUrl = `/policy/versionPolicyDetail/${item.fkPolicyUuid}?tabIndex=1&ruleUuid=${item.uuid}&policyVersion=${policyDetail.policyVersion}`;
												} else {
													baseUrl = `/policy/policyDetail/${item.fkPolicyUuid}?tabIndex=1&ruleUuid=${item.uuid}`;
												}
												dispatch(routerRedux.push(prefix + baseUrl));
											}}
										>
											{/* lang:禁用、启用、模拟 */}
											{item.valid === 0 && <Tag color="red">{commonLang.base("close")}</Tag>}
											{item.valid === 1 && <Tag color="green">{commonLang.base("formal")}</Tag>}
											{item.valid === 2 &&
                                                <Tag color="geekblue">{commonLang.base("simulation")}</Tag>}
											<a>
												{item.name}
											</a>
										</li>
									);
								})
							}
						</ul>
					</div>
				</div>
			</Drawer>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(BasicLayout);
