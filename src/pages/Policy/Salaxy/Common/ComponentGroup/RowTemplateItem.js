import { PureComponent, Fragment } from "react";
import { Input, Select, Col, Checkbox, DatePicker, TimePicker } from "antd";
import { connect } from "dva";
import moment from "moment";
import { indexListLang } from "@/constants/lang";
import TimePickerAddon from "./TimePickerAddon";

const { Option } = Select;
const { MonthPicker, WeekPicker } = DatePicker;
const DateFormat = "YYYY-MM-DD";
const MonthFormat = "YYYY-MM";
const timeFormat = "HH:mm:ss";
const weekFormat = "[第]WW[周]";
class RowTemplateItem extends PureComponent {
	constructor(props) {
		super(props);
	}
	// 监听模版中配置的改变自身的规则
	changeSelfEvent = (subGroupComponent, disabled) => {
		const { groupItem, currentIndexObj, subItem } = this.props;
		const { willChangeSelf, changeSelfByParent } = subGroupComponent; // 组件信息
		// 例如从属性中 复选框选中则展示当前
		let show = true;
		if (willChangeSelf) {
			// 匹配到联动的组件
			const { caseList = [], name } = willChangeSelf || {};

			// 获取handle实体
			const { children = [] } = subItem || {}; // 获取组下面的所有子项
			const changeObj = children && children.find(childItem => {
				return childItem.name === name;
			}) || {};

			// 获取handle value
			let willChangeVal = groupItem[changeObj.name];

			caseList &&
				caseList.length > 0 &&
				caseList.map((caseScene) => {
					const { modeValueList = [], changeType } = caseScene || {};
					// 模版中配置例如：当值为1时 显示
					if (modeValueList.indexOf(willChangeVal) < 0 && changeType === "show") {
						show = false;
					} else if (modeValueList.indexOf(willChangeVal) > -1 && changeType === "disabled") {
						disabled = true;
					}
				});
		}

		// 根据父节点改变自身
		if (changeSelfByParent && changeSelfByParent.length > 0) {
			let attachFieldsObj = currentIndexObj && currentIndexObj.attachFieldsObj ? currentIndexObj.attachFieldsObj : null;
			changeSelfByParent.forEach(changeSelf=>{
				// 匹配到联动的组件
				const { caseList = [], name } = changeSelf || {};
				let parentData = null;
				if (name.indexOf("attachFields.") > -1) {
					const attrName = name.split(".")[1];
					parentData = attachFieldsObj[attrName];
				} else {
					parentData = currentIndexObj[name];
				}
				caseList &&
				caseList.length > 0 &&
				caseList.map((caseScene) => {
					const { modeValueList = [], changeType } = caseScene || {};
					// 模版中配置例如：当值为1时 显示
					if (modeValueList.indexOf(parentData) > -1 && changeType === "hidden") {
						show = false;
					} else if (modeValueList.indexOf(parentData) > -1 && changeType === "disabled") {
						disabled = true;
					}
				});
			});
		}

		return {
			show,
			disabled
		};
	}
	render() {
		const { subItem = {}, groupItem, groupIndex, changeBaseField, isRunning } = this.props;
		const { globalStore, hasPublished } = this.props;
		const { children = [] } = subItem || {};
		const { allMap, personalMode } = globalStore;
		const lang = personalMode.lang === "cn" ? "cn" : "en";
		return (
			<Fragment>
				{
					children && children.length > 0 && children.map((subGroupComponent, subIndex) => {
						let value = groupItem[subGroupComponent.name]; // 数据
						// 处理是否可以编辑的状态
						let disabled = false;
						// hasPublished 已经发布过版本 如果已经发布过版本 则根据canEditSecondTimes判断是否可以编辑
						if (isRunning) {
							disabled = true;
						} else {
							if (hasPublished) {
								disabled = !subGroupComponent.canEditSecondTimes;
							}
						}
						const { span, selectName, selectType } = subGroupComponent; // 组件信息
						let { show, disabled: relateDisabled } = this.changeSelfEvent(subGroupComponent, disabled);
						disabled = relateDisabled;

						if (!show) return null;

						// 当外部当如指标时，部分属性例如从属性可能已经删除，不显示无效的值
						if (subGroupComponent.componentType === "select" && selectType === "local" && selectName) {
							let optionList = allMap[selectName] || [];
							const haveVal = optionList && optionList.length > 0 &&
                                optionList.filter((v) => {
                                	return String(v.name) === String(value);
                                });
							if (!(haveVal && haveVal.length > 0)) {
								value = undefined;
							}
						}
						if (value === "Invalid date") {
							value = "";
						}
						return (
							<Fragment key={subIndex} >
								{
									subGroupComponent.componentType === "input" &&
                                    <Col span={span}>
                                    	<Input
                                    		placeholder={subGroupComponent.placeholder}
                                    		value={value || undefined}
                                    		disabled={disabled}
                                    		onChange={(e) => changeBaseField(subGroupComponent.name, groupIndex, "input", e)}
                                    		addonBefore={subGroupComponent.addonBefore || null}
                                    		addonAfter={subGroupComponent.addonAfter || null}
                                    	/>
                                    </Col>
								}
								{
									subGroupComponent.componentType === "label" &&
                                    <Col span={span} className="line-hei-32" style={{ "textAlign": subGroupComponent.alignType || "left" }}>
                                    	{lang === "en" ? subGroupComponent.enText : subGroupComponent.text}
                                    </Col>
								}
								{
									subGroupComponent.componentType === "select" &&
                                    <Col span={span}>
                                    	<Select
                                    		placeholder={subGroupComponent.placeholder || indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                    		value={value || undefined}
                                    		showSearch
                                    		optionFilterProp="children"
                                    		disabled={disabled}
                                    		dropdownMatchSelectWidth={false}
                                    		onChange={(e) => changeBaseField(subGroupComponent.name, groupIndex, "select", e)}
                                    	>
                                    		{
                                    			selectType === "local" &&
                                                selectName &&
                                                allMap[selectName] &&
                                                allMap[selectName]
                                                	.filter(item => item && item.type === "STRING")
                                                	.map((optionItem, optionIndex) => {
                                                		return (
                                                			<Option
                                                				value={optionItem.name}
                                                				key={optionIndex}
                                                				title={lang === "cn" ? optionItem.dName : optionItem.enDName}
                                                			>
                                                				{lang === "cn" ? optionItem.dName : optionItem.enDName}
                                                			</Option>
                                                		);
                                                	})
                                    		}
                                    		{
                                    			subGroupComponent.selectType === "self" && subGroupComponent.selectOption &&
                                                subGroupComponent.selectOption.map((optionItem, optionIndex) => {
                                                	return (
                                                		<Option
                                                			value={optionItem.value}
                                                			key={optionIndex}
                                                			title={lang === "cn" ? optionItem.name : optionItem.enName}
                                                		>
                                                			{lang === "cn" ? optionItem.name : optionItem.enName}
                                                		</Option>
                                                	);
                                                })
                                    		}
                                    	</Select>
                                    </Col>
								}
								{
									subGroupComponent.componentType === "checkbox" &&
                                    <Col span={span}>
                                    	<Checkbox.Group
                                    		disabled={disabled}
                                    		value={value}
                                    		onChange={(e) => {
                                    			changeBaseField(subGroupComponent.name, groupIndex, "checkbox", e);
                                    		}}
                                    	>
                                    		{
                                    			subGroupComponent.selectOption &&
                                                subGroupComponent.selectOption.map((optionItem, optionIndex) => {
                                                	return (
                                                		<Checkbox
                                                			value={optionItem.value}
                                                			key={optionIndex}
                                                		>
                                                			{lang === "cn" ? optionItem.name : optionItem.enName}
                                                		</Checkbox>
                                                	);
                                                })
                                    		}
                                    	</Checkbox.Group>
                                    </Col>
								}
								{
									subGroupComponent.componentType === "DatePicker" &&
                                    <Col span={span}>
                                    	<TimePickerAddon addonBefore={subGroupComponent.addonBefore}>
                                    		<DatePicker
                                    			className="wid-100-percent"
                                    			placeholder={subGroupComponent.placeholder}
                                    			defaultValue={value ? moment(value, DateFormat) : undefined}
                                    			disabled={disabled}
                                    			format={DateFormat}
                                    			onChange={(e) => changeBaseField(subGroupComponent.name, groupIndex, "select", moment(e).format(DateFormat))}
                                    		/>
                                    	</TimePickerAddon>
                                    </Col>
								}
								{
									subGroupComponent.componentType === "MonthPicker" &&
                                    <Col span={span}>
                                    	<TimePickerAddon addonBefore={subGroupComponent.addonBefore}>
                                    		<MonthPicker
                                    			className="wid-100-percent"
                                    			placeholder={subGroupComponent.placeholder}
                                    			defaultValue={value ? moment(value, MonthFormat) : undefined}
                                    			disabled={disabled}
                                    			onChange={(e) => changeBaseField(subGroupComponent.name, groupIndex, "select", moment(e).format(MonthFormat))}
                                    		/>
                                    	</TimePickerAddon>
                                    </Col>
								}
								{
									subGroupComponent.componentType === "WeekPicker" &&
                                    <Col span={span}>
                                    	<TimePickerAddon addonBefore={subGroupComponent.addonBefore}>
                                    		<WeekPicker
                                    			format={weekFormat}
                                    			className="wid-100-percent"
                                    			placeholder={subGroupComponent.placeholder}
                                    			defaultValue={value ? moment(value, weekFormat) : undefined}
                                    			disabled={disabled}
                                    			onChange={(e) => changeBaseField(subGroupComponent.name, groupIndex, "select", moment(e).format(weekFormat))}
                                    		/>
                                    	</TimePickerAddon>
                                    </Col>
								}
								{
									subGroupComponent.componentType === "TimePicker" &&
                                    <Col span={span}>
                                    	<TimePickerAddon addonBefore={subGroupComponent.addonBefore}>
                                    		<TimePicker
                                    			className="wid-100-percent"
                                    			format={timeFormat}
                                    			placeholder={subGroupComponent.placeholder}
                                    			defaultValue={value ? moment(value, timeFormat) : undefined}
                                    			disabled={disabled}
                                    			onChange={(e) => changeBaseField(subGroupComponent.name, groupIndex, "select", moment(e).format(timeFormat))}
                                    		/>
                                    	</TimePickerAddon>
                                    </Col>
								}
							</Fragment>
						);
					})
				}
			</Fragment >
		);
	}
};
export default connect(state => ({
	globalStore: state.global
}))(RowTemplateItem);
