import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		tabForConfigList: {
			"cn": "免疫配置列表",
			"en": "Immune configuration list"
		},
		tabForHistory: {
			"cn": "免疫配置历史",
			"en": "Immune configuration history"
		}
	};
	return params[field][lang];
};

export default {
	common
};
