import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Icon, Row, Col, Checkbox } from "antd";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import "./Scene.less";

class Scene extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { globalStore: { allMap = {} }, indexData: { sceneList = [] }, spanWidth, className } = this.props;
		let newAppList = [];
		allMap["appNames"] && allMap["appNames"].forEach((item) => {
			if (item.name !== "all") {
				newAppList.push(item);
			}
		});

		return (
			<Fragment>
				<Row
					gutter={CommonConstants.gutterSpan}
					style={{ marginBottom: "10px" }}
				>
					<Col span={4} className="basic-info-title">
						{/* lang:场景 */}
						{indexListLang.ruleConfig("scene")}
					</Col>
					<Col span={spanWidth || 8}>
						<div className={`scene-table-wrap ${className}`}>
							<table>
								<thead>
									<tr>
										<th>
											{/* lang:应用 */}
											{indexListLang.ruleConfig("application")}
										</th>
										<th>
											{/* lang:策略集 */}
											{indexListLang.ruleConfig("policySet")}
										</th>
									</tr>
								</thead>
								<tbody>
									{
										sceneList &&
                                        sceneList.map((sceneItem, sceneIndex) => {
                                        	let appObj = newAppList.find(app => app.name === sceneItem.appName);
                                        	let policySetMapName = sceneItem.appName ? sceneItem.appName + "_scenes" : null;
                                        	let policySetList = policySetMapName && allMap[policySetMapName] ? allMap[policySetMapName] : [];
                                        	let eventDNameList = [];

                                        	sceneItem.eventList.forEach(eventId => {
                                        		let policySetObj = policySetList.find(setItem => setItem.eventId === eventId);
                                        		if (policySetObj) {
                                        			if (eventId) {
                                        				eventDNameList.push(policySetObj.dName);
                                        			}
                                        		}
                                        	});

                                        	if (appObj) {
                                        		return (
                                        			<tr key={sceneIndex}>
                                        				<td
                                        					width={150}
                                        				>
                                        					{appObj && appObj.dName}
                                        				</td>
                                        				<td>
                                        					{eventDNameList && eventDNameList.join(" | ")}
                                        				</td>
                                        			</tr>
                                        		);
                                        	}
                                        })
									}
								</tbody>
							</table>
						</div>
					</Col>
				</Row>
			</Fragment>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(Scene);
