@import "../../base/variables";

:global {
	.rule-storage-popover-wrap {
		& {
			.ant-popover-inner-content {
				padding: 0;
			}
		}
		.ant-popover-content {
			& {
				//z-index: 1;
				//padding: 20px 20px 20px 24px;
				//margin-left: -80px;
				width: 350px;
				//background-color: #fff;
				//border-radius: 4px;
				//box-shadow: 0 2px 8px rgba(0, 0, 0, .1);
				//filter: drop-shadow(0 0 8px rgba(0, 0, 0, .1));
				//-webkit-filter: drop-shadow(0 0 8px rgba(0, 0, 0, .1));
			}
		}
		.ant-popover-title {
			& {
				height: 48px;
				line-height: 48px;
				padding: 0;
				padding-left: 16px;
			}
			.title {
				& {
					position: relative;
				}
				h3 {
					float: left;
				}
				.empty-box {
					float: right;
					margin-right: 16px;
					cursor: pointer;
					i {
						margin-right: 5px;
					}
					&:hover {
						color: @blue;
					}
				}
			}
		}
		.ant-popover-inner-content {
			& {
				clear: both;
			}
			ul {
				& {
					padding: 0;
					margin: 0;
					min-height: 250px;
					max-height: 350px;
					overflow-y: scroll;
				}
				li {
					& {
						position: relative;
						list-style: none;
						clear: both;
						border-bottom: 1px dashed #dcdcdc;
						padding: 12px 0;
					}
					&:hover {
						background: #f5f5f5;
					}
					&:last-child {
						//border-bottom: none;
					}
					.line1 {
						& {
							margin-bottom: 5px;
							height: auto;
							overflow: hidden;
						}
						h3 {
							float: left;
							font-size: 14px;
							font-weight: normal;
							margin-bottom: 0;
							margin-left: 16px;
						}
						.action-btns {
							& {
								float: right;
								margin-right: 16px;
							}
							a {
								margin-left: 12px;
							}
						}
					}
					.line2 {
						& {
							clear: both;
							height: auto;
							overflow: hidden;
						}
						.source {
							& {
								margin-left: 16px;
								margin-bottom: 0;
								color: #999;
								font-size: 12px;
								float: left;
							}
							span {
								color: #999;
							}
						}
						.create-ts {
							margin-left: 16px;
							color: #999;
							font-size: 12px;
							margin-bottom: 0;
						}
						.not-exist {
							color: @red;
							margin-left: 16px;
							margin-bottom: 0;
						}
					}
				}
			}
			.none-data {
				& {
					position: relative;
					top: 30px;
					text-align: center;
				}
				i {
					font-size: 48px;
					color: #999;
				}
				p {
					font-size: 14px;
					color: #999;
				}
			}
		}
	}
}

