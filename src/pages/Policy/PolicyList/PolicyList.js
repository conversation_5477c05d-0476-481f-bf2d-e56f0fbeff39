import React, { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { searchToObject } from "@/utils/utils";
import { checkFunctionHasPermission } from "@/utils/permission";
import NoPermission from "@/components/NoPermission";
import { Tabs, Spin } from "antd";
import { policyListLang } from "@/constants/lang";
import EditorArea from "./EditorArea";

const TabPane = Tabs.TabPane;
const Workflow = React.lazy(() => import("./Workflow"));
const RunningArea = React.lazy(() => import("./RunningArea"));

class PolicyList extends PureComponent {
	constructor(props) {
		super(props);
	}

    switchTab = (key) => {
    	const { location, dispatch } = this.props;
    	const { pathname } = location;
    	const search = "?currentTab=" + key;

    	dispatch(routerRedux.push(pathname + search));
    }

    render() {
    	const { globalStore, workflowStore, location } = this.props;
    	const { dialogShow } = workflowStore;
    	const { menuTreeReady } = globalStore;
    	const { search } = location;
    	const searchObj = search && searchToObject(search) ? searchToObject(search) : null;
    	const currentTab = searchObj && searchObj.currentTab ? parseInt(searchObj.currentTab, 10) : 1;

    	return (
    		<div>
    			<div className="main-area-wrap">
    				<div className="page-global-tab">
    					<Tabs
    						activeKey={currentTab.toString()}
    						onChange={this.switchTab}
    						animated={false}
    					>
    						{/* lang：运行区 */}
    						<TabPane tab={policyListLang.common("tabForRunning")} key="1">
    							{
    								menuTreeReady &&
                                    checkFunctionHasPermission("ZB0101", "runningSearch") &&
                                    <React.Suspense fallback={null}>
                                    	<RunningArea pagePosition="runningArea" location={location} />
                                    </React.Suspense>
    							}
    							{
    								menuTreeReady &&
                                    !checkFunctionHasPermission("ZB0101", "runningSearch") &&
                                    <NoPermission />
    							}
    						</TabPane>
    						{/* lang：编辑区 */}
    						<TabPane tab={policyListLang.common("tabForEditor")} key="2">
    							{
    								menuTreeReady &&
                                    checkFunctionHasPermission("ZB0101", "editorSearch") &&
                                    <EditorArea pagePosition="editorArea" location={location} />
    							}
    							{
    								menuTreeReady &&
                                    !checkFunctionHasPermission("ZB0101", "editorSearch") &&
                                    <NoPermission />
    							}
    						</TabPane>
    					</Tabs>
    				</div>
    			</div>
    			{
    				dialogShow.workflow &&
                    <React.Suspense
                    	fallback={<Spin size="large" />}
                    >
                    	<Workflow />
                    </React.Suspense>
    			}
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor,
	workflowStore: state.workflow
}))(PolicyList);
