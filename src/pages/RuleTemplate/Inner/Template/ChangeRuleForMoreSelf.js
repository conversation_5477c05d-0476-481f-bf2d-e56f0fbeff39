import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Select } from "antd";

const { Option, OptGroup } = Select;

// 对修改自身增加更多的选项组
class ChangeRuleForMoreSelf extends PureComponent {
	constructor(props) {
		super(props);
		this.addChangeRuleItem = this.addChangeRuleItem.bind(this);
		this.addChangeRuleCaseItem = this.addChangeRuleCaseItem.bind(this);
		this.changeRuleBaseValue = this.changeRuleBaseValue.bind(this);
		this.changeRuleCaseValue = this.changeRuleCaseValue.bind(this);
		this.removeChangeRuleItem = this.removeChangeRuleItem.bind(this);
		this.removeChangeRuleCaseItem = this.removeChangeRuleCaseItem.bind(this);
	}

	changeRuleBaseValue(field, type, i, e) {
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		let { templateStore, childIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam["willChangeMoreSelf"][i][field] = value;

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	changeRuleCaseValue(index, field, type, i, e) {
		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		let { templateStore, childIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam["willChangeMoreSelf"][i]["caseList"][index][field] = value;

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	removeChangeRuleItem(i) {
		let { templateStore, childIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		currentParam["willChangeMoreSelf"].splice(i, 1);

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	removeChangeRuleCaseItem(caseIndex, index) {
		let { templateStore, childIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		if (currentParam["willChangeMoreSelf"] && currentParam["willChangeMoreSelf"][index]) {
			currentParam["willChangeMoreSelf"][index]["caseList"].splice(caseIndex, 1);
		}

		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	addChangeRuleItem() {
		let { templateStore, childIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};
		if (!currentParam["willChangeMoreSelf"]) {
			currentParam["willChangeMoreSelf"] = [];
		}
		let ruleItemTemp = {
			name: null,
			changeMode: null,
			caseList: [
				{
					modeValueList: [],
					changeType: null,
					mapName: null,
					mapLocation: null
				}
			]
		};
		currentParam["willChangeMoreSelf"].push(ruleItemTemp);

		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	addChangeRuleCaseItem(index) {
		let { templateStore, childIndex, dispatch } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};
		let ruleCaseTemp = {
			modeValueList: [],
			changeType: null,
			selectMap: null,
			mapLocation: null
		};
		if (currentParam["willChangeMoreSelf"]) {
			const caseList = currentParam["willChangeMoreSelf"][index];
			if (caseList && caseList["caseList"]) {
				caseList["caseList"].push(ruleCaseTemp);
			} else {
				caseList["caseList"] = [ruleCaseTemp];
			}
		}
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	render() {
		let { templateStore, childIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};
		let moreSelfChangeRule = currentParam["willChangeMoreSelf"] || [];
		return (
			<div>
				<div className="single-param-item">
					<div className="param-title">
						<span>添加更多自身规则：</span>
					</div>
					<div className="param-field">
						<span
							className="add-new-operation"
							onClick={this.addChangeRuleItem.bind(this)}
						>
							<Icon type="plus-square-o" />新增
						</span>
					</div>
				</div>
				{
					moreSelfChangeRule &&
                    moreSelfChangeRule.map((willChangeRule, i)=>{
                    	return (
                    		<div className="template-param-rule-list">
                    			<div className="template-param-rule-section">
                    				<div
                    					className="remove-rule-item"
                    					onClick={this.removeChangeRuleItem.bind(this, i)}
                    				>
                    					<Icon type="delete" />
                    				</div>
                    				<div className="template-param-rule-item">
                    					<div className="template-param-rule-title">
                    						<span>节点handle：</span>
                    					</div>
                    					<div className="template-param-rule-field">
                    						<Select
                    							placeholder="选择节点handle"
                    							value={willChangeRule.name || undefined}
                    							onChange={this.changeRuleBaseValue.bind(this, "name", "select", i)}
                    						>
                    							{
                    								params &&
													params.map((paramItem, paramIndex) => {
														return (
															<OptGroup
																label={paramItem.labelText || "暂无标题"}
																key={paramIndex}
															>
																{
																	paramItem.children &&
																	paramItem.children.filter(fItem => fItem.name).map((subItem, subIndex) => {
																		return (
																			<Option
																				value={subItem.name || undefined}
																				disabled={subItem["name"] === currentParam["name"]}
																				key={subIndex}
																			>
																				[{subItem.componentType}] {subItem.name}
																			</Option>
																		);
																	})
																}
															</OptGroup>
														);
													})
                    							}
                    						</Select>
                    					</div>
                    				</div>

                    				{
                    					willChangeRule.name &&
										<div className="template-param-rule-item">
											<div className="template-param-rule-title">
												<span>改变形式：</span>
											</div>
											<div className="template-param-rule-field">
												<Select
													placeholder="选择被改变节点类型"
													value={willChangeRule.changeMode || undefined}
													onChange={this.changeRuleBaseValue.bind(this, "changeMode", "select", i)}
												>
													<Option value="whenSomeValue">特定值时改变</Option>
													<Option value="whenSomeType">特定类型时改变</Option>
												</Select>
											</div>
										</div>
                    				}
                    				{
                    					willChangeRule.name &&
										<div className="template-param-rule-item">
											<div className="template-param-rule-title">
												<span>添加条件：</span>
											</div>
											<div className="template-param-rule-field">
												<span
													className="add-new-operation"
													onClick={this.addChangeRuleCaseItem.bind(this, i)}
												>
													<Icon type="plus-square-o" />添加
												</span>
											</div>
										</div>
                    				}

                    				{
                    					willChangeRule.name &&
										willChangeRule.caseList &&
										willChangeRule.caseList.length > 0 &&
										<div className="template-rule-case-list">
											{
												willChangeRule.caseList.map((caseItem, caseIndex) => {
													return (
														<div className="template-rule-case-item" key={caseIndex}>
															<div
																className="remove-rule-case-item"
																onClick={this.removeChangeRuleCaseItem.bind(this, caseIndex, i)}
															>
																<Icon type="delete" />
															</div>
															{
																willChangeRule.changeMode &&
																willChangeRule.changeMode === "whenSomeValue" &&
																<div className="template-param-rule-item">
																	<div className="template-param-rule-title">
																		<span>改变形式value：</span>
																	</div>
																	<div className="template-param-rule-field">
																		<Select
																			mode="tags"
																			placeholder="输入特定值，可以输入多个"
																			value={caseItem.modeValueList || undefined}
																			onChange={this.changeRuleCaseValue.bind(this, caseIndex, "modeValueList", "select", i)}
																		>
																		</Select>
																	</div>
																</div>
															}
															{
																willChangeRule.changeMode &&
																willChangeRule.changeMode === "whenSomeType" &&
																<div className="template-param-rule-item">
																	<div className="template-param-rule-title">
																		<span>改变形式value：</span>
																	</div>
																	<div className="template-param-rule-field">
																		<Select
																			mode="tags"
																			placeholder="选择特定类型，可以多选"
																			value={caseItem.modeValueList || undefined}
																			onChange={this.changeRuleCaseValue.bind(this, caseIndex, "modeValueList", "select", i)}
																		>
																			<Option value="string">string</Option>
																			<Option value="int">int</Option>
																			<Option value="double">double</Option>
																			<Option value="datetime">datetime</Option>
																			<Option value="enum">enum</Option>
																			<Option value="boolean">boolean</Option>
																		</Select>
																	</div>
																</div>
															}
															{
																willChangeRule.name &&
																<div className="template-param-rule-item">
																	<div className="template-param-rule-title">
																		<span>改变类型：</span>
																	</div>
																	<div className="template-param-rule-field">
																		{
																			params.map((paramItem) => {
																				let findObj = paramItem.children.find(fItem => fItem.name === willChangeRule.name);
																				const { children } = findObj || {};
																				const hasChildren = children && children.length > 0;
																				if (findObj) {
																					if (findObj.componentType === "select") {
																						return (
																							<Select
																								placeholder="选择被改变节点类型"
																								value={caseItem.changeType || undefined}
																								onChange={this.changeRuleCaseValue.bind(this, caseIndex, "changeType", "select", i)}
																							>
																								<Option value="disabled">禁用当前</Option>
																								<Option value="hidden">隐藏当前</Option>
																								<Option value="selectMap">改变当前selectMap</Option>
																								{
																									hasChildren &&
																									<Option value="hiddenByChild">根据子节点隐藏当前</Option>
																								}
																							</Select>
																						);
																					} else {
																						return (
																							<Select
																								placeholder="选择被改变节点类型"
																								value={caseItem.changeType || undefined}
																								onChange={this.changeRuleCaseValue.bind(this, caseIndex, "changeType", "select", i)}
																							>
																								<Option value="disabled">禁用当前</Option>
																								<Option value="hidden">隐藏当前</Option>
																								{
																									hasChildren &&
																									<Option value="hiddenByChild">根据子节点隐藏当前</Option>
																								}
																							</Select>
																						);
																					}
																				}
																			})
																		}
																	</div>
																</div>
															}
															{
																caseItem.changeType &&
																caseItem.changeType === "selectMap" &&
																<div className="template-param-rule-item">
																	<div className="template-param-rule-title">
																		<span>新的Map位置：</span>
																	</div>
																	<div className="template-param-rule-field">
																		<Select
																			placeholder="选择新的Map位置"
																			value={caseItem.mapLocation || undefined}
																			onChange={this.changeRuleCaseValue.bind(this, caseIndex, "mapLocation", "select", i)}
																		>
																			<Option value="local">local</Option>
																			<Option value="service">service</Option>
																		</Select>
																	</div>
																</div>
															}
															{
																willChangeRule.name &&
																caseItem.changeType &&
																caseItem.changeType === "hiddenByChild" &&
																<div className="template-param-rule-item">
																	<div className="template-param-rule-title">
																		<span>子节点：</span>
																	</div>
																	<div className="template-param-rule-field">
																		{
																			params.map((paramItem) => {
																				let findObj = paramItem.children.find(fItem => fItem.name === willChangeRule.name);
																				const { children } = findObj || {};
																				if (children && children.length > 0) {
																					return (
																						<Select
																							placeholder="选择子节点"
																							value={caseItem.childNodeName || undefined}
																							onChange={this.changeRuleCaseValue.bind(this, caseIndex, "childNodeName", "select", i)}
																						>
																							{
																								children.map((subItem, subIndex)=>{
																									return (
																										<Option
																											value={subItem.name || undefined}
																											disabled={subItem["name"] === currentParam["name"]}
																											key={subIndex}
																										>
																												[{subItem.componentType}] {subItem.name}
																										</Option>
																									);
																								})
																							}
																						</Select>
																					);
																				}
																			})
																		}
																	</div>
																</div>
															}
															{
																caseItem.changeType &&
																caseItem.changeType === "selectMap" &&
																<div className="template-param-rule-item">
																	<div className="template-param-rule-title">
																		<span>新的Map名称：</span>
																	</div>
																	<div className="template-param-rule-field">
																		<Input
																			value={caseItem.mapName || undefined}
																			onChange={this.changeRuleCaseValue.bind(this, caseIndex, "mapName", "input", i)}
																			placeholder="请输入新的Map名称"
																		/>
																	</div>
																</div>
															}
														</div>
													);
												})
											}
										</div>
                    				}
                    			</div>
                    		</div>

                    	);
                    })
				}
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(ChangeRuleForMoreSelf);
