import React from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Row, Col } from "antd";
import { PolicyConstants, CommonConstants } from "@/constants";
import { commonLang, policyDetailLang } from "@/constants/lang";
import { judgeExistVal } from "@/utils/utils";
import { filterAvailableFieldList } from "@/utils/filterFieldList";

const Option = Select.Option;
const InputGroup = Input.Group;

class OneCondition extends React.PureComponent {

	constructor(props) {
		super(props);
		this.deleteCondition = this.deleteCondition.bind(this);
		this.addCondition = this.addCondition.bind(this);
		this.changeLogicOperator = this.changeLogicOperator.bind(this);
		this.changeConditionField = this.changeConditionField.bind(this);
	}

	componentWillMount() {
		let { policyDetailStore, globalStore, dispatch, conditionSingleData, ruleIndexArr, conditionArr } = this.props;
		let { allMap } = globalStore;
		let { policyRules, policyDetail } = policyDetailStore;
		const { appName } = policyDetail || {};
		const ruleAndIndexFieldList = filterAvailableFieldList({allMap, appName});

		// 当外部当如指标时，部分属性例如从属性可能已经删除，不显示无效的值
		let conditionProperty = judgeExistVal(
			conditionSingleData && conditionSingleData["property"] ? conditionSingleData["property"] : undefined,
			ruleAndIndexFieldList
		);

		// 校验数据类型
		const leftOptionDataType = conditionSingleData ? conditionSingleData["propertyDataType"] : undefined;
		let conditionVal = judgeExistVal(
			conditionSingleData && conditionSingleData["value"] ? conditionSingleData["value"] : undefined,
			ruleAndIndexFieldList &&
			ruleAndIndexFieldList.filter(fItem => (
				fItem.type === leftOptionDataType ||
				(["DOUBLE", "INT"].indexOf(fItem.type) > -1 && ["DOUBLE", "INT"].indexOf(leftOptionDataType) > -1)
			))
		);

		let currentLine = null;
		// propertyObj的作用是判断选择的系统字段或指标是不是还存在，继而做下一步操作
		// let propertyObj = conditionSingleData && conditionSingleData["property"] && allMap && ruleAndIndexFieldList && ruleAndIndexFieldList.find(item => item.name === conditionSingleData["property"]);
		// if (!propertyObj) {
		if (ruleIndexArr.length === 1) {
			// 如果不是IF规则
			if (conditionArr.length === 1) {
				currentLine = policyRules[ruleIndexArr[0]]["conditions"]["children"][conditionArr[0]];
			} else if (conditionArr.length === 2) {
				// 如果是条件组
				let groupList = policyRules[ruleIndexArr[0]]["conditions"]["children"];
				currentLine = groupList[conditionArr[0]]["children"][conditionArr[1]];
			}
		} else if (ruleIndexArr.length === 2) {
			// 如果是IF规则
			if (conditionArr.length === 1) {
				currentLine = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"][conditionArr[0]];
			} else if (conditionArr.length === 2) {
				let groupList = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"];
				currentLine = groupList[conditionArr[0]]["children"][conditionArr[1]];
			}
		}

		if (conditionSingleData["propertyDataType"] === "ENUM") {
			currentLine["enumField"] = ((ruleAndIndexFieldList.find(v=>(
				v.name === conditionSingleData.property
			))||{}).enumField) || conditionSingleData.property ;

			let conditionEnumVal = judgeExistVal(
				conditionSingleData["value"] || undefined,
				allMap["fieldEnumObj"] && allMap["fieldEnumObj"][currentLine.enumField],
				"value"
			);
			currentLine["value"] = conditionEnumVal;

		} else if (conditionSingleData["rightValueType"] === "context" && conditionSingleData["propertyDataType"] !== "ENUM") {
			currentLine["value"] = conditionVal;
		}
		currentLine["property"] = conditionProperty;
		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});

		console.log(currentLine["enumField"])
		// currentLine["value"] = null;

		// 注释原来的代码
		/*
            currentLine["operator"] = "==";
            currentLine["property"] = null;
            currentLine["type"] = "context";
            currentLine["rightValueType"] = "context";

            dispatch({
                type: "policyDetail/setPolicyRule",
                payload: {
                    policyRules: policyRules
                }
            });
        */
		// }
	}

	addCondition() {
		let { policyDetailStore, dispatch, ruleIndexArr, conditionArr } = this.props;
		let { policyRules } = policyDetailStore;

		let singleConditionTemp = {
			property: null,
			operator: "==",
			enumField:null,
			value: null,
			propertyDataType: null,
			type: "context",
			rightValueType: "context"
		};

		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["conditions"]["children"][conditionArr[0]]["children"].splice(conditionArr[1] + 1, 0, singleConditionTemp);
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"][conditionArr[0]]["children"].splice(conditionArr[1] + 1, 0, singleConditionTemp);
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	deleteCondition() {
		let { policyDetailStore, dispatch, ruleIndexArr, conditionArr } = this.props;
		let { policyRules } = policyDetailStore;
		if (ruleIndexArr.length === 1) {
			if (conditionArr.length === 1) {
				policyRules[ruleIndexArr[0]]["conditions"]["children"].splice(conditionArr[0], 1);
			} else if (conditionArr.length === 2) {
				// 如果是条件组，还需要判断是否只有一条数据了
				let groupList = policyRules[ruleIndexArr[0]]["conditions"]["children"][conditionArr[0]]["children"];
				if (groupList.length === 1) {
					policyRules[ruleIndexArr[0]]["conditions"]["children"].splice(conditionArr[0], 1);
				} else {
					groupList.splice(conditionArr[1], 1);
				}
			}
		} else if (ruleIndexArr.length === 2) {
			if (conditionArr.length === 1) {
				policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"].splice(conditionArr[0], 1);
			} else if (conditionArr.length === 2) {
				// 条件组
				let groupList = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"];

				if (groupList[conditionArr[0]]["children"].length === 1) {
					// 如果规则组删除的只剩下一个了
					groupList.splice(conditionArr[0], 1);
				} else {
					groupList[conditionArr[0]]["children"].splice(conditionArr[1], 1);
				}
			}
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	changeLogicOperator(e) {
		let { policyDetailStore, dispatch, ruleIndexArr, conditionArr } = this.props;
		let { policyRules } = policyDetailStore;

		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["conditions"]["children"][conditionArr[0]]["logicOperator"] = e;
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"][conditionArr[0]]["logicOperator"] = e;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	changeConditionField(field, type, e) {
		let { policyDetailStore, globalStore, dispatch, ruleIndexArr, conditionArr } = this.props;
		let { policyRules } = policyDetailStore;
		let { allMap } = globalStore;
		const {ruleAndIndexFieldList = []} = allMap || {};
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		let currentLine = null;// 当前设置的条件line
		let currentRule = null;// 当前规则
		if (ruleIndexArr.length === 1) {
			// 如果不是IF规则
			if (conditionArr.length === 1) {
				currentLine = policyRules[ruleIndexArr[0]]["conditions"]["children"][conditionArr[0]];
			} else if (conditionArr.length === 2) {
				// 如果是条件组
				let groupList = policyRules[ruleIndexArr[0]]["conditions"]["children"];
				currentLine = groupList[conditionArr[0]]["children"][conditionArr[1]];
			}
			currentRule = policyRules[ruleIndexArr[0]];
		} else if (ruleIndexArr.length === 2) {
			// 如果是IF规则
			if (conditionArr.length === 1) {
				currentLine = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"][conditionArr[0]];
			} else if (conditionArr.length === 2) {
				let groupList = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"];
				currentLine = groupList[conditionArr[0]]["children"][conditionArr[1]];
			}
			currentRule = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]];
		}

		currentRule["hasModify"] = true;
		currentLine[field] = value;
		if (field === "property") {
			let mapItem = ruleAndIndexFieldList.filter(item => item.name === value)[0];
			if (currentLine.propertyDataType !== mapItem.type) {
				currentLine.operator = null;
			}
			currentLine["propertyDataType"] = mapItem.type;
			currentLine["value"] = null;
		} else if (field === "rightValueType") {
			currentLine["value"] = null;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}
	render() {
		let { globalStore, policyDetailStore, conditionData, conditionSingleData, conditionType, conditionArr } = this.props;
		let { allMap, personalMode } = globalStore;
		let { fieldParamListSelect = [] } = allMap;
		const { lang } = personalMode;
		const operaTypeBlong = conditionSingleData["operator"] === "belong" || conditionSingleData["operator"] === "notbelong";
		const belongNames = fieldParamListSelect.reduce((pre, v) => {
			pre.push(v.name);
			return pre;
		}, []);
		const { policyDetail } = policyDetailStore || {};
		const { appName } = policyDetail || {};
		const ruleAndIndexFieldList = filterAvailableFieldList({allMap, appName});
		return (
			<div className={conditionType === "group" ? "group-item" : ""}>
				<Row gutter={CommonConstants.gutterSpan}>
					{
						conditionType === "group" && conditionArr[1] === 0 &&
                        <Col span={2} offset={2} className="basic-info-title">
                        	<Select
                        		value={conditionData.logicOperator || undefined}
                        		onChange={this.changeLogicOperator.bind(this)}
                        		dropdownMatchSelectWidth={false}
                        	>
                        		<Option value="&&">
                        			{/* 与 */}
                        			{policyDetailLang.oneCondition("and")}
                        		</Option>
                        		<Option value="||">
                        			{/* 或 */}
                        			{policyDetailLang.oneCondition("or")}
                        		</Option>
                        	</Select>
                        </Col>
					}
					{
						conditionType === "group" && conditionArr[1] !== 0 &&
                        <Col span={4} className="basic-info-title"></Col>
					}
					{
						conditionType === "single" &&
                        <Col span={4} className="basic-info-title"></Col>
					}
					<Col span={5}>
						<Select
							// conditionSingleData && conditionSingleData["property"] ? conditionSingleData["property"] : undefined
							value={conditionSingleData && conditionSingleData["property"] ? conditionSingleData["property"] : undefined}
							placeholder={policyDetailLang.oneCondition("select")} // 请选择
							onChange={(value,_this) => {
								const { props={} } = _this;
								const { enumfield } = props;
								let selectObj = ruleAndIndexFieldList.find(field => field.name === value);
								this.changeConditionField("property", "select", value);
								this.changeConditionField("enumField", "select", enumfield || value);
								if (selectObj.type === "ENUM") {
									this.changeConditionField("rightValueType", "select", "input");
								}
							}}
							showSearch
							optionFilterProp="children"
							dropdownMatchSelectWidth={false}
						>
							{
								ruleAndIndexFieldList.map((item, index) => {
									return (
										<Option value={item.name} key={index} title={item.dName} enumfield={item.enumField}>
											[{commonLang.sourceName(item.sourceName)}]&nbsp;
											{item.dName}
										</Option>
									);
								})
							}
						</Select>
					</Col>
					<Col className="gutter-row" span={3}>
						<div className="gutter-box">
							<Select
								value={conditionSingleData && conditionSingleData["operator"] ? conditionSingleData["operator"] : undefined}
								placeholder={policyDetailLang.oneCondition("select")} // 请选择
								onChange={this.changeConditionField.bind(this, "operator", "select")}
								dropdownMatchSelectWidth={false}
							>
								{
									PolicyConstants.conditionOperator[conditionSingleData.propertyDataType ? conditionSingleData.propertyDataType : "STRING"].map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{lang === "en" ? item.enDName : item.dName}
											</Option>
										);
									})
								}
							</Select>
						</div>
					</Col>
					{
						conditionSingleData &&
                        conditionSingleData["operator"] &&
                        conditionSingleData["operator"] !== "isnull" &&
                        conditionSingleData["operator"] !== "notnull" &&
                        <Col className="gutter-row" span={8}>
                        	<div className="gutter-box">
                        		{
                        			conditionSingleData["rightValueType"] === "input" &&
                                    conditionSingleData["propertyDataType"] !== "ENUM" &&
                                    !operaTypeBlong &&
                                    <InputGroup compact>
                                    	<Select
                                    		value={conditionSingleData && conditionSingleData["rightValueType"] ? conditionSingleData["rightValueType"] : undefined}
                                    		style={{ width: "30%" }}
                                    		onChange={this.changeConditionField.bind(this, "rightValueType", "select")}
                                    		dropdownMatchSelectWidth={false}
                                    	>
                                    		<Option value="input">
                                    			{/* 常量 */}
                                    			{policyDetailLang.oneCondition("constant")}
                                    		</Option>
                                    		<Option value="context">
                                    			{/* 变量 */}
                                    			{policyDetailLang.oneCondition("variable")}
                                    		</Option>
                                    	</Select>
                                    	<Input
                                    		style={{ width: "70%" }}
                                    		value={conditionSingleData && conditionSingleData["value"] ? conditionSingleData["value"] : undefined}
                                    		placeholder={policyDetailLang.oneCondition("inputConstant")} // 请输入常量内容
                                    		onChange={this.changeConditionField.bind(this, "value", "input")}
                                    	/>
                                    </InputGroup>
                        		}
                        		{
                        			conditionSingleData &&
                                    conditionSingleData["rightValueType"] === "context" &&
                                    conditionSingleData["propertyDataType"] !== "ENUM" &&
                                    !operaTypeBlong &&
                                    <InputGroup compact>
                                    	<Select
                                    		value={conditionSingleData && conditionSingleData["rightValueType"] ? conditionSingleData["rightValueType"] : undefined}
                                    		style={{ width: "30%" }}
                                    		onChange={this.changeConditionField.bind(this, "rightValueType", "select")}
                                    		dropdownMatchSelectWidth={false}
                                    	>
                                    		<Option value="input">
                                    			{/* 常量 */}
                                    			{policyDetailLang.oneCondition("constant")}
                                    		</Option>
                                    		<Option value="context">
                                    			{/* 变量 */}
                                    			{policyDetailLang.oneCondition("variable")}
                                    		</Option>
                                    	</Select>
                                    	<Select
                                    		style={{ width: "70%" }}
                                    		// conditionSingleData && conditionSingleData["value"] ? conditionSingleData["value"] : undefined
                                    		value={conditionSingleData && conditionSingleData["value"] ? conditionSingleData["value"] : undefined}
                                    		placeholder={policyDetailLang.oneCondition("select")} // 请选择
                                    		onChange={this.changeConditionField.bind(this, "value", "select")}
                                    		showSearch
                                    		optionFilterProp="children"
                                    		dropdownMatchSelectWidth={false}
                                    	>
                                    		{
                                    			ruleAndIndexFieldList &&
                                                conditionSingleData &&
                                                conditionSingleData["propertyDataType"] &&
                                                ruleAndIndexFieldList.filter(fItem => {
                                                	let leftOptionDataType = conditionSingleData["propertyDataType"];
                                                	return (
                                                		fItem.type === leftOptionDataType ||
														(["DOUBLE", "INT"].indexOf(fItem.type) > -1 && ["DOUBLE", "INT"].indexOf(leftOptionDataType) > -1)
                                                	);
                                                }).map((item, index) => {
                                                	return (
                                                		<Option value={item.name} key={index} title={item.dName}>
                                                            [{commonLang.sourceName(item.sourceName)}]&nbsp;
											                {item.dName}
                                                		</Option>
                                                	);
                                                })
                                    		}
                                    	</Select>
                                    </InputGroup>
                        		}
                        		{
                        			conditionSingleData["propertyDataType"] === "ENUM" &&
                                    <Select
                                    	// conditionSingleData["value"] || undefined
                                    	value={conditionSingleData["value"] || undefined}
                                    	placeholder={policyDetailLang.oneCondition("select")} // lang:请选择
                                    	onChange={this.changeConditionField.bind(this, "value", "select")}
                                    	showSearch
                                    	optionFilterProp="children"
                                    	dropdownMatchSelectWidth={false}
                                    >
                                    	{
                                    		allMap &&
                                            allMap["fieldEnumObj"] &&
                                            allMap["fieldEnumObj"][conditionSingleData.enumField] &&
                                            allMap["fieldEnumObj"][conditionSingleData.enumField].map((item, index) => {
                                            	return (
                                            		<Option
                                            			value={item.value}
                                            			key={index}
                                            			title={item.value}
                                            		>
                                            			{item.description}  [{item.value}]
                                        		</Option>
                                            	);
                                            })
                                    	}
                                    </Select>
                        		}
                        		{
                        			operaTypeBlong &&
                                    <Select
                                    	value={
                                    		(conditionSingleData["value"] && belongNames.indexOf(conditionSingleData["value"]) > -1)
                                    			? conditionSingleData["value"]
                                    			: undefined
                                    	}
                                    	placeholder={policyDetailLang.oneCondition("select")} // lang:请选择
                                    	onChange={(value) => {
                                    		this.changeConditionField("value", "select", value);
                                    	}}
                                    	showSearch
                                    	optionFilterProp="children"
                                    	dropdownMatchSelectWidth={false}
                                    >
                                    	{
                                    		allMap &&
                                            allMap["fieldParamListSelect"] &&
                                            allMap["fieldParamListSelect"].map((item, index) => {
                                            	return (
                                            		<Option
                                            			value={item.name}
                                            			key={index}
                                            		>
                                                        [参数列表]{lang === "en" ? item.enDName : item.dName}
                                            		</Option>
                                            	);
                                            })
                                    	}
                                    </Select>
                        		}
                        	</div>
                        </Col>
					}
					<Col span={3} className="basic-info-oper">
						{
							conditionType === "group" &&
                            // 添加一项
                            <Tooltip title={policyDetailLang.oneCondition("addOne")} placement="left">
                            	<Icon
                            		className="add"
                            		type="plus-circle-o"
                            		onClick={this.addCondition.bind(this)}
                            	/>
                            </Tooltip>
						}
						{/* 删除当前行 */}
						<Tooltip title={policyDetailLang.oneCondition("deleteCur")} placement="right">
							<Icon
								className="delete"
								type="delete"
								onClick={this.deleteCondition.bind(this)}
							/>
						</Tooltip>
					</Col>
				</Row>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(OneCondition);
