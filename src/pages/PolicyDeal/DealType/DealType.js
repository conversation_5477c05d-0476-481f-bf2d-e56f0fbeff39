import { PureComponent } from "react";
import { connect } from "dva";
import { policyDealAPI } from "@/services";
import { Table, Button, Pagination, message, Modal, Tooltip } from "antd";
import AddDealTypeModal from "./Modal/AddDealType";
import ModifyDealTypeModal from "./Modal/ModifyDealType";
import { checkFunctionHasPermission } from "@/utils/permission";

import { dealTypeLang, commonLang } from "@/constants/lang";
import NoPermission from "@/components/NoPermission";

const confirm = Modal.confirm;

class DealType extends PureComponent {

	constructor(props) {
		super(props);
		this.paginationOnChange = this.paginationOnChange.bind(this);
		this.addDealTypeHandle = this.addDealTypeHandle.bind(this);
		this.modifyDealTypeHandle = this.modifyDealTypeHandle.bind(this);
		this.deleteDealTypeHandle = this.deleteDealTypeHandle.bind(this);
	}

	componentDidMount() {
		this.timer = setInterval(() => {
			const { globalStore } = this.props;
			const { menuTreeReady } = globalStore;

			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkFunctionHasPermission("ZB0302", "listPolicyDeal")) {
					this.search();
				}
			}
		}, 100);
	}

	search() {
		let { policyDealStore, dispatch } = this.props;
		let { dealType } = policyDealStore;
		let { curPage, pageSize } = dealType;
		dispatch({
			type: "policyDeal/getDealTypes",
			payload: {
				curPage: curPage,
				pageSize: pageSize
			}
		});
	}

	addDealTypeHandle() {
		let { dispatch } = this.props;
		dispatch({
			type: "policyDeal/setDialogShow",
			payload: {
				addDealType: true
			}
		});
	}

	modifyDealTypeHandle(item, e) {
		if (item.default) {
			// lang:当前为默认风险决策无法进行修改
			message.error(dealTypeLang.modal("isDefaultDealTypeTip2"));
			return;
		}

		let { dispatch, policyDealStore } = this.props;
		let { modifyDealTypeData } = policyDealStore.dialogData;
		dispatch({
			type: "policyDeal/setDialogShow",
			payload: {
				modifyDealType: true
			}
		});
		modifyDealTypeData["uuid"] = item.uuid;
		modifyDealTypeData["dealName"] = item.dealName;
		modifyDealTypeData["dealType"] = item.dealType;
		modifyDealTypeData["grade"] = item.grade;
		modifyDealTypeData["color"] = item.color;
		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				modifyDealTypeData: modifyDealTypeData
			}
		});
	}

	deleteDealTypeHandle(item) {
		if (item.default) {
			// lang:当前为默认风险决策无法进行删除
			message.error(dealTypeLang.modal("isDefaultDealTypeTip"));
			return;
		}

		let { policyDealStore, dispatch } = this.props;
		let { dealType } = policyDealStore;
		let { curPage, pageSize } = dealType;
		let params = {
			uuid: item.uuid
		};

		confirm({
			title: dealTypeLang.modal("deleteTip"),		// lang:删除风险决策提醒
			content: dealTypeLang.deleteDescription(item.dealName),
			onOk() {
				policyDealAPI.deleteDealType(params).then(res => {
					if (res.success) {
						// lang:删除风险决策成功
						message.success(dealTypeLang.modal("deleteSuccessTip"));
						dispatch({
							type: "policyDeal/getDealTypes",
							payload: {
								curPage: curPage,
								pageSize: pageSize
							}
						});

						dispatch({
							type: "global/getAllMap",
							payload: {}
						});
					} else {
						console.log(res.message);
						message.error(res.message);
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel() {
				console.log("Cancel");
			}
		});

	}

	paginationOnChange(current, pageSize) {
		let { dispatch } = this.props;
		dispatch({
			type: "policyDeal/setAttrValue",
			payload: {
				curPage: current,
				pageSize: pageSize
			}
		});
		dispatch({
			type: "policyDeal/getDealTypes",
			payload: {
				curPage: current,
				pageSize: pageSize
			}
		});
	}

	render() {
		let { policyDealStore, globalStore } = this.props;
		let { personalMode, menuTreeReady } = globalStore;
		let { dealType } = policyDealStore;
		let { dealTypesList, curPage, total } = dealType;

		const columns = [
			{
				title: dealTypeLang.table("name"),		// lang:风险决策名称
				dataIndex: "dealName"
			},
			{
				title: dealTypeLang.table("identifier"),		// lang:风险决策标识
				dataIndex: "dealType"
			},
			{
				title: dealTypeLang.table("level"),		// lang:风险决策等级
				dataIndex: "grade"
			},
			{
				title: dealTypeLang.table("isDefault"),		// lang:是否默认
				dataIndex: "default",
				render: (value) => value ? dealTypeLang.table("yes") : dealTypeLang.table("no")
			},
			{
				title: dealTypeLang.table("color"),		// lang:颜色
				dataIndex: "color",
				render: (value) => {
					return value && <span className="show-color-detail" style={{ "background": value }} />;
				}
			},
			{
				title: dealTypeLang.table("operator"),		// lang:操作
				width: 200,
				render: (record) => {
					return (
						<div className="table-action">
							{!record.default &&
                                checkFunctionHasPermission("ZB0302", "updatePolicyDeal")
								? <Tooltip
									title={dealTypeLang.table("editTip")} // lang:编辑风险决策
								>
									<a onClick={(e) => {
										this.modifyDealTypeHandle(record, e);
									}}>
										{/* lang:编辑 */}
										{dealTypeLang.table("edit")}
									</a>
								</Tooltip>
								: !record.default &&
                                <a className="a-disabled" onClick={() => {
                                	// lang:无权限操作
                                	message.warning(commonLang.messageInfo("noPermission"));
                                }}>
                                	{/* lang:编辑 */}
                                	{dealTypeLang.table("edit")}
                                </a>
							}
							{!record.default &&
                                checkFunctionHasPermission("ZB0302", "deletePolicyDeal")
								? <Tooltip
									title={dealTypeLang.table("deleteTip")} // lang:删除风险决策
								>
									<a onClick={() => {
										this.deleteDealTypeHandle(record);
									}}>
										{/* lang:删除 */}
										{dealTypeLang.table("delete")}
									</a>
								</Tooltip>
								: !record.default && <a className="a-disabled" onClick={() => {
									// lang:无权限操作
									message.warning(commonLang.messageInfo("noPermission"));
								}}>删除</a>
							}
						</div>
					);
				}
			}
		];

		return (
			<div>
				<div className="page-global-header">
					<div className="left-info">
						<h2>
							{/* lang:风险决策列表 */}
							{dealTypeLang.common("title")}
						</h2>
					</div>
					{
						menuTreeReady && checkFunctionHasPermission("ZB0302", "listPolicyDeal") &&
                        <div className="right-info">
                        	<div className="right-info-item">
                        		<Button
                        			type="primary"
                        			onClick={this.addDealTypeHandle.bind(this)}
                        			disabled={!checkFunctionHasPermission("ZB0302", "addPolicyDeal")}
                        		>
                        			{/* lang:新增风险决策 */}
                        			{dealTypeLang.common("add")}
                        		</Button>
                        	</div>
                        </div>
					}
				</div>
				{
					menuTreeReady &&
                    <div className="page-global-body">
                    	{
                    		checkFunctionHasPermission("ZB0302", "listPolicyDeal")
                    			? <div className="page-global-body-main has-table-border">
                    				<Table
                    					className="table-out-border"
                    					columns={columns}
                    					pagination={false}
                    					dataSource={dealTypesList}
                    					rowKey="uuid"
                    					size={personalMode.layout === "default" ? "default" : "middle"}
                    				/>
                    				<div className="page-global-body-pagination">
                    					<span className="count">
                    						{/* lang:共1条记录*/}
                    						{commonLang.getRecords(total)}
                    					</span>
                    					<Pagination
                    						showSizeChanger
                    						onChange={this.paginationOnChange.bind(this)}
                    						onShowSizeChange={this.paginationOnChange.bind(this)}
                    						defaultCurrent={1}
                    						total={total}
                    						current={curPage}
                    					/>
                    				</div>
                    			</div>
                    			: <NoPermission />
                    	}
                    </div>
				}
				<AddDealTypeModal />
				<ModifyDealTypeModal />
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(DealType);

