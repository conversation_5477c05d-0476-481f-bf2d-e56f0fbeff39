import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"tabForEditor": {
			"cn": "编辑区",
			"en": "Editing Area"
		},
		"tabForRunning": {
			"cn": "运行区",
			"en": "Running Area"
		},
		"titleForEditor": {
			"cn": "编辑区列表",
			"en": "Editing Area List"
		},
		"titleForRunning": {
			"cn": "运行区列表",
			"en": "Running Area List"
		},
		"searchPlaceholder": {
			"cn": "请输入搜索内容",
			"en": "Please enter the search content"
		},
		"addNewIndex": {
			"cn": "新建指标",
			"en": "Add Indicator"
		},
		"import": {
			"cn": "导入",
			"en": "Import"
		},
		"export": {
			"cn": "导出",
			"en": "Export"
		},
		"indicatorVersionList": {
			"cn": "指标版本列表",
			"en": "Indicator Version List"
		},
		"batchOffLine": {
			"cn": "批量下线",
			"en": "Batch OffLine"
		},
		"batchOperate": {
			"cn": "批量操作",
			"en": "Batch operation"
		},
		"batchSubmit": {
			"cn": "批量提交",
			"en": "Batch up"
		},
		"batchOnline": {
			"cn": "批量上线",
			"en": "Batch on-line"
		},
		"batchOffWarn": {
			"cn": "指标（批量）下线后，运行区将移除下线指标，请至编辑区查询操作，请确认",
			"en": "After the indicator (batch) is offline, the offline indicator will be removed from the operation area. Please go to the editing area to query. Please confirm"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"all": {
			"cn": "全部",
			"en": "All"
		},
		"allPolicySets": {
			"cn": "全部场景",
			"en": "All Scene"
		},
		"selectPolicySets": {
			"cn": "选择策略集",
			"en": "Select PolicySet"
		},
		"selectSceneType": {
			"cn": "选择场景类型",
			"en": "Select scene type"
		},
		"selectScene": {
			"cn": "选择场景",
			"en": "Select scene"
		},
		"allIndexTypes": {
			"cn": "全部指标类型",
			"en": "All Indicator Types"
		},
		"selectIndexTypes": {
			"cn": "选择指标类型",
			"en": "Select Indicator Type"
		},
		"indexName": {
			"cn": "指标名称",
			"en": "Indicator Name"
		},
		"search": {
			"cn": "搜索",
			"en": "Search"
		},
		"selectIndexTip": {
			"cn": "请先选择需要导出的指标项",
			"en": "Select indicators you want to export first"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"indexName": {
			"cn": "指标名称",
			"en": "Indicator Name"
		},
		"policySet": {
			"cn": "策略集",
			"en": "PolicySet"
		},
		"id": {
			"cn": "指标编号",
			"en": "ID"
		},
		"noId": {
			"cn": "暂无ID",
			"en": "No ID"
		},
		"status": {
			"cn": "指标状态",
			"en": "Status"
		},
		"operator": {
			"cn": "操作",
			"en": "Operation"
		},
		"on": {
			"cn": "开启",
			"en": "On"
		},
		"off": {
			"cn": "关闭",
			"en": "Off"
		},
		"noIndexInfo": {
			"cn": "暂无指标信息",
			"en": "No Indicator Info"
		},
		"version": {
			"cn": "版本号",
			"en": "Version"
		},
		"switchVersion": {
			"cn": "切换版本",
			"en": "Switch Version"
		},
		"addNotSave": {
			"cn": "新添加，未保存",
			"en": "New added, not saved."
		},
		"hasModify": {
			"cn": "有修改",
			"en": "Has modify"
		},
		"hasModifyNeedRefresh": {
			"cn": "已保存，页面待刷新",
			"en": "Has modify,page need refresh"
		},
		"calcType": {
			"cn": "指标类型",
			"en": "IndicatorType"
		},
		"noCalcType": {
			"cn": "暂无指标类型",
			"en": "No Indicator Type"
		}
	};
	return params[field][lang];
};

const operator = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"switchVersionSuccessTip": {
			"cn": "切换版本到编辑区成功",
			"en": "Switch version to edit area successfully"
		},
		"switchVersion": {
			"cn": "切换版本到编辑区",
			"en": "Switch version to edit area"
		},
		"switchVersionContentText1": {
			"cn": "确认将当前指标V",
			"en": "Confirm that the current indicator V"
		},
		"switchVersionContentText2": {
			"cn": "覆盖到编辑区吗？",
			"en": " is overwritten to the edit area?"
		},
		"copyIndexSuccess": {
			"cn": "复制指标成功",
			"en": "Copy Indicator successful"
		},
		"modifyIndexStatusSuccess": {
			"cn": "修改指标状态成功",
			"en": "Modify Indicator status successful"
		},
		"deleteIndexStatusSuccess": {
			"cn": "删除指标成功",
			"en": "Delete Indicator status successful"
		},
		"operateSuccessfully": {
			"cn": "操作成功",
			"en": "Operate successfully"
		},
		"exportPrefixText": {
			"cn": "指标_",
			"en": "Indicator_"
		},
		"exportIndexSuccess": {
			"cn": "导出所选指标列表成功",
			"en": "Export the selected Indicator list successfully"
		},
		"saveBeforeCopy": {
			"cn": "请保存指标后再复制",
			"en": "Please save the Indicator before copying"
		},
		"deleteIndexTip": {
			"cn": "删除指标提醒",
			"en": "Delete Indicator tip"
		},
		"deleteIndexDesc1": {
			"cn": "您真的要删除《",
			"en": "Do you really want to delete "
		},
		"deleteIndexDesc2": {
			"cn": "》吗？",
			"en": "?"
		},
		"delIndexReCall": {
			"cn": "删除指标将一并删除回溯任务",
			"en": "Deleting indicators will also delete the backtracking task"
		},
		"saveBeforeAuth": {
			"cn": "请保存指标后再授权",
			"en": "Please save the indicator before authorizing"
		},
		"saveBeforeRecall": {
			"cn": "请保存指标后再添加回溯",
			"en": "Please save the indicator before adding a backtrace"
		}
	};
	return params[field][lang];
};

const ruleConfig = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"tip1": {
			"cn": "操作配置存在空值，请补充完整！",
			"en": "Operating configuration is null, please complete!"
		},
		"tip2": {
			"cn": "输入框为空，请补充完整！",
			"en": "Input box is empty, please complete!"
		},
		"tip3": {
			"cn": "选项为空，请选择！",
			"en": "Option is empty, please choose!"
		},
		"tip4": {
			"cn": "存在空值，请补充完整！",
			"en": "Is null, please complete!"
		},
		"tip5": {
			"cn": "条件配置存在空值，请补充完整！",
			"en": "Condition configuration is null, please complete!"
		},
		"indexAddedSuccessTip": {
			"cn": "新增指标成功",
			"en": "Indicator added successfully"
		},
		"indexUpdateSuccessTip": {
			"cn": "更新指标成功",
			"en": "Update Indicator successful"
		},
		"basicSetting": {
			"cn": "基本设置",
			"en": "Basic Setting"
		},
		"indexConfig": {
			"cn": "指标配置",
			"en": "Indicator Config"
		},
		"updateConfig": {
			"cn": "更新配置",
			"en": "Update Config"
		},
		"packUp": {
			"cn": "收起",
			"en": "Pack up"
		},
		"update": {
			"cn": "更新",
			"en": "Update"
		},
		"add": {
			"cn": "添加",
			"en": "Add"
		},
		"submitVersion": {
			"cn": "提交版本",
			"en": "Submit Version"
		},
		"noPermission": {
			"cn": "无权限操作",
			"en": "No permission operation"
		},
		"scene": {
			"cn": "场景",
			"en": "Scene"
		},
		"allMissScene": {
			"cn": "全指标场景缺失：",
			"en": "Missing full index scenario:"
		},
		"exceptHandle": {
			"cn": "异常处理：",
			"en": "Exception handling:"
		},
		"application": {
			"cn": "应用",
			"en": "Application"
		},
		"policySet": {
			"cn": "策略集",
			"en": "Policy Set"
		},
		"searchSceneTip": {
			cn: "搜索应用、策略集",
			en: "Search scene tip"
		},
		"exitSearch": {
			cn: "退出搜索",
			en: "Exit search"
		},
		"scenePolicySet": {
			"cn": "默认选择导入策略作为指标场景",
			"en": "Select import policy as indicator scenario by default"
		},
		"sceneRules": {
			"cn": "默认选择导入规则作为指标场景",
			"en": "Select import rule as indicator scenario by default"
		},
		"sceneWorkFlow": {
			cn: "默认选择导入规则流作为指标场景",
			en: "Import rule flow as indicator scenario is selected by default"
		},
		"sceneIndex": {
			cn: "忽略异常不处理",
			en: "Ignore exceptions do not handle"
		},
		"batchSetScene": {
			cn: "批量设置场景",
			en: "Batch setting scenario"
		},
		"indexMissWarnTip": {
			cn: "缺失异常项不展示，可能存在部分下拉框内容为空的情况",
			en: "The missing exception items are not displayed. There may be some cases where the content of the drop-down box is empty"
		},
		"sceneDetail": {
			cn: "具体场景",
			en: "Specific scenario"
		},
		"searchSceneTip1": {
			cn: "搜索场景",
			en: "Search scene tip"
		},
		"selectLogic": {
			cn: "选择运算符",
			en: "Selection operator"
		},
		"noData": {
			cn: "暂无数据",
			en: "No Data"
		}
	};
	return params[field][lang];
};

const ruleAttr = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"indexName": {
			"cn": "指标名称",
			"en": "Indicator Name"
		},
		indexAlias: {
			"cn": "指标别名",
			"en": "Indicator Alias"
		},
		"indexDescription": {
			"cn": "指标描述",
			"en": "Indicator Description"
		},
		"indexDescriptionPlaceholder": {
			"cn": "请输入指标说明",
			"en": "Please enter Indicator Description"
		},
		"appName": {
			"cn": "应用名",
			"en": "App Name"
		},
		"select": {
			"cn": "选择",
			"en": "select"
		},
		"pleaseSelect": {
			"cn": "请选择",
			"en": "Please select"
		},
		"pleaseEnter": {
			"cn": "请输入",
			"en": "Please enter"
		},
		"policySet": {
			"cn": "策略集",
			"en": "Policy Set"
		},
		"filterConditions": {
			"cn": "过滤条件",
			"en": "Filter Conditions"
		},
		"addFilter": {
			"cn": "新增过滤",
			"en": "Add Filter"
		},
		"constant": {
			"cn": "常量",
			"en": "constant"
		},
		"variable": {
			"cn": "变量",
			"en": "variable"
		},
		"constantInputPlaceholder": {
			"cn": "请填写常量内容",
			"en": "Please enter constant text"
		},
		"deleteCurrentRow": {
			"cn": "删除当前行",
			"en": "Delete current row"
		},
		"current": {
			"cn": "[当前] ",
			"en": "[Current] "
		},
		"progress": {
			"cn": "计算进度",
			"en": "Progress"
		},
		"status": {
			"cn": "计算状态",
			"en": "Status"
		},
		"offlineStartTime": {
			"cn": "计算开始时间",
			"en": "Offline Start Time"
		},
		"offlineUpdateTime": {
			"cn": "计算更新时间",
			"en": "Offline Update Time"
		},
		"add": {
			"cn": "新增",
			"en": "Add"
		},
		"filterWarn": {
			"cn": "指标变量过滤条件右边字段不能与左边字段相同",
			"en": "The right field of indicator variable filter condition cannot be the same as the left field"
		},
		"slaveWarn": {
			"cn": "从属性与从属性取值字段重复",
			"en": "Duplicate from attribute and from attribute value field"
		},
		"slaveGroupWarn": {
			"cn": "从属性重复配置",
			"en": "Duplicate configuration from property"
		},
		"inputIndexName": {
			"cn": "请输入指标名称",
			"en": "Please enter indicator name"
		},
		"indexNameMaxLen": {
			"cn": "指标名称长度不大于80",
			"en": "Index name length is no more than 80"
		},
		"periodWarn": {
			"cn": "请输入时间段区间",
			"en": "Please enter time period"
		},
		"periodWarn1": {
			"cn": "请输入正确的时间区间",
			"en": "Please enter the correct time interval"
		},
		"thresholdLess100": {
			"cn": "集中度阀值小于100",
			"en": "Concentration threshold less than 100"
		},
		"thresholdUp0": {
			"cn": "请输入大于0的集中度阀值",
			"en": "Please enter a concentration threshold greater than 0"
		},
		"logicTip": {
			"cn": "请选择运算符",
			"en": "Please select operator"
		},
		"selectScene": {
			"cn": "请选择场景",
			"en": "Please select operator"
		}
	};
	return params[field][lang];
};

const tooltip = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"viewHistoryVersion": {
			"cn": "查看历史版本",
			"en": "View history version"
		},
		"viewIndexCite": {
			"cn": "查看指标引用",
			"en": "View indicator cite"
		},
		"copyIndex": {
			"cn": "复制",
			"en": "Copy indicator"
		},
		"deleteIndex": {
			"cn": "删除当前指标",
			"en": "Delete indicator"
		},
		"indexHasNoVersionRecord": {
			"cn": "当前指标暂无版本记录",
			"en": "Current indicator has no version record"
		},
		"policyHasNoCiteIndex": {
			"cn": "当前策略暂无引用指标",
			"en": "Current policy has no reference indicator"
		},
		"authorization": {
			"cn": "授权",
			"en": "Authorization"
		}
	};
	return params[field][lang];
};

const importIndexModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"indexImport": {
			"cn": "指标导入",
			"en": "Indicator Import"
		},
		"importMode": {
			"cn": "导入模式",
			"en": "Import mode"
		},
		"selectFile": {
			"cn": "选择文件",
			"en": "Select file"
		},
		"selectFileFirst": {
			"cn": "请先选择文件",
			"en": "Please select the file first"
		},
		"zbModeRequired": {
			"cn": "请选择指标导入模式",
			"en": "Please select indicator import mode"
		},
		"sameIdSkipping": {
			"cn": "相同ID指标跳过",
			"en": "Same ID skipping"
		},
		"sameIdCoverage": {
			"cn": "相同ID指标覆盖",
			"en": "Same ID coverage"
		},
		"sameIdInterrupt": {
			"cn": "相同ID中断",
			"en": "Same Id interrupt"
		},
		"fileAllowedTip": {
			"cn": "只允许上传zb格式的文件",
			"en": "Only files in zb format are allowed"
		},
		"fileSizeTip": {
			"cn": "文件大小请100M内",
			"en": "File size in 100M please"
		},
		//	other
		"indexName": {
			"cn": "指标名称",
			"en": "Indicator Name"
		},
		"importStatus": {
			"cn": "导入状态",
			"en": "Import status"
		},
		"reason": {
			"cn": "原因",
			"en": "Reason"
		},
		"success": {
			"cn": "成功",
			"en": "Success"
		},
		"fail": {
			"cn": "失败",
			"en": "Fail"
		},
		//	button
		"cancel": {
			"cn": "取消",
			"en": "Cancle"
		},
		"ok": {
			"cn": "确定",
			"en": "Ok"
		},
		"upload": {
			"cn": "上传",
			"en": "upload"
		}
	};
	return params[field][lang];
};

const indexCommitModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"title": {
			"cn": "指标版本提交",
			"en": "Indicator Version Submit"
		},
		"indexName": {
			"cn": "指标名称",
			"en": "Indicator Name"
		},
		"inputIndexName": {
			"cn": "请输入指标名称",
			"en": "Please enter indicator name"
		},
		"description": {
			"cn": "发版描述",
			"en": "Description"
		},
		"inputReleaseDes": {
			"cn": "请输入发版描述",
			"en": "Please enter the release"
		},
		"indexSubmitSuccessTip": {
			"cn": "指标版本提交成功",
			"en": "Indicator version submit successful"
		},
		"descriptionCannotEmptyTip": {
			"cn": "发版描述不能为空",
			"en": "Description cannot be empty"
		}
	};
	return params[field][lang];
};

const citeDrawer = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"citeDetail": {
			"cn": "引用详情",
			"en": "Cite Detail"
		},
		"appName": {
			"cn": "所属应用",
			"en": "App Name"
		},
		"appName2": {
			"cn": "所属应用：",
			"en": "App name:"
		},
		"policy": {
			"cn": "所属策略：",
			"en": "Policy:"
		},
		"policySet": {
			"cn": "所属策略集",
			"en": "Policy Set"
		},
		"policySet2": {
			"cn": "所属策略集：",
			"en": "Policy set:"
		},
		"inPolicySetTip": {
			"cn": "所在策略集",
			"en": "In which policy set"
		},
		"createTime": {
			"cn": "创建时间",
			"en": "Create Time"
		},
		"modifyTime": {
			"cn": "修改时间",
			"en": "Modify Time"
		},
		"noneCiteText": {
			"cn": "当前指标还没有被引用",
			"en": "The current indicator is not yet referenced"
		},
		"runningArea": {
			"cn": "运行区",
			"en": "Operation area"
		},
		"formulaIndex": {
			"cn": "公式指标",
			"en": "Formula index"
		},
		"publicStrategy": {
			"cn": "公共策略",
			"en": "Public strategy"
		},
		"editArea": {
			"cn": "编辑区",
			"en": "Editing area"
		}

	};
	return params[field][lang];
};

const batchRes = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"name": {
			"cn": "指标名称",
			"en": "Indicator Name"
		},
		"id": {
			"cn": "指标Id",
			"en": "Indicator Id"
		},
		"result": {
			"cn": "结果",
			"en": "Result"
		},
		"failDescription": {
			"cn": "失败描述",
			"en": "Failure description"
		},
		"success": {
			"cn": "成功",
			"en": "Success"
		},
		"fail": {
			"cn": "失败",
			"en": "failed"
		},
		"batchOfflineTitle": {
			"cn": "批量下线结果",
			"en": "Batch offline results"
		},
		"batchSubmitTitle": {
			"cn": "批量提交结果",
			"en": "Batch Submit results"
		}
	};
	return params[field][lang];
};

const example = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"indexExample": {
			"cn": "指标样例",
			"en": "Index example"
		},
		"indexDemo": {
			"cn": "指标示例：",
			"en": "Example of index:"
		},
		"sampleImg": {
			"cn": "样例图片：",
			"en": "Sample image:"
		}
	};
	return params[field][lang];
};

const authModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"title": {
			"cn": "授权使用列表",
			"en": "Authorized use list"
		},
		"authorized": {
			"cn": "已授权",
			"en": "Authorized"
		},
		"unauthorized": {
			"cn": "未授权",
			"en": "Unauthorized"
		},
		"authWarn": {
			"cn": "至少授权一个应用",
			"en": "Authorize at least one app"
		}
	};
	return params[field][lang];
};
export default {
	common,
	searchParams,
	table,
	operator,
	ruleConfig,
	ruleAttr,
	tooltip,
	importIndexModal,
	indexCommitModal,
	citeDrawer,
	batchRes,
	example,
	authModal
};
