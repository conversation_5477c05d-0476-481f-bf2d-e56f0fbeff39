export { default as commonLang } from "./common";
export { default as topLang } from "./top";
export { default as policyListLang } from "./policyList";
export { default as indexListLang } from "./indexList";
export { default as debugLang } from "./debug";
export { default as policyDetailLang } from "./policyDetail";
export { default as dealTypeLang } from "./dealType";
export { default as ruleImmunoLang } from "./ruleImmuno";
export { default as replayTaskLang } from "./replayTask";
export { default as approvalSettingLang } from "./approvalSetting";
export { default as approvalLogLang } from "./approvalLog";
export { default as approvalTaskLang } from "./approvalTask";
export { default as workflowLang } from "./workflow";
export { default as fieldSetManagementLang } from "./fieldSetManagement";
export { default as publicPolicyListLang } from "./publicPolicyList";
export { default as immuneLang } from "./immune";
export { default as immuneConfigLang } from "./immuneConfig";
export { default as immuneHistoryLang } from "./immuneHistory";
export { default as timePickerLang } from "./timePicker";
export { default as reCallTaskLang } from "./reCallTask";
export { default as imExportModeLang } from "./imExportMode";
