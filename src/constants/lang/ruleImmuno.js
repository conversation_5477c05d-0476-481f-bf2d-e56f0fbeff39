import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"add": {
			"cn": "添加",
			"en": "Add"
		},
		"modify": {
			"cn": "修改",
			"en": "Modify"
		},
		"addTitle": {
			"cn": "添加免打扰",
			"en": "Add Rule Immuno"
		},
		"modifyTitle": {
			"cn": "修改免打扰",
			"en": "Modify Rule Immuno"
		},
		"ruleName": {
			"cn": "规则名称：",
			"en": "Rule Name:"
		},
		"expireTs": {
			"cn": "失效时间：",
			"en": "Expire Time:"
		},
		"expireTsPlaceholder": {
			"cn": "请选择免打扰失效时间",
			"en": "Please select expire time"
		},
		"description": {
			"cn": "免打扰说明：",
			"en": "Description:"
		},
		"descriptionPlaceholder": {
			"cn": "请输入免打扰说明",
			"en": "Please enter description"
		},
		"logicOperator": {
			"cn": "执行条件：",
			"en": "Logic Operator:"
		},
		"logic1": {
			"cn": "满足以下所有条件",
			"en": "Satisfy all of the following conditions"
		},
		"logic2": {
			"cn": "满足以下任意条件",
			"en": "Satisfy the following conditions"
		},
		"ok": {
			"cn": "确定",
			"en": "OK"
		},
		"cancel": {
			"cn": "取消",
			"en": "Cancel"
		},
		"deleteImmunity": {
			"cn": "删除当前免打扰",
			"en": "Delete current immunity"
		}
	};
	return params[field][lang];
};
const rule = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"pleaseSelectPlaceholder": {
			"cn": "请选择",
			"en": "select"
		},
		"constant": {
			"cn": "常量",
			"en": "constant"
		},
		"variable": {
			"cn": "变量",
			"en": "variable"
		},
		"constantInputPlaceholder": {
			"cn": "请输入常量值",
			"en": "Please enter a constant value"
		}
	};
	return params[field][lang];
};

const tooltip = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"addSuccess": {
			"cn": "新增免打扰成功",
			"en": "Add Immunity successfully"
		},
		"modifySuccess": {
			"cn": "修改免打扰成功",
			"en": "Modify Immunity successfully"
		},
		"deleteSuccess": {
			"cn": "删除免打扰成功",
			"en": "Delete Immunity successfully"
		},
		"timeHasEmpty": {
			"cn": "免打扰时间不能为空",
			"en": "Immunity time cannot be empty"
		},
		"logicHasEmpty": {
			"cn": "请选择执行条件",
			"en": "Select the execution condition"
		},
		"ruleHasEmpty": {
			"cn": "请配置执行规则",
			"en": "Configure the execution rules"
		},
		"ruleFieldHasEmpty": {
			"cn": "执行规则存在空值，请补充完整!",
			"en": "The execution rule has a null value, please complete!"
		},
		"deleteImmuneAlertTitle": {
			"cn": "删除免打扰提醒",
			"en": "Delete immune alerts"
		},
		"deleteImmuneAlertDesc": {
			"cn": "确认要删除当前规则免打扰吗？",
			"en": "Are you sure you want to remove current rule immunity"
		}
	};
	return params[field][lang];
};

export default {
	common,
	rule,
	tooltip
};
