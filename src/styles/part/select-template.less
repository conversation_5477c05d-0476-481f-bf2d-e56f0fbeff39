@import "../base/variables";

:global {
	.select-template-wrap {
		& {
			position: relative;
		}
		.ant-modal-body {
			//padding: 0;
		}
		.select-template-content {
			& {
				position: relative;
				height: 400px;
			}
			.template-group-list {
				& {
					width: 40%;
					float: left;
					background: #f0f0f0;
					height: 100%;
				}
				.group-item {
					& {
						width: 100%;
						height: 40px;
						line-height: 40px;
						cursor: pointer;
						padding: 0 24px;
					}
					&.current {
						background: @blue;
						color: #fff;
					}
				}
			}
			.template-rule-list {
				& {
					width: 55%;
					float: right;
					background: #fff;
					height: 400px;
					overflow-y: scroll;
				}
				.oper-list {
					a {

					}
					a.disabled {
						color: #999;
						cursor: not-allowed;
					}
				}
			}
		}
	}
}
