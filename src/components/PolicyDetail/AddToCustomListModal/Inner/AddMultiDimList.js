import { Fragment } from "react";
import { connect } from "dva";
import { Modal, Input, InputNumber, Button, Select, Row, Col, Icon, Alert, Tooltip, DatePicker, message, Empty } from "antd";
import { policyDetailAPI } from "@/services";
import { CommonConstants } from "@/constants";
import moment from "moment";
import { policyDetailLang } from "@/constants/lang";

const InputGroup = Input.Group;
const { Option } = Select;

export default (props) => {
	let { itemIndex, itemData, readOnly, globalStore, curPolicyAppName, onChange, removeCustomItem } = props;
	let { allMap } = globalStore;
	let { ruleAndIndexFieldList = [], multiDimCustomListBusiTypeSelect = [], multiDimCustomListTypeSelect = [], multiDimCustomListSelect = [] } = allMap;

	let customList = [];
	let defineType = itemData.do.arguments.defineType || null;
	let multiDimListUuid = itemData.do.arguments.multiDimListUuid || null;

	if (defineType) {
		customList = multiDimCustomListSelect.filter(listItem => {
			let type = listItem.type ? listItem.type.toString() : null;
			if (type) {
				return type === defineType;
			}
		});
	}

	const fieldList = ruleAndIndexFieldList.filter(field => field.type === "STRING");
	console.log(customList);
	// 根据   multiDimListUuid  和  customList   获取用户选择的名单集对象
	let customObj;
	if (customList && multiDimListUuid) {
		customObj = customList.find(customItem => customItem.uuid === multiDimListUuid);
	}

	console.log(customObj);

	const renderColData = (colObj, colIndex, arr) => {
		return (
			<Row
				gutter={CommonConstants.gutterSpan}
				className="custom-list-row"
				key={itemIndex}
			>
				<Col span={2} className="custom-list-title">
					{
						colIndex === 0 &&
                        <div>字段</div>
					}
				</Col>
				<Col span={5}>
					<Select
						value={colObj.fieldName || undefined}
						onChange={(value) => {
							let colData = itemData.do.arguments.colData;
							colData[colIndex]["fieldName"] = value;
							onChange("colData", itemIndex, colData);
						}}
						placeholder={policyDetailLang.addToCustomListModal("select")} // 请选择
						showSearch
						optionFilterProp="children"
						disabled={readOnly}
						dropdownMatchSelectWidth={false}
					>
						{
							allMap &&
                            allMap["ruleFieldList"] &&
                            allMap["ruleFieldList"].map((item, index) => {
                            	return (
                            		<Option
                            			value={item.name}
                            			key={index}
                            		>
                            			{item.dName}
                            		</Option>
                            	);
                            })
						}
					</Select>
				</Col>
				<Col span={1}>
					<Icon className="trans-arrow" type="arrow-right" />
				</Col>
				<Col span={9}>
					<Select
						value={colObj.colUuid || undefined}
						onChange={(value) => {
							let columnBOList = customObj.columnBOList || [];
							let colData = itemData.do.arguments.colData;
							let selectObj = columnBOList.find(columnBO => columnBO.uuid === value);

							colData[colIndex]["colUuid"] = value;
							colData[colIndex]["key"] = selectObj.key;
							onChange("colData", itemIndex, colData);
						}}
						disabled={readOnly}
						placeholder="请选择名单字段"
					>
						{
							customObj &&
                            customObj.columnBOList &&
                            customObj.columnBOList.map((item, index) => {
                            	return (
                            		<Option
                            			value={item.uuid}
                            			key={index}
                            		>
                            			{item.key ? "[主键]" : ""}{item.name}
                            		</Option>
                            	);
                            })
						}
					</Select>
				</Col>
				<Col span={2} className="basic-info-oper">
					{/* 添加一项 */}
					<Tooltip title={policyDetailLang.addToCustomListModal("add")}
						placement="top">
						<Icon
							className="add"
							type="plus-circle-o"
							onClick={() => {
								let colData = itemData.do.arguments.colData;
								let colObj = {
									colUuid: null,
									fieldName: null
								};
								colData.splice(colIndex + 1, 0, colObj);

								onChange("colData", itemIndex, colData);
							}}
						/>
					</Tooltip>
					{/* 删除当前行 */}
					{
						arr.length > 1 &&
                        <Tooltip title={policyDetailLang.addToCustomListModal("deleteLine")}
                        	placement="top">
                        	<Icon
                        		className="delete"
                        		type="delete"
                        		onClick={() => {
                        			let colData = itemData.do.arguments.colData;
                        			colData.splice(colIndex, 1);

                        			onChange("colData", itemIndex, colData);
                        		}}
                        	/>
                        </Tooltip>
					}
				</Col>
			</Row>
		);
	};
	console.log(props);

	return (
		<div className="multiple-section">
			<Row
				gutter={CommonConstants.gutterSpan}
				key={itemIndex}
				className="custom-list-row"
			>
				<Col span={2} className="custom-list-title">
                    名单
				</Col>
				<Col span={4}>
					<Select
						value={itemData.do.arguments.defineType || undefined}
						onChange={(value) => {
							onChange("defineType", itemIndex, value);
							// 切换名单类型的时候清空后面的名单数据
							onChange("multiDimListUuid", itemIndex, "");
							// 清空已经选择的名单字段

							itemData.do.arguments &&
                                itemData.do.arguments.colData &&
                                itemData.do.arguments.colData.length > 0 &&
                                itemData.do.arguments.colData.map((item, index) => {
                                	let colData = itemData.do.arguments.colData;

                                	colData[index]["colUuid"] = null;
                                	colData[index]["key"] = null;
                                	onChange("colData", itemIndex, colData);
                                });
						}}
						placeholder={policyDetailLang.addToCustomListModal("select")} // 请选择
						showSearch
						optionFilterProp="children"
						disabled={readOnly}
						dropdownMatchSelectWidth={false}
					>
						{
							multiDimCustomListTypeSelect.map((item, index) => {
								return (
									<Option
										value={item.name}
										key={index}
									>
										{item.dName}
									</Option>
								);
							})
						}
					</Select>
				</Col>
				<Col span={5}>
					<Select
						value={itemData.do.arguments.multiDimListUuid || undefined}
						onChange={(value) => {
							onChange("multiDimListUuid", itemIndex, value);
						}}
						placeholder={policyDetailLang.addToCustomListModal("select")} // 请选择
						showSearch
						optionFilterProp="children"
						disabled={readOnly}
						dropdownMatchSelectWidth={false}
					>
						{
							customList &&
                            customList.map((listItem, index) => {
                            	return (
                            		<Option
                            			value={listItem.uuid}
                            			key={index}
                            		>
                            			{listItem.name}
                            		</Option>
                            	);
                            })
						}
					</Select>
				</Col>
				<Col span={10}>
					<InputGroup compact>
						<Select
							style={{ width: "40%" }}
							value={itemData.do.arguments.expireType || undefined}
							onChange={(value) => {
								onChange("expireType", itemIndex, value);
								onChange("expireValue", itemIndex, "");
							}}
							disabled={readOnly}
						>
							<Option value="forever">{policyDetailLang.addToCustomListModal("forever")}</Option>
							<Option value="byDay">{policyDetailLang.addToCustomListModal("byDay")}</Option>
							<Option value="byTs">{policyDetailLang.addToCustomListModal("byTs")}</Option>
							<Option value="byHour">{policyDetailLang.addToCustomListModal("byHour")}</Option>
							<Option value="byMin">{policyDetailLang.addToCustomListModal("byMin")}</Option>
						</Select>
						{
							(itemData.do.arguments.expireType === "byDay" || itemData.do.arguments.expireType === "byHour" || itemData.do.arguments.expireType === "byMin") &&
                            <InputNumber
                            	min={1}
                            	max={1000}
                            	style={{ width: "60%" }}
                            	value={itemData.do.arguments.expireValue || undefined}
                            	onChange={(value) => {
                            		onChange("expireValue", itemIndex, value);
                            	}}
                            	disabled={readOnly}
                            />
						}
						{
							itemData.do.arguments.expireType === "byTs" &&
                            <DatePicker
                            	style={{ width: "60%" }}
                            	value={itemData.do.arguments.expireValue ? moment(itemData.do.arguments.expireValue) : undefined}
                            	format="YYYY-MM-DD HH:mm:ss"
                            	showTime
                            	disabledDate={(current) => {
                            		return current && current < moment().endOf("day");
                            	}}
                            	onChange={(value) => {
                            		onChange("expireValue", itemIndex, value ? value.valueOf() : null);
                            	}}
                            	disabled={readOnly}
                            />
						}
					</InputGroup>
				</Col>
				{
					!readOnly &&
                    <Col span={2} className="basic-info-oper">
                    	{/* 删除当前 */}
                    	<Tooltip title={policyDetailLang.addToCustomListModal("deleteCurrentConfig")}
                    		placement="top">
                    		<Icon
                    			className="delete"
                    			type="delete"
                    			onClick={() => {
                    				removeCustomItem(itemIndex);
                    			}}
                    		/>
                    	</Tooltip>
                    </Col>
				}
			</Row>
			{
				itemData.do.arguments &&
                itemData.do.arguments.colData &&
                itemData.do.arguments.colData.length > 0 &&
                itemData.do.arguments.colData.map((item, index, arr) => {
                	return renderColData(item, index, arr);
                })
			}
		</div>
	);
};
