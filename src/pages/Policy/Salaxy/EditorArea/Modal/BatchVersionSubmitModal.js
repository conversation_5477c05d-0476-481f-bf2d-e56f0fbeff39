import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Modal, message, Input, Form } from "antd";
import { indexEditorAPI } from "@/services";
import { indexListLang } from "@/constants/lang";
import OffLineResult from "../../Common/BatchDeal/OffLineResult";

const TextArea = Input.TextArea;
const formItemLayout = {
	labelCol: {
		xs: { span: 24 },
		sm: { span: 5 }
	},
	wrapperCol: {
		xs: { span: 24 },
		sm: { span: 19 }
	}
};

class BatchVersionSubmitModal extends PureComponent {
	constructor(props) {
		super(props);
	}

    closeModal = () => {
    	const { dispatch } = this.props;
    	dispatch({
    		type: "indexEditor/setDialogShow",
    		payload: {
    			batchVersionSubmit: false
    		}
    	});
    }

    cancelZbRes = () => {
    	this.handleBatchRes([], false);
    }

    handleBatchRes = (batchResData, visible) => {
    	const { dispatch, handleOk } = this.props;
    	if (batchResData && batchResData.length > 0) {
    		dispatch({
    			type: "indexEditor/setDialogData",
    			payload: {
    				batchResData
    			}
    		});
    	}
    	dispatch({
    		type: "indexEditor/setDialogShow",
    		payload: {
    			batchRes: visible
    		}
    	});
    	if (!visible) {
    		handleOk();
    	}
    }

    batchVersionSubmit = () => {
    	let { indexEditorStore } = this.props;
    	let { dialogData } = indexEditorStore;
    	let { batchVersionSubmitData } = dialogData;
    	let { zbs } = batchVersionSubmitData;

    	this.props.form.validateFields((err, values) => {
    		if (!err) {
    			const ids = zbs && zbs.length > 0 && zbs.map(v => { return v.id; });
    			let params = {
    				ids: ids && ids.join(","),
    				description: values.description
    			};
    			indexEditorAPI.batchSubmit(params).then(res => {
    				if (res.success) {
    					this.closeModal();
    					this.props.form.resetFields();
    					this.handleBatchRes(res.data || [], true);
    				} else {
    					message.error(res.message);
    				}
    			}).catch(err => {
    				message.error(err.message);
    			});
    		}
    	});
    }

    render() {
    	const { indexEditorStore, form } = this.props;
    	const { getFieldDecorator } = form;
    	const { dialogShow, dialogData } = indexEditorStore;
    	const { batchVersionSubmitData, batchResData } = dialogData;
    	const { zbs } = batchVersionSubmitData;
    	const { batchVersionSubmit, batchRes } = dialogShow;
    	const zbNames = zbs && zbs.length > 0 && zbs.reduce((pre, v) => { pre.push(v.name); return pre; }, []).join(";");
    	return (
    		<Fragment>
    			<Modal
    				title={indexListLang.indexCommitModal("title")} // lang:指标版本提交
    				visible={batchVersionSubmit}
    				maskClosable={false}
    				onOk={this.batchVersionSubmit.bind(this)}
    				className="index-version-modal-wrap"
    				onCancel={this.closeModal}
    			>
    				<Form {...formItemLayout}>
    					{/* lang:指标名称 */}
    					<Form.Item label={indexListLang.indexCommitModal("indexName")}>
    						<TextArea
    							autoSize
    							disabled
    							placeholder={indexListLang.indexCommitModal("inputIndexName")} // lang:请输入发版描述
    							value={zbNames || undefined}
    						/>
    					</Form.Item>
    					{/* lang:发版描述 */}
    					<Form.Item label={indexListLang.indexCommitModal("description")}>
    						{getFieldDecorator("description", {
    							rules: [
    								{
    									required: true,
    									message: indexListLang.indexCommitModal("descriptionCannotEmptyTip")
    								}
    							]
    						})(
    							<TextArea
    								placeholder={indexListLang.indexCommitModal("inputReleaseDes")} // lang:请输入发版描述
    								rows={4}
    							/>
    						)}
    					</Form.Item>
    				</Form>
    			</Modal>
    			<OffLineResult
    				title={indexListLang.batchRes("batchSubmitTitle")}
    				visible={batchRes}
    				data={batchResData}
    				onCancel={this.cancelZbRes}
    			/>
    		</Fragment>

    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor
}))(Form.create({ name: "batch-submit" })(BatchVersionSubmitModal));
