import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

// 免疫配置列表
const immuneList = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/ruleImmuno/list", params), {
		method: "GET",
		headers: getHeader()
	});
};
// 添加免疫配置
const addImmune = async(params) => {
	return request("/admin/ruleImmuno/add", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};
// 编辑免疫配置
const modifyImmune = async(params) => {
	return request("/admin/ruleImmuno/modify", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};
// 删除免疫配置
const removeImmune = async(params) => {
	return request("/admin/ruleImmuno/remove", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};

// 免疫历史
const immuneHistoryList = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/ruleImmuno/log/list", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 免疫详情
const immuneLogDetail = async(params) => {
	return request(getUrl("/admin/ruleImmuno/log/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};

export default {
	immuneList,
	addImmune,
	modifyImmune,
	removeImmune,
	immuneHistoryList,
	immuneLogDetail
};
