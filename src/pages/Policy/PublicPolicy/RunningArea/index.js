import React, { PureComponent, Fragment, Suspense } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Table, Spin, Pagination, Tooltip, message, Modal } from "antd";
import { commonLang, publicPolicyListLang } from "@/constants/lang";
import checkPermissionPublicPolicy from "@/constants/permission/checkPermissionPublicPolicy";
import { publicPolicyRunningAPI } from "@/services";
import Search from "../Common/Search";
import EffectScope from "@/components/PublicPolicy/EffectScope/EffectScope";
import ExportPublicPolicy from "../Common/ExportPublicPolicy";
import "./index.less";

const PublicPolicyDrawer = React.lazy(() => import("@/components/PublicPolicy/PublicPolicyDrawer"));
const QuoteInfo = React.lazy(() => import("./QuoteInfo"));
const { confirm } = Modal;
class RunningArea extends PureComponent {
	componentWillMount() {
		// 初始化
		this.closePolicyDetail(); // 关闭公共策略详情
		this.closeQuoteDetail(); // 关闭引用详情
	}
	componentDidMount() {
		this.timer = setInterval(() => {
			const { globalStore } = this.props;
			const { menuTreeReady } = globalStore;
			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkPermissionPublicPolicy.PubPolicyListOnline()) {
					this.search();
				}
			}
		}, 100);
	}
	// 初始化搜索查看
	search = () => {
		let { dispatch } = this.props;
		dispatch({
			type: "publicPolicyRunning/getPublicRunningPolicy"
		});
	};
	// 操作之后刷新操作
	operaRefresh = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyRunning/setAttrValue",
			payload: {
				curPage: 1
			}
		});
		this.search();
		dispatch({
			type: "global/getAllMap",
			payload: {}
		});
	};

	// 搜索折叠
	toggleSearch = () => {
		const { dispatch, publicPolicyRunningStore } = this.props;
		const { searchExpand } = publicPolicyRunningStore;
		dispatch({
			type: "publicPolicyRunning/setAttrValue",
			payload: {
				searchExpand: !searchExpand
			}
		});
	};

	// 查看公共策略详情
	publicPolicyDetail = (record) => {
		this.closeQuoteDetail();
		const { dispatch, publicPolicyRunningStore } = this.props;
		const { dialogData } = publicPolicyRunningStore || {};
		const { policyDrawer } = dialogData || {};
		if (policyDrawer.policyUuid && policyDrawer.policyUuid === record.policyUuid) {
			dispatch({
				type: "publicPolicyRunning/setDialogShow",
				payload: {
					policyDrawer: false
				}
			});
			dispatch({
				type: "publicPolicyRunning/setDialogData",
				payload: {
					policyDrawer: {
						policyDetail: {},
						policyUuid: null
					}
				}
			});
			return;
		}
		publicPolicyRunningAPI.publicVersionPolicyDetail({ policyUuid: record.policyUuid }).then(res => {
			if (res.success) {
				dispatch({
					type: "publicPolicyRunning/setDialogShow",
					payload: {
						policyDrawer: true
					}
				});
				dispatch({
					type: "publicPolicyRunning/setDialogData",
					payload: {
						policyDrawer: {
							policyDetail: res.data || {},
							policyUuid: record.policyUuid
						}
					}
				});
			} else {
				message.error(res.message || publicPolicyListLang.fetchError("policyDetail")); // 查看公共策略详情失败
			}

		}).catch(e => {
			message.error(e.message || publicPolicyListLang.fetchError("policyDetail")); // 查看公共策略详情失败
		});
	};
	// 关闭公共策略详情抽屉
	closePolicyDetail = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyRunning/setDialogShow",
			payload: {
				policyDrawer: false
			}
		});
		dispatch({
			type: "publicPolicyRunning/setDialogData",
			payload: {
				policyDrawer: {
					policyDetail: {},
					policyUuid: null
				}
			}
		});
	};
	// 下线公共策略
	offlinePolicy = (record) => {
		const params = {
			policyUuid: record.policyUuid
		};
		const { dispatch } = this.props;
		confirm({
			title: publicPolicyListLang.offLine("confirmOffLine"),	// lang:确认下线
			content: publicPolicyListLang.offLine("offPolicyDesc1") + record.policyName + publicPolicyListLang.offLine("offPolicyDesc2"),		// lang:您真的要下线《《发版策略》吗？
			onOk: () => {
				publicPolicyRunningAPI.offlinePublicPolicy(params).then(res => {
					if (res.success) {
						// 变更编辑区状态
						dispatch({
							type: "publicPolicyEditor/getPublicPolicy"
						});
						this.operaRefresh();
					} else {
						message.error(res.message);
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel() {
				console.log("Cancel");
			}
		});
	};

	// 查看引用
	quote = (record) => {
		this.closePolicyDetail();
		const { dispatch, publicPolicyRunningStore } = this.props;
		const { dialogData } = publicPolicyRunningStore || {};
		const { quoteDrawer } = dialogData || {};
		if (quoteDrawer.policyUuid && quoteDrawer.policyUuid === record.policyUuid) {
			dispatch({
				type: "publicPolicyRunning/setDialogShow",
				payload: {
					quoteDrawer: true
				}
			});
			dispatch({
				type: "publicPolicyRunning/setDialogData",
				payload: {
					quoteDrawer: {
						quoteDetail: {},
						policyUuid: null
					}
				}
			});
			return;
		}
		publicPolicyRunningAPI.flowRef({ policyUuid: record.policyUuid }).then(res => {
			if (res.success) {
				dispatch({
					type: "publicPolicyRunning/setDialogShow",
					payload: {
						quoteDrawer: true
					}
				});
				dispatch({
					type: "publicPolicyRunning/setDialogData",
					payload: {
						quoteDrawer: {
							quoteDetail: res.data ? {
								...res.data,
								...record
							} : {},
							policyUuid: record.policyUuid
						}
					}
				});
			} else {
				message.error(res.message || publicPolicyListLang.fetchError("quote")); //  查看引用失败
			}

		}).catch(e => {
			message.error(e.message || publicPolicyListLang.fetchError("quote"));//  查看引用失败
		});
	};
	// 关闭引用抽屉
	closeQuoteDetail = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyRunning/setDialogShow",
			payload: {
				quoteDrawer: false
			}
		});
		dispatch({
			type: "publicPolicyRunning/setDialogData",
			payload: {
				quoteDrawer: {
					quoteDetail: {},
					policyUuid: null
				}
			}
		});
	};

	// 修改查看参数
	changeSearchField = (name, e, type) => {
		const { dispatch } = this.props;
		let value = e;
		if (type === "input") {
			value = e.target.value;
		}
		dispatch({
			type: "publicPolicyRunning/setSearchData",
			payload: {
				[name]: value
			}
		});
	};
	conditionSearch = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyRunning/setAttrValue",
			payload: {
				curPage: 1
			}
		});
		this.search();
	}
	// 重置
	reset = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyRunning/setSearchData",
			payload: {
				policyName: "",
				riskType: "",
				eventIds: "",
				ruleName: "",
				ruleCustomId: ""
			}
		});
		dispatch({
			type: "publicPolicyRunning/setAttrValue",
			payload: {
				curPage: 1
			}
		});
	};
	// 翻页
	paginationOnChange = (current, pageSize) => {
		const { dispatch } = this.props;
		dispatch({
			type: "publicPolicyRunning/setAttrValue",
			payload: {
				curPage: current,
				pageSize: pageSize
			}
		});
		this.search();
	};

	// 点击公共策略名跳转链接
	goPublicPolicy = (item, e) => {
		e.stopPropagation();
		let { dispatch, location } = this.props;
		let pathname = location.pathname;
		let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
		let baseUrl = `/policy/versionPublicPolicyDetail/${item.policyUuid}?tabIndex=1&policyVersion=${item.policyVersion}`;
		dispatch(routerRedux.push(prefix + baseUrl));
	};

	// 表单内容
	columns = () => {
		return (
			[
				{
					title: publicPolicyListLang.table("publicPolicyName"), // 公共策略名称
					dataIndex: "policyName",
					key: "policyName",
					width: 240,
					ellipsis: true,
					render: (text, record) => {
						return (
							<Fragment>
								<Tooltip placement="left" title={text}>
									<a onClick={(e) => {
										this.goPublicPolicy(record, e);
									}}>{text}</a>
								</Tooltip>
							</Fragment>
						);
					}
				},
				{
					title: publicPolicyListLang.table("version"), // "版本号"
					dataIndex: "policyVersion",
					key: "policyVersion",
					render: (text) => {
						return `V${text}`;
					}
				},
				{
					title: publicPolicyListLang.table("effectScope"), // 生效范围
					dataIndex: "scene",
					key: "scene",
					width: 300,
					render: (text, record) => {
						return <EffectScope scene={record.scene && JSON.parse(record.scene)} />;
					}
				},
				{
					title: publicPolicyListLang.table("description"), // 描述
					dataIndex: "description",
					key: "description",
					render: (text)=>{
						return (
							<Tooltip placement="left" title={text}>
								<div className="line-num-2">{text}</div>
							</Tooltip>
						);
					}
				},
				{
					title: publicPolicyListLang.table("modifyTime"), // 修改时间
					width: 120,
					key: "gmtModified",
					dataIndex: "gmtModified"
				},
				{
					title: publicPolicyListLang.table("modifier"), // 修改人
					key: "updatedBy",
					dataIndex: "updatedBy"
				},
				{
					title: publicPolicyListLang.table("operation"), // 操作
					key: "action",
					width: 120,
					render: (text, record) => (
						<div className="table-action">
							{/* 导出 */}
							<ExportPublicPolicy
								record={record}
								isEditorArea={false}
							/>
							{/* 查看引用 */}
							<Tooltip title={publicPolicyListLang.tooltip("viewReference")} placement="top">
								<a onClick={(e) => {
									e.stopPropagation();
									if (checkPermissionPublicPolicy.PubPolicyFlowRef()) {
										this.quote(record);
									} else {
										// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}}>
									<i className={`iconfont icon-yinyong ${!checkPermissionPublicPolicy.PubPolicyFlowRef() ? "icon-disabled" : ""}`}></i>
								</a>
							</Tooltip>
							{/* 下线 */}
							<Tooltip title={publicPolicyListLang.tooltip("offline")} placement="top">
								<a onClick={(e) => {
									e.stopPropagation();
									if (checkPermissionPublicPolicy.OfflinePubPolicy()) {
										this.offlinePolicy(record);
									} else {
										// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}}>
									<i className={`salaxy-iconfont salaxy-offline ${!checkPermissionPublicPolicy.OfflinePubPolicy() ? "icon-disabled" : ""}`}></i>
								</a>
							</Tooltip>
						</div >
					)
				}
			]
		);
	};

	render() {
		let { publicPolicyRunningStore, globalStore } = this.props;
		let { menuTreeReady, allMap } = globalStore;
		let { publicPolicyListLoad, publicPolicyList = [], curPage, total, dialogData, dialogShow, searchData, searchExpand } = publicPolicyRunningStore;
		const { policyDrawer, quoteDrawer } = dialogData || {};
		return (
			<div className="page-global-body" >
				{
					menuTreeReady &&
					allMap &&
					Object.keys(allMap).length > 0 &&
					checkPermissionPublicPolicy.PubPolicyListOnline() &&
					<Fragment>
						<Search
							changeSearchField={this.changeSearchField}
							searchData={searchData}
							search={this.conditionSearch}
							reset={this.reset}
							toggle={this.toggleSearch}
							searchExpand={searchExpand}
							isRunning={true}
						/>

						<Spin spinning={publicPolicyListLoad}>
							<div className="page-global-body-main mt10">
								<Table
									columns={this.columns()}
									dataSource={publicPolicyList}
									pagination={false}
									rowKey={(e, ind) => ind}
									onRow={(record) => {
										return {
											onClick: (e) => {
												// 运行区公共策略详情权限
												if (checkPermissionPublicPolicy.OnlinePubPolicyDetail()) {
													this.publicPolicyDetail(record, e);
												} else {
													// lang:无权限操作
													message.warning(commonLang.messageInfo("noPermission"));
												}
											}
										};
									}}
								/>

								<div className="page-global-body-pagination">
									{/* lang:共x条记录 */}
									<span className="count">{commonLang.getRecords(total)}</span>
									<Pagination
										showSizeChanger
										onChange={this.paginationOnChange}
										onShowSizeChange={this.paginationOnChange}
										defaultCurrent={1}
										total={total}
										current={curPage}
									/>
								</div>
							</div>
						</Spin>
					</Fragment>
				}

				<Suspense fallback={null}>
					<PublicPolicyDrawer
						isRunning={true}
						location={location}
						visible={dialogShow.policyDrawer}
						policyDetail={policyDrawer.policyDetail}
						onClose={this.closePolicyDetail}
					/>
				</Suspense>
				<Suspense fallback={null}>
					<QuoteInfo
						isRunning={true}
						location={location}
						visible={dialogShow.quoteDrawer}
						policyDetail={quoteDrawer.quoteDetail}
						onClose={this.closeQuoteDetail}
					/>
				</Suspense>
			</div>
		);
	}
};
export default connect(state => ({
	globalStore: state.global,
	publicPolicyRunningStore: state.publicPolicyRunning,
	publicPolicyEditorStore: state.publicPolicyEditor
}))(RunningArea);
