import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Input, Form, message, Switch, Select } from "antd";
import { templateAPI } from "@/services";
import cloneDeep from "lodash.clonedeep";

const FormItem = Form.Item;
const { Option } = Select;

class StepOne extends PureComponent {
	constructor(props) {
		super(props);
		this.changeHandle = this.changeHandle.bind(this);
		this.modifyTemplate = this.modifyTemplate.bind(this);
	}

	changeHandle(field, type, e) {
		let { dispatch, templateStore } = this.props;
		let { modifyTemplateData } = templateStore.dialogData;
		let value = "";
		if (type === "input" || type === "textarea") {
			value = e.target.value;
		}
		if (type === "select") {
			value = e;
		}
		modifyTemplateData[field] = value;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	modifyTemplate() {
		let { templateStore, dispatch } = this.props;
		let { currentType, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		templateAPI.modifyTemplate(modifyTemplateData).then((res) => {
			if (res.success) {
				dispatch({
					type: "template/getTemplateList",
					payload: {
						type: currentType
					}
				});
				message.success("更新模板成功");
				dispatch({
					type: "template/setDialogShow",
					payload: {
						modifyTemplate: false
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { templateStore } = this.props;
		let { currentType, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let { advanceConfig = {} } = modifyTemplateData;
		let formItemLayout = {
			labelCol: { span: 4 },
			wrapperCol: { span: 20 }
		};

		return (
			<Form style={{ maxWidth: "650px", margin: "0 auto" }} className="step-one-form">
				<FormItem label="显示名称" {...formItemLayout}>
					<Input
						type="text"
						addonBefore="中文名称"
						placeholder="请输入显示名称"
						value={modifyTemplateData.displayName || undefined}
						onChange={this.changeHandle.bind(this, "displayName", "input")}
					/>
					<Input
						type="text"
						addonBefore="英文名称"
						placeholder="请输入显示名称"
						value={modifyTemplateData.enDisplayName || undefined}
						onChange={this.changeHandle.bind(this, "enDisplayName", "input")}
					/>
				</FormItem>
				<FormItem label="唯一标识" {...formItemLayout}>
					<Input
						type="text"
						placeholder="唯一标识请参考以前的"
						value={modifyTemplateData.name || undefined}
						onChange={this.changeHandle.bind(this, "name", "input")}
					/>
				</FormItem>
				<FormItem label="示例" {...formItemLayout}>
					<Input
						type="text"
						value={advanceConfig.demo || undefined}
						onChange={(e) => {
							let newAdvanceConfig = { ...advanceConfig };
							newAdvanceConfig["demo"] = e.target.value;
							this.changeHandle("advanceConfig", "select", newAdvanceConfig);
						}}
					/>
				</FormItem>
				<FormItem label="样例图片" {...formItemLayout}>
					<Input
						type="text"
						value={advanceConfig.demoPic || undefined}
						onChange={(e) => {
							let newAdvanceConfig = { ...advanceConfig };
							newAdvanceConfig["demoPic"] = e.target.value;
							this.changeHandle("advanceConfig", "select", newAdvanceConfig);
						}}
					/>
				</FormItem>
				<FormItem label="标签" {...formItemLayout}>
					<Select
						allowClear
						placeholder="请选择标签"
						value={(advanceConfig && advanceConfig.tag && advanceConfig.tag.value) ? advanceConfig.tag.value : undefined}
						onChange={(e, obj)=>{
							let newAdvanceConfig = { ...advanceConfig };
							const { name = "", value = "" } = (obj && obj.props) || {};
							newAdvanceConfig["tag"] = {
								name,
								value
							};
							this.changeHandle("advanceConfig", "select", newAdvanceConfig);
						}}
					>
						<Option value="credit" name="信贷">信贷</Option>
					</Select>
				</FormItem>
				<FormItem label="描述" {...formItemLayout}>
					<Input
						type="text"
						addonBefore="中文描述"
						placeholder="请输入模板中文描述"
						value={modifyTemplateData.description || undefined}
						onChange={this.changeHandle.bind(this, "description", "input")}
					/>
					<Input
						type="text"
						addonBefore="英文描述"
						placeholder="请输入模板英文描述"
						value={modifyTemplateData.enDescription || undefined}
						onChange={this.changeHandle.bind(this, "enDescription", "input")}
					/>
				</FormItem>
				<FormItem label="排序" {...formItemLayout}>
					<Input
						type="text"
						placeholder="请输入排序"
						value={modifyTemplateData.sort || undefined}
						onChange={this.changeHandle.bind(this, "sort", "input")}
					/>
				</FormItem>
				{
					currentType === 2 &&
					<Fragment>
						<FormItem label="没有场景选择" {...formItemLayout}>
							<Switch
								checkedChildren="没有场景"
								unCheckedChildren="有场景"
								checked={!!advanceConfig.noScene}
								onChange={(value) => {
									let newAdvanceConfig = { ...advanceConfig };
									newAdvanceConfig["noScene"] = value;
									this.changeHandle("advanceConfig", "select", newAdvanceConfig);
								}}
							/>
							{/* <span>默认是有过滤条件的</span>*/}
						</FormItem>
						<FormItem label="没有过滤条件" {...formItemLayout}>
							<Switch
								checkedChildren="没有过滤"
								unCheckedChildren="有过滤"
								checked={!!advanceConfig.noFilterFunction}
								onChange={(value) => {
									let newAdvanceConfig = { ...advanceConfig };
									newAdvanceConfig["noFilterFunction"] = value;
									this.changeHandle("advanceConfig", "select", newAdvanceConfig);
								}}
							/>
							{/* <span>默认是有过滤条件的</span>*/}
						</FormItem>
						<FormItem label="支持回溯计算" {...formItemLayout}>
							<Switch
								checkedChildren="支持"
								unCheckedChildren="不支持"
								checked={!!advanceConfig.supportOffline}
								onChange={(value) => {
									let newAdvanceConfig = { ...advanceConfig };
									newAdvanceConfig["supportOffline"] = value;
									this.changeHandle("advanceConfig", "select", newAdvanceConfig);
								}}
							/>
						</FormItem>
					</Fragment>
				}
			</Form>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(StepOne);
