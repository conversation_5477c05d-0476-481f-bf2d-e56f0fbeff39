import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Alert, Radio, message } from "antd";
import { policyDetailAPI } from "@/services";
import { policyDetailLang } from "@/constants/lang";
import Sortable from "react-sortablejs";
import "./Less/sort-rule-item.less";

class RuleSortModal extends PureComponent {

	constructor(props) {
		super(props);
		this.commitModal = this.commitModal.bind(this);
		this.closeModal = this.closeModal.bind(this);
		this.changeOrder = this.changeOrder.bind(this);
	}

	commitModal() {
		let { policyDetailStore, dispatch } = this.props;
		let { dialogData, policyDetail } = policyDetailStore;
		let { ruleSortData } = dialogData;
		let { ruleList } = ruleSortData;

		let rulesJson = [];
		ruleList.forEach((item, index) => {
			rulesJson.push({
				"uuid": item.uuid,
				"displayOrder": index + 1
			});
		});

		let params = {
			policyUuid: policyDetail.uuid,
			rulesJson: JSON.stringify(rulesJson, null, 4)
		};
		policyDetailAPI.changeRulesOrder(params).then(res => {
			if (res.success) {
				message.success("规则排序成功");
				// 刷新规则列表
				dispatch({
					type: "policyDetail/getPolicyRules",
					payload: {
						policyUuid: policyDetail.uuid
					}
				});
				this.closeModal();
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	closeModal() {
		let { policyDetailStore, dispatch } = this.props;
		let { dialogData } = policyDetailStore;
		let { ruleSortData } = dialogData;
		ruleSortData["ruleList"] = [];

		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				ruleSort: false
			}
		});

		dispatch({
			type: "policyDetail/setDialogData",
			payload: {
				ruleSortData: ruleSortData
			}
		});
	}

	changeOrder(order) {
		let { policyDetailStore, dispatch } = this.props;
		let { dialogData } = policyDetailStore;
		let { ruleSortData } = dialogData;
		let { ruleList } = ruleSortData;
		let newRuleList = [];

		order.forEach((number) => {
			newRuleList.push(ruleList[number]);
		});
		ruleSortData["ruleList"] = newRuleList;

		dispatch({
			type: "policyDetail/setDialogData",
			payload: {
				ruleSortData: ruleSortData
			}
		});
	}

	render() {
		let { policyDetailStore } = this.props;
		let { dialogShow, dialogData } = policyDetailStore;
		let { ruleSort } = dialogShow;
		let { ruleSortData } = dialogData;
		let { ruleList } = ruleSortData;

		return (
			<Modal
				title={policyDetailLang.ruleSortModal("title")} // 规则排序
				visible={ruleSort}
				maskClosable={true}
				onOk={this.commitModal.bind(this)}
				onCancel={this.closeModal.bind(this)}
				width={750}
			>
				<Alert
					message={policyDetailLang.ruleSortModal("alert")}
					type="info"
					showIcon
					style={{ marginBottom: "20px" }}
				/>
				<Sortable
					options={{
						animation: 150,
						ghostClass: "blue-background-class"
					}}
					ref={(c) => {
						if (c) {
							// sortable = c.sortable;
						}
					}}
					tag="div"
					onChange={(order, sortable, evt) => {
						// onChange(order);
						this.changeOrder(order);
					}}
				>
					{
						ruleList &&
                        ruleList.map((item, index) => {
                        	return (
                        		<li
                        			key={index}
                        			data-id={index}
                        			className="sort-rule-item"
                        		>
                        			<div className="sort-rule-name">
                        				{item.name}
                        			</div>
                        			<div className="right-info">
                        				<div className="sort-rule-status">
                        					<Radio.Group
                        						value={item.unSave ? undefined : item.valid.toString()}
                        						buttonStyle="solid"
                        						size="small"
                        						disabled={true}
                        					>
                        						<Radio.Button value="0">
                        							{/* 关闭 */}
                        							{policyDetailLang.ruleList("close")}
                        						</Radio.Button>
                        						<Radio.Button value="2">
                        							{/* 模拟 */}
                        							{policyDetailLang.ruleList("simulation")}
                        						</Radio.Button>
                        						<Radio.Button value="1">
                        							{/* 正式 */}
                        							{policyDetailLang.ruleList("formal")}
                        						</Radio.Button>
                        					</Radio.Group>
                        				</div>
                        				<div className="sort-rule-id">
                        					{item.id}
                        				</div>
                        			</div>
                        		</li>
                        	);
                        })
					}
				</Sortable>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(RuleSortModal);
