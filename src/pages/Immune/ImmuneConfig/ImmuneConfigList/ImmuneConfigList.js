import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Table, Pagination, Modal, message, Spin, Tooltip } from "antd";
import { checkFunctionHasPermission } from "@/utils/permission";
import { immuneConfigLang, commonLang } from "@/constants/lang";
import { ImmuneConstants } from "@/constants";
import { immuneAPI } from "@/services";
import ViewImmuneBtn from "../../Common/ViewImmuneBtn";

const confirm = Modal.confirm;

class ImmuneConfigList extends PureComponent {
	getColumns = () => {
		return (
			[
				{
					title: immuneConfigLang.table("ruleName"), // lang:"规则名称",
					dataIndex: "ruleName",
					key: "ruleName",
					width: 200,
					render: (text)=>{
						return (
							<Tooltip title={text}>
								<span
									className="text-overflow"
									style={{"maxWidth": "200px", "display": "inline-block"}}
								>{text}</span>
							</Tooltip>
						);
					}
				},
				{
					title: immuneConfigLang.table("policyType"), // lang:"策略类型",
					dataIndex: "policyType",
					key: "policyType",
					render: (text)=>{
						return (
							ImmuneConstants.getPolicyType().find(v=>{
								return v.value === text;
							}) || {}
						).name || "";
					}
				},
				{
					title: immuneConfigLang.table("policyOrSetName"), // lang:"策略(集)名称",
					dataIndex: "policyOrSetName",
					key: "policyOrSetName",
					render: (text)=>{
						if (text && text.length > 6) {
							text = <Tooltip title={text}>{text.slice(0, 6) + "..."}</Tooltip>;
						}
						return text;
					}
				},
				{
					title: immuneConfigLang.table("createType"), // lang:"创建类型",
					dataIndex: "createType",
					key: "createType",
					render: (text) => {
						return (
							ImmuneConstants.getCreateType().find(v=>{
								return v.value === text;
							}) || {}
						).name || "";
					}
				},
				{
					title: immuneConfigLang.table("createPerson"), // lang:"创建人",
					dataIndex: "createdBy",
					key: "createdBy"
				},
				{
					title: immuneConfigLang.table("failureTime"), // lang:"失效时间",
					dataIndex: "effectTo",
					key: "effectTo"
				},
				{
					title: immuneConfigLang.table("remarks"), // lang:"备注",
					dataIndex: "remarks",
					key: "remarks",
					render: (text)=>{
						if (text && text.length > 6) {
							text = <Tooltip title={text}>{text.slice(0, 6) + "..."}</Tooltip>;
						}
						return text;
					}
				},
				{
					title: immuneConfigLang.table("operation"), // lang: "操作",
					key: "operation",
					width: 120,
					render: (record, index) => {
						return (
							<div className="table-action" key={index}>
								{/* lang:编辑免疫配置 */}
								<Tooltip title={immuneConfigLang.tooltip("editImmune")}>
									<span>
										<ViewImmuneBtn
											openType="edit"
											record={record}
											permission={checkFunctionHasPermission("ZB0106", "RuleImmunoModify")}
											noRightTip={commonLang.messageInfo("noPermission")}
										>
											<i className="iconfont icon-edit"></i>
										</ViewImmuneBtn>
									</span>
								</Tooltip>

								{/* lang:查看免疫配置 */}
								<Tooltip title={immuneConfigLang.tooltip("viewImmune")}>
									<span>
										<ViewImmuneBtn
											openType="view"
											record={record}
											permission={checkFunctionHasPermission("ZB0106", "RuleImmunoDetail")}
											noRightTip={commonLang.messageInfo("noPermission")}
										>
											<i className="salaxy-iconfont salaxy-view1"></i>
										</ViewImmuneBtn>
									</span>
								</Tooltip>

								{/* lang:删除免疫配置 */}
								<Tooltip title={immuneConfigLang.tooltip("delImmune")}>
									<a onClick={(e) => {
										e.stopPropagation();
										if (checkFunctionHasPermission("ZB0106", "RuleImmunoRemove")) {
											this.deleteImmuneConfig(record);
										} else {
										// lang:无权限操作
											message.warning(commonLang.messageInfo("noPermission"));
										}
									}}>
										<i className="iconfont icon-delete delete"></i>
									</a>
								</Tooltip>
							</div>
						);
					}
				}
			]
		);
	}
	// 删除免疫配置
	deleteImmuneConfig = (item) => {
		const { refresh, refreshCallBack } = this.props;
		const params = {
			ruleImmunoUuid: item.uuid
		};
		confirm({
			title: immuneConfigLang.table("delMsgTitle"), // "删除免疫配置提醒"
			content: immuneConfigLang.table("delMsgContent"), // "您真的要删除免疫配置吗？"
			onOk() {
				immuneAPI.removeImmune(params).then(res => {
					if (res.success) {
						message.success(res.message);
						refresh();
						refreshCallBack && refreshCallBack();
					} else {
						message.error(res.message);
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel() {}
		});
	}
	render() {
		const {immuneConfigListStore = {}, paginationOnChange } = this.props;
		const { immuneConfigListLoad, immuneConfigList, total, curPage, pageSize } = immuneConfigListStore;
		const columns = this.getColumns();
		return (
			<Spin spinning={immuneConfigListLoad}>
				<div className="page-global-body-main has-table-border mt10">
					<Table
						columns={columns}
						pagination={false}
						dataSource={immuneConfigList}
						rowKey="uuid"
					/>
					<div className="page-global-body-pagination">
						{/* lang:共x条记录 */}
						<span className="count">{commonLang.getRecords(total)}</span>
						<Pagination
							showSizeChanger
							onChange={paginationOnChange}
							onShowSizeChange={paginationOnChange}
							defaultCurrent={1}
							total={total}
							current={curPage}
							pageSize={pageSize}
						/>
					</div>
				</div>
			</Spin>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	immuneConfigListStore: state.immuneConfigList
}))(ImmuneConfigList);

