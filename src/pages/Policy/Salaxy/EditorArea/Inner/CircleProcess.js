import { PureComponent, Fragment } from "react";
import { Alert } from "antd";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import "./CircleProcess.less";

export default connect()(class CircleProcess extends PureComponent {
	state = {
		barProcess: 439.82,
		pct: 0
	}
	componentDidMount() {
		const { percent } = this.props;
		const r = 70;
		const c = Math.PI * (r * 2);
		const barProcess = ((100 - percent) / 100) * c;
		this.setState({
			barProcess: barProcess,
			pct: percent
		});
	}
	toDeal = (e) => {
		e.stopPropagation();
		const { dispatch, location, uuid, zbName } = this.props;
		const { pathname } = location;
		const prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
		const baseUrl = `/policy/reCallTask?zbUuid=${uuid}`;
		dispatch(routerRedux.push(prefix + baseUrl));
	}
	render() {
		const { barProcess, pct } = this.state;
		const { lang, fillBackResultStatus } = this.props;
		const hasCompleted = String(pct) === "100" && fillBackResultStatus === "PEND";
		return (
			<Fragment>
				{
					hasCompleted &&
					<Alert
						message={
							<Fragment>
								{ lang === "cn" ? "待生效" : "To effect" }
								<a
									className="ant-btn-link ml5"
									onClick={this.toDeal}
								>

									{ lang === "cn" ? "立即处理" : "Deal" }
								</a>
							</Fragment>
						}
						type="warning"
						showIcon
						className="to-effect"
					/>
				}
				<div className="cont-wrap" data-pct={pct || 0}>
					<svg className="circle-process" width="160" height="160" viewPort="0 0 80 80" version="1.1" xmlns="http://www.w3.org/2000/svg">
						<circle r="70" cx="80" cy="80" fill="transparent" stroke-dasharray="439.82" stroke-dashoffset="0">
						</circle>
						<circle className={`bar ${String(pct) === "100" ? "success" : ""}`} r="70" cx="80" cy="80" fill="transparent" stroke-dasharray="439.82"
							stroke-dashoffset="439.82px" style={{"stroke-dashoffset": barProcess + "px"}}>
						</circle>
					</svg>
					<div className="txt">
						{ lang === "cn" ? "已完成" : "Completed" }
					</div>
				</div>
			</Fragment>
		);
	}
});

