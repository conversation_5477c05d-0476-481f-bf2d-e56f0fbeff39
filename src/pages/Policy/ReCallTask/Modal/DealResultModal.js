import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { message, Button, Modal, Input } from "antd";
import { reCallTaskAPI } from "@/services";
import { commonLang, reCallTaskLang } from "@/constants/lang";

const { TextArea } = Input;
class DealResultModal extends PureComponent {
	state={
		description: "",
		postLoad: false
	}
	confirm = () => {
		const { initSearch, reCallTaskStore } = this.props;
		const { dialogData } = reCallTaskStore;
		const { dealResultData } = dialogData || {};
		const {
			resultStatus,
			currentReCall
		} = dealResultData || {};

		const { description } = this.state;

		const params = {
			zbFillBackUuid: currentReCall.uuid,
			resultStatus
		};
		if (resultStatus === "APPLIED" && !description) {
			message.warning((reCallTaskLang.table("effectReson"))); // 请输入生效描述
			return;
		}
		if (resultStatus === "APPLIED") {
			params.description = description;
		}
		this.setState({postLoad: true}, ()=>{
			reCallTaskAPI.dealProcessResult(params).then(res => {
				this.setState({
					postLoad: false
				});
				if (res.success) {
					message.success(res.message || reCallTaskLang.table("backResultSuc")); // 回溯结果处理成功
					this.onCancel();
					initSearch();
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				this.setState({
					postLoad: false
				});
				console.log(err);
			});
		});

	}
	onCancel = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "reCallTask/setDialogShow",
			payload: {
				dealResultModal: false
			}
		});
		dispatch({
			type: "reCallTask/setDialogData",
			payload: {
				dealResultData: {
					resultStatus: "",
					currentReCall: {}
				}
			}
		});
		this.setState({
			description: ""
		});
	}
	render() {
		const { dialogData, dialogShow } = this.props.reCallTaskStore;
		const { dealResultData } = dialogData || {};
		const { dealResultModal } = dialogShow || {};
		const {
			resultStatus,
			currentReCall
		} = dealResultData || {};
		const { postLoad } = this.state;
		let footer = [
			<Button onClick={this.confirm} loading={postLoad} type="primary">
				{/* 确定 */}
				{commonLang.base("ok")}
			</Button>
		];
		if (resultStatus === "APPLIED") {
			footer = [
				<div className="fl" style={{"color": "#f5222d"}}>
					{/* 回溯任务生效，系统自动提交指标版本，请确认！ */}
					{reCallTaskLang.table("effectWarn")}
				</div>
			].concat(footer);
		}

		const ModalDetail = resultStatus === "APPLIED"
			? (<div>
				<div className="mb10">
					{/* 指标： */}
					{reCallTaskLang.table("zbIndex")}
					{currentReCall.zbName}
				</div>
				<TextArea
					placeholder={reCallTaskLang.table("effectReson")}
					value={this.state.description}
					onChange={(e)=>{
						this.setState({
							description: e.target.value
						});
					}}
				/>
			</div>)
			: (reCallTaskLang.table("zbIndex") + currentReCall.zbName);

		return (
			<Modal
				visible={dealResultModal}
				footer = {footer}
				maskClosable={false}
				onCancel={this.onCancel}
				title= {
					resultStatus === "APPLIED"
						? reCallTaskLang.table("effectTitle")
						: reCallTaskLang.table("discardTitle")
				}
			>
				<Fragment>
					{
						currentReCall.zbName ? ModalDetail : null
					}
				</Fragment>
			</Modal>
		);
	}
}
export default connect(state => ({
	reCallTaskStore: state.reCallTask
}))(DealResultModal);
