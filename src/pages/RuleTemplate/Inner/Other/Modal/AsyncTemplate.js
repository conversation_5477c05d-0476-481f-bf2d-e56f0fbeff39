import {Component} from "react";
import {connect} from "dva";
import {Modal, TreeSelect, Select, message, Alert, Icon, Row, Col } from "antd";
import "./AsyncTemplate.less";
import { templateAPI } from "@/services";
import cloneDeep from "lodash/cloneDeep";

const { Option } = Select;
class ModifySalaxyModal extends Component {
    state = {
    	ids: [],
    	changedDataTemplate: [],
    	postResult: [],
    	asyncType: [],
    	isPosting: false
    }
    onCancel = () => {
    	const { dispatch, templateStore } = this.props;
    	dispatch({
    		type: "template/setDialogShow",
    		payload: {
    			asyncTemplate: false
    		}
    	});
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			asyncTemplateData: {
    				templateList: [],
    				labelText: ""
    			}
    		}
    	});
    	this.setState({
    		ids: [],
    	    changedDataTemplate: [],
    		postResult: [],
    		asyncType: [],
    		isPosting: false
    	});
    	let { currentType } = templateStore;
    	dispatch({
    		type: "template/getTemplateList",
    		payload: {
    			type: currentType
    		}
    	});
    }
    changeTemplate = (ids) => {
    	this.setState({
    		ids,
    		changedDataTemplate: [],
    		postResult: [],
    		isPosting: false
    	});
    }
    startPost = () => {
    	let changedDataTemplate = [...this.state.changedDataTemplate];
    	let postResult = [...this.state.postResult];
    	if (changedDataTemplate.length > 0) {
    		const paramData = changedDataTemplate[0];
    		delete paramData.gmtCreate;
    		delete paramData.gmtModified;
    		templateAPI.modifyTemplate({
    			...paramData,
    			advanceConfig: JSON.stringify(paramData["advanceConfig"])
    		}).then(data=>{
    			changedDataTemplate.shift();
    			if (!(data && data.success)) {
    				postResult.push({
    					...paramData,
    					status: "error"
    				});
    			}
    			this.setState({
    				changedDataTemplate,
    				postResult
    			}, ()=>{
    				this.startPost();
    			});

    		}).catch((e)=>{
    			console.log(e);
    			changedDataTemplate.shift();
    			postResult.push({
    				...paramData,
    				status: "error"
    			});
    			this.setState({
    				changedDataTemplate,
    				postResult
    			});
    		});
    	} else {
    		message.success("请求完毕");
    	}
    }
    getParam = (targetChildren) =>{
    	const { asyncType = [] } = this.state;
    	let {templateStore} = this.props;
    	let {dialogData} = templateStore;
    	let { modifyTemplateData, asyncTemplateData } = dialogData;
    	const { labelText } = asyncTemplateData;
    	const { cfgJson = {} } = modifyTemplateData;
    	const { params } = cfgJson || {};
    	const { children = [] } = (params.find(v=>(v.labelText === labelText)) || {});
    	const newTargetChildren = targetChildren.map(target=>{
    		if (asyncType.indexOf(target.name) > -1) {
    			const childItem = children.find(source=>{
    				return source.name === target.name;
    			});
    			target = childItem;
    		}
    		return target;
    	});
    	console.log(newTargetChildren);
    	return newTargetChildren;
    }
    submit = () => {
    	const { ids, asyncType = [] } = this.state;
    	if (!(asyncType && asyncType.length > 0)) {
    		message.warning("选择想要同步的模块");
    		return;
    	}
    	if (!(ids && ids.length > 0)) {
    		message.warning("请选择需要同步的模版");
    		return;
    	}
    	let {templateStore} = this.props;
    	let {dialogData} = templateStore;
    	let { asyncTemplateData } = dialogData;
    	const { labelText, templateList = [] } = asyncTemplateData || {};

    	const changeTemplate = [];
    	templateList.forEach(template=>{
    		const { templateDataList = [] } = template || {};
    		if (templateDataList && templateDataList.length > 0) {
    			templateDataList.forEach(v=>{
    				if (ids.indexOf(v.id) > -1) {
    					changeTemplate.push(v);
    				}
    			});
    		}
    	});

    	if (changeTemplate && changeTemplate.length > 0) {
    		const changedDataTemplate = changeTemplate.map(v=>{
    			let { cfgJson } = v || {};
    			if (cfgJson) {
    				cfgJson = JSON.parse(cfgJson);
    				let {params = []} = cfgJson || {};
    				const newParams = params.map(param=>{
    					const { labelText: targetLabelText, children: targetChildren } = param || {};
    					if (labelText === targetLabelText) {
    						console.log("zzf");
    						console.log(this.getParam(targetChildren));
    						param.children = this.getParam(targetChildren);
    					}
    					return param;
    				});
    				cfgJson.params = newParams;
    				v.cfgJson = JSON.stringify(cfgJson);
    			}
    			return v;
    		});
    		console.log(changedDataTemplate);
    		this.setState({
    			changedDataTemplate,
    			isPosting: true,
    			postResult: []
    		}, ()=>{
    			this.startPost();
    		});
    	}
    }
    transferTree = (templateList) => {
    	const treeTemplateList = [];
    	templateList.map(v=>{
    		let { templateDataList } = v;
    		templateDataList = templateDataList.map((template)=>{
    			return {
    				...template,
    				title: template.displayName,
    				value: template.id,
    				key: template.id
    			};
    		});
    		if (templateDataList.length > 0) {
    			treeTemplateList.push({
    				title: v.groupDisplayName,
    				value: v.groupId,
    				key: v.groupId,
    				children: templateDataList
    			});
    		}
    	});
    	return treeTemplateList;
    }
    render() {
    	const {templateStore} = this.props;
    	const {dialogShow, dialogData} = templateStore;
    	const {asyncTemplateData = {}, modifyTemplateData = {}} = dialogData;
    	const {asyncTemplate} = dialogShow;
    	const { templateList, labelText } = asyncTemplateData || {};
    	const { changedDataTemplate, isPosting, postResult, ids, asyncType } = this.state;
    	const statusList = [].concat(changedDataTemplate || []).concat(postResult || []);
    	const hasPostStatus = isPosting && statusList.length > 0;
    	const treeTemplateList = this.transferTree(cloneDeep(templateList));
    	const { cfgJson = {} } = modifyTemplateData || {};
    	const { params = [] } = cfgJson || {};
    	const {children = []} = (params.find(v=>{
    		return v.labelText === labelText;
    	}) || {});
    	console.log(children);
    	return (
    		<Modal
    			title="同步其他模版"
    			width={750}
    			visible={asyncTemplate}
    			onCancel={this.onCancel}
    			onOk={this.submit.bind(this)}
    		>
    			<div className="mb10">选择想要同步的模块：</div>
    			<Select
    				style={{"width": "100%", "marginBottom": "10px"}}
    				placeholder="选择想要同步的模块"
    				mode="multiple"
    				value={asyncType || []}
    				onChange={(asyncType)=>{
    					console.log(asyncType);
    					this.setState({asyncType});
    				}}
    			>
    				{
    					children &&
                        children.length > 0 &&
                        children.map(v=>{
                        	return (
                        		<Option value={v.name} key={v.name}>{v.name}</Option>
                        	);
                        })
    				}
    			</Select>
    			<div className="mb10">选择需同步的模版：</div>
    			<TreeSelect
    				treeData={treeTemplateList || []}
    				allowClear
    				value={ids || []}
    				placeholder="选择需同步的模版"
    				treeCheckable={true}
    				style={{"width": "100%"}}
    				onChange={this.changeTemplate.bind(this)}
    			/>
    			<div className={hasPostStatus ? "mt10 template-async-status" : ""}>
    				{
    					hasPostStatus &&
                        statusList.map(v=>{
                        	return (
                        		<Alert
                        			message={
                        				<Row type="flex" justify="space-between">
                        					<Col>{v.displayName}</Col>
                        					<Col>
                        						{
                        							!v.status && <Icon type="sync" spin />
                        						}
                        						{
                        							v.status === "success" && <Icon type="check-circle" theme="twoTone" twoToneColor="#52c41a" />
                        						}
                        						{
                        							v.status === "error" && <Icon type="close-circle" theme="twoTone" twoToneColor="#f44336"/>
                        						}
                        					</Col>
                        				</Row>
                        			}
                        			type="info"
                        		/>
                        	);
                        })
    				}
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	templateStore: state.template
}))(ModifySalaxyModal);
