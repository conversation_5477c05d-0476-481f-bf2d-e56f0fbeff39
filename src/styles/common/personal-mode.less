@import "../base/variables";

:global {
	.personal-mode-wrap {
		.ant-popover-inner-content {
			padding: 0;
		}
	}
	.personal-mode {
		& {
			z-index: 1;
			padding: 20px 20px 20px 24px;
			margin-left: -80px;
			min-width: 235px;
			background-color: #fff;
			border-radius: 4px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, .1);
			filter: drop-shadow(0 0 8px rgba(0, 0, 0, .1));
			-webkit-filter: drop-shadow(0 0 8px rgba(0, 0, 0, .1));
		}
		.theme-select-handle {
			& {

			}
			.theme-select-item {
				& {
					position: relative;
					margin-right: 16px;
					border-radius: 4px;
					cursor: pointer;
					width: 33.3333%;
					display: inline-block;
				}
				img {
					width: 48px;
				}
				.select-icon {
					& {
						position: absolute;
						top: 0;
						right: 0;
						width: 100%;
						padding-top: 15px;
						padding-left: 24px;
						height: 100%;
						color: #1890ff;
						font-size: 14px;
						font-weight: 700;
					}
				}
			}
		}
		.line {
			margin: 20px -20px;
			position: relative;
			width: auto;
			height: 1px;
			background: #f0f0f0;
			clear: both;
			display: block;
		}
		.day-night-group {
			width: auto;
			vertical-align: middle;
			display: inline-block;
		}
		.switch {
			& {
				font-size: 0;
				letter-spacing: -4px;
			}
			.switch-btn {
				& {
					width: 49%;
					text-align: center;
					font-size: 14px;
					letter-spacing: 0 !important;
					color: #666;
					border: 1px solid #e5e5e5;
					vertical-align: middle;
					display: inline-block;
				}
				&.active {
					color: #fff;
					background-color: @blue2;
					border-color: @blue2;
					box-shadow: inset 0 0 6px rgba(0, 0, 0, .1);
				}
				&:first-child {
					padding: 10px 13px 10px 17px;
					border-radius: 20px 0 0 20px;
					border-right: none;
				}
				&:last-child {
					padding: 10px 17px 10px 13px;
					border-radius: 0 20px 20px 0;
					border-left: none;
				}
			}
			&.day-night-group {
				width: auto;
				vertical-align: middle;
				display: inline-block;
			}
			&.font-family-group {
				margin-bottom: 10px;
			}
			&.layout-style-group {
			}
		}
	}
}
