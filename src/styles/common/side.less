@import "../base/variables";

:global {
	.basic-layout {
		& {
			height: 100vh;
		}

		.logo-shadow-mask {
			width:10px;
			height:@header-height;
			position: absolute;
			top:0;
			right:0;
			background: @headerColor1;
			z-index: 102;
		}

		.logo {
			& {
				position: absolute;
				z-index: 10;
				width: @left-width-base;
				height: @header-height;
				line-height: @header-height;
				padding-left: 24px;
				cursor: pointer;
				box-shadow: 0 1px 8px rgba(0, 0, 0, .3);
				transition: box-shadow 200ms cubic-bezier(0.4, 0, 0.2, 1);
				background-color: @headerColor1;
				overflow: hidden;
			}

			&.collapsed {
				& {
					width: 100%;
					padding-left: 0;
				}

				.logo-icon {
					width: 100%;
					text-align: center;
				}
			}

			.logo-icon {
				& {
					height: @header-height;
					float: left;
				}

				i {
					font-size: 28px;
					color: #fff;
				}
			}

			img {
				width: 28px;
				height: 28px;
			}

			span {
				width: 144px;
				height: 60px;
				overflow: hidden;
				margin-left: 12px;
				vertical-align: text-bottom;
				font-size: 22px;
				display: inline-block;
				font-weight: 400;
				color: #fff;
				text-rendering: optimizeLegibility;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
			}
		}

		.ant-menu.ant-menu-root {
			& {
				position: relative;
				height: ~"calc(100% - 60px)";
				top: @header-height;
				-webkit-transition: none !important;
				transition: none !important;
				padding-top: 10px;
			}

			.ant-menu-submenu {
				& {
					-webkit-transition: none !important;
					transition: none !important;
					background: none !important;
				}

				.ant-menu-submenu-open.ant-menu-submenu-selected {
					transform: none !important;
				}

				.ant-menu-item.ant-menu-item-selected {
					transform: none !important;
				}
			}
		}
	}
}
