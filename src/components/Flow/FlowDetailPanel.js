import { PureComponent } from "react";
import { connect } from "dva";
import { DetailPanel, NodePanel, EdgePanel, CanvasPanel } from "gg-editor";
import NodeDetail from "./NodeDetail";
import EdgeDetail from "./EdgeDetail";
import { Card, Select, Form, Input, Row, Col } from "antd";
import "./style/detail.less";
import { workflowLang } from "@/constants/lang";

const { Item } = Form;
const TextArea = Input.TextArea;
const Option = Select.Option;

class FlowDetailPanel extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { globalStore, workflowStore, disabled } = this.props;
		let { allMap } = globalStore;
		let { policySetItem } = workflowStore;

		return (
			<DetailPanel className="flow-detail-panel">
				<NodePanel className="flow-detail-main">
					<NodeDetail disabled={disabled} />
				</NodePanel>
				<EdgePanel className="flow-detail-main">
					<EdgeDetail disabled={disabled} />
				</EdgePanel>
				<CanvasPanel className="flow-detail-main">
					<Card
						type="inner"
						title={workflowLang.flowDetailPanel("title")} // lang:"策略集信息"
						bordered="false"
					>
						<div className="param-list">
							<Row className="param-item">
								<Col span={24} className="param-title">
									{/* lang:策略集名称 */}
									{workflowLang.flowDetailPanel("policySetName")}
								</Col>
								<Col span={24} className="param-content">
									<Input
										value={policySetItem && policySetItem.name ? policySetItem.name : undefined}
										placeholder={workflowLang.flowDetailPanel("noPolicyName")} // lang:"暂无策略名称"
										disabled={true}
									/>
								</Col>
							</Row>
							<Row className="param-item">
								<Col span={24} className="param-title">
									{/* lang:所属应用 */}
									{workflowLang.flowDetailPanel("application")}
								</Col>
								<Col span={24} className="param-content">
									<Select
										placeholder={workflowLang.flowDetailPanel("application")} // lang:所属应用
										value={policySetItem && policySetItem.appName ? policySetItem.appName : undefined}
										disabled={true}
									>
										{
											allMap &&
                                            allMap["appNames"] &&
                                            allMap["appNames"].map((item, index) => {
                                            	return (
                                            		<Option
                                            			value={item.name}
                                            			key={index}
                                            		>
                                            			{item.dName}
                                            		</Option>
                                            	);
                                            })
										}
									</Select>
								</Col>
							</Row>
							<Row className="param-item">
								<Col span={24} className="param-title">
									{/* lang:事件类型 */}
									{workflowLang.flowDetailPanel("eventType")}
								</Col>
								<Col span={24} className="param-content">
									<Select
										placeholder={workflowLang.flowDetailPanel("eventType")} // lang:事件类型
										value={policySetItem && policySetItem.eventType ? policySetItem.eventType : undefined}
										disabled={true}
									>
										{
											allMap &&
                                            allMap["EventType"] &&
                                            allMap["EventType"].map((item, index) => {
                                            	return (
                                            		<Option
                                            			value={item.name}
                                            			key={index}
                                            		>
                                            			{item.dName}
                                            		</Option>
                                            	);
                                            })
										}
									</Select>
								</Col>
							</Row>
							<Row className="param-item">
								<Col span={24} className="param-title">
									{/* lang:事件标识 */}
									{workflowLang.flowDetailPanel("eventId")}
								</Col>
								<Col span={24} className="param-content">
									<Input
										value={policySetItem && policySetItem.eventId ? policySetItem.eventId : undefined}
										placeholder={workflowLang.flowDetailPanel("noEventId")} // lang:"暂无事件标识"
										disabled={true}
									/>
								</Col>
							</Row>
							<Row className="param-item">
								<Col span={24} className="param-title">
									{/* lang:策略描述 */}
									{workflowLang.flowDetailPanel("policyDescription")}
								</Col>
								<Col span={24} className="param-content">
									<TextArea
										value={policySetItem && policySetItem.description ? policySetItem.description : undefined}
										placeholder={workflowLang.flowDetailPanel("noPolicyDescription")} // lang:"暂无策略描述"
										rows={4}
										disabled={true}
									/>
								</Col>
							</Row>
						</div>
					</Card>
				</CanvasPanel>
			</DetailPanel>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	workflowStore: state.workflow
}))(FlowDetailPanel);

