import { approvalTaskLang } from "./lang";

const operationTypeMap = {
	"DEL": ()=>({
		text: approvalTaskLang.table("delete"),		// lang:删除
		color: "red"
	}),
	"PUBLISH": ()=>({
		text: approvalTaskLang.table("publish"),		// lang:发布
		color: "blue"
	}),
	"OFFLINE": ()=>({
		text: approvalTaskLang.table("offline"),		// lang:下线
		color: ""
	}),
	"APPLY_FILL_BACK": ()=>({
		text: approvalTaskLang.table("applyFillBack"),		// lang: 回溯发布
		color: "blue"
	})
};

export default {
	operationTypeMap
};
