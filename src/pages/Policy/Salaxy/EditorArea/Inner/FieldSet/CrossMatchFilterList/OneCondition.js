import React from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Row, Col } from "antd";
import { PolicyConstants, CommonConstants } from "@/constants";
import { commonLang, policyDetailLang, indexListLang } from "@/constants/lang";


const Option = Select.Option;
const InputGroup = Input.Group;
let addressSetOperator = [
	{
		name: "similarityMatch",
		dName: "匹配",
		enDName: "Match"
	},
	{
		name: "similarityUnMatch",
		dName: "不匹配",
		enDName: "UnMatch"
	}
];

let commonSetOperator = [
	{
		name: "match",
		dName: "匹配",
		enDName: "match"
	},
	{
		name: "unmatch",
		dName: "不匹配",
		enDName: "unmatch"
	}
];
class OneCondition extends React.PureComponent {

	constructor(props) {
		super(props);
	}

	addCondition = () => {
		let { onChange, filterObj, conditionArr } = this.props;

		let singleConditionTemp = {
			"fieldType": "systemField",
			"leftPropertyName": null,
			"operator": "==",
			"rightValue": null,
			"rightValueType": "input",
			"type": "STRING",
			"similarity": null
		};

		filterObj.children[conditionArr[0]]["children"].push(singleConditionTemp);
		onChange(filterObj);
	}

	deleteCondition = () => {
		let { filterObj, onChange, conditionArr } = this.props;
		let currentGroup = filterObj.children[conditionArr[0]];

		if (currentGroup && currentGroup.children && currentGroup.children.length > 1) {
			currentGroup["children"].splice(conditionArr[1], 1);
		} else {
			filterObj.children.splice(conditionArr[0], 1);
		}
		onChange(filterObj);
	}

	changeLogicOperator = (value) => {
		let { conditionArr, filterObj, onChange } = this.props;

		filterObj.children[conditionArr[0]]["logicOperator"] = value;
		onChange(filterObj);
	}

	changeConditionField = (field, type, e) => {
		let { globalStore, conditionType, conditionArr, filterObj, onChange } = this.props;
		let { allMap } = globalStore;

		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		let currentLine;
		if (conditionType === "group") {
			currentLine = filterObj.children[conditionArr[0]]["children"][conditionArr[1]];
		} else {
			currentLine = filterObj.children[conditionArr[0]];
		}
		currentLine[field] = value;

		if (field === "fieldType") {
			let mapItem = allMap && allMap["ruleFieldList"] && allMap["ruleFieldList"].filter(item => item.name === value)[0];
			currentLine["leftPropertyName"] = null;
			currentLine["operator"] = null;
			currentLine["rightValue"] = null;
			currentLine["rightValueType"] = null;
		}

		let thisLineObj = currentLine;
		// 如果前置fieldType是系统字段
		if (thisLineObj && thisLineObj["fieldType"] && thisLineObj["fieldType"] === "systemField") {
			let ruleFieldItem = allMap && allMap["ruleFieldList"] && allMap["ruleFieldList"].find(item => item.name === value);

			if (field === "leftPropertyName") {
				currentLine["type"] = ruleFieldItem.type ? ruleFieldItem.type : null;
				currentLine["rightValue"] = null;
				currentLine["operator"] = null;
				currentLine["rightValueType"] = "input";
			}
			if (field === "rightValue") {
				if (thisLineObj["operator"] === "belong" || thisLineObj["operator"] === "unbelong") {
					currentLine["rightValueType"] = "list";
				}
			}
			if (field === "rightValueType") {
				currentLine["rightValue"] = null;
			}
		}

		// 如果前置fieldType是字段集
		if (currentLine && currentLine["fieldType"] && currentLine["fieldType"] === "fieldSet") {
			let fieldSetSelectItem = allMap && allMap["fieldSetSelect"] && allMap["fieldSetSelect"].find(item => item.name === value);

			if (field === "leftPropertyName") {
				currentLine["type"] = fieldSetSelectItem.type ? fieldSetSelectItem.type : null;
				currentLine["rightValue"] = null;
				currentLine["operator"] = null;
			}
			if (field === "rightValueType") {
				currentLine["rightValue"] = null;
			}
			if (field === "rightValue") {
				currentLine["rightValueType"] = fieldSetSelectItem.type;
			}
		}

		onChange(filterObj);
	}

	render() {
		let { globalStore, conditionData, conditionSingleData, conditionType, conditionArr, disabled } = this.props;
		let { allMap, personalMode } = globalStore;
		let { fieldParamListSelect = [], ruleFieldList } = allMap || {};
		const { lang } = personalMode;
		const operaTypeBlong = conditionSingleData["operator"] === "belong" || conditionSingleData["operator"] === "notbelong";
		const belongNames = fieldParamListSelect.reduce((pre, v) => {
			pre.push(v.name);
			return pre;
		}, []);
		let newBelongOperator = PolicyConstants.conditionOperator[conditionSingleData.type ? conditionSingleData.type : "STRING"] || [];
		return (
			<div className={conditionType === "group" ? "group-item" : ""}>
				<Row gutter={CommonConstants.gutterSpan}>
					{
						conditionType === "group" &&
						conditionArr[1] === 0 &&
						<Col span={2} offset={2} className="basic-info-title">
							<Select
								value={conditionData.logicOperator || undefined}
								onChange={(value) => {
									this.changeLogicOperator(value);
								}}
								dropdownMatchSelectWidth={false}
								disabled={disabled}
							>
								<Option value="&&">
									{/* 与 */}
									{policyDetailLang.oneCondition("and")}
								</Option>
								<Option value="||">
									{/* 或 */}
									{policyDetailLang.oneCondition("or")}
								</Option>
							</Select>
						</Col>
					}
					{
						conditionType === "group" && conditionArr[1] !== 0 &&
						<Col span={4} className="basic-info-title"></Col>
					}
					{
						conditionType === "single" &&
						<Col span={4} className="basic-info-title"></Col>
					}
					{
						conditionSingleData && conditionSingleData["leftVar"] &&
						<Col span={3}>
							<Select
								value={conditionSingleData && conditionSingleData["leftVar"] ? conditionSingleData["leftVar"] : undefined}
								placeholder={policyDetailLang.oneCondition("select")} // 请选择
								onChange={(value) => {
									let selectObj = allMap["ruleAndIndexFieldList"].find(field => field.name === value);
									this.changeConditionField("leftVar", "select", value);

									if (selectObj.type === "ENUM") {
										this.changeConditionField("rightVarType", "select", "input");
									}
								}}
								showSearch
								optionFilterProp="children"
								dropdownMatchSelectWidth={false}
								disabled={disabled}
							>
								{
									ruleFieldList &&
									ruleFieldList.map((item, index) => {
										return (
											<Option
												value={item.name}
												key={index}
												title={item.dName}
											>
												{policyDetailLang.oneCondition("current")}
												{lang === "en" ? item.enDName : item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>}
					<Col className="gutter-row" span={3}>
						<div className="gutter-box">
							<Select
								value={conditionSingleData["fieldType"] || undefined}
								placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
								onChange={(value) => {
									this.changeConditionField("fieldType", "select", value);
								}}
							>
								<Option value="systemField">系统字段</Option>
								<Option value="fieldSet">字段集</Option>
							</Select>
							{/* <Select
    							value={conditionSingleData && conditionSingleData["operator"] ? conditionSingleData["operator"] : undefined}
    							placeholder={policyDetailLang.oneCondition("select")} // 请选择
    							onChange={(value) => {
    								this.changeConditionField("operator", "select", value);
    							}}
    							dropdownMatchSelectWidth={false}
    							disabled={disabled}
    						>
    							{
    								PolicyConstants.conditionOperator[conditionSingleData.leftValueType ? conditionSingleData.leftValueType.toLocaleUpperCase() : "STRING"].map((item, index) => {
    									return (
    										<Option
    											value={item.name}
    											key={index}
    										>
    											{lang === "en" ? item.enDName : item.dName}
    										</Option>
    									);
    								})
    							}
    						</Select> */}

						</div>
					</Col>
					{/* 如果选择的字段类型是系统字段 */}
					{
						conditionSingleData["fieldType"] &&
						conditionSingleData["fieldType"] === "systemField" &&
						<Col span={3}>
							<Select
								value={conditionSingleData["leftPropertyName"] || undefined}
								placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
								// onChange={this.changeOperationField.bind(this, "leftPropertyName", "select", index)}
								onChange={(e) => {
									this.changeConditionField("leftPropertyName", "select", e)
								}}
								showSearch
								optionFilterProp="children"
								disabled={disabled}
								dropdownMatchSelectWidth={false}
							>
								{
									allMap &&
									allMap["ruleFieldList"] &&
									allMap["ruleFieldList"].map((item, index) => {
										return (
											<Option value={item.name} key={index} title={item.dName}>
												{
													item.dName
												}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					}
					{/* 如果选择的字段类型是字段集 */}
					{
						conditionSingleData["fieldType"] &&
						conditionSingleData["fieldType"] === "fieldSet" &&
						<Col span={3}>
							<Select
								value={conditionSingleData["leftPropertyName"] || undefined}
								placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
								// onChange={this.changeOperationField.bind(this, "leftPropertyName", "select", index)}
								onChange={(e) => {
									this.changeConditionField("leftPropertyName", "select", e)
								}}
								showSearch
								optionFilterProp="children"
								disabled={disabled}
								dropdownMatchSelectWidth={false}
							>
								{
									allMap &&
									allMap["fieldSetSelect"] &&
									allMap["fieldSetSelect"].map((item, index) => {
										return (
											<Option
												value={item.name}
												key={index}
												title={item.dName}
											>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					}
					{
						conditionSingleData["fieldType"] === "systemField" &&
						conditionSingleData["leftPropertyName"] &&
						<Col span={3} className="basic-info-text">
							<Select
								value={conditionSingleData["operator"] || undefined}
								disabled={disabled}
								// onChange={this.changeOperationField.bind(this, "operator", "select", index)}
								onChange={(e) => {
									this.changeConditionField("operator", "select", e)
								}}
								placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
							>
								{
									newBelongOperator.map((item, index) => {
										return (
											<Option
												value={item.name}
												key={index}
												title={lang === "cn" ? item.dName : item.enDName}
											>
												{lang === "cn" ? item.dName : item.enDName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					}
					{/* 这里判断operator */}
					{
						conditionSingleData["fieldType"] === "fieldSet" &&
						conditionSingleData["type"] === "fieldSet" &&
						<Col span={3} className="basic-info-text">
							<Select
								value={conditionSingleData["operator"] || undefined}
								disabled={disabled}
								// onChange={this.changeOperationField.bind(this, "operator", "select", index)}
								onChange={(e) => {
									this.changeConditionField("operator", "select", e)
								}}
								placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
								dropdownMatchSelectWidth={false}
							>
								{
									commonSetOperator.map((item, index) => {
										return (
											<Option
												value={item.name}
												key={index}
												title={lang === "cn" ? item.dName : item.enDName}
											>
												{lang === "cn" ? item.dName : item.enDName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					}
					{
						conditionSingleData["fieldType"] === "fieldSet" &&
						conditionSingleData["type"] === "addressSet" &&
						<Col span={3} className="basic-info-text">
							<Select
								value={conditionSingleData["operator"] || undefined}
								disabled={disabled}
								// onChange={this.changeOperationField.bind(this, "operator", "select", index)}
								onChange={(e) => {
									this.changeConditionField("operator", "select", e)
								}}
								placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
								dropdownMatchSelectWidth={false}
							>
								{
									addressSetOperator.map((item, index) => {
										return (
											<Option
												value={item.name}
												key={index}
												title={lang === "cn" ? item.dName : item.enDName}
											>
												{lang === "cn" ? item.dName : item.enDName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					}
					{/* 如果选择的是字段集，并且字段集类型是fieldSet */}
					{
						conditionSingleData["fieldType"] &&
						conditionSingleData["fieldType"] === "fieldSet" &&
						conditionSingleData["type"] === "fieldSet" &&
						<Col span={3} className="basic-info-text">
							<Select
								value={conditionSingleData["rightValue"] || undefined}
								placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
								// onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
								onChange={(e) => {
									this.changeConditionField("rightValue", "select", e)
								}}
								showSearch
								optionFilterProp="children"
								disabled={disabled}
								dropdownMatchSelectWidth={false}
							>
								{
									allMap &&
									allMap["fieldSetSelect"] &&
									allMap["fieldSetSelect"].map((item, index) => {
										return (
											<Option value={item.name} key={index} title={item.dName}>
												{indexListLang.ruleAttr("current") + item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					}
					{
						conditionSingleData["fieldType"] &&
						conditionSingleData["fieldType"] === "fieldSet" &&
						conditionSingleData["type"] === "addressSet" &&
						<Col span={3} className="basic-info-text">
							<Input
								className="similarity-warp"
								addonBefore="相似度"
								addonAfter="%"
								value={conditionSingleData.similarity}
								// onChange={this.changeOperationField.bind(this, "similarity", "input", index)}
								onChange={(e) => {
									this.changeConditionField("similarity", "input", e)
								}}
							/>
						</Col>
					}
					{
						conditionSingleData["fieldType"] &&
						conditionSingleData["fieldType"] === "fieldSet" &&
						conditionSingleData["type"] === "addressSet" &&
						<Col span={3} className="basic-info-text">
							<Select
								value={conditionSingleData["rightValue"] || undefined}
								placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
								// onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
								onChange={(e) => {
									this.changeConditionField("rightValue", "select", e)
								}}
								showSearch
								optionFilterProp="children"
								disabled={disabled}
								dropdownMatchSelectWidth={false}
							>
								{
									allMap &&
									allMap["fieldSetSelect"] &&
									allMap["fieldSetSelect"].map((item, index) => {
										return (
											<Option
												value={item.name}
												key={index}
												title={item.dName}
											>
												{indexListLang.ruleAttr("current") + item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					}
					{
						conditionSingleData["fieldType"] &&
						conditionSingleData["fieldType"] === "systemField" &&
						conditionSingleData["operator"] !== "belong" &&
						conditionSingleData["operator"] !== "unbelong" &&
						<Col span={5} className="basic-info-text">
							{
								conditionSingleData.rightValueType === "input" &&
								conditionSingleData.type !== "ENUM" &&
								<InputGroup compact>
									<Select
										value={conditionSingleData["rightValueType"] || undefined}
										style={{ width: "30%" }}
										placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
										// onChange={this.changeOperationField.bind(this, "rightValueType", "select", index)}
										onChange={(e) => {
											this.changeConditionField("rightValueType", "select", e)
										}}
										disabled={disabled}
									>
										{/* lang:常量变量*/}
										<Option value="input">
											{indexListLang.ruleAttr("constant")}
										</Option>
										<Option
											value="context">
											{indexListLang.ruleAttr("variable")}
										</Option>
									</Select>
									<Input
										style={{ width: "70%" }}
										value={conditionSingleData.rightValue || undefined}
										placeholder={indexListLang.ruleAttr("constantInputPlaceholder")} // lang:请填写常量内容
										// onChange={this.changeOperationField.bind(this, "rightValue", "input", index)}
										onChange={(e) => {
											this.changeConditionField("rightValue", "input", e)
										}}
										disabled={disabled}
									/>
								</InputGroup>
							}
							{
								conditionSingleData.rightValueType === "context" &&
								conditionSingleData.type !== "ENUM" &&
								<InputGroup compact>
									<Select
										value={conditionSingleData["rightValueType"] || undefined}
										style={{ width: "30%" }}
										placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
										// onChange={this.changeOperationField.bind(this, "rightValueType", "select", index)}
										onChange={(e) => {
											this.changeConditionField("rightValueType", "select", e)
										}}
										disabled={disabled}
									>
										{/* lang:常量变量*/}
										<Option value="input">
											{indexListLang.ruleAttr("constant")}
										</Option>
										<Option
											value="context">
											{indexListLang.ruleAttr("variable")}
										</Option>
									</Select>
									<Select
										style={{ width: "70%" }}
										value={conditionSingleData.rightValue || undefined}
										placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
										// onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
										onChange={(e) => {
											this.changeConditionField("rightValue", "select", e)
										}}
										showSearch
										optionFilterProp="children"
										disabled={disabled}
										dropdownMatchSelectWidth={false}
									>
										{
											allMap &&
											allMap["ruleFieldList"] &&
											allMap["ruleFieldList"].filter(fItem => {
												return (
													fItem.type === conditionSingleData.type ||
													(["DOUBLE", "INT"].indexOf(fItem.type) > -1 && ["DOUBLE", "INT"].indexOf(conditionSingleData.type) > -1)
												);
											}).map((item, index) => {
												return (
													<Option
														value={item.name}
														key={index}
														title={item.dName}
													>
														{/* lang:当前*/}
														{indexListLang.ruleAttr("current") + item.dName}
													</Option>
												);
											})
										}
									</Select>
								</InputGroup>
							}
							{
								conditionSingleData.type === "ENUM" &&
								<Select
									value={conditionSingleData.rightValue || undefined}
									placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
									// onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
									onChange={(e) => {
										this.changeConditionField("rightValue", "select", e)
									}}
									showSearch
									optionFilterProp="children"
									disabled={disabled}
									dropdownMatchSelectWidth={false}
								>
									{
										allMap &&
										allMap["fieldEnumObj"] &&
										allMap["fieldEnumObj"][conditionSingleData.leftPropertyName] &&
										allMap["fieldEnumObj"][conditionSingleData.leftPropertyName].map((item, index) => {
											return (
												<Option
													value={item.value}
													key={index}
													title={item.value}
												>
													{item.description}
												</Option>
											);
										})
									}
								</Select>
							}
						</Col>
					}
					{
						conditionSingleData["fieldType"] &&
						conditionSingleData["fieldType"] === "systemField" &&
						(conditionSingleData["operator"] === "belong" || conditionSingleData["operator"] === "unbelong") &&
						<Col span={3}>
							<Select
								value={conditionSingleData["rightValue"] || undefined}
								placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
								// onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
								onChange={(e) => {
									this.changeConditionField("rightValue", "select", e)
								}}
								showSearch
								optionFilterProp="children"
								disabled={disabled}
								dropdownMatchSelectWidth={false}
							>
								{
									allMap &&
									allMap["customList"] &&
									allMap["customList"].map((item, index) => {
										return (
											<Option
												value={item.name}
												key={index}
												title={item.dName}
											>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					}
					{
						!disabled &&
						<Col span={2} className="basic-info-oper">
							{
								conditionType === "group" &&
								// 添加一项
								<Tooltip title={policyDetailLang.oneCondition("addOne")} placement="left">
									<Icon
										className="add"
										type="plus-circle-o"
										onClick={this.addCondition}
									/>
								</Tooltip>
							}
							{/* 删除当前行 */}
							<Tooltip title={policyDetailLang.oneCondition("deleteCur")} placement="right">
								<Icon
									className="delete"
									type="delete"
									onClick={this.deleteCondition}
								/>
							</Tooltip>
						</Col>
					}
				</Row>
			</div >
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(OneCondition);
