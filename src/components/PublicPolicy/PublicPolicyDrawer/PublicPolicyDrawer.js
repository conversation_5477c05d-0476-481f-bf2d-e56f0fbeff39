import React from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Drawer, Tag } from "antd";
import { PolicyConstants } from "@/constants";
import { publicPolicyListLang, commonLang } from "@/constants/lang";
import { searchToObject } from "@/utils/utils";
import EffectScopeTable from "../EffectScope/EffectScopeTable";
import "@/components/PolicyList/PolicyDrawer/PolicyDrawer.less";
import "./PublicPolicyDrawer.less";

class PublicPolicyDrawer extends React.PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { policyDetail = {}, visible, globalStore, isRunning, location: { search }, onClose } = this.props;
		let { allMap, policyModel, personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		const { publicPolicyRiskSelect = [] } = allMap || {};

		let statusMap = lang === "cn" ? PolicyConstants.policyStatusMap : PolicyConstants.policyStatusMap2;

		let total = policyDetail.rules ? policyDetail.rules.length : 0;
		let x = policyDetail.rules && policyDetail.rules.filter(item => item.valid === 1).length;
		let y = policyDetail.rules && policyDetail.rules.filter(item => item.valid === 0).length;
		let z = policyDetail.rules && policyDetail.rules.filter(item => item.valid === 2).length;

		// 这块后续要优化
		const searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		const currentTab = searchObj && searchObj.currentTab ? parseInt(searchObj.currentTab, 10) : 1;
		isRunning = currentTab !== 2;

		const { scene: sceneOld } = policyDetail || {};
		let scene = "";
		if (typeof sceneOld === "string") {
			scene = JSON.parse(sceneOld);
		} else {
			scene = { ...sceneOld };
		}
		return (
			<Drawer
				title={publicPolicyListLang.detailDrawer("title")} // 公共策略详情
				width={550}
				className="policy-drawer-wrap"
				placement="right"
				closable={false}
				onClose={() => {
				}}
				visible={visible}
				mask={false}
			>
				<div className="policy-detail">
					<div className="policy-detail-title">
						<i className="iconfont icon-celve title-pre"></i>
						<span
							className="policy-detail-title-text text-overflow"
							style={{
								maxWidth: "300px",
								display: "inline-block",
								verticalAlign: "bottom"
							}}
						>
							{policyDetail.name}
						</span>
						{
							statusMap[policyDetail.status] &&
							<Tag color={statusMap[policyDetail.status]["color"]} className="policy-status">
								{statusMap[policyDetail.status]["text"]}
							</Tag>
						}
						<i className="iconfont icon-close title-close" onClick={onClose}></i>
					</div>
					<div className="policy-detail-base">
						<div className="policy-detail-group policy-detail-group-flex">
							<label>
								{/* lang:生效范围 */}
								{publicPolicyListLang.detailDrawer("effectScope")}
							</label>
							<span>
								<EffectScopeTable scene={scene} />
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:风险类型 */}
								{publicPolicyListLang.detailDrawer("riskType")}
							</label>
							<span>
								{
									publicPolicyRiskSelect &&
									publicPolicyRiskSelect.length > 0 &&
									publicPolicyRiskSelect.map((item) => {
										if (item.name === policyDetail.riskType) {
											return item.dName;
										}
									})
								}
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:策略模式 */}
								{publicPolicyListLang.detailDrawer("policyMode")}
							</label>
							<span>
								{
									policyModel && policyModel.map((item, index) => {
										if (item.name === policyDetail.mode) {
											return item.dName;
										}
									})
								}
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:策略描述 */}
								{publicPolicyListLang.detailDrawer("policyDescription")}
							</label>
							<span>
								{/* lang:暂无描述*/}
								{policyDetail.description === "" ? publicPolicyListLang.detailDrawer("noDescription") : policyDetail.description}
							</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:最后修改 */}
								{publicPolicyListLang.detailDrawer("lastModify")}
							</label>
							<span>{policyDetail.updatedBy}</span>
						</div>
						<div className="policy-detail-group">
							<label>
								{/* lang:修改时间 */}
								{publicPolicyListLang.detailDrawer("modifyTime")}
							</label>
							<span>{policyDetail.gmtModified || policyDetail.gmtCreate}</span>
						</div>
					</div>
					<div className="policy-detail-sep"></div>
					<div className="policy-detail-rule">
						<h4>
							{/* lang:规则列表 */}
							{publicPolicyListLang.detailDrawer("ruleList")}
						</h4>
						<div className="policy-detail-status">
							{/* lang:当前策略规则总计：6条，启用：3条，禁用：0条，模拟：3条 */}
							{publicPolicyListLang.ruleRecords(total, x, y, z)}
						</div>
						<ul className="policy-detail-ruleList">
							{
								policyDetail.rules && policyDetail.rules.map((item, index) => {
									return (
										<li
											key={index}
											className={"rule-status-" + item.valid}
											onClick={() => {
												let { dispatch, location } = this.props;
												let pathname = location.pathname;
												let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
												let baseUrl;
												if (isRunning) {
													baseUrl = `/policy/versionPublicPolicyDetail/${item.fkPolicyUuid}?tabIndex=1&ruleUuid=${item.uuid}&policyVersion=${policyDetail.policyVersion}`;
												} else {
													baseUrl = `/policy/publicPolicyDetail/${item.fkPolicyUuid}?tabIndex=1&ruleUuid=${item.uuid}`;
												}
												dispatch(routerRedux.push(prefix + baseUrl));
											}}
										>
											{/* lang:禁用、启用、模拟 */}
											{item.valid === 0 && <Tag color="red">{commonLang.base("close")}</Tag>}
											{item.valid === 1 && <Tag color="green">{commonLang.base("formal")}</Tag>}
											{item.valid === 2 &&
												<Tag color="geekblue">{commonLang.base("simulation")}</Tag>}
											<a>
												{item.name}
											</a>
										</li>
									);
								})
							}
						</ul>
					</div>
				</div>
			</Drawer>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(PublicPolicyDrawer);
