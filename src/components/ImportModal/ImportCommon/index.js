import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Button, Row, Col, message } from "antd";
import { CommonConstants, ImportConstants } from "@/constants";
import { imExportModeLang, commonLang } from "@/constants/lang";
import ImportMode from "../FileImportCondition/ImportMode";
import SelectScene from "../FileImportCondition/SelectScene";
import CheckImportFile from "../FileImportCondition/CheckImportFile";
import FileImportResult from "../FileImportResult";
import "./index.less";

const { showModeList } = ImportConstants;

class ImportCommon extends PureComponent {
	state = {
		fileChecked: false,
		fileSuccess: false,
		checkResult: {},
		hasSuccess: false,
		successData: {},
		uploadLoading: false
	};

	constructor(props) {
		super(props);
	}

	// 监听导入字段输入
	changeImport = (e, fileName, type = "input") => {
		this.props.changeField(e, fileName, type);
	}

	// 导入前校验文件
	fileCheck = async() => {
		let { modalData, fetchMethod } = this.props;
		if (!modalData["file"]) {
			// lang:请选择要上传的文件
			message.warning(imExportModeLang.importPolicyModal("selectFileFirst"));
			return;
		}
		await this.setState({
			uploadLoading: true
		});
		await fetchMethod({
			...modalData,
			isVerify: 1
		}).then(res => {
			this.setState({
				uploadLoading: false
			});
			if (res.success) {
				this.refs.checkImportFile.resetFile(); // 清空上传文件输入框
				const checkResult = res.data || {};
				const { dependencyMiss } = checkResult;
				// 如果没有场景缺失，则之际进入下一步
				if (!dependencyMiss) {
					this.setState({
						fileSuccess: true
					});
				}
				this.setState({
					checkResult,
					fileChecked: true
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			this.setState({
				uploadLoading: false
			});
			console.log(err);
		});
	}

	// 执意导入
	confirmImport = () => {
		this.setState({
			fileSuccess: true
		});
	}
	// 出现字段缺失不再进行 返回上一步
	stepOne = () => {
		this.setState({
			fileChecked: false,
			fileSuccess: false,
			checkResult: {}
		});
	}

	// 放弃导入 返回上一步
	stepTwo = () => {
		const { checkResult } = this.state;
		const {dependencyMiss} = checkResult; // 如果没有缺失则返回到最上一面(即退回两步)，有则返回上一步
		if (dependencyMiss) {
			this.setState({
				fileChecked: true,
				fileSuccess: false,
				hasSuccess: false,
				successData: {}
			});
		} else {
			this.setState({
				fileChecked: false,
				fileSuccess: false,
				checkResult: {},
				hasSuccess: false,
				successData: {}
			});
		}

	}

	// 导入
	importPolicySets = async() => {
		let { dispatch, modalData, importCallback, importType, fetchMethod } = this.props;

		if (!modalData["policyMode"] && showModeList[importType].policyMode) {
			// lang:请选择策略导入模式
			message.warning(imExportModeLang.importPolicyModal("policyModeRequired"));
			return;
		}

		if (!modalData["zbMode"] && showModeList[importType].zbMode) {
			// lang:请选择指标导入模式
			message.warning(imExportModeLang.importPolicyModal("zbModeRequired"));
			return;
		}

		if (!modalData["ruleMode"] && showModeList[importType].ruleMode) {
			// lang:请选择规则导入模式
			message.warning(imExportModeLang.importPolicyModal("ruleModeRequired"));
			return;
		}

		if (!modalData["file"]) {
			// lang:请选择要上传的文件
			message.warning(imExportModeLang.importPolicyModal("selectFileFirst"));
			return;
		}

		const newModalData = { ...modalData };
		if (!newModalData.replaceScene) {
			delete newModalData.replaceScene;
		} else {
			newModalData.replaceScene = JSON.stringify(newModalData.replaceScene);
		}
		await this.setState({
			uploadLoading: true
		});
		await fetchMethod(newModalData).then(res => {
			this.setState({
				uploadLoading: false
			});
			if (res.success) {
				this.setState({
					hasSuccess: true,
					successData: res.data || {}
				});
				// 1.刷新allMap
				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
				// 导入成功后
				// 2. 重新刷新编辑区策略列表
				importCallback && importCallback();
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			this.setState({
				uploadLoading: false
			});
			console.log(err);
		});
	}

	closeModal = () => {
		let { onCancel } = this.props;
		onCancel();
		if (this.refs.policySetImportFile && this.refs.policySetImportFile.value) {
			this.refs.policySetImportFile.value = "";
		}
		this.setState({
			hasSuccess: false,
			successData: {},
			uploadLoading: false,
			fileChecked: false,
			fileSuccess: false,
			checkResult: {}
		});
	}
	// 获取按钮
	getFooterBtn = () => {
		let { hasSuccess, uploadLoading, fileChecked, fileSuccess } = this.state;
		let footerButton = [];
		if (!fileChecked) {
			footerButton = [
				<Button type="primary" onClick={this.fileCheck.bind(this)}>
					{/* lang:确定 */}
					{commonLang.base("ok")}
				</Button>
			];
		} else if (!fileSuccess) {
			footerButton = [
				<Button onClick={this.stepOne.bind(this)}>
					{/* lang:上一步 */}
					{imExportModeLang.importPolicyModal("previousStep")}
				</Button>,
				<Button type="primary" onClick={this.confirmImport.bind(this)}>
					{/* lang:确定 */}
					{commonLang.base("ok")}
				</Button>
			];
		} else if (hasSuccess) {
			footerButton = [
				<Button type="primary" onClick={this.closeModal.bind(this)}>
					{/* lang:确定 */}
					{commonLang.base("ok")}
				</Button>
			];
		} else {
			footerButton = [
				<Button onClick={this.stepTwo.bind(this)}>
					{/* lang:上一步 */}
					{imExportModeLang.importPolicyModal("previousStep")}
				</Button>,
				<Button
					type="primary"
					onClick={this.importPolicySets.bind(this)}
					loading={uploadLoading}
					disabled={uploadLoading}
				>
					{/* lang:导入 */}
					{commonLang.base("importBtn")}
				</Button>
			];
		}
		return footerButton;
	}
	transferSceneResult = (data) => {
		let { globalStore } = this.props;
    	let { personalMode } = globalStore;
		const newData = {};
		const infoTitle = {};
		Object.keys(data).length > 0 &&
		Object.keys(data).map(v=>{
			if (v !== "sceneMissInfo") {
				newData[v] = data[v];
			} else {
				const scene = data[v];
				scene && scene.length > 0 && scene.map(sceneInfo=>{
					const name = sceneInfo.sceneType.toLowerCase();
					let {sceneName, sceneType} = sceneInfo;
					if (!(sceneName && sceneName.indexOf("缺失") > -1)) {
						sceneName += "缺失";
						sceneType += " Miss";
					}
					newData[name] = sceneInfo.sceneItems;
					infoTitle[name] = personalMode.lang === "cn" ? sceneName : sceneType;
				});
			}
		});
		return {
			res: Object.keys(newData).length ? newData : data,
			infoTitle
		};
	}
	render() {
		let { visible, modalData, title, importType } = this.props;
		let { successData: successOldData, hasSuccess, fileChecked, checkResult: checkResultOld, fileSuccess } = this.state;
		const {res: successData, infoTitle: succesInfoTitle} = this.transferSceneResult(successOldData);
		const {res: checkResult, infoTitle: checkResultInfoTitle} = this.transferSceneResult(checkResultOld);
		const { replaceScene } = modalData || {};
		let footerButton = this.getFooterBtn();
		return (
			<Modal
				title={title} // 策略导入
				visible={visible}
				maskClosable={false}
				onCancel={this.closeModal.bind(this)}
				className="rules-import-modal-wrap"
				footer={footerButton}
				width={700}
			>
				<div className="basic-form s2">
					{/* 未做过文件校验 进行文件校验 */}
					{
						!fileChecked &&
						<CheckImportFile
							ref="checkImportFile"
							importType={importType}
							fileValue={modalData.file}
							changeFileValue={(file) => { this.changeImport(file, "file", "file"); }}
						/>
					}
					{
						(fileChecked && !fileSuccess) &&
						<Row gutter={CommonConstants.gutterSpan} type="flex" style={{ height: "auto" }}>
							<Col className="basic-info-title">
								{/* 文件名: */}
								{imExportModeLang.importPolicyModal("fileName")}：
							</Col>
							<Col>{modalData.file.name}</Col>
						</Row>
					}
					{
						(fileSuccess && !hasSuccess) &&
						<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
							<Col span="5" className="basic-info-title">
								{/* 文件名: */}
								{imExportModeLang.importPolicyModal("fileName")}：
							</Col>
							<Col span="19">{modalData.file.name}</Col>
						</Row>
					}
					{/* 文件校验通过 即执意导入或不存在缺失字段 */}
					{
						fileSuccess && !hasSuccess &&
						<div>
							{
								modalData.policySetName &&
								<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
									<Col span={5} className="basic-info-title">
										{/* lang:策略集*/}
										{imExportModeLang.importPolicyModal("policySet")}：
                        			</Col>
									<Col span={10}>
										{modalData.policySetName}
									</Col>
								</Row>
							}
							{/* 导入场景 */}
							<ImportMode
								changeImport={this.changeImport}
								data={modalData}
								importType={importType}
								checkResult={checkResult}
							>
								{/* 选择场景 */}
								<SelectScene
									changeImport={this.changeImport}
									data={modalData}
									type={importType}
								/>
							</ImportMode>
						</div>
					}
					{/* 导入成功 */}
					{
						hasSuccess &&
						<FileImportResult
							importType={importType}
							successData={successData}
							replaceScene={replaceScene}
							infoTitle={succesInfoTitle}
						/>
					}
					{/* 文件校验存在缺失字段 */}
					{
						fileChecked &&
						!fileSuccess &&
						<FileImportResult
							importType={importType}
							checkFileResult={true}
							successData={checkResult}
							infoTitle={checkResultInfoTitle}
						/>
					}
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(ImportCommon);
