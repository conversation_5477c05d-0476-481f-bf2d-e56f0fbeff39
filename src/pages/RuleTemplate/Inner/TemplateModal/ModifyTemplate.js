import { PureComponent } from "react";
import { connect } from "dva";
import { Steps, message } from "antd";
import { templateAPI } from "@/services";
import StepOne from "../Step/StepOne";
import StepTwo from "../Step/StepTwo";
import StepThree from "../Step/StepThree";
import StepFour from "../Step/StepFour";

const Step = Steps.Step;

class ModifyTemplate extends PureComponent {
	constructor(props) {
		super(props);
		this.changeHandle = this.changeHandle.bind(this);
		this.modifyTemplate = this.modifyTemplate.bind(this);
		this.changeStep = this.changeStep.bind(this);
		this.closeEditorModal = this.closeEditorModal.bind(this);
	}

	changeHandle(field, type, e) {
		let { dispatch, templateStore } = this.props;
		let { modifyTemplateData } = templateStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		}
		modifyTemplateData[field] = value;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	modifyTemplate() {
		let { templateStore, dispatch } = this.props;
		let { currentType, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		// modifyTemplateData["advanceConfig"] = JSON.stringify(modifyTemplateData["advanceConfig"]);
		// modifyTemplateData["cfgJson"] = JSON.stringify(modifyTemplateData["cfgJson"]);
		templateAPI.modifyTemplate({
			...modifyTemplateData,
			advanceConfig: JSON.stringify(modifyTemplateData["advanceConfig"]),
			cfgJson: JSON.stringify(modifyTemplateData["cfgJson"])
		}).then((res) => {
			if (res.success) {
				dispatch({
					type: "template/getTemplateList",
					payload: {
						type: currentType
					}
				});
				message.success("更新模板成功");
				dispatch({
					type: "template/setDialogShow",
					payload: {
						modifyTemplate: false
					}
				});
				dispatch({
					type: "template/setAttrValue",
					payload: {
						currentStep: 0
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	changeStep(currentStep) {
		let { dispatch } = this.props;

		dispatch({
			type: "template/setAttrValue",
			payload: {
				currentStep: currentStep
			}
		});
	}

	closeEditorModal() {
		let { dispatch } = this.props;

		dispatch({
			type: "template/setDialogShow",
			payload: {
				modifyTemplate: false
			}
		});
		dispatch({
			type: "template/setAttrValue",
			payload: {
				currentStep: 0
			}
		});
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: {
					id: null,
					name: null,
					displayName: null,
					description: null,
					type: null,
					cfgJson: null,
					parentId: null,
					sort: null
				}
			}
		});
	}

	render() {
		let { templateStore } = this.props;
		let { currentStep } = templateStore;

		return (
			<div className="algo-modal">
				<div className="algo-config-wrap">
					<div className="algo-config-mask">
						<div className="algo-config-content">
							<div
								className="algo-config-close"
								onClick={this.closeEditorModal.bind(this)}
							>
								<i className="iconfont icon-close-thin"></i>
							</div>
							<div className="algo-config-header">
								<div className="algo-config-title">
									<h2>修改模板</h2>
								</div>
								<Steps current={currentStep}>
									<Step title="基本信息" />
									<Step title="自定义" />
									<Step title="预览" />
									<Step title="生成JSON" />
								</Steps>
							</div>
							<div
								className="algo-config-body"
								style={{ height: "calc(100% - 230px)" }}
							>
								{
									currentStep === 0 &&
                                    <StepOne />
								}
								{
									currentStep === 1 &&
                                    <StepTwo />
								}
								{
									currentStep === 2 &&
                                    <StepThree />
								}
								{
									currentStep === 3 &&
                                    <StepFour />
								}
							</div>
							<div className="algo-config-footer">
								<div className="footer-btns">
									<button
										style={{ marginRight: 8 }}
										disabled={!(currentStep > 0)}
										onClick={this.changeStep.bind(this, currentStep - 1)}
										className="pre"
									>
                                        上一步
									</button>
									{
										currentStep < 3 &&
                                        <button
                                        	onClick={this.changeStep.bind(this, currentStep + 1)}
                                        	className="next"
                                        >
                                            下一步
                                        </button>
									}
									{
										currentStep === 3 &&
                                        <button
                                        	onClick={this.modifyTemplate.bind(this)}
                                        	className="next"
                                        >
                                            完成
                                        </button>
									}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(ModifyTemplate);
