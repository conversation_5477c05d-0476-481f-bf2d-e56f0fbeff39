import React from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Row, Col } from "antd";
import { PolicyConstants, CommonConstants } from "@/constants";
import { commonLang, policyDetailLang } from "@/constants/lang";

const Option = Select.Option;
const InputGroup = Input.Group;

class OneCondition extends React.PureComponent {

	constructor(props) {
		super(props);
	}

    addCondition = () => {
    	let { onChange, filterObj, conditionArr } = this.props;

    	let singleConditionTemp = {
    		operator: "==",
    		leftValueType: "STRING",
    		leftVarType: "context",
    		leftVar: null,
    		rightVarType: "input",
    		rightVar: null
    	};

    	console.log(filterObj);
    	console.log(conditionArr);
    	filterObj.children[conditionArr[0]]["children"].push(singleConditionTemp);
    	onChange(filterObj);
    }

    deleteCondition = () => {
    	let { filterObj, onChange, conditionArr } = this.props;
    	let currentGroup = filterObj.children[conditionArr[0]];

    	if (currentGroup && currentGroup.children && currentGroup.children.length > 1) {
    		currentGroup["children"].splice(conditionArr[1], 1);
    	} else {
    		filterObj.children.splice(conditionArr[0], 1);
    	}
    	onChange(filterObj);
    }

    changeLogicOperator = (value) => {
    	let { conditionArr, filterObj, onChange } = this.props;

    	filterObj.children[conditionArr[0]]["logicOperator"] = value;
    	onChange(filterObj);
    }

    changeConditionField = (field, type, e) => {
    	let { globalStore, conditionType, conditionArr, filterObj, onChange } = this.props;
    	let { allMap } = globalStore;

    	let value = "";
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}

    	let currentLine;
    	if (conditionType === "group") {
    		currentLine = filterObj.children[conditionArr[0]]["children"][conditionArr[1]];
    	} else {
    		currentLine = filterObj.children[conditionArr[0]];
    	}

    	if (field === "leftVar") {
    		let mapItem = allMap && allMap["ruleFieldList"] && allMap["ruleFieldList"].filter(item => item.name === value)[0];

    		currentLine["leftValueType"] = mapItem.type;
    		currentLine["rightVar"] = null;
    	}

    	currentLine[field] = value;
    	onChange(filterObj);
    }

    render() {
    	let { globalStore, conditionData, conditionSingleData, conditionType, conditionArr, disabled } = this.props;
    	let { allMap, personalMode } = globalStore;
    	let { fieldParamListSelect = [], ruleFieldList } = allMap || {};
    	const { lang } = personalMode;
    	const operaTypeBlong = conditionSingleData["operator"] === "belong" || conditionSingleData["operator"] === "notbelong";
    	const belongNames = fieldParamListSelect.reduce((pre, v) => {
    		pre.push(v.name);
    		return pre;
    	}, []);
    	return (
    		<div className={conditionType === "group" ? "group-item" : ""}>
    			<Row gutter={CommonConstants.gutterSpan}>
    				{
    					conditionType === "group" &&
                        conditionArr[1] === 0 &&
                        <Col span={2} offset={2} className="basic-info-title">
                        	<Select
                        		value={conditionData.logicOperator || undefined}
                        		onChange={(value) => {
                        			this.changeLogicOperator(value);
                        		}}
                        		dropdownMatchSelectWidth={false}
                        		disabled={disabled}
                        	>
                        		<Option value="&&">
                        			{/* 与 */}
                        			{policyDetailLang.oneCondition("and")}
                        		</Option>
                        		<Option value="||">
                        			{/* 或 */}
                        			{policyDetailLang.oneCondition("or")}
                        		</Option>
                        	</Select>
                        </Col>
    				}
    				{
    					conditionType === "group" && conditionArr[1] !== 0 &&
                        <Col span={4} className="basic-info-title"></Col>
    				}
    				{
    					conditionType === "single" &&
                        <Col span={4} className="basic-info-title"></Col>
    				}
    				<Col span={5}>
    					<Select
    						value={conditionSingleData && conditionSingleData["leftVar"] ? conditionSingleData["leftVar"] : undefined}
    						placeholder={policyDetailLang.oneCondition("select")} // 请选择
    						onChange={(value) => {
    							let selectObj = allMap["ruleAndIndexFieldList"].find(field => field.name === value);
    							this.changeConditionField("leftVar", "select", value);

    							if (selectObj.type === "ENUM") {
    								this.changeConditionField("rightVarType", "select", "input");
    							}
    						}}
    						showSearch
    						optionFilterProp="children"
    						dropdownMatchSelectWidth={false}
    						disabled={disabled}
    					>
    						{
    							ruleFieldList &&
                                ruleFieldList.map((item, index) => {
                                	return (
                                		<Option
                                			value={item.name}
                                			key={index}
                                			title={item.dName}
                                		>
                                			{policyDetailLang.oneCondition("current")}
                                			{lang === "en" ? item.enDName : item.dName}
                                		</Option>
                                	);
                                })
    						}
    					</Select>
    				</Col>
    				<Col className="gutter-row" span={3}>
    					<div className="gutter-box">
    						<Select
    							value={conditionSingleData && conditionSingleData["operator"] ? conditionSingleData["operator"] : undefined}
    							placeholder={policyDetailLang.oneCondition("select")} // 请选择
    							onChange={(value) => {
    								this.changeConditionField("operator", "select", value);
    							}}
    							dropdownMatchSelectWidth={false}
    							disabled={disabled}
    						>
    							{
    								PolicyConstants.conditionOperator[conditionSingleData.leftValueType ? conditionSingleData.leftValueType.toLocaleUpperCase() : "STRING"].map((item, index) => {
    									return (
    										<Option
    											value={item.name}
    											key={index}
    										>
    											{lang === "en" ? item.enDName : item.dName}
    										</Option>
    									);
    								})
    							}
    						</Select>
    					</div>
    				</Col>
    				{
    					conditionSingleData &&
                        conditionSingleData["operator"] &&
                        conditionSingleData["operator"] !== "isnull" &&
                        conditionSingleData["operator"] !== "notnull" &&
                        <Col className="gutter-row" span={8}>
                        	<div className="gutter-box">
                        		{
                        			conditionSingleData["leftValueType"] !== "ENUM" &&
                                    !operaTypeBlong &&
                                    <InputGroup compact>
                                    	<Select
                                    		value={conditionSingleData && conditionSingleData["rightVarType"] ? conditionSingleData["rightVarType"] : undefined}
                                    		onChange={(e) => {
                                    			this.changeConditionField("rightVarType", "select", e);
                                    		}}
                                    		style={{ width: "30%" }}
                                    		disabled={disabled}
                                    	>
                                    		<Option value="input">
                                    			{/* 常量 */}
                                    			{policyDetailLang.oneCondition("constant")}
                                    		</Option>
                                    		<Option value="context">
                                    			{/* 变量 */}
                                    			{policyDetailLang.oneCondition("variable")}
                                    		</Option>
                                    	</Select>
                                    	{
                                    		conditionSingleData["rightVarType"] === "input" &&
                                            <Input
                                            	style={{ width: "70%" }}
                                            	value={conditionSingleData && conditionSingleData["rightVar"] ? conditionSingleData["rightVar"] : undefined}
                                            	placeholder={policyDetailLang.oneCondition("inputConstant")} // 请输入常量内容
                                            	onChange={(e) => {
                                            		this.changeConditionField("rightVar", "input", e);
                                            	}}
                                            	disabled={disabled}
                                            />
                                    	}
                                    	{
                                    		conditionSingleData["rightVarType"] === "context" &&
                                            <Select
                                            	placeholder={policyDetailLang.oneCondition("select")} // lang:"请选择"
                                            	value={conditionSingleData.rightVar || undefined}
                                            	style={{ width: "70%" }}
                                            	onChange={(e) => { this.changeConditionField("rightVar", "select", e); }}
                                            	showSearch
                                            	optionFilterProp="children"
                                            	disabled={disabled}
                                            	dropdownMatchSelectWidth={false}
                                            >
                                            	{
                                            		ruleFieldList &&
                                                    ruleFieldList.map((item, index) => {
                                                    	return (
                                                    		<Option
                                                    			value={item.name}
                                                    			key={index}
                                                    			title={item.dName}
                                                    		>
                                                    			{policyDetailLang.oneCondition("current")}
                                                    			{lang === "en" ? item.enDName : item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                            	}
                                            </Select>
                                    	}
                                    </InputGroup>
                        		}
                        		{
                        			conditionSingleData["leftValueType"] === "ENUM" &&
                                    <Select
                                    	value={conditionSingleData["rightVar"] || undefined}
                                    	placeholder={policyDetailLang.oneCondition("select")} // lang:请选择
                                    	onChange={(value) => {
                                    		this.changeConditionField("rightVar", "select", value);
                                    	}}
                                    	showSearch
                                    	optionFilterProp="children"
                                    	dropdownMatchSelectWidth={false}
                                    	disabled={disabled}
                                    >
                                    	{
                                    		allMap &&
                                            allMap["fieldEnumObj"] &&
                                            allMap["fieldEnumObj"][conditionSingleData.leftVar] &&
                                            allMap["fieldEnumObj"][conditionSingleData.leftVar].map((item, index) => {
                                            	return (
                                            		<Option
                                            			value={item.value}
                                            			key={index}
                                            			title={item.value}
                                            		>
                                            			{item.description}  [{item.value}]
                                        		    </Option>
                                            	);
                                            })
                                    	}
                                    </Select>
                        		}
                        		{
                        			operaTypeBlong &&
                                    <Select
                                    	value={
                                    		(conditionSingleData["rightVar"] && belongNames.indexOf(conditionSingleData["rightVar"]) > -1)
                                    			? conditionSingleData["rightVar"]
                                    			: undefined
                                    	}
                                    	placeholder={policyDetailLang.oneCondition("select")} // lang:请选择
                                    	onChange={(value) => {
                                    		this.changeConditionField("rightVar", "select", value);
                                    	}}
                                    	showSearch
                                    	optionFilterProp="children"
                                    	dropdownMatchSelectWidth={false}
                                    	disabled={disabled}
                                    >
                                    	{
                                    		allMap &&
                                            allMap["fieldParamListSelect"] &&
                                            allMap["fieldParamListSelect"].map((item, index) => {
                                            	return (
                                            		<Option
                                            			value={item.name}
                                            			key={index}
                                            		>
                                                        [参数列表]{lang === "en" ? item.enDName : item.dName}
                                            		</Option>
                                            	);
                                            })
                                    	}
                                    </Select>
                        		}
                        	</div>
                        </Col>
    				}
    				{
    					!disabled &&
                        <Col span={3} className="basic-info-oper">
                        	{
                        		conditionType === "group" &&
                                // 添加一项
                                <Tooltip title={policyDetailLang.oneCondition("addOne")} placement="left">
                                	<Icon
                                		className="add"
                                		type="plus-circle-o"
                                		onClick={this.addCondition}
                                	/>
                                </Tooltip>
                        	}
                        	{/* 删除当前行 */}
                        	<Tooltip title={policyDetailLang.oneCondition("deleteCur")} placement="right">
                        		<Icon
                        			className="delete"
                        			type="delete"
                        			onClick={this.deleteCondition}
                        		/>
                        	</Tooltip>
                        </Col>
    				}
    			</Row>
    		</div >
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(OneCondition);
