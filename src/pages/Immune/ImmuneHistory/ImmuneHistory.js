import { PureComponent } from "react";
import { connect } from "dva";
import { checkFunctionHasPermission } from "@/utils/permission";
import Search from "./Search/Search";
import ImmuneHistoryList from "./ImmuneHistoryList/ImmuneHistoryList";
import AddModifyImmune from "../Common/Modal/AddModifyImmune";

class ImmuneHistory extends PureComponent {
	constructor(props) {
		super(props);

	}
	componentDidMount() {
		this.timer = setInterval(() => {
			const { globalStore, dispatch } = this.props;
			const { menuTreeReady, currentApp } = globalStore;
			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkFunctionHasPermission("ZB0106", "RuleImmunoLogList")) {
					let appName = currentApp.name;
					if (appName) {
						appName = currentApp.name === "all" ? "" : currentApp.name;
						dispatch({
							type: "immuneHistory/setSearchData",
							payload: {
								appName
							}
						});
						this.search();
					}
				}
			}
		}, 100);
	}

	// 监听app切换
	componentWillReceiveProps(nextProps) {
		let { dispatch, globalStore } = this.props;
		let { menuTreeReady } = globalStore;
		if (menuTreeReady && checkFunctionHasPermission("ZB0106", "RuleImmunoLogList")) {
			const preCurrentApp = this.props.globalStore.currentApp;
			const nextCurrentApp = nextProps.globalStore.currentApp;
			if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
				// 检测到切换应用，开始刷新列表
				// 首先清空搜索项目
				let { name } = nextCurrentApp || {};
				if (name === "all") {
					name = "";
				}
				dispatch({
					type: "immuneHistory/setSearchData",
					payload: {
						appName: name
					}
				});
				this.reset();
				this.search();
			}
		}
	}

	// 查询条件
	changeSearchField = (name, e, type) => {
		const { dispatch } = this.props;
		let value = e || "";
		if (type === "input") {
			value = e.target.value;
		}
		dispatch({
			type: "immuneHistory/setSearchData",
			payload: {
				[name]: value
			}
		});
	}

	// 重置
	reset = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "immuneHistory/setSearchData",
			payload: {
				ruleName: "",
				updateType: "",
				operationType: "",
				operationTime: []
			}
		});
	}

	// 分页
	paginationOnChange = (curPage, pageSize) => {
    	const { dispatch } = this.props;
    	dispatch({
    		type: "immuneHistory/setAttrValue",
    		payload: {
				curPage,
				pageSize
    		}
    	});
    	dispatch({
			type: "immuneHistory/immuneHistoryList"
		});
	};

	// 搜索
	search = () => {
		const { dispatch } = this.props;
		dispatch({
    		type: "immuneHistory/setAttrValue",
    		payload: {
				curPage: 1
    		}
    	});
		dispatch({
			type: "immuneHistory/immuneHistoryList"
		});
	}
	render() {
		const { immuneHistoryStore, immuneConfigStore } = this.props;
		const { searchData } = immuneHistoryStore;
		const { addModifyImmune } = immuneConfigStore;
		return (
			<div className="page-global-body">
				<Search
					changeSearchField={this.changeSearchField}
					searchData={searchData}
					search={this.search}
					reset={this.reset}
				/>
				<ImmuneHistoryList
					paginationOnChange={this.paginationOnChange}
				/>
				{
					addModifyImmune &&
					<AddModifyImmune disabled={true}/>
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	immuneHistoryStore: state.immuneHistory,
	immuneConfigStore: state.immuneConfig
}))(ImmuneHistory);

