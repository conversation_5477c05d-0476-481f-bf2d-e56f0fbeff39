import { PureComponent } from "react";
import { connect } from "dva";
import { isJSON } from "@/utils/isJSON";
import { Input, Icon, Tooltip, Select, Row, Col } from "antd";
import { CommonConstants } from "@/constants";

const InputGroup = Input.Group;
const Option = Select.Option;

class FieldList extends PureComponent {
    state = {
    	fieldList: [
    		{
    			fieldSet: "test3",
    			blackListSet: "test3",
    			similarityGrade: ""
    		},
    		{
    			fieldSet: "address",
    			blackListSet: "test3",
    			similarityGrade: ""
    		}
    	]
    };

    constructor(props) {
    	super(props);
    	this.changeFieldValue = this.changeFieldValue.bind(this);
    }

    initMatchSets() {
    	let { indexEditorStore, dispatch } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;

    	if (currentIndexObj && currentIndexObj.attachFieldsObj) {
    		let matchSets = currentIndexObj.attachFieldsObj.matchSets;
    		let emptyObj = {
    			fieldSet: null,
    			blackListSet: null,
    			similarityGrade: null
    		};
    		if (matchSets && isJSON(matchSets)) {
    			let matchSetsList = JSON.parse(matchSets);
    			if (matchSetsList.length === 0) {
    				matchSetsList = [emptyObj];
    			}
    			currentIndexObj.attachFieldsObj["matchSets"] = JSON.stringify(matchSetsList);
    		} else {
    			let matchSetsList = [emptyObj];
    			matchSets = JSON.stringify(matchSetsList);
    			currentIndexObj.attachFieldsObj["matchSets"] = matchSets;
    		}
    	}

    	console.log(currentIndexObj);
    	dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			editIndexMap: editIndexMap
    		}
    	});
    }

    changeFieldValue(index, field, type, e) {
    	let { indexEditorStore, dispatch } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;

    	let value = "";
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}

    	let matchSets = currentIndexObj.attachFieldsObj.matchSets;
    	let matchSetsList = JSON.parse(matchSets);
    	matchSetsList[index][field] = value;
    	if (field === "fieldSet") {
    		matchSetsList[index]["blackListSet"] = null;
    	}

    	currentIndexObj.attachFieldsObj.matchSets = JSON.stringify(matchSetsList);
    	currentIndexObj["hasModify"] = true;

    	dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			editIndexMap: editIndexMap
    		}
    	});
    }

    addFieldSet(index) {
    	let { indexEditorStore, dispatch } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
    	let matchSets = currentIndexObj.attachFieldsObj.matchSets;
    	let matchSetsList = JSON.parse(matchSets);

    	let emptyObj = {
    		fieldSet: null,
    		blackListSet: null,
    		similarityGrade: null
    	};
    	matchSetsList.splice(index + 1, 0, emptyObj);

    	currentIndexObj.attachFieldsObj.matchSets = JSON.stringify(matchSetsList);
    	currentIndexObj["hasModify"] = true;

    	dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			editIndexMap: editIndexMap
    		}
    	});
    }

    removeFieldSet(index) {
    	let { indexEditorStore, dispatch } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
    	let matchSets = currentIndexObj.attachFieldsObj.matchSets;
    	let matchSetsList = JSON.parse(matchSets);

    	matchSetsList.splice(index, 1);

    	currentIndexObj.attachFieldsObj.matchSets = JSON.stringify(matchSetsList);
    	currentIndexObj["hasModify"] = true;

    	dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			editIndexMap: editIndexMap
    		}
    	});
    }

    render() {
    	let { indexEditorStore, templateStore, globalStore, templateName, dispatch } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let { indexTemplateListObj } = templateStore;
    	let { personalMode, allMap } = globalStore;
    	let { fieldSetSelect } = allMap;
    	let lang = personalMode.lang === "cn" ? "cn" : "en";

    	let currentTemplate = indexTemplateListObj && templateName ? indexTemplateListObj[templateName] : null;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;

    	// 如果字段集没有数据，给他初始化一段默认数据
    	if (currentIndexObj && currentIndexObj.attachFieldsObj) {
    		let matchSets = currentIndexObj.attachFieldsObj.matchSets;
    		let emptyObj = {
    			fieldSet: null,
    			blackListSet: null,
    			similarityGrade: null
    		};
    		if (matchSets && isJSON(matchSets)) {
    			let matchSetsList = JSON.parse(matchSets);
    			if (matchSetsList.length === 0) {
    				matchSetsList = [emptyObj];
    			}
    			currentIndexObj.attachFieldsObj["matchSets"] = JSON.stringify(matchSetsList);
    		} else {
    			let matchSetsList = [emptyObj];
    			matchSets = JSON.stringify(matchSetsList);
    			currentIndexObj.attachFieldsObj["matchSets"] = matchSets;
    		}
    	}

    	let matchSetsList = currentIndexObj && currentIndexObj.attachFieldsObj && currentIndexObj.attachFieldsObj.matchSets ? JSON.parse(currentIndexObj.attachFieldsObj.matchSets) : [];
    	console.log(currentIndexObj);

    	return (
    		<div className="ml-1">
    			{
    				matchSetsList &&
                    matchSetsList.map((item, index, arr) => {
                    	let fieldSetType;
                    	if (item.fieldSet) {
                    		let fieldSetSelectObj = fieldSetSelect.find(fItem => fItem.name === item.fieldSet);
                    		if (fieldSetSelectObj) {
                    			fieldSetType = fieldSetSelectObj.type;
                    		}
                    	}

                    	return (
                    		<Row
                    			className="mb10"
                    			gutter={CommonConstants.gutterSpan}
                    			key={index}
                    		>
                    			<Col span={14}>
                    				<InputGroup compact>
                    					<Select
                    						value={item.fieldSet || undefined}
                    						style={{ width: "35%" }}
                    						placeholder="请选择字段集"
                    						onChange={this.changeFieldValue.bind(this, index, "fieldSet", "select")}
                    						showSearch
                    						optionFilterProp="children"
                    						dropdownMatchSelectWidth={false}
                    					>
                    						{
                    							fieldSetSelect &&
                                                fieldSetSelect.map((fItem, fIndex) => {
                                                	return (
                                                		<Option
                                                			value={fItem.name}
                                                			key={fIndex}
                                                		>
                                                			{fItem.dName}
                                                		</Option>
                                                	);
                                                })
                    						}
                    					</Select>
                    					<Input
                    						style={{ width: "30%" }}
                    						defaultValue="对比黑名单字段集"
                    						disabled={true}
                    					/>
                    					<Select
                    						value={item.blackListSet || undefined}
                    						style={{ width: "35%" }}
                    						placeholder="请选择字段集"
                    						onChange={this.changeFieldValue.bind(this, index, "blackListSet", "select")}
                    						showSearch
                    						optionFilterProp="children"
                    						dropdownMatchSelectWidth={false}
                    					>
                    						{
                    							fieldSetSelect &&
                                                fieldSetSelect.map((fItem, fIndex) => {
                                                	return (
                                                		<Option
                                                			value={fItem.name}
                                                			key={fIndex}
                                                			disabled={fItem.type !== fieldSetType}
                                                		>
                                                			{fItem.dName}
                                                		</Option>
                                                	);
                                                })
                    						}
                    					</Select>
                    				</InputGroup>
                    			</Col>
                    			{
                    				fieldSetType &&
                                    fieldSetType === "addressSet" &&
                                    <Col span={4}>
                                    	<Input
                                    		addonBefore="相似度"
                                    		addonAfter="%"
                                    		value={item.similarityGrade}
                                    		onChange={this.changeFieldValue.bind(this, index, "similarityGrade", "input")}
                                    		className="similarity-grade"
                                    	/>
                                    </Col>
                    			}
                    			<Col span={3} className="basic-info-oper">
                    				<Tooltip
                    					title="添加字段集"
                    					placement="top"
                    				>
                    					<Icon
                    						className="add"
                    						type="plus-circle"
                    						onClick={this.addFieldSet.bind(this, index)}
                    					/>
                    				</Tooltip>
                    				<Tooltip
                    					title="删除当前行"
                    					placement="top"
                    				>
                    					<Icon
                    						className="delete"
                    						type="delete"
                    						onClick={this.removeFieldSet.bind(this, index)}
                    					/>
                    				</Tooltip>
                    			</Col>
                    		</Row>
                    	);
                    })
    			}
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(FieldList);

