import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getPolicySets = async(params) => {
	return request(getUrl("/admin/policySets/version", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 策略集回测
const addPolicySetReplay = async(params) => {
	params = deleteEmptyObjItem(params);
	return request("/admin/policySet/replay", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};
const modifyPolicySetReplay = async(params) => {
	params = deleteEmptyObjItem(params);
	return request("/admin/policySet/replay", {
		method: "PUT",
		headers: getHeader(),
		body: params
	});
};

const getReplayTaskDetail = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/policySet/replay", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 获取回测任务运行数量和事件
const getReplayTaskRunInfo = async(params) => {
	params = deleteEmptyObjItem(params);
	return request("/admin/policySet/replay/estimate", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};

// 获取策略集下的所有策略以及规则
const getVersionPolicys = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/policys/versionPolicies", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 当前策略集生效公共策略
const refPublicPolicy = async(params) => {
	return request(getUrl("/admin/policySets/refPublicPolicy", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 下线运行区策略
const policyOffLine = async(params) => {
	return request("/admin/policys/offline", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};
export default {
	getPolicySets,
	addPolicySetReplay,
	modifyPolicySetReplay,
	getReplayTaskDetail,
	getReplayTaskRunInfo,
	getVersionPolicys,
	refPublicPolicy,
	policyOffLine
};
