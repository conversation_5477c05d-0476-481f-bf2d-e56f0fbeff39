import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Select, Switch } from "antd";

const Option = Select.Option;

class VariableTemplate extends PureComponent {
	constructor(props) {
		super(props);
		this.changeParamValue = this.changeParamValue.bind(this);
		this.deleteChild = this.deleteChild.bind(this);
	}

	changeParamValue(type, field, actionType, e) {
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		if (actionType === "onBlur") {
			// 正则表达式去除首尾空格
			value = value.replace(/(^\s*)|(\s*$)/g, "");
		}
		let { templateStore, childIndex, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex][field] = value;
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	deleteChild(index) {
		let { templateStore, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex]["children"].splice(index, 1);
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	render() {
		let { templateStore, childIndex } = this.props;
		let { operatorTemplateItemIndex, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
		let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
		let currentParam = children && children[childIndex] ? children[childIndex] : {};

		return (
			<div className="single-param">
				<div className="single-param-header">
					<h2>常量/变量</h2>
					<Icon
						type="delete"
						onClick={this.deleteChild.bind(this, childIndex)}
					/>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>唯一标识：</span>
					</div>
					<div className="param-field">
						<Input
							value={currentParam["name"] || undefined}
							onChange={this.changeParamValue.bind(this, "input", "name", "onChange")}
							onBlur={this.changeParamValue.bind(this, "input", "name", "onBlur")}
							placeholder="请输入唯一标识"
						/>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>常量还是变量：</span>
					</div>
					<div className="param-field">
						<Select
							value={currentParam["type"] || undefined}
							rows={4}
							placeholder="请选择"
							style={{ width: "100%" }}
							onChange={this.changeParamValue.bind(this, "select", "type", "onChange")}
						>
							<Option value="all">all</Option>
							<Option value="input">常量</Option>
							<Option value="context">变量</Option>
						</Select>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>字段过滤类型：</span>
					</div>
					<div className="param-field">
						<Select
							value={currentParam["filterType"] || undefined}
							rows={4}
							placeholder="请选择字段过滤类型"
							style={{ width: "100%" }}
							onChange={this.changeParamValue.bind(this, "select", "filterType", "onChange")}
							mode="tags"
						>
							<Option value="string">string</Option>
							<Option value="int">int</Option>
							<Option value="double">double</Option>
							<Option value="datetime">datetime</Option>
							<Option value="enum">enum</Option>
							<Option value="boolean">boolean</Option>
						</Select>
					</div>
				</div>
				<div className="single-param-item">
					<div className="param-title">
						<span>是否包含指标：</span>
					</div>
					<div className="param-field">
						<Switch
							checkedChildren="包含"
							unCheckedChildren="不包含"
							checked={currentParam["includeIndex"]}
							onChange={this.changeParamValue.bind(this, "select", "includeIndex", "onChange")}
						/>
						<p>右变量的下拉字段是否包含指标</p>
					</div>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(VariableTemplate);
