import { Fragment, PureComponent } from "react";
import { connect } from "dva";
import { Form, Col, Select } from "antd";
import AppScene from "@/components/SelectScene/AppScene";
import ShowAppScene from "@/components/SelectScene/ShowAppScene";
import { transferSceneList } from "@/utils/scene";
import { immuneConfigLang } from "@/constants/lang";

const { Option } = Select;
class PublicPolicy extends PureComponent {
	constructor(props) {
		super(props);
		this.changeField = props.changeField;
	}

	// 修改公共策略
	changePolicy = (value, policy) => {
		this.changeField("publicPolicyUuid", value || "", "select");
		const { dispatch, globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};

		// 重置相关数据源列表
		let { scene } = policy || {};
		let sceneListSource = [];
		if (typeof scene === "string" && scene) {
			scene = JSON.parse(scene);
		};
		if (scene && scene.length > 0) {
			const isGlobal = scene.filter(v=>{
				return v.appName === "GLOBAL_APP";
			});
			if (isGlobal && isGlobal.length > 0) {
				sceneListSource = publicPolicyScene.filter(v=>{
					return v.name !== "GLOBAL_APP";
				});
			} else {
				sceneListSource = transferSceneList(scene, publicPolicyScene); // 数据转换
			}
		}

		dispatch({
			type: "immuneConfig/setAttrValue",
			payload: {
				publicRuleList: [],
				sceneListSource
			}
		});
		// 清空值
		dispatch({
			type: "immuneConfig/setAddModifyImmuneData",
			payload: {
				"publicRuleName": "", // 规则名
				"publicRuleUuid": "" // 规则
			}
		});
		// 表单相应的重置
		this.props.form.resetFields(["publicRuleUuid"]);

		// 获取公共策略下的规则
		dispatch({
			type: "immuneConfig/getPublicPolicyRules",
			payload: {
				isFirst: true,
				policyUuid: policy.policyUuid,
				policyVersion: policy.policyVersion
			}
		});
	}

	render() {
		const { form, immuneConfigStore, disabled } = this.props;
		const { getFieldDecorator } = form;
	 	const { addModifyImmuneData, publicPolicyList = [], publicRuleList = [], sceneListSource, openType } = immuneConfigStore || {};
	 	let {
			publicPolicyUuid,
			publicRuleUuid,
			effectRange
		 } = addModifyImmuneData || {};
		return (
			<Fragment>
				<Col span="24">
					{/* 选择策略 */}
					<Form.Item label={immuneConfigLang.immuneConfigModal("selectPolicy")}>
						{
							getFieldDecorator("publicPolicyUuid", {
								initialValue: publicPolicyUuid || undefined,
								rules: [
									{
										required: true,
										message: immuneConfigLang.immuneConfigModal("selectPolicyMsg") // 请选择策略
									}
								]
							})(
								<Select
									disabled={disabled || openType === "edit"}
									value={publicPolicyUuid || undefined}
									placeholder={immuneConfigLang.immuneConfigModal("selectPolicyMsg")}// 请选择策略
									filterOption={ (input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 }
									showSearch
									allowClear
									onChange={(e, _this)=>{
										const { policy } = (_this && _this.props) ? _this.props : {} ;
										this.changePolicy(e, policy);
									}}
								>

									{
										publicPolicyList &&
										publicPolicyList.length > 0 &&
										publicPolicyList.map(v=>{
											return (
												<Option
													value={v.policyUuid}
													key={v.policyUuid}
													policy={v}
												>
													{v.policyName}
												</Option>
											);
										})
									}
								</Select>
							)
						}
					</Form.Item>
				</Col>
				<Col span="24">
					{/* 选择规则 */}
					<Form.Item label={immuneConfigLang.immuneConfigModal("selectRule")}>
						{
							getFieldDecorator("publicRuleUuid", {
								initialValue: publicRuleUuid || undefined,
								rules: [
									{
										required: true,
										message: immuneConfigLang.immuneConfigModal("selectRuleMsg") // 请选择规则
									}
								]
							})(
								<Select
									disabled={disabled || openType === "edit"}
									value={publicRuleUuid || undefined}
									placeholder={immuneConfigLang.immuneConfigModal("selectRuleMsg")} // 请选择规则
									allowClear
									filterOption={ (input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 }
									showSearch
									onChange={(e, _this)=>{
										const { name } = (_this && _this.props) ? _this.props : {} ;
										this.changeField("publicRuleUuid", e, "select");
										this.changeField("publicRuleName", name, "select");
									}}
								>
									{
										publicRuleList &&
										publicRuleList.length > 0 &&
										publicRuleList.map((v)=>{
											return (
												<Option value={v.uuid} key={v.uuid} name={v.name}>{v.name}</Option>
											);
										})
									}
								</Select>
							)
						}
					</Form.Item>
					{/* 免疫应用 */}
					{
						sceneListSource &&
						sceneListSource.length > 0 &&
						<Form.Item label={immuneConfigLang.immuneConfigModal("immuneApp")}>
							{
								disabled
									? <ShowAppScene
										sceneListSource={sceneListSource}
										scene={effectRange || []}
									/>
									: getFieldDecorator("effectRange", {
										initialValue: effectRange || undefined,
										rules: [
											{
												required: true,
												message: immuneConfigLang.immuneConfigModal("immuneAppMsg") // 请选择免疫应用
											}
										]
									})(
										<AppScene
											disabled={disabled}
											name="effectRange"
											sceneListSource={sceneListSource}
											scene={effectRange || []}
											changeField={this.changeField}
										/>
									)
							}
						</Form.Item>
					}
				</Col>
			</Fragment>
		);
	}
};
export default connect(state => ({
	globalStore: state.global,
	immuneConfigStore: state.immuneConfig
}))(PublicPolicy);
