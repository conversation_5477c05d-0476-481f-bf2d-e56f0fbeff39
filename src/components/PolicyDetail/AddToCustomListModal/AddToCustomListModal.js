import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, InputNumber, Button, Select, Row, Col, Icon, Alert, Tooltip, DatePicker, message, Empty } from "antd";
import { policyDetailAPI } from "@/services";
import { CommonConstants } from "@/constants";
import moment from "moment";
import "./AddToCustomListModal.less";
import { cloneDeep } from "lodash";
import { policyDetailLang } from "@/constants/lang";
import AddCustomList from "./Inner/AddCustomList";
import AddMultiDimList from "./Inner/AddMultiDimList";

const InputGroup = Input.Group;
const { Option } = Select;

class AddToCustomListModal extends PureComponent {
	constructor(props) {
		super(props);
	}

    addCustomItem = (type, index) => {
    	const { policyDetailStore, dispatch } = this.props;
    	let { dialogData } = policyDetailStore;
    	let { addToCustomListData } = dialogData;

    	let singleObj = {
    		when: "hitRule",
    		do: {
    			action: "addCustomList",
    			arguments: {
    				field: null,
    				customListUuid: null,
    				expireType: "forever",
    				expireValue: null
    			}
    		}
    	};
    	let multipleObj = {
    		when: "hitRule",
    		do: {
    			action: "addMultiDimList",
    			arguments: {
    				colData: [
    					{
    						colUuid: null,
    						fieldName: null
    					}
    				],
    				multiDimListUuid: null,
    				expireType: "forever",
    				expireValue: null
    			}
    		}
    	};

    	if (type === "single") {
    		addToCustomListData.triggers.splice(index + 1, 0, cloneDeep(singleObj));
    	} else if (type === "multiple") {
    		addToCustomListData.triggers.splice(index + 1, 0, cloneDeep(multipleObj));
    	}

    	dispatch({
    		type: "policyDetail/setDialogData",
    		payload: {
    			addToCustomListData: addToCustomListData
    		}
    	});
    }

    changeCustomItem = (field, index, value) => {
    	let { policyDetailStore, dispatch } = this.props;
    	let { dialogData } = policyDetailStore;
    	let { addToCustomListData } = dialogData;

    	addToCustomListData.triggers[index]["do"]["arguments"][field] = value;

    	dispatch({
    		type: "policyDetail/setDialogData",
    		payload: {
    			addToCustomListData: addToCustomListData
    		}
    	});
    }

    removeCustomItem = (index) => {
    	let { policyDetailStore, dispatch } = this.props;
    	let { dialogData } = policyDetailStore;
    	let { addToCustomListData } = dialogData;

    	addToCustomListData.triggers.splice(index, 1);

    	dispatch({
    		type: "policyDetail/setDialogData",
    		payload: {
    			addToCustomListData: addToCustomListData
    		}
    	});
    }

    submitModal = () => {
    	let { policyDetailStore, dispatch } = this.props;
    	let { policyDetail, policyRules } = policyDetailStore;
    	let { dialogData } = policyDetailStore;
    	let { addToCustomListData, addToCustomListData: { ruleIndexArr } } = dialogData;

    	let currentRule = null;
    	if (ruleIndexArr.length === 1) {
    		currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
    	} else if (ruleIndexArr.length === 2) {
    		currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
    	}

    	let hasEmptyField = false;
    	let colDataHasPrimaryKey = false;
    	if (addToCustomListData.triggers.length) {
    		addToCustomListData.triggers.map((item) => {
    			let argumentObj = item.do.arguments;

    			if (item.do.action === "addCustomList") {
    				// 简单名单
    				if (!argumentObj.field && !argumentObj.customListUuid) {
    					hasEmptyField = true;
    				}
    			} else if (item.do.action === "addMultiDimList") {
    				// 复合名单
    				if (!argumentObj.defineType && !argumentObj.multiDimListUuid) {
    					hasEmptyField = true;
    				}
    				// 开始判断复杂名单的colData

    				argumentObj.colData && argumentObj.colData.map((item, index) => {
    					if (!item.fieldName && !item.colUuid) {
    						hasEmptyField = true;
    					}
    					if (item.key) {
    						colDataHasPrimaryKey = true;
    					}
    				});
    			}

    			if (argumentObj.expireType === "byDay" || argumentObj.expireType === "byTs" || argumentObj.expireType === "byHour" || argumentObj.expireType === "byMin") {
    				if (!argumentObj.expireValue) {
    					hasEmptyField = true;
    				}
    			}
    		});
    	}

    	if (hasEmptyField) {
    		message.warning(policyDetailLang.addToCustomListModal("tip1")); // 发现参数为空，请填写完整
    		return;
    	}

    	if (addToCustomListData.triggers && addToCustomListData.triggers.length > 0) {
    		if (addToCustomListData.triggers.find(item => item.do.action === "addMultiDimList")) {
    			if (!colDataHasPrimaryKey) {
    				message.warning(policyDetailLang.addToCustomListModal("tip4")); // 发现多维度名单缺少主键
    				return;
    			}
    		}
    	}

    	let triggerField;
    	let hasSame = false;

    	// addToCustomListData.triggers.map((item) => {
    	//     if (item.do.arguments.field === triggerField) {
    	//         hasSame = true;
    	//     } else {
    	//         triggerField = item.do.arguments.field;
    	//     }
    	// });

    	if (hasSame) {
    		// lang:不能选择相同的系统字段
    		message.error(policyDetailLang.addToCustomListModal("tip2"));
    		return;
    	}

    	let params = {
    		triggers: JSON.stringify(addToCustomListData.triggers || []),
    		ruleUuid: addToCustomListData.ruleUuid
    	};

    	policyDetailAPI.addCustomList(params).then(res => {
    		if (res.success) {
    			currentRule.triggers = addToCustomListData.triggers;
    			// lang:添加到名单成功！
    			message.success(policyDetailLang.addToCustomListModal("tip3"));
    			dispatch({
    				type: "policyDetail/setDialogShow",
    				payload: {
    					addToCustomList: false
    				}
    			});
    			dispatch({
    				type: "policyDetail/setPolicyRule",
    				payload: {
    					policyRules: policyRules
    				}
    			});
    			// 更改策略状态
    			dispatch({
    				type: "policyDetail/changePolicyStatus",
    				payload: {
    					uuid: policyDetail.uuid
    				}
    			});
    			this.closeModal();
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    closeModal = () => {
    	let { policyDetailStore, dispatch } = this.props;
    	let { policyDetail } = policyDetailStore;

    	dispatch({
    		type: "policyDetail/setDialogShow",
    		payload: {
    			addToCustomList: false
    		}
    	});

    	// 更改策略状态
    	dispatch({
    		type: "policyDetail/changePolicyStatus",
    		payload: {
    			uuid: policyDetail.uuid
    		}
    	});
    }

    render() {
    	let { policyDetailStore, globalStore, readOnly, dispatch } = this.props;
    	let { dialogShow, dialogData, policyDetail } = policyDetailStore;
    	let { allMap } = globalStore;
    	let { addToCustomListData: { triggers, ruleUuid } } = dialogData;

    	let curPolicyAppName = policyDetail.appName;

    	return (
    		<Modal
    			title={readOnly ? policyDetailLang.addToCustomListModal("preview") : policyDetailLang.addToCustomListModal("title")} // 添加到名单
    			visible={dialogShow.addToCustomList}
    			width={960}
    			className="add-to-custom-list-wrap"
    			maskClosable={false}
    			onCancel={this.closeModal}
    			maskClosable={!!readOnly}
    			footer={readOnly ? null : [
    				<Button
    					onClick={this.closeModal}
    					key="close"
    				>
    					{/* 取消 */}
    					{policyDetailLang.addToCustomListModal("cancel")}
    				</Button>,
    				<Button
    					type="primary"
    					onClick={this.submitModal}
    					key="submit"
    				>
    					{/* 确定 */}
    					{policyDetailLang.addToCustomListModal("ok")}
    				</Button>
    			]}
    			afterClose={() => {
    				dispatch({
    					type: "policyDetail/setDialogData",
    					payload: {
    						addToCustomListData: {
    							triggers: [],
    							ruleIndexArr: [],
    							ruleUuid: null
    						}
    					}
    				});
    			}}
    		>
    			<div className="add-to-custom-list">
    				{/* "配置自动添加到名单的选项" : "请配置自动添加到名单的选项" */}
    				<Alert
    					message={readOnly ? policyDetailLang.addToCustomListModal("autoAdd") : policyDetailLang.addToCustomListModal("autoAdd2")}
    					type="info"
    				/>
    				<form className="custom-form">
    					{
    						ruleUuid &&
                            triggers &&
                            triggers.map((item, index) => {
                            	if (item.do.action === "addCustomList") {
                            		return (
                            			<AddCustomList
                            				key={index}
                            				itemIndex={index}
                            				itemData={item}
                            				readOnly={readOnly}
                            				globalStore={globalStore}
                            				curPolicyAppName={curPolicyAppName}
                            				onChange={this.changeCustomItem}
                            				addCustomItem={this.addCustomItem}
                            				removeCustomItem={this.removeCustomItem}
                            			/>
                            		);
                            	}
                            	if (item.do.action === "addMultiDimList") {
                            		return (
                            			<AddMultiDimList
                            				key={index}
                            				itemIndex={index}
                            				itemData={item}
                            				readOnly={readOnly}
                            				globalStore={globalStore}
                            				curPolicyAppName={curPolicyAppName}
                            				onChange={this.changeCustomItem}
                            				removeCustomItem={this.removeCustomItem}
                            			/>
                            		);
                            	}
                            })
    					}
    				</form>
    				{
    					ruleUuid &&
                        !readOnly &&
                        <div className="add-handle">
                        	<a
                        		className="single"
                        		onClick={() => {
                        			this.addCustomItem("single", triggers.length);
                        		}}
                        	>
                                添加到单维度名单
                        	</a>
                        	{/* <a
                        		className="multiple"
                        		onClick={() => {
                        			this.addCustomItem("multiple", triggers.length);
                        		}}
                        	>
                                添加到多维度名单
                        	</a> */}
                        </div>
    				}
    				{
    					readOnly &&
                        ruleUuid &&
                        !triggers.length &&
                        <Empty />
    				}
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	templateStore: state.template,
	policyDetailStore: state.policyDetail
}))(AddToCustomListModal);
