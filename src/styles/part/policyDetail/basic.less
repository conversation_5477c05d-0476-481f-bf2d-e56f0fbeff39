@import "../../base/variables";

:global {
	.policy-detail-body {
		.basic-form {
			& {
				width: 600px;
			}
			.ant-row {
				& {
					margin-bottom: 12px;
				}
				.basic-info-title {
					text-align: right;
					line-height: 32px;
				}
				.basic-info-btns {
					button {
						margin-right: 12px;
					}
				}
			}
			.ant-slider {
				.ant-slider-rail {
					background: #dcdcdc;
				}
			}
			.ant-select-selection-selected-value, .ant-input-disabled {
				color: #666;
			}
			.ant-select-disabled .ant-select-selection, .ant-input-disabled {
				background: #e6e6e6;
			}
		}
		.no-rule-tip {
			background: url("../../../sources/images/policy/noneRuleTip.png") no-repeat center center scroll transparent;
			height: 150px;
			margin-top: 30px;
		}
	}
}

