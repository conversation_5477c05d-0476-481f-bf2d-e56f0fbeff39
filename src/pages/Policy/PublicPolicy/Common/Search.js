import { PureComponent } from "react";
import { connect } from "dva";
import { Row, Col, Input, Button, Select, Icon, TreeSelect } from "antd";
import { publicPolicyListLang } from "@/constants/lang";
import "./Search.less";

class Search extends PureComponent {
	state = {
		treeData: [],
		eventNames: [],
		treeWidth: 0
	}
	toggle = () => {
		this.setState({
			expand: !this.state.expand
		}, () => {
			const treeWidth = document.getElementById("treeSelect") && document.getElementById("treeSelect").clientWidth;
			this.setState({
				treeWidth
			});
		});
	}
	componentWillMount() {
		let { globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		let treeData = [];
		publicPolicyScene && publicPolicyScene.length > 0 && publicPolicyScene.map((v) => {
			const scene = {};
			const { eventList = [] } = v;
			scene.title = v.dName;
			scene.value = v.name;
			scene.key = v.name;
			scene.children = [];
			eventList.map((child) => {
				scene.children.push({
					title: child.dName,
					value: child.name,
					key: child.name
				});
			});
			treeData.push(scene);
		});
		this.setState({
			treeData
		});
	}
	componentDidMount() {
		if (document.getElementById("treeSelect")) {
			this.setState({
				treeWidth: document.getElementById("treeSelect").clientWidth
			});
		}
	}
	getFields = () => {
		const { treeData, eventNames } = this.state;
		let { treeWidth } = this.state;
		const { changeSearchField, searchData = {}, globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyRiskSelect = [] } = allMap || {};
		let tagsNum = 0;
		if (treeWidth > 0) {
			treeWidth = treeWidth - 10 - 20 - 16;
			eventNames.map(v => {
				console.log(treeWidth);
				if (treeWidth > 0) {
					if ((treeWidth - (v.length * 14 + 30 + 4)) > 50) {
						tagsNum++;
					}
					treeWidth = treeWidth - (v.length * 14 + 30 + 4);
				}
			});
		}
		return (
			[
				<Col span={6}>
					<Input
						onChange={(e) => {
							changeSearchField("policyName", e, "input");
						}}
						value={searchData.policyName || undefined}
						placeholder={publicPolicyListLang.searchParams("publicPolicyName")} // 请输入公共策略名称
					/>
				</Col>,
				<Col
					// span={searchExpand ? 12 : 6}
					span={6}
					id="treeSelect"
				>
					<TreeSelect
						dropdownStyle={{ "maxHeight": "400px" }}
						allowClear
						className="wid-100-percent"
						treeData={treeData}
						treeCheckable={true}
						searchPlaceholder={publicPolicyListLang.searchParams("effectScope")} // 请选择生效范围
						onChange={(e, b) => {
							this.setState({
								eventNames: b
							});
							changeSearchField("eventIds", e);
						}}
						value={searchData.eventIds || undefined}
						maxTagCount={tagsNum}
						maxTagPlaceholder="..."
					/>
				</Col>,
				<Col span={6}>
					<Input
						placeholder={publicPolicyListLang.searchParams("ruleName")} // 请输入规则名称
						onChange={(e) => {
							changeSearchField("ruleName", e, "input");
						}}
						value={searchData.ruleName || undefined}
					/>
				</Col>,
				<Col span={6}>
					<Input
						placeholder={publicPolicyListLang.searchParams("ruleCustomId")} // 请输入规则编号
						onChange={(e) => {
							changeSearchField("ruleCustomId", e, "input");
						}}
						value={searchData.ruleCustomId || undefined}
					/>
				</Col>,
				<Col span={6}>
					<Select
						className="wid-100-percent"
						placeholder={publicPolicyListLang.searchParams("riskType")} // 风险类型
						showSearch
						dropdownMatchSelectWidth={false}
						optionFilterProp="children"
						onChange={(e) => {
							changeSearchField("riskType", e);
						}}
						value={searchData.riskType || undefined}
					>
						{
							publicPolicyRiskSelect &&
							publicPolicyRiskSelect.map((item, index) => {
								return (
									<Option value={item.name} key={index}>
										{item.dName}
									</Option>
								);
							})
						}
					</Select>
				</Col>
			]
		);
	}
	render() {
		let fieldsDom = this.getFields();
		const { reset, search, searchExpand, toggle } = this.props;
		const count = searchExpand ? fieldsDom.length : 3;
		const newFieldsDom = fieldsDom.slice(0, count);
		return (
			<Row gutter={10} className="search-wrap">
				{newFieldsDom}
				<Col span={6} className="search-btn-wrap">
					<Button onClick={reset}>
						{/* 重置 */}
						{publicPolicyListLang.searchParams("reset")}
					</Button>
					<Button onClick={search} type="primary" style={{ "marginLeft": "12px" }}>
						{/* 查询 */}
						{publicPolicyListLang.searchParams("search")}
					</Button>
					<a className="btn-expand" onClick={toggle}>
						{/* 展开  */}
						{publicPolicyListLang.searchParams("expand")}
						<Icon type={searchExpand ? "up" : "down"} />
					</a>
				</Col>
			</Row>
		);
	}
};
export default connect(state => ({
	globalStore: state.global
}))(Search);
