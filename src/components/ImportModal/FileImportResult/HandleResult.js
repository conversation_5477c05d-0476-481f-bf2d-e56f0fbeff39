import { imExportModeLang } from "@/constants/lang";
import { Fragment } from "react";
export default (props) => {
	const { policyResults = [], ruleResults = [], zbResults = [] } = props;
	return (
		<Fragment>
			{/* 策略处理结果 */}
			{
				policyResults &&
				policyResults.length > 0 &&
				<table className="handle-result mb10">
					<thead>
						<tr>
							{/* 策略编号 */}
							<th width="250px">{imExportModeLang.importInfo("policyNo")}</th>
							{/* 策略名称 */}
							<th width="250px">{imExportModeLang.importInfo("policyName")}</th>
							{/* 处理模式 */}
							<th>{imExportModeLang.importInfo("mode")}</th>
						</tr>
					</thead>
					<tbody>
						{
							policyResults.map((v) => {
								return (
									<tr>
										<td> {v.id} </td>
										<td> {v.name} </td>
										<td> {v.msg} </td>
									</tr>
								);
							})
						}
					</tbody>
				</table>
			}
			{/* 规则处理结果 */}
			{
				ruleResults &&
				ruleResults.length > 0 &&
				<table className="handle-result mb10">
					<thead>
						<tr>
							{/* 规则编号 */}
							<th width="250px">{imExportModeLang.importInfo("ruleNo")}</th>
							{/* 规则名称 */}
							<th width="250px">{imExportModeLang.importInfo("ruleName")}</th>
							{/* 处理模式 */}
							<th>{imExportModeLang.importInfo("mode")}</th>
						</tr>
					</thead>
					<tbody>
						{
							ruleResults.map((v) => {
								return (
									<tr>
										<td> {v.id} </td>
										<td> {v.name} </td>
										<td> {v.msg} </td>
									</tr>
								);
							})
						}
					</tbody>
				</table>
			}
			{/* 指标处理结果 */}
			{
				zbResults &&
				zbResults.length > 0 &&
				<table className="handle-result mb10">
					<thead>
						<tr>
							{/* 指标编号 */}
							<th width="250px">{imExportModeLang.importInfo("zbNo")}</th>
							{/* 指标名称 */}
							<th width="250px">{imExportModeLang.importInfo("zbName")}</th>
							{/* 处理模式 */}
							<th>{imExportModeLang.importInfo("mode")}</th>
						</tr>
					</thead>
					<tbody>
						{
							zbResults.map((v) => {
								return (
									<tr>
										<td>{v.id} </td>
										<td>{v.name} </td>
										<td> {v.msg} </td>
									</tr>
								);
							})
						}
					</tbody>
				</table>
			}
		</Fragment>
	);
};
