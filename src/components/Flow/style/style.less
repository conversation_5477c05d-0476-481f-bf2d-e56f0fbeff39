:global {
    .flow-editor {
        display: flex;
        flex: 1;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
    }

    .flow-editor-bd {
        flex: 1;
    }

    .flow-editor-content {
        & {
            height: 100%;
        }

        .flow-editor-hd {
            height: 48px;
            line-height: 48px;
            border-bottom: 1px solid #e8e8e8;
        }

        .flow,
        .flow .graph-container {
            height: ~"calc(100% - 48px)";
            flex: 1;
        }
    }

    .flow-editor-sidebar {
        display: flex;
        flex-direction: column;
    }

    .flow-editor-sidebar {
        & {
            background: #fafafa;
            height: 100%;
        }

        &:first-child {
            border-right: 1px solid #E6E9ED;
        }

        &:last-child {
            border-left: 1px solid #E6E9ED;
        }
    }

    .flow {
        flex: 1;
    }

}
