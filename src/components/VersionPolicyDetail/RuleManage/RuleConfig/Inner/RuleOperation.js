import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Select, Row, Col } from "antd";
import { CommonConstants } from "@/constants";
import { policyDetailLang } from "@/constants/lang";

const Option = Select.Option;
const InputGroup = Input.Group;

class RuleOperation extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { globalStore, currentRule } = this.props;
		let { allMap } = globalStore;

		return (
			<div>
				{
					currentRule &&
                    currentRule.operationActions &&
                    currentRule.operationActions.map((item, index) => {
                    	return (
                    		<Row gutter={CommonConstants.gutterSpan} className="rule-operation-item" key={index}>
                    			<Col span={4} className="basic-info-title">
                    				{/* 设置： */}
                    				{policyDetailLang.ruleOperation("setting")}：
                    			</Col>
                    			<Col span={5}>
                    				<Select
                    					value={item.left || undefined}
                    					placeholder={policyDetailLang.ruleOperation("select")} // 请选择
                    					disabled={true}
                    				>
                    					{
                    						allMap &&
                                            allMap["ruleFieldList"] &&
                                            allMap["ruleFieldList"].map((item, index) => {
                                            	return (
                                            		<Option value={item.name} key={index}>
                                            			{item.dName}
                                            		</Option>
                                            	);
                                            })
                    					}
                    				</Select>
                    			</Col>
                    			<Col span={3} className="basic-info-text">
                    				{/* 等于 */}
                    				<Select value={policyDetailLang.ruleOperation("equal")} disabled={true}></Select>
                    			</Col>
                    			<Col span={8} className="basic-info-text">
                    				{
                    					item.rightType === "constant" &&
                                        <InputGroup compact>
                                        	<Select
                                        		value={item["rightType"] || undefined}
                                        		style={{ width: "30%" }}
                                        		placeholder={policyDetailLang.ruleOperation("select")} // 请选择
                                        		disabled={true}
                                        	>
                                        		<Option value="constant">
                                        			{/* 常量 */}
                                        			{policyDetailLang.ruleOperation("constant")}
                                        		</Option>
                                        		<Option value="variable">
                                        			{/* 变量 */}
                                        			{policyDetailLang.ruleOperation("variable")}
                                        		</Option>
                                        	</Select>
                                        	<Input
                                        		style={{ width: "70%", textAlign: "left" }}
                                        		value={item.right || undefined}
                                        		placeholder={policyDetailLang.ruleOperation("inputConstant")} // 请填写常量内容
                                        		disabled={true}
                                        	/>
                                        </InputGroup>
                    				}
                    				{
                    					item.rightType === "variable" &&
                                        <InputGroup compact>
                                        	<Select
                                        		value={item["rightType"] || undefined}
                                        		style={{ width: "30%" }}
                                        		placeholder={policyDetailLang.ruleOperation("select")} // 请选择
                                        		disabled={true}
                                        	>
                                        		<Option value="constant">
                                        			{/* 常量 */}
                                        			{policyDetailLang.ruleOperation("constant")}
                                        		</Option>
                                        		<Option value="variable">
                                        			{/* 变量 */}
                                        			{policyDetailLang.ruleOperation("variable")}
                                        		</Option>
                                        	</Select>
                                        	<Select
                                        		style={{ width: "70%"}}
                                        		value={item.right || undefined}
                                        		placeholder={policyDetailLang.ruleOperation("select")} // 请选择
                                        		disabled={true}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["ruleFieldList"] &&
                                                    allMap["ruleFieldList"].filter(fItem => {
                                                    	return item.leftFieldType ? fItem.type === item.leftFieldType : true;
                                                    }).map((item, index) => {
                                                    	return (
                                                    		<Option value={item.name} key={index}>
                                                    			{policyDetailLang.ruleOperation("current")} {item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </InputGroup>
                    				}
                    			</Col>
                    		</Row>
                    	);
                    })
				}
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(RuleOperation);
