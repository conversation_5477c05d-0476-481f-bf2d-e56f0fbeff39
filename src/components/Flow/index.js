import { PureComponent } from "react";
import { Row, Col, Select, Tag, message, Button } from "antd";
import { connect } from "dva";
import GGEditor, {
	Flow,
	RegisterNode,
	Command,
	ContextMenu,
	NodeMenu,
	EdgeMenu,
	MultiMenu,
	CanvasMenu
} from "gg-editor";
import { isJSON } from "@/utils/isJSON";
import { PolicyConstants, FlowConstants } from "@/constants";
import { workflowAPI } from "@/services";
import FlowToolbar from "./FlowToolbar";
import FlowItemPanel from "./FlowItemPanel";
import FlowDetailPanel from "./FlowDetailPanel";
import { checkFunctionHasPermission } from "@/utils/permission";
import { commonLang, workflowLang } from "@/constants/lang";
import "./style/style.less";
import "./style/menu.less";

GGEditor.setTrackable(false); // 关闭g6的埋点数据

const Option = Select.Option;

class FlowPage extends PureComponent {
	state = {
		currentVersion: null
	};

	constructor(props) {
		super(props);
		this.getFlowData = this.getFlowData.bind(this);
		this.initGraph = this.initGraph.bind(this);
		this.getWorkflowByVersion = this.getWorkflowByVersion.bind(this);
		this.switchWorkflowVersion = this.switchWorkflowVersion.bind(this);
		this.getWorkflowFromEditor = this.getWorkflowFromEditor.bind(this);
		props.bindThis(this);
	}

	componentDidMount() {
		this.initGraph();
	}

	initGraph(flowDataProps) {
		let { workflowStore } = this.props;
		let { flowData } = workflowStore;

		if (flowDataProps) {
			flowData = flowDataProps;
		}
		const newFlowData = {...flowData};
		const { nodes, edges } = newFlowData;
		console.log(newFlowData);
		// 对历史数据中开始、结束等节点设置节点label颜色
		const exclusivity = {};
		nodes.map((node)=>{
			if (node.color) {
				delete node.color;
			}
			if (["flow-start", "flow-end", "flow-exclusivity"].indexOf(node.shape) > -1) {
				if (typeof node.label === "string") {
					node.label = {
						text: node.label,
						fill: "#fff"
					};
				}
				if (node.shape === "flow-exclusivity") {
					exclusivity[node.id] = true;
				}
			} else {
				if (typeof node.label === "string") {
					node.label = {
						text: node.label
					};
				}
			}
			return node;
		});
		edges.map((edge)=>{
			const { source, conditionsGroup = {}, label} = edge || {};
			const { children = [] } = conditionsGroup || {};
			// 基于排他的连接线设置
			if (exclusivity[source]) {
				if (!(children.length > 0 && label)) {
					edge.style = {
						stroke: "#FF0000"
					};
				}
				edge.labelRectStyle = {
					fill: "#f2f3f4"
				};
			}
			return edge;
		});
		this.page.changeAddEdgeModel({
			// 将线设置为圆角折线
			shape: "flow-smooth",
			labelRectStyle: {
				fill: "#f2f3f4"
			}
		});
		this.page.read(newFlowData);
		this.page.getItems();
	}

	renderFlow(isApprovalPage) {
		let grid = {
			cell: 20,
			line: {
				fill: "#f0f2f5",
				stroke: "#dcdcdc",
				shape: "square"
			}
		};
		let align = {
			item: true,
			grid: true
		};
		return (
			<Flow
				className="flow"
				grid={grid}
				ref={g => (this.page = g ? g.page : null)}
				align={align}
				item={true}
				noEndEdge={false} // 是否支持悬空边
				onEdgeClick={(e) => {
					const { shapeObj, model = {} } = e.item;
					const {style} = model || {};
					// 连接线修改选中效果
					if (shapeObj && shapeObj.getSelectedStyle) {
						if (style && Object.keys(style).length > 0) {
							shapeObj.getSelectedStyle = FlowConstants.warnEdgeStyle;
						} else {
							shapeObj.getSelectedStyle = FlowConstants.defaultEdgeSelectedStyle;
						}
					};
				}}
				onEdgeMouseEnter={(e)=>{
					// 连接线修改鼠标掠过效果
					const { shapeObj, model = {} } = e.item;
					const {style} = model || {};
					if (shapeObj && shapeObj.getActivedStyle) {
						if (style && Object.keys(style).length > 0) {
							shapeObj.getActivedStyle = FlowConstants.warnEdgeStyle;
						} else {
							shapeObj.getActivedStyle = FlowConstants.defaultEdgeActivedStyle;
						}
					};
				}}
				onDragStart={(e) => {
					if (e.currentShape && e.currentShape.getItem && e.currentShape.getItem()) {
						const { model } = e.currentShape.getItem();
						if (model && model.shape) {
							this.currentNode = model;
						}
					}
					if (isApprovalPage) {
						e.domEvent.stopPropagation();
						e.domEvent.preventDefault();
						return;
					}
				}}
			/>
		);
	}

	getFlowData() {
		let flowData = this.page.getItems();
		return flowData;
	}

	getWorkflowByVersion(version) {
		let { workflowStore, dispatch } = this.props;
		let { policySetItem } = workflowStore;
		let params = {
			policySetUuid: policySetItem.uuid,
			flowVersion: version
		};

		this.setState({
			currentVersion: version
		});

		workflowAPI.getWorkflowByVersion(params).then(res => {
			if (res.success) {
				message.success(workflowLang.message("changeRuleStreamSuccess")); // lang:切换规则流成功

				let flowData = {
					nodes: [],
					edges: []
				};
				let processContentStr = res.data && res.data.processContent;
				flowData = isJSON(processContentStr) ? JSON.parse(processContentStr) : flowData;

				dispatch({
					type: "workflow/setAttrValue",
					payload: {
						flowData: flowData,
						exportFlowVersion: version		// 请求成功后将当前version信息放到model里面，后面导出的时候用到。
					}
				});
				this.page.read(flowData);
				this.page.getItems();
			} else {
				message.error(res.message);
			}
		});
	}

	async getWorkflowFromEditor() {
		let { workflowStore, dispatch } = this.props;
		let { policySetItem } = workflowStore;

		// 根据策略集获取规则流数据
		await dispatch({
			type: "workflow/getDecisionFlow",
			payload: {
				policySetUuid: policySetItem.uuid
			}
		});

		await this.initGraph();
	}

	switchWorkflowVersion() {
		let { currentVersion } = this.state;
		let { workflowStore } = this.props;
		let { policySetItem } = workflowStore;

		if (!currentVersion) {
			currentVersion = policySetItem && policySetItem.decisionFlowVersion;
		}
		let params = {
			policySetUuid: policySetItem.uuid,
			flowVersion: currentVersion
		};

		workflowAPI.switchWorkflowVersion(params).then(res => {
			if (res.success) {
				message.success(workflowLang.message("changeEditRuleStreamSuccess")); // lang:"规则流编辑区切换成功
			} else {
				message.error(res.message);
			}
		});
	}

	// 设置节点样式
	nodeConfig = ({fill, lineWidth, stroke}) => {
		return (
			{
				getStyle() {
					return {
						fill,
						lineWidth,
						stroke,
						opacity: 1
					};
				},
				getActivedStyle() {
					return {
						fill,
						lineWidth,
						stroke,
						opacity: 1
					};
				},
				getSelectedStyle() {
					return {
						fill,
						lineWidth,
						stroke,
						opacity: 1
					};
				}
			}
		);
	}

	render() {
		let { workflowStore, disabled, isApprovalPage } = this.props;
		let { pageName, status, workflowVersionList, policySetItem } = workflowStore;
		let workflowStatusObj;
		if (status && PolicyConstants.policyStatusMap[status]) {
			workflowStatusObj = PolicyConstants.policyStatusMap[status];
		}
		return (
			<GGEditor
				className="flow-editor"
				ref={g => (this.editor = g ? g.editor : null)}
				onBeforeCommandExecute={({ command }) => {
					// 如果节点类型是线条 来源是排他开始 则默认线条颜色为红色需要配置条件
					if (command.type === "edge" && this.currentNode) {
						const { shape, nodeType } = this.currentNode || {};
						if (shape === "flow-exclusivity" && nodeType === "CONDITION_START") {
							command.addModel.style = {
								stroke: "#FF0000"
							};
						}
					}
					// if (isApprovalPage) {
					// 	command.enable();
					// }
				}}
				onAfterCommandExecute={({ command }) => {
					const { selectedItems = [], name, pasteData } = command;
					if (pasteData && pasteData.length > 0 && name === "paste") {
						pasteData.forEach(item=>{
							this.editor.getCurrentPage().setSelected(item.model.id, true);
						});
					}
					// 排他结束无需配置线条条件 颜色也不需要飘红
					if (selectedItems && selectedItems.length > 0 && name === "common") {
						const items = this.page.getItems();
						const id = selectedItems[0];
						const node = items.find(v=>(
							v.model.id === id && v.model.shape === "flow-exclusivity"
						));
						const edge = node ? items.find(v=>{
							return v.type !== "node" && v.source.id === id;
						}) : null;
						if (edge && node && node.model && node.model.shape === "flow-exclusivity") {
							console.log(command);
							if (node.model.nodeType === "CONDITION_START") {
								const { label, conditionsGroup = {} } = edge.model || {};
								if (!(label && conditionsGroup && conditionsGroup.children && conditionsGroup.children.length > 0)) {
									edge.model.style = {"stroke": "#f00"};
									edge.shapeObj.getActivedStyle = FlowConstants.warnEdgeStyle;
								}
							} else {
								edge.model.style = {};
								edge.shapeObj.getActivedStyle = FlowConstants.defaultEdgeActivedStyle;
							}
						}
					}
				}}
			>
				<Row type="flex" className="flow-editor-bd">
					{
						!isApprovalPage &&
						<Col span={3} className="flow-editor-sidebar">
							<FlowItemPanel disabled={disabled} />
						</Col>
					}
					<Col span={isApprovalPage ? 18 : 15} className="flow-editor-content">
						<Row type="flex" className="flow-editor-hd">
							<Col span={24}>
								<FlowToolbar
									disabled={disabled}
									isApprovalPage={isApprovalPage}
									status={status}
								/>
								{
									!isApprovalPage &&
									<div className="toolbar-right">
										{
											pageName &&
											pageName === "policyRunning" &&
											checkFunctionHasPermission("ZB0101", "decisionFlowHistory") &&
											<div className="toolbar-right-item">
												<Select
													defaultValue={policySetItem && policySetItem.decisionFlowVersion}
													onChange={this.getWorkflowByVersion.bind(this)}
												>
													{
														workflowVersionList.map((item, index) => {
															let statusText = item.status === "online" ? "正式版本" : "历史版本";
															return (
																<Option value={item.flowVersion}>
																	[{statusText}] V{item.flowVersion}
																</Option>
															);
														})
													}
												</Select>
											</div>
										}
										{
											!disabled &&
											workflowStatusObj &&
											<div className="toolbar-right-item">
												<Tag color={workflowStatusObj.color}>
													{workflowStatusObj.text}
												</Tag>
											</div>
										}
										{
											disabled &&
											status === "wait_review" &&
											workflowStatusObj &&
											<div className="toolbar-right-item">
												<Tag color={workflowStatusObj.color}>
													{workflowStatusObj.text}
												</Tag>
											</div>
										}
										{
											disabled &&
											status !== "wait_review" &&
											<div className="toolbar-right-item">
												{
													checkFunctionHasPermission("ZB0101", "decisionFlowSwitch") &&
													<Button
														type="primary"
														onClick={() => {
															if (checkFunctionHasPermission("ZB0101", "decisionFlowSwitch")) {
																this.switchWorkflowVersion();
															} else {
																// lang:无权限操作
																message.warning(commonLang.messageInfo("noPermission"));
															}
														}}
													>
														{/* lang:覆盖回编辑区*/}
														{workflowLang.common("overwriteBack")}
													</Button>
												}
											</div>
										}
									</div>
								}
							</Col>
						</Row>
						{this.renderFlow(isApprovalPage)}
					</Col>
					<Col span={6} className="flow-editor-sidebar">
						<FlowDetailPanel disabled={disabled} />
					</Col>
				</Row>
				<RegisterNode name="flow-start" extend="flow-circle" config={this.nodeConfig({fill: "#ca808b", lineWidth: 1, stroke: "#ca808b"})} />
				<RegisterNode name="flow-end" extend="flow-circle" config={this.nodeConfig({fill: "#748993", lineWidth: 1, stroke: "#748993"})}/>
				<RegisterNode name="flow-exclusivity" extend="flow-rhombus" config={this.nodeConfig({fill: "#f1bba9", lineWidth: 1, stroke: "#f1987a"})}/>
				<RegisterNode name="flow-parallel" extend="flow-rhombus" config={this.nodeConfig({fill: "#c6e5d6", lineWidth: 1, stroke: "#69c9a4"})}/>
				<RegisterNode name="flow-policy" extend="flow-capsule" config={this.nodeConfig({fill: "#d9dffc", lineWidth: 1, stroke: "#bbc5f7"})}/>
				<RegisterNode name="flow-model" extend="flow-capsule" config={this.nodeConfig({fill: "#bdd9fc", lineWidth: 1, stroke: "#9bd3fa"})}/>
				<RegisterNode name="flow-data" extend="flow-capsule" config={this.nodeConfig({fill: "#7ab6f1", lineWidth: 1, stroke: "#64aef5"})}/>
				<RegisterNode name="flow-public-policy" extend="flow-capsule" config={this.nodeConfig({fill: "#c4dfa7", lineWidth: 1, stroke: "#8BC34A"})}/>
				{/* 节点右键菜单 */}
				{
					!disabled &&
					<ContextMenu className="flow-editor-menu-wrap">
						<NodeMenu>
							<Command name="copy">
								{/* lang:复制*/}
								{workflowLang.menuAction("copy")}
							</Command>
							<Command name="delete">
								{/* lang:删除*/}
								{workflowLang.menuAction("delete")}
							</Command>
							<hr />
							<Command name="toFront">
								{/* lang:向上一层*/}
								{workflowLang.menuAction("upLayer")}
							</Command>
							<Command name="toBack">
								{/* lang:向下一层*/}
								{workflowLang.menuAction("downLayer")}
							</Command>
						</NodeMenu>
						<EdgeMenu>
							<Command name="delete">
								{/* lang:删除*/}
								{workflowLang.menuAction("delete")}
							</Command>
						</EdgeMenu>
						<MultiMenu>
							<Command name="copy">
								{/* lang:复制*/}
								{workflowLang.menuAction("copy")}
							</Command>
							<Command name="delete">
								{/* lang:删除*/}
								{workflowLang.menuAction("delete")}
							</Command>
						</MultiMenu>
						<CanvasMenu>
							<Command name="selectAll">
								{/* lang:全选*/}
								{workflowLang.menuAction("allSelect")}
							</Command>
							<Command name="paste">
								{/* lang:粘贴*/}
								{workflowLang.menuAction("paste")}
							</Command>
							<hr />
							<Command name="undo">
								{/* lang:撤销*/}
								{workflowLang.menuAction("revoke")}
							</Command>
							<Command name="redo">
								{/* lang:重做*/}
								{workflowLang.menuAction("redo")}
							</Command>
							<hr />
							<Command name="resetZoom">
								{/* lang:实际尺寸*/}
								{workflowLang.menuAction("realSize")}
							</Command>
							<Command name="autoZoom">
								{/* lang:自适应尺寸*/}
								{workflowLang.menuAction("adaptiveSize")}
							</Command>
						</CanvasMenu>
					</ContextMenu>
				}
			</GGEditor>
		);
	}
}

export default connect(state => ({
	policyEditorStore: state.policyEditor,
	workflowStore: state.workflow
}))(FlowPage);
