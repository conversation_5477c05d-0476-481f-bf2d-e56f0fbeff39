.total-import-data{
    margin-bottom:20px;
    i{
        margin-right:12px;
        color:#87d068;
        font-size:26px;
    }
    b{
        font-size:16px;
        display: inline-block;
        margin:0 4px;
    }
}
.ant-collapse.policy-import-collapse{
	.ant-collapse-content{
		border-top:1px solid #EBEEF5;
	}
    .ant-collapse-content > .ant-collapse-content-box{
        padding: 8px 16px;
	}
	> .ant-collapse-item{
		&:last-of-type{
			border-bottom:0;
		}
	}
    > .ant-collapse-item > .ant-collapse-header{
        padding-top:8px;
        padding-bottom:8px;
    }
}

.filed-num {
    label{
        display: inline-block;
        margin:auto 4px;
        color:#1890ff;
    }
}
.import-field-ul{
    padding:0;
    list-style-type: none;
    margin:0 auto 0 24px;
    &.wid-50{
        li > div{
            width:50%;
        }
    }
    &.width-30{
        li > div{
            width:33.33%;
        }
    }
    li{
        width:100%;
        height:30px;
        line-height: 30px;
        border-bottom:1px dashed #e6e6e6;
        &:first-of-type{
            font-weight: bold;
            font-size:15px;
        }
        &:last-of-type{
            border:none;
        }
        display: flex;
        >div{
            flex:1;
            padding-right:16px;
            display: block;
            max-width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
    }
}
.down-csv{
    height:auto;
    overflow:hidden;
    margin:auto 0 0 auto;
    text-align: right;
}

table.handle-result {
	table-layout: fixed;
	width: 100%;
	font-family: verdana, arial, sans-serif;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.45);
	border-width: 1px;
	border-color: #d9d9d9;
	border-collapse: collapse;
	th {
		border-width: 1px;
		padding: 5px 8px;
		border-style: solid;
		border-color: #d9d9d9;
		background-color: #ededed;
		font-weight: normal;
		color: #777;
	}
	td{
		border: 1px solid #d9d9d9;
		padding: 5px 8px;
		background-color: #fff;
	}
}
