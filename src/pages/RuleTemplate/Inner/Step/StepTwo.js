import { PureComponent } from "react";
import { connect } from "dva";
import { message } from "antd";
import { templateAPI } from "@/services";
import LeftNavCon from "../Other/LeftNavCon";
import RightContent from "../Other/RightContent";

class StepTwo extends PureComponent {
	constructor(props) {
		super(props);
		this.changeHandle = this.changeHandle.bind(this);
		this.modifyTemplate = this.modifyTemplate.bind(this);
	}

	changeHandle(field, type, e) {
		let { dispatch, templateStore } = this.props;
		let { modifyTemplateData } = templateStore.dialogData;
		let value = "";
		if (type === "input" || type === "textarea") {
			value = e.target.value;
		}
		modifyTemplateData[field] = value;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	modifyTemplate() {
		let { templateStore, dispatch } = this.props;
		let { currentType, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		templateAPI.modifyTemplate(modifyTemplateData).then((res) => {
			if (res.success) {
				dispatch({
					type: "template/getTemplateList",
					payload: {
						type: currentType
					}
				});
				message.success("更新模板成功");
				dispatch({
					type: "template/setDialogShow",
					payload: {
						modifyTemplate: false
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {

		return (
			<div className="config-step-wrap">
				<div className="left-nav">
					<LeftNavCon />
				</div>
				<div className="right-content">
					<RightContent />
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(StepTwo);
