import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Select, Row, Col, Checkbox, Popover } from "antd";
// import IndexMore from "./IndexMore";
import { CommonConstants, PolicyConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import { isJSON } from "@/utils/isJSON";
import { getSimpleCfgList, getHandleType } from "@/utils/salaxy";
import FieldList from "./FieldSet/FieldList";
import FormulaEditor from "./FormulaEditor";
import ComponentGroup from "../../Common/ComponentGroup";
import WrapperCol from "../../Common/WrapperCol/index";

const InputGroup = Input.Group;
const Option = Select.Option;
const TextArea = Input.TextArea;

class IndexCondition extends PureComponent {
	state = {};

	constructor(props) {
		super(props);
		this.changeBaseField = this.changeBaseField.bind(this);
	}
	changeBase<PERSON>ield(field, type, isAttachField, e) {
		let { indexEditorStore, templateStore, globalStore, dispatch, templateName } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let { allMap } = globalStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		let attachFieldsObj = currentIndexObj && currentIndexObj.attachFieldsObj ? currentIndexObj.attachFieldsObj : null;
		// 这里处理模板信息
		let { indexTemplateListObj } = templateStore;
		let currentTemplate = indexTemplateListObj && templateName ? indexTemplateListObj[templateName] : null;
		let cfgJson = currentTemplate && currentTemplate.cfgJson && isJSON(currentTemplate.cfgJson) ? JSON.parse(currentTemplate.cfgJson) : null;

		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		} else if (type === "checkbox") {
			value = e ? e.toString() : "";
		} else if (type === "variableType") {
			value = e;
		}
		// 暂时先写死，后面排查模版中为什么没生效
		if (field === "focusUnit" || field === "focusType") {
			attachFieldsObj.ranges = [];
		}

		if (isAttachField) {
			attachFieldsObj[field] = value;
		} else {
			currentIndexObj[field] = value;
		}
		currentIndexObj["hasModify"] = true;

		if (field === "hasDimReadCheckbox") {
			currentIndexObj["dimRead1"] = "";
		}
		// 这里处理规则
		let simpleCfgList = getSimpleCfgList(cfgJson, currentIndexObj);
		let currentSimpleObj = simpleCfgList.find(fItem => {
			let name = fItem.name;
			if (name.indexOf("attachFields.") > -1) {
				name = name.split("attachFields.")[1];
			}
			return name === field;
		});

		if (currentSimpleObj) {
			if (currentSimpleObj["willChangeOther"] && currentSimpleObj["willChangeOther"].length > 0) {
				// 这里处理改变其他规则
				currentSimpleObj["willChangeOther"].map((ruleItem) => {
					if (ruleItem["changeMode"] && ruleItem["changeMode"] === "whenSomeValue") {
						// 特定值时改变
						// 如果当前修改的值在特定列表中，则需要对目标值进行修改
						if (ruleItem["modeValueList"] && ruleItem["modeValueList"].length > 0) {
							if (ruleItem["modeValueList"].find(mvItem => mvItem === value)) {
								// 如果命中，则执行改变
								let valueChangeTo = ruleItem.valueChangeTo ? ruleItem.valueChangeTo : null;
								let finalValue = changeType && changeType === "emptyValue" ? null : valueChangeTo;
								// 层层判断得出最后的值
								const { changeType, childNodeName, name } = ruleItem; // 改变类型
								// changeChildValue 为改变子节点 其他则为同一级节点变更
								if (changeType !== "changeChildValue") {
									if (name.indexOf("attachFields.") > -1) {
										let attName = name.split("attachFields.")[1];
										attachFieldsObj[attName] = finalValue;
									} else {
										currentIndexObj[name] = finalValue;
									}
								} else {
									const groupData = currentIndexObj[name] || [];
									if (groupData && groupData.length > 0) {
										groupData.forEach(groupItem => {
											groupItem[childNodeName] = finalValue;
										});
									}
								}
							}
						}
					} else if (ruleItem["changeMode"] && ruleItem["changeMode"] === "whenSomeType") {
						// 特定类型时改变：如果当前是select并且select的type为特定
						let type = getHandleType(currentSimpleObj, allMap);

						if (ruleItem["modeValueList"] && ruleItem["modeValueList"].length > 0) {
							if (ruleItem["modeValueList"].find(mvItem => mvItem === type)) {
								let { changeType, valueChangeTo, name, childNodeName } = ruleItem;
								// 如果命中，则执行改变
								valueChangeTo = valueChangeTo ? valueChangeTo : null;
								// 层层判断得出最后的值
								let finalValue = changeType && changeType === "emptyValue" ? null : valueChangeTo;

								if (changeType !== "changeChildValue") {
									if (name.indexOf("attachFields.") > -1) {
										let attName = name.split("attachFields.")[1];
										attachFieldsObj[attName] = finalValue;
									} else {
										currentIndexObj[name] = finalValue;
									}
								} else {
									const groupData = currentIndexObj[name] || [];
									if (groupData && groupData.length > 0) {
										groupData.forEach(groupItem => {
											groupItem[childNodeName] = finalValue;
										});
									}
								}
							}
						}
					}
				});
			}
		}
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				editIndexMap: editIndexMap
			}
		});
	}

	// 详情提示
	popoverTitle = (tipTitle, tipContentArr) => {
		return (
			<Col span={1}>
				<Popover
					popupClassName="rule-param-pop-tip"
					placement="right"
					title={tipTitle}
					content={
						tipContentArr.map((tip, tipIndex) => {
							return (
								<div key={tipIndex}>{tip}</div>
							);
						})
					}
				>
					<Icon
						type="question-circle-o"
						className="param-tip-icon"
					/>
				</Popover>
			</Col>
		);
	}

	changeSelfEvent = (willChangeSelf, simpleCfgList) => {
		let { globalStore } = this.props;
		let { allMap } = globalStore;
		// 获取handle 名称
		let changeHandleName = willChangeSelf && willChangeSelf.name ? willChangeSelf.name : null;
		// 获取handle 实体
		let changeHandleObj = changeHandleName ? simpleCfgList.find(fItem => fItem.name === changeHandleName) : null;
		// 获取handle value
		let changeHandleValue = changeHandleObj ? changeHandleObj.value : null;
		/*
		* 预先设置如下几个变量
		* ruleDisabled
		* ruleHidden
		* ruleSelectMap
		* ruleMapLocation
		* */
		let ruleDisabled = false;
		let ruleHidden = false;
		let ruleMapName = null;
		let ruleMapLocation = null;

		if (willChangeSelf) {
			if (willChangeSelf.changeMode && willChangeSelf.changeMode === "whenSomeValue") {
				// 当为具体值的时候
				const { caseList } = willChangeSelf || {};
				caseList && caseList.map((caseItem) => {
					let ChangeHandleByChild = null;
					if (caseItem.changeType === "hiddenByChild" && Array.isArray(changeHandleValue)) {
						changeHandleValue.forEach((changeItem) => {
							ChangeHandleByChild = ChangeHandleByChild || changeItem[caseItem.childNodeName];
						});
					}
					if (
						caseItem["modeValueList"] &&
						caseItem["modeValueList"].find(mvItem => (mvItem === changeHandleValue || mvItem === String(changeHandleValue) || mvItem === ChangeHandleByChild))
					) {
						// 如果modeValueList列表中确实有handle value，则进行如下操作
						if (caseItem.changeType && caseItem.changeType === "disabled") {
							ruleDisabled = true;
						} else if (caseItem.changeType && (caseItem.changeType === "hidden" || caseItem.changeType === "hiddenByChild")) {
							ruleHidden = true;
						} else if (caseItem.changeType && caseItem.changeType === "selectMap") {
							// 如果是改变select map
							ruleMapName = caseItem.mapName;
							ruleMapLocation = caseItem.mapLocation;
						}
					}
				});
			} else if (willChangeSelf.changeMode && willChangeSelf.changeMode === "whenSomeType") {
				// 当为具体类型的时候
				let type = getHandleType(changeHandleObj, allMap);
				willChangeSelf["caseList"] && willChangeSelf["caseList"].map((caseItem) => {
					let ChangeHandleByChild = null;
					if (caseItem.changeType === "hiddenByChild" && Array.isArray(changeHandleValue)) {
						changeHandleValue.forEach((changeItem) => {
							ChangeHandleByChild = ChangeHandleByChild || changeItem[caseItem.childNodeName];
						});
					}
					if (caseItem["modeValueList"] && caseItem["modeValueList"].find(mvItem => (mvItem.toLowerCase() === type.toLowerCase()) || ChangeHandleByChild === mvItem)) {
						// 如果modeValueList列表中确实有handle value，则进行如下操作
						if (caseItem.changeType && caseItem.changeType === "disabled") {
							ruleDisabled = true;
						} else if (caseItem.changeType && (caseItem.changeType === "hidden" || caseItem.changeType === "hiddenByChild")) {
							ruleHidden = true;
						} else if (caseItem.changeType && caseItem.changeType === "selectMap") {
							// 如果是改变select map
							ruleMapName = caseItem.mapName;
							ruleMapLocation = caseItem.mapLocation;
						}
					}
				});
			}
		}
		return {
			ruleDisabled,
			ruleHidden,
			ruleMapName,
			ruleMapLocation
		};

	}

	getInputAddonAfter = (subItem, currentIndexObj) => {
		let { globalStore } = this.props;
		let { allMap, personalMode } = globalStore;
		let { addonAfter = null, addonAfterListName } = subItem;
		if (addonAfterListName && addonAfter) {
			let addonAfterVal = currentIndexObj[addonAfter];
			addonAfter = allMap[addonAfterListName].find(v => (v.name === addonAfterVal));
			if (addonAfter) {
				addonAfter = personalMode.lang === "cn" ? addonAfter.dName : addonAfter.enDName;
			}
		}
		return addonAfter;
	}
	render() {
		let { indexEditorStore, templateStore, globalStore, templateName } = this.props;
		let { editIndexMap, currentIndexId } = indexEditorStore;
		let { indexTemplateListObj } = templateStore;
		let { allMap, personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";

		let currentTemplate = indexTemplateListObj && templateName ? indexTemplateListObj[templateName] : null;
		let cfgJson = currentTemplate && currentTemplate.cfgJson && isJSON(currentTemplate.cfgJson) ? JSON.parse(currentTemplate.cfgJson) : null;

		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		let attachFieldsObj = currentIndexObj && currentIndexObj.attachFieldsObj ? currentIndexObj.attachFieldsObj : null;
		let colSpan = {
			"winType": 3,
			"winCount": 2,
			"winSize": 3,
			"attachFields.calMode": 5,
			"attachFields.operatorValue": 3,
			"attachFields.operatorValueType": 8,
			"attachFields.hasDimReadCheckbox": 4,
			"hasDimReadCheckbox": 4,
			"attachFields.endTime": 3
		};

		let colStyle = {
			"attachFields.hasEndTime": {
				width: "125px"
			},
			"attachFields.includeCurrent": {
				width: "136px"
			}
		};

		if (lang === "en") {
			colSpan["attachFields.hasDimReadCheckbox"] = 8;
			colSpan["hasDimReadCheckbox"] = 8;
			colStyle["attachFields.hasEndTime"] = {
				width: "185px"
			};
			colStyle["attachFields.includeCurrent"] = {
				width: "190px"
			};
		}

		// 判断是否有主属性取值非同一字段、主属性取值字段
		let hasDimReadCheckbox = false;
		let dimReadCheckboxValue = null;
		let hasDimRead1 = null;
		let dimRead1 = null;
		cfgJson &&
			cfgJson.params &&
			cfgJson.params.map((item, index) => {
				item.children &&
					item.children.map((subItem, subIndex, arr) => {
						if (subItem.name === "attachFields.hasDimReadCheckbox") {
							hasDimReadCheckbox = true;
						}
						if (subItem.name === "dimRead1") {
							hasDimRead1 = true;
						}
					});
			});
		if (hasDimReadCheckbox && hasDimRead1) {
			dimReadCheckboxValue = attachFieldsObj && attachFieldsObj["hasDimReadCheckbox"] ? attachFieldsObj["hasDimReadCheckbox"] : undefined;
			dimRead1 = currentIndexObj && currentIndexObj["dimRead1"] ? currentIndexObj["dimRead1"].toString() : undefined;
		}

		// 这里处理规则
		let simpleCfgList = getSimpleCfgList(cfgJson, currentIndexObj);

		// 获取当前记录是否已提交, 若未提交可以继续修改，此为大前提
		const { hasCommited } = currentIndexObj || {};
		return (
			<div className="ml-1">
				{
					// 循环处理每一行
					cfgJson &&
					cfgJson.params &&
					cfgJson.params.map((item, index) => {

						let tipTitle = lang === "cn" ? item["tipTitle"] : item["enTipTitle"];
						let tipContent = lang === "cn" ? item["tipContent"] : item["enTipContent"];
						let tipContentArr = [];
						if (tipContent) {
							tipContentArr = tipContent.split(/\n/);
						}
						const PopoverTitle = this.popoverTitle(tipTitle, tipContentArr);
						let RowDom = (
							<Row className="mb10" gutter={CommonConstants.gutterSpan} key={index}>
								<Col span={4} className="basic-info-title">
									{lang === "cn" ? item.labelText : item["enLabelText"]}
								</Col>
								{
									item.children &&
									item.children.map((subItem, subIndex, arr) => {
										let span = 8;
										let styleSet = {};
										if (arr.length > 1) {
											span = colSpan[subItem.name] ? colSpan[subItem.name] : 4;
											styleSet = colStyle[subItem.name] ? colStyle[subItem.name] : {};
										}
										let value = "";
										let operatorValueType = "input";
										let isAttachField = false;
										let field = null;
										if (subItem.name.indexOf("attachFields.") > -1) {
											field = subItem.name.split(".")[1];
											value = attachFieldsObj && attachFieldsObj[field] ? attachFieldsObj[field] : undefined;
											operatorValueType = attachFieldsObj && attachFieldsObj["operatorValueType"] ? attachFieldsObj["operatorValueType"] : operatorValueType;
											isAttachField = true;
										} else {
											field = subItem.name;
											value = currentIndexObj && currentIndexObj[subItem.name] ? currentIndexObj[subItem.name] : undefined;
										}


										// 处理像ruleFieldList.DOUBLE这样类型的字段
										let selectName = "";
										let filterType = null;
										if (subItem.componentType === "select" && subItem.selectType === "service" || subItem.selectType === "local") {
											if (subItem["selectName"].indexOf("ruleFieldList.") > -1) {
												selectName = subItem["selectName"].split(".")[0];
												filterType = [subItem["selectName"].split(".")[1]];
											} else {
												selectName = subItem["selectName"];
												filterType = subItem["filterType"] && subItem["filterType"].length ? subItem["filterType"] : null;
											}
										}

										let disabled = false;
										// 如果已经提交过了，则判断是否支持二次编辑，只要未提交过则默认可以编辑
										if (hasCommited === "1") {
											disabled = !subItem.canEditSecondTimes;
										}
										// if (currentIndexObj && !currentIndexObj["unSave"]) {
										// 	if (subItem.canEditSecondTimes === undefined) {
										// 		disabled = false;
										// 	} else {
										// 		disabled = !subItem.canEditSecondTimes;
										// 	}
										// }

										/*
										* 从这里处理changeRuleForOther规则
										* handle：指的是改变当前参数的那个值
										* */
										let currentSimpleObj = simpleCfgList.find(fItem => fItem.name === subItem.name);
										// 得到willChangeSelf
										let willChangeSelf = currentSimpleObj && currentSimpleObj.willChangeSelf ? currentSimpleObj.willChangeSelf : null;
										let {
											ruleDisabled,
											ruleHidden,
											ruleMapName,
											ruleMapLocation
										} = this.changeSelfEvent(willChangeSelf, simpleCfgList);
										// 最小改动，在原来的基础上新增更多改变自身事件列表。
										let willChangeMoreSelf = currentSimpleObj && currentSimpleObj.willChangeMoreSelf ? currentSimpleObj.willChangeMoreSelf : null;
										let ruleHiddenMore = false;
										willChangeMoreSelf && willChangeMoreSelf.length > 0 && willChangeMoreSelf.map((chillChangeMore) => {
											const {
												ruleDisabled: curRuleDisabled,
												ruleHidden: curRuleHidden
											} = this.changeSelfEvent(chillChangeMore, simpleCfgList);
											ruleHiddenMore = ruleHiddenMore || curRuleHidden;
											ruleDisabled = ruleDisabled || curRuleDisabled;
										});

										if (ruleHidden) {
											return;
										}
										if (ruleHiddenMore) {
											return;
										}
										if (subItem.componentType === "fieldSet" && currentIndexObj && currentIndexObj.calcType !== "blackList") {
											return;
										}

										if (subItem.componentType === "fieldSet") {
											span = 20;
										}
										if (subItem.componentType === "formula") {
											span = 12;
										}

										// 当外部当如指标时，部分属性例如主属性可能已经删除，不显示无效的值
										if (subItem.componentType === "select") {
											let optionList = [];
											let serviceSelect = false;
											if (subItem.selectType === "service" && selectName && !ruleMapLocation) {
												serviceSelect = true;
												optionList = allMap[selectName];
											}
											if (subItem.selectType === "service" && ruleMapLocation && ruleMapLocation === "service" && ruleMapName) {
												serviceSelect = true;
												optionList = allMap[ruleMapName];

											}

											if (serviceSelect) {
												// kkk
												const haveVal = optionList && optionList.length > 0 &&
													optionList.filter((v) => {
														return String(v.name) === String(value);
													});

												if (!(haveVal && haveVal.length > 0)) {
													value = undefined;
												}
											}
										}
										return (
											<WrapperCol span={span} componentType={subItem.componentType} styleSet={styleSet}>
												{
													subItem.componentType === "fieldSet" &&
													<FieldList />
												}
												{
													subItem.componentType === "formula" &&
													<FormulaEditor />
												}
												{
													subItem.componentType === "group" &&
													<ComponentGroup
														currentIndexObj={currentIndexObj}
														groupList={value || []}
														subItem={subItem}
														disabled={disabled || ruleDisabled}
														onChange={({ fieldName, data, groupIsAttachField }) => {
															this.changeBaseField(fieldName, "select", groupIsAttachField, data);
														}}
													>
														{PopoverTitle}
													</ComponentGroup>
												}
												{
													subItem.componentType === "input" &&
													subItem.name !== "attachFields.expression" &&
													<Input
														value={value}
														onChange={this.changeBaseField.bind(this, field, "input", isAttachField)}
														disabled={disabled || ruleDisabled}
														addonBefore={subItem.addonBefore}
														addonAfter={this.getInputAddonAfter(subItem, currentIndexObj)}
													/>
												}
												{
													subItem.componentType === "input" &&
													subItem.name === "attachFields.expression" &&
													<TextArea
														value={value}
														onChange={this.changeBaseField.bind(this, field, "input", isAttachField)}
														placeholder="业务链表达式"
														rows={4}
														disabled={disabled || ruleDisabled}
													/>
												}
												{
													subItem.componentType === "select" &&
													<Select
														placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
														value={value}
														onChange={this.changeBaseField.bind(this, field, "select", isAttachField)}
														showSearch
														optionFilterProp="children"
														disabled={disabled || ruleDisabled}
														dropdownMatchSelectWidth={false}
													>
														{
															subItem.selectType === "local" &&
															selectName &&
															!ruleMapLocation &&
															PolicyConstants[selectName] &&
															PolicyConstants[selectName].map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.name}
																		key={optionIndex}
																		title={optionItem.dName}
																	>
																		{optionItem.dName}
																	</Option>
																);
															})
														}
														{
															ruleMapName &&
															ruleMapLocation &&
															ruleMapLocation === "local" &&
															PolicyConstants[ruleMapName] &&
															PolicyConstants[ruleMapName].map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.name}
																		key={optionIndex}
																		title={optionItem.dName}
																	>
																		{lang === "cn" ? optionItem.dName : optionItem.enDName}
																	</Option>
																);
															})
														}
														{
															subItem.selectType === "self" && subItem.selectOption &&
															subItem.selectOption.map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.value}
																		key={optionIndex}
																		title={lang === "cn" ? optionItem.name : optionItem.enName}
																	>
																		{lang === "cn" ? optionItem.name : optionItem.enName}
																	</Option>
																);
															})
														}
														{
															subItem.selectType === "service" &&
															selectName &&
															!ruleMapLocation &&
															allMap[selectName] &&
															allMap[selectName].filter(fItem => {
																return filterType ? filterType.indexOf(fItem.type.toLowerCase()) > -1 : true;
															}).map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.name}
																		key={optionIndex}
																		title={optionItem.dName}
																	>
																		{lang === "en" ? optionItem.enDName ? optionItem.enDName : optionItem.dName : optionItem.dName}
																	</Option>
																);
															})
														}
														{
															subItem.selectType === "service" &&
															ruleMapLocation &&
															ruleMapLocation === "service" &&
															ruleMapName &&
															allMap[ruleMapName] &&
															allMap[ruleMapName].filter(fItem => {
																return filterType ? filterType.indexOf(fItem.type.toLowerCase()) > -1 : true;
															}).map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.name}
																		key={optionIndex}
																		title={optionItem.dName}
																	>
																		{lang === "en" ? optionItem.enDName ? optionItem.enDName : optionItem.dName : optionItem.dName}
																	</Option>
																);
															})
														}
													</Select>
												}
												{
													subItem.componentType === "checkbox" &&
													subItem.name !== "attachFields.hasDimReadCheckbox" &&
													<Checkbox.Group
														value={value ? value.toString().split(",") : undefined}
														onChange={this.changeBaseField.bind(this, field, "checkbox", isAttachField)}
														disabled={disabled || ruleDisabled}
													>
														{
															subItem.selectType === "self" &&
															subItem.selectOption &&
															subItem.selectOption.map((optionItem, optionIndex) => {
																return (
																	<Checkbox
																		value={optionItem.value}
																		key={optionIndex}
																	>
																		{lang === "cn" ? optionItem.name : optionItem.enName}
																	</Checkbox>
																);
															})
														}
														{
															subItem.selectType === "service" &&
															subItem.selectName &&
															allMap[subItem.selectName] &&
															allMap[subItem.selectName].map((optionItem, optionIndex) => {
																return (
																	<Checkbox
																		value={optionItem.name}
																		key={optionIndex}
																	>
																		{lang === "cn" ? optionItem.name : optionItem.enName}
																	</Checkbox>
																);
															})
														}
													</Checkbox.Group>
												}
												{
													subItem.componentType === "checkbox" &&
													subItem.name === "attachFields.hasDimReadCheckbox" &&
													<Checkbox.Group
														value={attachFieldsObj && attachFieldsObj["hasDimReadCheckbox"] ? attachFieldsObj["hasDimReadCheckbox"] : undefined}
														onChange={this.changeBaseField.bind(this, field, "checkbox", isAttachField)}
														disabled={disabled || ruleDisabled}
													>
														{
															subItem.selectType === "self" &&
															subItem.selectOption &&
															subItem.selectOption.map((optionItem, optionIndex) => {
																return (
																	<Checkbox
																		value={optionItem.value}
																		key={optionIndex}
																	>
																		{lang === "cn" ? optionItem.name : optionItem.enName}
																	</Checkbox>
																);
															})
														}
													</Checkbox.Group>
												}
												{
													subItem.componentType === "variable" &&
													<InputGroup compact>
														<Select
															value={value}
															style={{ width: "30%" }}
															onChange={this.changeBaseField.bind(this, "operatorValueType", "variableType", isAttachField)}
															disabled={disabled}
															placeholder={indexListLang.ruleAttr("select")} // lang:请选择
														>
															{/* lang:常量变量*/}
															<Option value="input">
																{indexListLang.ruleAttr("constant")}
															</Option>
															<Option
																value="context">
																{indexListLang.ruleAttr("variable")}
															</Option>
														</Select>
														{
															operatorValueType === "input" &&
															<Input
																style={{ width: "70%" }}
																value={attachFieldsObj && attachFieldsObj["operatorValue"] ? attachFieldsObj["operatorValue"] : undefined}
																onChange={this.changeBaseField.bind(this, "operatorValue", "input", isAttachField)}
																placeholder={indexListLang.ruleAttr("pleaseEnter")} // lang:请输入
																disabled={disabled}
															/>
														}
														{
															operatorValueType === "context" &&
															<Select
																style={{ width: "70%" }}
																value={attachFieldsObj && attachFieldsObj["operatorValue"] ? attachFieldsObj["operatorValue"] : undefined}
																placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
																onChange={this.changeBaseField.bind(this, "operatorValue", "select", isAttachField)}
																disabled={disabled}
																showSearch
																optionFilterProp="children"
																dropdownMatchSelectWidth={false}
															>
																{
																	allMap &&
																	allMap["ruleFieldList"] &&
																	allMap["ruleFieldList"].map((item, index) => {
																		return (
																			<Option
																				value={item.name}
																				key={index}
																				title={item.dName}
																			>
																				{/* lang:当前*/}
																				{indexListLang.ruleAttr("current") + item.dName}
																			</Option>
																		);
																	})
																}
															</Select>
														}
													</InputGroup>
												}
											</WrapperCol>
										);
									})
								}
								{/* 右侧文案查看提示 */}
								{
									tipTitle &&
									tipContent &&
									item.labelText !== "从属性" &&
									PopoverTitle
								}
							</Row>
						);

						// console.log(item);
						// 在这里处理行规则willChangeLine
						let showThisLine = false;
						let willChangeLine = JSON.stringify(item["willChangeLine"]) !== "{}" ? item["willChangeLine"] : undefined;
						if (willChangeLine) {
							let { changeValue } = willChangeLine;
							let currentSimpleObj = simpleCfgList.find(fItem => fItem.name === willChangeLine.name) || {};
							// 是否隐藏当前行
							changeValue = changeValue;
							changeValue = changeValue.split("||");
							showThisLine = changeValue && changeValue.filter(value => (value === currentSimpleObj.value));
							showThisLine = showThisLine && showThisLine.length > 0;
							// showThisLine = currentSimpleObj.value === willChangeLine.changeValue;
						}
						if (willChangeLine && showThisLine) {
							return RowDom;
						}
						if (!willChangeLine) {
							if (item.labelText === "主属性取值字段") {
								if (hasDimReadCheckbox && hasDimRead1 && dimReadCheckboxValue === "1") {
									return RowDom;
								}
							} else {
								return RowDom;
							}
						}
					})
				}

				{/* {
    				currentIndexObj &&
                    <IndexMore templateName={templateName} />
    			} */}
			</div>
		);
	}
}

export default connect(state => ({
	indexEditorStore: state.indexEditor,
	globalStore: state.global,
	templateStore: state.template
}))(IndexCondition);

