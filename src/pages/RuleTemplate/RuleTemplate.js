import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import TemplateList from "./Inner/TemplateList";
import TemplateEditor from "./Inner/TemplateEditor";
import AddGroupModal from "./Inner/TemplateModal/AddGroup";
import ModifyGroupModal from "./Inner/TemplateModal/ModifyGroup";
import AddTemplateModal from "./Inner/TemplateModal/AddTemplate";
import ModifyTemplateModal from "./Inner/TemplateModal/ModifyTemplate";
import { checkFunctionHasPermission } from "@/utils/permission";
import NoPermission from "@/components/NoPermission";
import "./RuleTemplate.less";

class RuleTemplate extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { templateStore, globalStore } = this.props;
		const { menuTreeReady } = globalStore;
		let { dialogShow } = templateStore;

		return (
			<Fragment>
				<div className="system-register">
					<div className="page-global-header">
						<div className="left-info">
							<h2>规则模板列表</h2>
						</div>
					</div>
					{
						menuTreeReady &&
						<Fragment>
							{
								checkFunctionHasPermission("ZB0201", "listTemplate")
									? <div className="page-global-body">
										<div className="tnt-vertical-box white-bg">
											<div className="tnt-vertical-box-body white-bg">
												<div className="box-item box-item-30 border-right">
													<TemplateList />
												</div>
												<div className="box-item box-item-70">
													<TemplateEditor />
												</div>
											</div>
										</div>
									</div>
									: <NoPermission />
							}
						</Fragment>
					}
				</div>
				<AddGroupModal />
				<ModifyGroupModal />
				<AddTemplateModal />
				{
					dialogShow.modifyTemplate &&
					<ModifyTemplateModal />
				}
			</Fragment>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	templateStore: state.template
}))(RuleTemplate);

