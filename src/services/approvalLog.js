import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getPolicyApprovalLogList = async(params) => {
	return request(getUrl("/admin/approval/log/listPolicy", params), {
		method: "GET",
		headers: getHeader()
	});
};
const getSalaxyApprovalLogList = async(params) => {
	return request(getUrl("/admin/approval/log/listZb", params), {
		method: "GET",
		headers: getHeader()
	});
};
const getWorkflowApprovalLogList = async(params) => {
	return request(getUrl("/admin/approval/log/listDecisionFlow", params), {
		method: "GET",
		headers: getHeader()
	});
};

export default {
	getPolicyApprovalLogList,
	getSalaxyApprovalLogList,
	getWorkflowApprovalLogList
};
