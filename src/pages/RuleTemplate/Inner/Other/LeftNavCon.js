import { PureComponent } from "react";
import { connect } from "dva";
import { Icon, message, Tooltip, Dropdown, <PERSON>u, <PERSON>, Badge, Button } from "antd";
import AsyncTemplate from "./Modal/AsyncTemplate";
import { cloneDeep } from "lodash";

class LeftNavCon extends PureComponent {
	constructor(props) {
		super(props);
		this.modifyConfigIndex = this.modifyConfigIndex.bind(this);
		this.removeConfig = this.removeConfig.bind(this);
		this.sortConfig = this.sortConfig.bind(this);
		this.addOneLine = this.addOneLine.bind(this);
		this.state = {
			modelSet: localStorage.getItem("modelSet") ? JSON.parse(localStorage.getItem("modelSet")) : []
		};
	}

	addOneLine() {
		let { templateStore, dispatch } = this.props;
		let { dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let templateObj = {
			"labelText": "",
			"children": []
		};
		cfgJson["params"].push(templateObj);
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

    pasteOneLine = (templateObj) => {
    	let { templateStore, dispatch } = this.props;
    	let { dialogData } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;
    	cfgJson["params"].push(templateObj);
    	modifyTemplateData.cfgJson = cfgJson;
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    }

    modifyConfigIndex(index) {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "template/setAttrValue",
    		payload: {
    			operatorTemplateItemIndex: index
    		}
    	});
    }

    removeConfig(index, e) {
    	e.stopPropagation();
    	let { templateStore, dispatch } = this.props;
    	let { dialogData } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	cfgJson["params"].splice(index, 1);
    	modifyTemplateData.cfgJson = cfgJson;
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    	dispatch({
    		type: "template/setAttrValue",
    		payload: {
    			operatorTemplateItemIndex: 0
    		}
    	});
    }

    sortConfig(index, sortType) {
    	let { templateStore, dispatch } = this.props;
    	let { dialogData } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	if (sortType === "down") {
    		cfgJson["params"][index] = cfgJson["params"].splice(index + 1, 1, cfgJson["params"][index])[0];
    	} else if (sortType === "up") {
    		cfgJson["params"][index] = cfgJson["params"].splice(index - 1, 1, cfgJson["params"][index])[0];
    	}

    	modifyTemplateData.cfgJson = cfgJson;
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    	message.success("移动顺序成功");
    }

    // 复制
    copyConfig = (index) => {
    	let { templateStore } = this.props;
    	let { dialogData } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	const { displayName, cfgJson = {} } = modifyTemplateData;
    	const detail = cfgJson.params && cfgJson.params[index] || {};
    	const { modelSet: modelSetOld } = this.state;
    	const modelSet = [...modelSetOld];
    	const key = "模版" + displayName + "-" + detail.labelText;
    	modelSet.push({
    		key,
    		detail
    	});
    	try {
    		localStorage.setItem("modelSet", JSON.stringify(modelSet));
    		this.setState({
    			modelSet
    		});
    		message.success(`已复制${key}模版`);
    	} catch (e) {
    		message.error(e.message);
    	}
    }
    // 删除缓存的模版
    delLocalStorageConfig = (index, e) => {
    	e.stopPropagation();
    	const { modelSet: modelSetOld } = this.state;
    	let modelSet = [...modelSetOld];
    	modelSet.splice(index, 1);
    	try {
    		localStorage.setItem("modelSet", JSON.stringify(modelSet));
    		this.setState({ modelSet });
    		message.success("删除成功");
    	} catch (e) {
    		message.error(e.message);
    	}
    }

    // 同步其他模版
    syncOther = (item) => {
    	const { labelText } = item;
    	let {templateStore, dispatch} = this.props;
    	let {indexTemplateList, dialogData} = templateStore;
    	let { modifyTemplateData } = dialogData;
    	const newTemplateList = cloneDeep(indexTemplateList).map(indexTemplate=>{
    		const { templateDataList = [] } = indexTemplate || {};
    		const newTemplateDataList = templateDataList.filter(v=>{
    			let { cfgJson, id } = v;
    			let hasSamePrototype = false;
    			if (cfgJson && id !== modifyTemplateData.id) {
    				cfgJson = JSON.parse(cfgJson);
    				cfgJson.params &&
                        cfgJson.params.forEach(cfg=>{
                        	if (cfg.labelText === labelText) {
                        		hasSamePrototype = true;
                        	}
                        });
    				if (hasSamePrototype) {
    					return v;
    				}
    			}
    		});
    		indexTemplate.templateDataList = newTemplateDataList;
    		return indexTemplate;
    	});
    	dispatch({
    		type: "template/setDialogShow",
    		payload: {
    			asyncTemplate: true
    		}
    	});
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			asyncTemplateData: {
    				templateList: newTemplateList,
    				labelText: labelText
    			}
    		}
    	});
    }
    render() {
    	let { templateStore } = this.props;
    	let { currentStep, operatorTemplateItemIndex, dialogData } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;
    	let { modelSet } = this.state;
    	const menu = (
    		<Menu>
    			{
    				modelSet &&
                    modelSet.map((v = {}, index) => {
                    	return (
                    		<Menu.Item
                    			key={index}
                    			onClick={
                    				(e) => {
                    					const detail = [...modelSet][index].detail;
                    					console.log(detail);
                    					this.pasteOneLine({ ...detail, labelText: "[复制]" + detail.labelText });
                    				}
                    			}
                    		>
                    			<Row type="flex" justify="space-between" align="middle" className="model-copy-item">
                    				<div>{v.key}</div>
                    				<Icon
                    					type="close-circle"
                    					onClick={this.delLocalStorageConfig.bind(this, index)}
                    				/>
                    			</Row>
                    		</Menu.Item>
                    	);
                    })
    			}

    		</Menu>
    	);
    	return (
    		<div>
    			<ul>
    				{
    					cfgJson && cfgJson.params && cfgJson.params.map((item, index, arr) => {
    						return (
    							<li
    								className={operatorTemplateItemIndex === index ? item.labelText ? "current" : "current empty" : ""}
    								key={index}
    								onClick={this.modifyConfigIndex.bind(this, index)}
    							>
    								<div className="param-left"
    									style={{ width: "calc(100% - 100px)" }}
    									onClick={this.modifyConfigIndex.bind(this, index)}>
    									{
    										item.labelText
    											? <span className="param-label">【{item.labelText}】</span>
    											: <span className="param-label no-label">【暂无标签名】</span>
    									}
    								</div>
    								<div className="param-oper-list">
    									<Tooltip placement="top" content="下移">
    										<Icon
    											className={index === arr.length - 1 ? "disabled" : ""}
    											onClick={index === arr.length - 1 ? () => {
    												message.error("已经是最后一个了");
    												return;
    											} : this.sortConfig.bind(this, index, "down")}
    											type="arrow-down"
    										/>
    									</Tooltip>
    									<Tooltip placement="top" content="上移">
    										<Icon
    											className={index === 0 ? "disabled" : ""}
    											onClick={index === 0 ? () => {
    												message.error("已经是第一个了");
    												return;
    											} : this.sortConfig.bind(this, index, "up")}
    											type="arrow-up"
    										/>
    									</Tooltip>
    									<Tooltip placement="top" content="移除此选项">
    										<Icon onClick={this.removeConfig.bind(this, index)} type="delete" />
    									</Tooltip>
    									{
    										item.labelText &&
                                            <Tooltip placement="top" content="复制此选项">
                                            	<Icon onClick={this.copyConfig.bind(this, index)} type="copy" />
                                            </Tooltip>
    									}
    									<Tooltip placement="top" content="同步其他模版">
    										<Icon type="sync" onClick={this.syncOther.bind(this, item)} />
    									</Tooltip>
    								</div>
    							</li>
    						);
    					})
    				}
    			</ul>
    			{
    				currentStep === 1 &&
                    <Row type="flex" className="boder-1">
                    	<div className="add-control" onClick={this.addOneLine.bind(this)}>
                            添加一项
                    	</div>
                    	{
                    		modelSet &&
                            modelSet.length > 0 &&
                            <div className="add-control">
                            	<Dropdown
                            		overlay={menu}
                            		overlayClassName="model-copy-drop-down"
                            		trigger="click"
                            	>
                            		<div>
                                        粘贴
                            			<Badge count={modelSet.length} offset={[0, -25]} />
                            		</div>
                            	</Dropdown>
                            </div>
                    	}
                    </Row>

    			}
    			<AsyncTemplate/>
    		</div>
    	);
    }
}

export default connect(state => ({
	templateStore: state.template
}))(LeftNavCon);
