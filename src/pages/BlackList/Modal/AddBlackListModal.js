import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Form, Row, Col, message, Radio, DatePicker } from "antd";
import { blackListAPI } from "@/services";

const RadioGroup = Radio.Group;

class AddBlackListModal extends PureComponent {

	constructor(props) {
		super(props);
		this.startSearch = this.startSearch.bind(this);
		this.submitModal = this.submitModal.bind(this);
		this.changeFieldValue = this.changeFieldValue.bind(this);
	}

	submitModal() {
		let { blackListStore } = this.props;
		let { dialogData, dialogShow } = blackListStore;
		let { addBlackListData } = dialogData;
		let { addBlackListModal, modifyBlackListModal } = dialogShow;
		let { tableHeaderList, listName, expireTime, modifyObj } = addBlackListData;

		if (!listName) {
			message.warning("请选择名单类型");
			return;
		}

		let params = {
			listName: listName
		};

		if (expireTime) {
			params["expireTime"] = expireTime;
		}

		tableHeaderList.forEach((item) => {
			if (item.value) {
				params[item["headerField"]] = item.value;
			}
		});

		if (addBlackListModal) {
			blackListAPI.addBlackListData(params).then(res => {
				if (res.success) {
					message.success("添加数据成功");
					setTimeout(() => {
						this.startSearch();
					}, 1000);
					this.closeModal();
				} else {

					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		} else if (modifyBlackListModal) {
			// 如果是修改数据
			params["black_list_number"] = modifyObj["black_list_number"];
			blackListAPI.modifyBlackListData(params).then(res => {
				if (res.success) {
					message.success("修改数据成功");
					setTimeout(() => {
						this.startSearch();
					}, 1000);
					this.closeModal();
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		}
	}

	startSearch() {
		let { dispatch, blackListStore } = this.props;
		let { searchParams } = blackListStore;
		// 页面初始化数据
		searchParams["curPage"] = 1;

		dispatch({
			type: "blackList/getBlackListData",
			payload: {
				...searchParams
			}
		});
		dispatch({
			type: "blackList/setAttrValue",
			payload: {
				searchParams: searchParams
			}
		});
	}

	closeModal() {
		let { dispatch } = this.props;

		dispatch({
			type: "blackList/setDialogShow",
			payload: {
				addBlackListModal: false,
				modifyBlackListModal: false
			}
		});
		setTimeout(() => {
			dispatch({
				type: "blackList/setDialogData",
				payload: {
					addBlackListData: {
						listName: null,
						expireTime: null,
						modifyObj: null,
						tableHeaderList: []
					}
				}
			});
		}, 300);
	}

	changeFieldValue(index, field, e) {
		let { blackListStore, dispatch } = this.props;
		let { dialogData } = blackListStore;
		let { addBlackListData } = dialogData;
		let { tableHeaderList } = addBlackListData;

		tableHeaderList[index]["value"] = e.target.value;
		dispatch({
			type: "blackList/setDialogData",
			payload: {
				addBlackListData: addBlackListData
			}
		});
	}

	render() {
		let { blackListStore, globalStore } = this.props;
		let { allMap } = globalStore;
		let { dialogShow, dialogData } = blackListStore;
		let { addBlackListData } = dialogData;
		let { tableHeaderList, listName } = addBlackListData;
		let { blackListSelect } = allMap;

		return (
			<Modal
				title={dialogShow.addBlackListModal ? "新增名单" : "修改名单"}
				visible={dialogShow.addBlackListModal || dialogShow.modifyBlackListModal}
				maskClosable={true}
				width={650}
				onOk={this.submitModal.bind(this)}
				onCancel={this.closeModal.bind(this)}
			>
				<Form className="modal-form">
					<Row gutter={20}>
						<Col
							span={8}
							className="basic-info-title"
						>
                            名单类型：
						</Col>
						<Col span={16}>
							<RadioGroup
								value={listName || undefined}
								onChange={(e) => {
									let value = e.target.value;
									let { blackListStore, dispatch } = this.props;
									let { dialogData } = blackListStore;
									let { addBlackListData } = dialogData;

									addBlackListData["listName"] = value;

									dispatch({
										type: "blackList/setDialogData",
										payload: {
											addBlackListData: addBlackListData
										}
									});
								}}
							>
								{
									blackListSelect &&
                                    blackListSelect.map((item, index) => {
                                    	return (
                                    		<Radio
                                    			value={item.name}
                                    			key={index}
                                    		>
                                    			{item.dName}
                                    		</Radio>
                                    	);
                                    })
								}
							</RadioGroup>
						</Col>
					</Row> <Row gutter={20}>
						<Col
							span={8}
							className="basic-info-title"
						>
                            过期时间：
						</Col>
						<Col span={16}>
							<DatePicker
								style={{ width: "100%" }}
								showTime
								format="YYYY-MM-DD HH:mm:ss"
								onChange={(date, dateString) => {
									let { blackListStore, dispatch } = this.props;
									let { dialogData } = blackListStore;
									let { addBlackListData } = dialogData;

									addBlackListData["expireTime"] = dateString;

									dispatch({
										type: "blackList/setDialogData",
										payload: {
											addBlackListData: addBlackListData
										}
									});
								}}
							/>
						</Col>
					</Row>
					{
						tableHeaderList &&
                        tableHeaderList.map((item, index) => {
                        	if (item.headerField !== "black_list_number" && item.headerField !== "create" && item.headerField !== "expireTime") {
                        		return (
                        			<Row gutter={20} key={index}>
                        				<Col
                        					span={8}
                        					className="basic-info-title"
                        				>
                        					{item["headerFieldName"]}：
                        				</Col>
                        				<Col span={16}>
                        					<Input
                        						onChange={this.changeFieldValue.bind(this, index, "headerField")}
                        						value={item.value || undefined}
                        						placeholder="请输入"
                        					/>
                        				</Col>
                        			</Row>
                        		);
                        	}
                        })
					}
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	blackListStore: state.blackList
}))(AddBlackListModal);
