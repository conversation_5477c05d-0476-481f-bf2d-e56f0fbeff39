import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, InputNumber, Button, Select, Row, Col, Icon, Alert, Tooltip, DatePicker, message, Empty } from "antd";
import { policyDetailAPI } from "@/services";
import { CommonConstants } from "@/constants";
import moment from "moment";
import { policyDetailLang } from "@/constants/lang";

const InputGroup = Input.Group;
const { Option } = Select;

export default (props) => {
	let { itemIndex, itemData, readOnly, globalStore, curPolicyAppName, onChange, addCustomItem, removeCustomItem } = props;
	let { allMap } = globalStore;

	console.log(props);
	return (
		<Row
			gutter={CommonConstants.gutterSpan}
			className="custom-list-row single-section"
			key={itemIndex}
		>
			<Col span={2}></Col>
			<Col span={5}>
				<Select
					value={itemData.do.arguments.field || undefined}
					onChange={(value) => {
						onChange("field", itemIndex, value);
					}}
					placeholder={policyDetailLang.addToCustomListModal("select")} // 请选择
					showSearch
					optionFilterProp="children"
					disabled={readOnly}
					dropdownMatchSelectWidth={false}
				>
					{
						allMap &&
                        allMap["ruleFieldList"] &&
                        allMap["ruleFieldList"].map((item, itemIndex) => {
                        	return (
                        		<Option
                        			value={item.name}
                        			key={itemIndex}
                        		>
                        			{item.dName}
                        		</Option>
                        	);
                        })
					}
				</Select>
			</Col>
			<Col span={1}>
				<Icon className="trans-arrow" type="arrow-right" />
			</Col>
			<Col span={5}>
				<Select
					value={itemData.do.arguments.customListUuid || undefined}
					onChange={(value) => {
						onChange("customListUuid", itemIndex, value);
					}}
					placeholder={policyDetailLang.addToCustomListModal("select")} // 请选择
					showSearch
					optionFilterProp="children"
					disabled={readOnly}
					dropdownMatchSelectWidth={false}
				>
					{
						allMap &&
                        allMap["customList"] &&
                        allMap["customList"].filter((item) => {
                        	return item.app === "all" || item.app === curPolicyAppName;
                        }).map((item, index) => {
                        	return (
                        		<Option
                        			value={item.name}
                        			key={index}
                        		>
                        			{item.dName}
                        		</Option>
                        	);
                        })
					}
				</Select>
			</Col>
			<Col span={8}>
				<InputGroup compact>
					<Select
						style={{ width: "30%" }}
						value={itemData.do.arguments.expireType || undefined}
						onChange={(value) => {
							onChange("expireType", itemIndex, value);
							onChange("expireValue", itemIndex, "");
						}}
						disabled={readOnly}
					>
						<Option value="forever">{policyDetailLang.addToCustomListModal("forever")}</Option>
						<Option value="byDay">{policyDetailLang.addToCustomListModal("byDay")}</Option>
						<Option value="byTs">{policyDetailLang.addToCustomListModal("byTs")}</Option>
						<Option value="byHour">{policyDetailLang.addToCustomListModal("byHour")}</Option>
						<Option value="byMin">{policyDetailLang.addToCustomListModal("byMin")}</Option>
					</Select>
					{
						(itemData.do.arguments.expireType === "byDay" || itemData.do.arguments.expireType === "byHour" || itemData.do.arguments.expireType === "byMin") &&
                        <InputNumber
                        	min={1}
                        	max={1000}
                        	style={{ width: "70%" }}
                        	value={itemData.do.arguments.expireValue || undefined}
                        	onChange={(value) => {
                        		onChange("expireValue", itemIndex, value);
                        	}}
                        	disabled={readOnly}
                        />
					}
					{
						itemData.do.arguments.expireType === "byTs" &&
                        <DatePicker
                        	style={{ width: "70%" }}
                        	value={itemData.do.arguments.expireValue ? moment(itemData.do.arguments.expireValue) : undefined}
                        	format="YYYY-MM-DD HH:mm:ss"
                        	showTime
                        	disabledDate={(current) => {
                        		return current && current < moment().endOf("day");
                        	}}
                        	onChange={(value) => {
                        		onChange("expireValue", itemIndex, value ? value.valueOf() : null);
                        	}}
                        	disabled={readOnly}
                        />
					}
				</InputGroup>
			</Col>
			{
				!readOnly &&
                <Col span={2} className="basic-info-oper">
                	{/* 添加一项 */}
                	<Tooltip title={policyDetailLang.addToCustomListModal("add")}
                		placement="top">
                		<Icon
                			className="add"
                			type="plus-circle-o"
                			onClick={() => {
                				addCustomItem("single", itemIndex);
                			}}
                		/>
                	</Tooltip>
                	{/* 删除当前行 */}
                	<Tooltip title={policyDetailLang.addToCustomListModal("deleteLine")}
                		placement="top">
                		<Icon
                			className="delete"
                			type="delete"
                			onClick={() => {
                				removeCustomItem(itemIndex);
                			}}
                		/>
                	</Tooltip>
                </Col>
			}
		</Row>
	);
};
