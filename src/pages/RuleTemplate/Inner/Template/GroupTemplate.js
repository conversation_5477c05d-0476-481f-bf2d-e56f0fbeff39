import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Switch, InputNumber, Menu, Dropdown } from "antd";
import ChangeGroupRuleForSelf from "./ChangeGroupRuleForSelf";
import ChangeGroupRuleForParent from "./ChangeGroupRuleForParent";
import ChangeGroupSelfRuleForParent from "./ChangeGroupSelfRuleForParent";
import { cloneDeep } from "lodash";

const InputGroup = Input.Group;
const { Option } = Select;

class GroupTemplate extends PureComponent {
	constructor(props) {
		super(props);
		this.deleteChild = this.deleteChild.bind(this);
		this.addSelectOption = this.addSelectOption.bind(this);
		this.changeSelectOption = this.changeSelectOption.bind(this);
		this.removeSelectOption = this.removeSelectOption.bind(this);
		this.switchTypeMenu = this.switchTypeMenu.bind(this);
	}

    changeParamValue = (type, field, actionType, e) => {
    	let value = "";
    	if (type === "input") {
    		value = e.target.value;
    	} else {
    		value = e && e === "dataSetNull" ? null : e;
    	}
    	if (actionType === "onBlur") {
    		// 正则表达式去除首尾空格
    		value = value.replace(/(^\s*)|(\s*$)/g, "");
    	}
    	let { templateStore, childIndex, dispatch } = this.props;
    	let { dialogData, operatorTemplateItemIndex } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex][field] = value;
    	modifyTemplateData.cfgJson = cfgJson;
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    }

    deleteChild(index) {
    	let { templateStore, dispatch } = this.props;
    	let { dialogData, operatorTemplateItemIndex } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	cfgJson["params"][operatorTemplateItemIndex]["children"].splice(index, 1);
    	modifyTemplateData.cfgJson = cfgJson;
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    }

    addSelectOption(index) {
    	let { templateStore, childIndex, dispatch } = this.props;
    	let { dialogData, operatorTemplateItemIndex } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	let optionTemplate = {
    		"name": "",
    		"limitNum": "",
    		"canEditSecondTimes": "",
    		"value": ""
    	};
    	if (cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["children"][index]["selectOption"]) {
    		cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["children"][index]["selectOption"].push(optionTemplate);
    	} else {
    		cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["children"][index]["selectOption"] = [
    			{
    				"name": "",
    				"limitNum": "",
    				"canEditSecondTimes": "",
    				"value": ""
    			}
    		];
    	}
    	modifyTemplateData.cfgJson = cfgJson;
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    }

    changeSelectOption(index, childInd, field, e) {
    	let { templateStore, childIndex, dispatch } = this.props;
    	let { dialogData, operatorTemplateItemIndex } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["children"][index]["selectOption"][childInd][field] = e.target.value;

    	modifyTemplateData.cfgJson = cfgJson;
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    }

    removeSelectOption(parentInd, index) {
    	let { templateStore, childIndex, dispatch } = this.props;
    	let { dialogData, operatorTemplateItemIndex } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["children"][parentInd]["selectOption"].splice(index, 1);

    	modifyTemplateData.cfgJson = cfgJson;
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    }

    switchTypeMenu({ key }) {
    	let { templateStore, childIndex, dispatch } = this.props;
    	let { dialogData, operatorTemplateItemIndex } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["componentType"] = key;

    	modifyTemplateData.cfgJson = cfgJson;
    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    }

    // 新加组内元素
    addGroupChild = (type, typeDetail) => {
    	let { templateStore, childIndex, dispatch } = this.props;
    	let { dialogData, operatorTemplateItemIndex } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	const inputObj = {
    		componentType: "input",
    		defaultValue: "",
    		name: "",
    		selectSourceName: "systemList",
    		addonBefore: "",
    		addonAfter: "",
    		placeholder: "",
    		canEditSecondTimes: true,
    		type: "string",
    		span: 4
    	};

    	const selectObj = {
    		componentType: "select",
    		defaultValue: "",
    		name: "",
    		selectType: null,
    		selectName: null,
    		selectOption: [],
    		mapType: "static",
    		placeholder: "",
    		canEditSecondTimes: true,
    		type: "string",
    		span: 4
    	};

    	const checkboxObj = {
    		componentType: "checkbox",
    		defaultValue: "",
    		name: "",
    		selectType: null,
    		selectName: null,
    		selectOption: [],
    		canEditSecondTimes: true,
    		type: "string",
    		span: 4,
    		typeFinal: ""
    	};

    	// 时间控件
    	let dateObj = {};
    	if (typeDetail) {
    		dateObj = {
    			componentType: typeDetail,
    			defaultValue: "",
    			name: "",
    			selectSourceName: "systemList",
    			addonBefore: "",
    			placeholder: "",
    			canEditSecondTimes: true,
    			type: "string",
    			span: 4
    		};
    	}

    	// 标签
    	const labelObj = {
    		componentType: "label",
    		text: "",
    		alignType: "",
    		span: 4
    	};

    	let needObj = null;
    	switch (type) {
    		case "input": needObj = cloneDeep(inputObj); break;
    		case "select": needObj = cloneDeep(selectObj); break;
    		case "checkbox": needObj = cloneDeep(checkboxObj); break;
    		case "date": needObj = cloneDeep(dateObj); break;
    		case "label": needObj = cloneDeep(labelObj); break;
    	}
    	if (cfgJson) {
    		cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["children"].push(needObj);

    		dispatch({
    			type: "template/setDialogData",
    			payload: {
    				modifyTemplateData: modifyTemplateData
    			}
    		});
    	}
    }

    deleteGroupChildParam(index) {
    	let { templateStore, childIndex, dispatch } = this.props;
    	let { dialogData, operatorTemplateItemIndex } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	let childItem = cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["children"];

    	childItem.splice(index, 1);

    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    }

    changeGroupChildParam = (index, field, type, actionType, e) => {
    	let value;
    	if (type === "input") {
    		value = e.target.value;
    	} else {
    		value = e;
    	}
    	if (actionType === "onBlur") {
    		// 正则表达式去除首尾空格
    		value = value.replace(/(^\s*)|(\s*$)/g, "");
    	}
    	let { templateStore, childIndex, dispatch } = this.props;
    	let { dialogData, operatorTemplateItemIndex } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;

    	let childItem = cfgJson["params"][operatorTemplateItemIndex]["children"][childIndex]["children"][index];

    	childItem[field] = value;

    	dispatch({
    		type: "template/setDialogData",
    		payload: {
    			modifyTemplateData: modifyTemplateData
    		}
    	});
    }

    renderGroupChild = (item, index) => {
    	let { childIndex } = this.props;
    	if (item.componentType === "input") {
    		return (
    			<div
    				className="group-child-item"
    				key={index}
    			>
    				<div className="child-item-header">
    					<span className="title">
                            INPUT
    					</span>
    					<span className="delete">
    						<Icon
    							type="delete"
    							onClick={() => {
    								this.deleteGroupChildParam(index);
    							}}
    						/>
    					</span>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>唯一标识：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.name || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "name", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "name", "input", "onChange", e);
    							}}
    							placeholder="请输入唯一标识"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>默认值：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.defaultValue || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "defaultValue", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "defaultValue", "input", "onChange", e);
    							}}
    							placeholder="请输入默认值"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>前缀：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.addonBefore || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "addonBefore", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "addonBefore", "input", "onChange", e);
    							}}
    							placeholder="请输入input前缀"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>后缀：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.addonAfter || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "addonAfter", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "addonAfter", "input", "onChange", e);
    							}}
    							placeholder="请输入input后缀"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>placeholder：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.placeholder || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "placeholder", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "placeholder", "input", "onChange", e);
    							}}

    							placeholder="中文placeholder（非必填）"
    							addonBefore="cn"
    							size="small"
    						/>
    						<Input
    							value={item.enPlaceholder || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "enPlaceholder", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "enPlaceholder", "input", "onChange", e);
    							}}
    							placeholder="英文placeholder（非必填）"
    							addonBefore="en"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>二次编辑：</span>
    					</div>
    					<div className="param-field">
    						<Switch
    							checkedChildren="可以"
    							unCheckedChildren="不可以"
    							checked={item.canEditSecondTimes}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "canEditSecondTimes", "select", "onChange", e);
    							}}
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>组件长度：</span>
    					</div>
    					<div className="param-field">
    						<InputNumber
    							min={1}
    							max={18}
    							step={1}
    							value={item.span || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "span", "select", "onChange", e);
    							}}
    							size="small"
    							style={{ width: "100%" }}
    							placeholder="网格系统24，如果一个参数，可以设置8，不填默认是4"
    						/>
    					</div>
    				</div>
    				<hr style={{ borderColor: "#fff" }} />
    				<ChangeGroupRuleForSelf componentType={item.componentType} childIndex={childIndex} groupItemIndex={index}/>
    			</div>
    		);
    	} else if (item.componentType === "select") {
    		return (
    			<div
    				className="group-child-item"
    				key={index}
    			>
    				<div className="child-item-header">
    					<span className="title">
                            SELECT
    					</span>
    					<span className="delete">
    						<Icon
    							type="delete"
    							onClick={() => {
    								this.deleteGroupChildParam(index);
    							}}
    						/>
    					</span>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>唯一标识：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.name || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "name", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "name", "input", "onChange", e);
    							}}
    							placeholder="请输入唯一标识"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>默认值：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.defaultValue || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "defaultValue", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "defaultValue", "input", "onChange", e);
    							}}
    							placeholder="请输入默认值"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>选项范围：</span>
    					</div>
    					<div className="param-field">
    						<Select
    							value={item.selectType || undefined}
    							style={{ width: "100%" }}
    							size="small"
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "selectType", "select", "onChange", e);
    							}}
    						>
    							<Option value="local">local</Option>
    							<Option value="self">self</Option>
    							{/* <Option value="service">service</Option> */}
    						</Select>
    					</div>
    				</div>
    				{
    					item["selectType"] === "service" &&
                        <div className="single-param-item">
                        	<div className="param-title">
                        		<span>map类型：</span>
                        	</div>
                        	<div className="param-field">
                        		<Select
                        			value={item["mapType"] || undefined}
                        			rows={4}
                        			placeholder="请选择map类型"
                        			style={{ width: "100%" }}
                        			onChange={(e) => {
                        				this.changeGroupChildParam(index, "mapType", "select", "onChange", e);
                        			}}
                        		>
                        			<Option value="static">静态map</Option>
                        			<Option value="dynamic">动态map</Option>
                        			{/* <Option value="attrFilter">属性过滤map</Option> */}
                        		</Select>
                        	</div>
                        </div>
    				}
    				{
    					(item["selectType"] === "local" || item["selectType"] === "service") &&
                        <div className="single-param-item">
                        	<div className="param-title">
                        		<span>Map名称：</span>
                        	</div>
                        	<div className="param-field">
                        		<Input
                        			value={item["selectName"] || undefined}
                        			placeholder="请输入select选择项map名称"
                        			onChange={(e) => {
                        				this.changeGroupChildParam(index, "selectName", "input", "onChange", e);
                        			}}
                        			onBlur={(e) => {
                        				this.changeGroupChildParam(index, "selectName", "input", "onChange", e);
                        			}}
                        		/>
                        	</div>
                        </div>
    				}
    				{
    					item["selectType"] === "self" &&
                        <li className="single-param-item">
                        	<div className="param-title">
                        		<span>添加选项：</span>
                        	</div>
                        	<div className="param-field">
                        		<span className="add-select-option" onClick={this.addSelectOption.bind(this, index)}>添加选项</span>
                        	</div>
                        	<div className="param-select-option">
                        		<ul>
                        			{
                        				item["selectOption"] && item["selectOption"].map((item, childInd) => {
                        					return (
                        						<li key={index}>
                        							<InputGroup compact>
                        								<Input
                        									value={item.name}
                        									addonBefore="中文名称"
                        									onChange={this.changeSelectOption.bind(this, index, childInd, "name")}
                        									style={{ width: "35%" }}
                        								/>
                        								<Input
                        									value={item.enName}
                        									addonBefore="英文名称"
                        									onChange={this.changeSelectOption.bind(this, index, childInd, "enName")}
                        									style={{ width: "35%" }}
                        								/>
                        								<Input value={item.value}
                        									addonBefore="选项值"
                        									onChange={this.changeSelectOption.bind(this, index, childInd, "value")}
                        									style={{ width: "30%" }}
                        								/>
                        								<Tooltip placement="top" content="移除此选项">
                        									<Icon
                        										onClick={this.removeSelectOption.bind(this, index, childInd)}
                        										className="param-select-option-delete"
                        										type="minus-circle"
                        									/>
                        								</Tooltip>
                        							</InputGroup>
                        						</li>
                        					);
                        				})
                        			}
                        		</ul>
                        	</div>
                        </li>
    				}
    				{/*
    				<div className="param-item">
    					<div className="param-title">
    						<span>map名称：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.selectName || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "selectName", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "selectName", "input", "onChange", e);
    							}}
    							placeholder="请输入select选择项map名称"
    							size="small"
    						/>
    					</div>
    				</div> */}
    				<div className="param-item">
    					<div className="param-title">
    						<span>placeholder：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.placeholder || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "placeholder", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "placeholder", "input", "onChange", e);
    							}}
    							placeholder="中文placeholder（非必填）"
    							addonBefore="cn"
    							size="small"
    						/>
    						<Input
    							value={item.enPlaceholder || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "enPlaceholder", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "enPlaceholder", "input", "onChange", e);
    							}}
    							placeholder="英文placeholder（非必填）"
    							addonBefore="en"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>二次编辑：</span>
    					</div>
    					<div className="param-field">
    						<Switch
    							checkedChildren="可以"
    							unCheckedChildren="不可以"
    							checked={item.canEditSecondTimes}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "canEditSecondTimes", "select", "onChange", e);
    							}}
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>组件长度：</span>
    					</div>
    					<div className="param-field">
    						<InputNumber
    							min={1}
    							max={18}
    							step={1}
    							value={item.span || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "span", "select", "onChange", e);
    							}}
    							size="small"
    							style={{ width: "100%" }}
    							placeholder="网格系统24，如果一个参数，可以设置8，不填默认是4"
    						/>
    					</div>
    				</div>
    				<hr style={{ borderColor: "#fff" }} />
    				<ChangeGroupRuleForSelf componentType={item.componentType} childIndex={childIndex} groupItemIndex={index}/>
    				<ChangeGroupSelfRuleForParent componentType={item.componentType} childIndex={childIndex} groupItemIndex={index}/>
    				<ChangeGroupRuleForParent componentType={item.componentType} childIndex={childIndex} groupItemIndex={index}/>
    			</div>
    		);
    	} else if (item.componentType === "checkbox") {
    		return (
    			<div
    				className="group-child-item"
    				key={index}
    			>
    				<div className="child-item-header">
    					<span className="title">
                            CHECKBOX
    					</span>
    					<span className="delete">
    						<Icon
    							type="delete"
    							onClick={() => {
    								this.deleteGroupChildParam(index);
    							}}
    						/>
    					</span>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>唯一标识：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.name || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "name", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "name", "input", "onChange", e);
    							}}
    							placeholder="请输入唯一标识"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>默认值：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.defaultValue || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "defaultValue", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "defaultValue", "input", "onChange", e);
    							}}
    							placeholder="请输入默认值"
    							size="small"
    						/>
    					</div>
    				</div>
    				<li className="param-item">
    					<div className="param-title">
    						<span>添加选项：</span>
    					</div>
    					<div className="param-field">
    						<span className="add-select-option" onClick={this.addSelectOption.bind(this, index)}>添加选项</span>
    					</div>
    					<div className="param-select-option">
    						<ul>
    							{
    								item["selectOption"] && item["selectOption"].map((item, childInd) => {
    									return (
    										<li key={childInd}>
    											<InputGroup compact>
    												<Input
    													value={item.name}
    													addonBefore="中文名称"
    													onChange={this.changeSelectOption.bind(this, index, childInd, "name")}
    													style={{ width: "35%" }}
    												/>
    												<Input
    													value={item.enName}
    													addonBefore="英文名称"
    													onChange={this.changeSelectOption.bind(this, index, childInd, "enName")}
    													style={{ width: "35%" }}
    												/>
    												<Input value={item.value}
    													addonBefore="选项值"
    													onChange={this.changeSelectOption.bind(this, index, childInd, "value")}
    													style={{ width: "30%" }}
    												/>
    												<Tooltip placement="top" content="移除此选项">
    													<Icon
    														onClick={this.removeSelectOption.bind(this, index, childInd)}
    														className="param-select-option-delete"
    														type="minus-circle"
    													/>
    												</Tooltip>
    											</InputGroup>
    										</li>
    									);
    								})
    							}
    						</ul>
    					</div>
    				</li>
    				<div className="param-item">
    					<div className="param-title">
    						<span>字段最终类型：</span>
    					</div>
    					<div className="param-field">
    						<Select
    							value={item.typeFinal || undefined}
    							rows={4}
    							placeholder="请选择数据最终类型"
    							style={{ width: "100%" }}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "typeFinal", "select", "onChange", e);
    							}}
    						>
    							<Option value="string">string</Option>
    							<Option value="int">int</Option>
    							<Option value="context">context</Option>
    						</Select>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>二次编辑：</span>
    					</div>
    					<div className="param-field">
    						<Switch
    							checkedChildren="可以"
    							unCheckedChildren="不可以"
    							checked={item.canEditSecondTimes}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "canEditSecondTimes", "select", "onChange", e);
    							}}
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>组件长度：</span>
    					</div>
    					<div className="param-field">
    						<InputNumber
    							min={1}
    							max={18}
    							step={1}
    							value={item.span || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "span", "select", "onChange", e);
    							}}
    							size="small"
    							style={{ width: "100%" }}
    							placeholder="网格系统24，如果一个参数，可以设置8，不填默认是4"
    						/>
    					</div>
    				</div>
    				<hr style={{ borderColor: "#fff" }} />
    				<ChangeGroupRuleForSelf componentType={item.componentType} childIndex={childIndex} groupItemIndex={index}/>
    				<ChangeGroupSelfRuleForParent componentType={item.componentType} childIndex={childIndex} groupItemIndex={index}/>
    				<ChangeGroupRuleForParent componentType={item.componentType} childIndex={childIndex} groupItemIndex={index}/>
    			</div>
    		);
    	} else if (item.componentType.toLocaleUpperCase().indexOf("PICKER") > -1) {
    		return (
    			<div
    				className="group-child-item"
    				key={index}
    			>
    				<div className="child-item-header">
    					<span className="title">
    						{item.componentType.toLocaleUpperCase()}
    					</span>
    					<span className="delete">
    						<Icon
    							type="delete"
    							onClick={() => {
    								this.deleteGroupChildParam(index);
    							}}
    						/>
    					</span>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>唯一标识：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.name || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "name", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "name", "input", "onChange", e);
    							}}
    							placeholder="请输入唯一标识"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>默认值：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.defaultValue || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "defaultValue", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "defaultValue", "input", "onChange", e);
    							}}
    							placeholder="请输入默认值"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>前缀：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.addonBefore || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "addonBefore", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "addonBefore", "input", "onChange", e);
    							}}
    							placeholder="请输入input前缀"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>placeholder：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.placeholder || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "placeholder", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "placeholder", "input", "onChange", e);
    							}}

    							placeholder="中文placeholder（非必填）"
    							addonBefore="cn"
    							size="small"
    						/>
    						<Input
    							value={item.enPlaceholder || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "enPlaceholder", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "enPlaceholder", "input", "onChange", e);
    							}}
    							placeholder="英文placeholder（非必填）"
    							addonBefore="en"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>二次编辑：</span>
    					</div>
    					<div className="param-field">
    						<Switch
    							checkedChildren="可以"
    							unCheckedChildren="不可以"
    							checked={item.canEditSecondTimes}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "canEditSecondTimes", "select", "onChange", e);
    							}}
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>组件长度：</span>
    					</div>
    					<div className="param-field">
    						<InputNumber
    							min={1}
    							max={18}
    							step={1}
    							value={item.span || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "span", "select", "onChange", e);
    							}}
    							size="small"
    							style={{ width: "100%" }}
    							placeholder="网格系统24，如果一个参数，可以设置8，不填默认是4"
    						/>
    					</div>
    				</div>
    				<hr style={{ borderColor: "#fff" }} />
    				<ChangeGroupRuleForSelf componentType={item.componentType} childIndex={childIndex} groupItemIndex={index}/>
    			</div>
    		);
    	} else if (item.componentType === "label") {
    		return (
    			<div
    				className="group-child-item"
    				key={index}
    			>
    				<div className="child-item-header">
    					<span className="title">
                            Label
    					</span>
    					<span className="delete">
    						<Icon
    							type="delete"
    							onClick={() => {
    								this.deleteGroupChildParam(index);
    							}}
    						/>
    					</span>
    				</div>

    				<div className="param-item">
    					<div className="param-title">
    						<span>内容：</span>
    					</div>
    					<div className="param-field">
    						<Input
    							value={item.text || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "text", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "text", "input", "onChange", e);
    							}}

    							placeholder="中文内容"
    							addonBefore="cn"
    							size="small"
    						/>
    						<Input
    							value={item.enText || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "enText", "input", "onChange", e);
    							}}
    							onBlur={(e) => {
    								this.changeGroupChildParam(index, "enText", "input", "onChange", e);
    							}}
    							placeholder="英文内容"
    							addonBefore="en"
    							size="small"
    						/>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>对齐方式：</span>
    					</div>
    					<div className="param-field">
    						<Select
    							style={{ width: "100%" }}
    							placeholder="请选择对齐方式"
    							value={item.alignType || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "alignType", "select", "onChange", e);
    							}}
    						>
    							<Option value="center">居中</Option>
    							<Option value="left">居左</Option>
    							<Option value="right">居右</Option>
    						</Select>
    					</div>
    				</div>
    				<div className="param-item">
    					<div className="param-title">
    						<span>组件长度：</span>
    					</div>
    					<div className="param-field">
    						<InputNumber
    							min={1}
    							max={18}
    							step={1}
    							value={item.span || undefined}
    							onChange={(e) => {
    								this.changeGroupChildParam(index, "span", "select", "onChange", e);
    							}}
    							size="small"
    							style={{ width: "100%" }}
    							placeholder="网格系统24，如果一个参数，可以设置8，不填默认是4"
    						/>
    					</div>
    				</div>
    			</div>
    		);
    	}
    }

    render() {
    	let { templateStore, childIndex, componentType } = this.props;
    	let { operatorTemplateItemIndex, dialogData } = templateStore;
    	let { modifyTemplateData } = dialogData;
    	let cfgJson = modifyTemplateData.cfgJson || null;
    	let params = cfgJson && cfgJson["params"] ? cfgJson["params"] : [];
    	let children = params && params[operatorTemplateItemIndex] && params[operatorTemplateItemIndex]["children"] ? params[operatorTemplateItemIndex]["children"] : [];
    	let currentParam = children && children[childIndex] ? children[childIndex] : {};

    	const menu = (
    		<Menu
    			onClick={(e) => {
    				this.addGroupChild("date", e.key);
    			}}
    		>
    			<Menu.Item key="DatePicker">
                    年/月/日
    			</Menu.Item>
    			<Menu.Item key="MonthPicker">
                    月
    			</Menu.Item>
    			<Menu.Item key="WeekPicker">
                    周
    			</Menu.Item>
    			<Menu.Item key="TimePicker">
                    时/分/秒
    			</Menu.Item>
    		</Menu>
    	);

    	return (
    		<div className="single-param">
    			<div className="single-param-header">
    				<h2>群组</h2>
    				<Icon
    					type="delete"
    					onClick={this.deleteChild.bind(this, childIndex)}
    				/>
    			</div>
    			<div className="single-param-item">
    				<div className="param-title">
    					<span>唯一标识：</span>
    				</div>
    				<div className="param-field">
    					<Input
    						value={currentParam["name"] || undefined}
    						onChange={(e) => {
    							this.changeParamValue("input", "name", "onChange", e);
    						}}
    						onBlur={(e) => {
    							this.changeParamValue("input", "name", "onBlur", e);
    						}}
    						placeholder="请输入唯一标识"
    					/>
    				</div>
    			</div>
    			<div className="single-param-item">
    				<div className="param-title">
    					<span>二次编辑：</span>
    				</div>
    				<div className="param-field">
    					<Switch
    						checkedChildren="可以"
    						unCheckedChildren="不可以"
    						checked={currentParam.canEditSecondTimes}
    						onChange={(e) => {
    							this.changeParamValue("select", "canEditSecondTimes", "onChange", e);
    						}}
    						size="small"
    					/>
    				</div>
    			</div>
    			<div className="single-param-item">
    				<div className="param-title">
    					<span>限制追加个数：</span>
    				</div>
    				<div className="param-field">
    					<InputNumber
    						style={{ "width": "100%" }}
    						value={currentParam["limitNum"]}
    						min={0}
    						onChange={(e) => {
    							this.changeParamValue("select", "limitNum", "onChange", e);
    						}}
    						placeholder="请输入限制的追加个数"
    					/>
    				</div>
    			</div>
    			<div className="single-param-item">
    				<div className="param-title">
    					<span>选项：</span>
    				</div>
    				<div className="param-field">
    					<a
    						className="mr10"
    						onClick={() => {
    							this.addGroupChild("input");
    						}}
    					>
                            添加input
    					</a>
    					<a
    						className="mr10"
    						onClick={() => {
    							this.addGroupChild("select");
    						}}
    					>
                            添加select
    					</a>
    					<a
    						className="mr10"
    						onClick={() => {
    							this.addGroupChild("label");
    						}}
    					>
                            添加文本
    					</a>
    					<a
    						className="mr10"
    						onClick={() => {
    							this.addGroupChild("checkbox");
    						}}
    					>
                            添加checkbox
    					</a>
    					<Dropdown
    						overlay={menu}
    						overlayClassName="no-pad-drop-down"
    					>
    						<a className="ant-dropdown-link" href="#">
                                添加时间控件 <Icon type="down" />
    						</a>
    					</Dropdown>
    				</div>
    			</div>
    			<hr style={{ borderColor: "#fff" }} />
    			<div className="single-param-item">
    				<div className="param-title"></div>
    				<div className="param-field">
    					<div className="group-child-list">
    						{
    							currentParam &&
                                currentParam.children &&
                                currentParam.children.map((item, index) => {
                                	return this.renderGroupChild(item, index);
                                })
    						}
    					</div>
    				</div>
    			</div>
    		</div>
    	);
    }
}

export default connect(state => ({
	templateStore: state.template
}))(GroupTemplate);
