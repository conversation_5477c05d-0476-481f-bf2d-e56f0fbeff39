import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		title: {
			"cn": "审批设置",
			"en": "Approval Setting"
		},
		updateSuccessfully: {
			"cn": "更新审批项目配置成功",
			"en": "The approval project configuration was updated successfully"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"name": {
			"cn": "名称",
			"en": "Name"
		},
		"status": {
			"cn": "状态",
			"en": "Status"
		},
		"off": {
			"cn": "关闭",
			"en": "Off"
		},
		"on": {
			"cn": "开启",
			"en": "On"
		}
	};
	return params[field][lang];
};

export default {
	common,
	table
};
