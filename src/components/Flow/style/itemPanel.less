:global {
    .flow-item-panel {
        & {
            position: relative;
            flex: 1;
            background: #fafafa;
        }

        .flow-item-panel-mask {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
            cursor: not-allowed;
            background: rgba(255, 255, 255, 0.18);
        }

        .flow-item-body {
            & {
                position: relative;
                top: 48px;
                display: flex;
                flex-direction: column;
                align-items: center;
                height: ~"calc(100vh - 184px)";
                overflow: auto;
            }

            img {
                margin-bottom: 10px;
                width: 78px;
                height: 72px;
            }

            .flow-item {
                & {
                    margin-bottom: 10px;
                    user-select: none;
                    cursor: move;
                }

                &.flow-circle {
                    width: 50px;
                    height: 50px;
                    text-align: center;
                    line-height: 50px;
					border-radius: 50%;
					color:#fff;
                    // border: 1px solid #1992e5;
                }
                &.flow-start {
                    // border: 1px solid #ca7782;
					// background: rgba(235, 142, 109, 0.6);
					background: #ca808b;
                }
                &.flow-end {
                    // border: 1px solid #ebb164;
					// background: rgba(248, 207, 106, 0.51);
					background: #748993;
                }
                &.flow-rhombus {
                    width: 52px;
                    height: 48px;
                }

				&.flow-public-policy,
                &.flow-policy,
                &.flow-model,
                &.flow-service {
                    width: 100px;
                    height: 40px;
                    text-align: center;
                    line-height: 40px;
                    border-radius: 20px;
                }

                &.flow-policy {
                    border: 1px solid #bbc5f7; //#90a2e8;
					background: #d9dffc; //rgba(158, 176, 247, 0.5);
                }

				&.flow-public-policy{
					border: 1px solid #8BC34A;
                    background: #c4dfa7; //rgba(139, 195, 74, 0.5);
				}

                &.flow-model {
                    border: 1px solid #7faee8; //#dc4a27;
                    background: #bdd9fc; //rgba(235, 142, 109, 0.6);
                }

                &.flow-service {
                    border: 1px solid #64aef5; //#187ad4;
                    background: #7ab6f1; //rgba(24, 122, 212, 0.4);
                }
            }
        }
    }
}
