import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Transfer, Modal, Row, Col, Spin, message, Button, Tag, Tooltip } from "antd";
import { indexListLang, commonLang } from "@/constants/lang";
import { indexEditorAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";
import "./index.less";

class AuthListModal extends PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			refresh: props.refresh || void 0,
			disabled: props.disabled
		};
	}
	submitModal = () => {
		const { refresh } = this.state;
		const { indexEditorStore, globalStore, dispatch } = this.props;
		const { dialogData } = indexEditorStore;
		const { authListData = {} } = dialogData || {};
		const { targetKeys, zbUuid } = authListData || {};
		if (!(targetKeys && targetKeys.length > 0)) {
			// 至少授权一个应用
			message.warning(indexListLang.authModal("authWarn"));
			return ;
		}

		const { allMap } = globalStore;
		const { allApplications = [] } = allMap || {};
		indexEditorAPI.modifyLicenseApps({
			zbUuid: zbUuid,
			app: JSON.stringify(targetKeys)
		}).then(res=>{
			if (res.success) {
				message.success(res.message);
				this.onCancel();
				refresh && refresh();
				// 刷新allMap
				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
			} else {
				const { data = [] } = res;
				let appName = [];
				if (allApplications && allApplications.length > 0 && data && data.length > 0) {
					allApplications.forEach(v=>{
						if (data.indexOf(v.name) > -1) {
							appName.push(v.dName);
						};
					});
				}
				Modal.error({
					title: res.message,
					width: "460px",
					content: (
						(
							appName &&
							appName.length > 0
						)
							? <Fragment>
								{
									appName.map(v=>{
										return <Tooltip title={v}><Tag className="auth-app-warn" color="blue">{v}</Tag></Tooltip>;
									})
								}
							</Fragment>
							: ""
					)
				});
			}
		}).catch(e=>{
			message.success(e.message);
		});
	}
	handleChange = (targetKeys) => {
		const { indexEditorStore, dispatch } = this.props;
		const { dialogData } = indexEditorStore;
		const { authListData = {} } = dialogData || {};
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				authListData: {
					...authListData,
					targetKeys
				}
			}
		});
	}
	handleSelectChange = (sourceSelectedKeys, targetSelectedKeys) => {
		const { indexEditorStore, dispatch } = this.props;
		const { dialogData } = indexEditorStore;
		const { authListData = {} } = dialogData || {};
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				authListData: {
					...authListData,
					selectedKeys: [...sourceSelectedKeys, ...targetSelectedKeys]
				}
			}
		});
	}
	onCancel = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				authList: false,
				runningAuthList: false
			}
		});
	}
	filterOption = (inputValue, option) => option.dName.indexOf(inputValue) > -1;

	render() {
		const { disabled } = this.state;
		const { indexEditorStore, globalStore } = this.props;
		const { dialogShow, dialogData } = indexEditorStore;
		const { authListData = {} } = dialogData || {};
		const { selectedKeys, targetKeys, authorizedLoad } = authListData || {};
		const { allMap } = globalStore;
		const { allApplications = [] } = allMap || {};
		const newAllApplications = authorizedLoad ? [] : allApplications.map(v=>{
			return {
				...v,
				key: v.name
			};
		});
		const footer = [
			<Button key="back" onClick={this.onCancel}>
				{/* 取消 */}
				{commonLang.base("cancel")}
			</Button>
		];
		if (!disabled && checkFunctionHasPermission("ZB0102", "modifyLicenseApplication")) {
			footer.push(
				<Button key="save" onClick={this.submitModal} type="primary">
					{/* 保存 */}
					{commonLang.base("save")}
				</Button>
			);
		}
		let visible = false;
		if (disabled && dialogShow.runningAuthList) {
			visible = true;
		}
		if (!disabled && dialogShow.authList) {
			visible = true;
		}
		return (
			<Modal
    			title={indexListLang.authModal("title")} // lang:"授权使用列表"
    			width={688}
    			visible={visible}
    			maskClosable={false}
    			onOk={this.submitModal}
				onCancel={this.onCancel}
				footer={footer}
    		>
				<Spin spinning={authorizedLoad}>
					<Row>
						<Col span={24}>
							<Transfer
								className="wid-100-percent"
								listStyle={{
									width: 300,
									height: 400
								}}
								disabled={disabled}
								showSelectAll
								dataSource={newAllApplications}
								showSearch
								targetKeys={targetKeys}
								selectedKeys={selectedKeys}
								filterOption={this.filterOption}
								onChange={this.handleChange}
								onSelectChange={this.handleSelectChange}
								render={item => item.dName}
								titles={[indexListLang.authModal("unauthorized"), indexListLang.authModal("authorized")]}
							/>
						</Col>
					</Row>
				</Spin>
    		</Modal>
		);
	}

}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor
}))(AuthListModal);
