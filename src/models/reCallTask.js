import { message } from "antd";
import { reCallTaskAPI, indexEditorAPI } from "@/services";

export default {
	namespace: "reCallTask",
	state: {
		searchParams: {
			zbUuid: "",
			zbName: "",
			status: "",
			curPage: 1,
			pageSize: 10
		},
		filterOption: { // 临时存放搜索条件
			zbName: "",
			status: ""
		},
		total: 0,
		reCallList: [],
		dialogShow: {
			searchResultModal: false,
			dealResultModal: false
		},
		dialogData: {
			dealResultData: {
				resultStatus: "",
				currentReCall: {}
			}
		},
		zbInfo: {
			zbName: "",
			zbFillBackUuid: "",
			dim1Value: "",
			dim1: "",
			slaveDimsForWeb: [],
			calcType: "",
			calcField: "",
			calcFieldValue: ""
		}
	},
	effects: {
		*getReCallList({ payload }, { call, put, select }) {
			const { searchParams, filterOption } = yield select(state=>state.reCallTask);
			let response = yield call(reCallTaskAPI.getReCallList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const { dataList = [] } = response.data || {};
			// 如果存在uuid 且没有指标名称 则将查询到的指标名称赋值
			if (payload.zbUuid && !searchParams.zbName && dataList && dataList.length > 0) {
				const zbName = dataList[0] ? dataList[0].zbName : "";
				yield put({
					type: "setAttrValue",
					payload: {
						searchParams: {
							...payload,
							zbName
						},
						filterOption: {
							...filterOption,
							zbName
						}
					}
				});
			}
			yield put({
				type: "initReCall",
				payload: response
			});
		},
		*viewIndexItemDetail({payload}, {call, put}) {
			const { zbUuid, zbFillBackUuid, zbName } = payload;
			const response = yield call(indexEditorAPI.viewIndexItemDetail, {
				uuid: zbUuid
			});
			if (response.success) {
				const { data = {} } = response || {};
				const { slaveDimsForWeb = [], dim1, calcType, calcField } = data || {};
				let slaveDimsData = [];
				if (slaveDimsForWeb && slaveDimsForWeb.length > 0) {
					slaveDimsForWeb.forEach(v=>{
						slaveDimsData.push({
							slaveField: v.slaveField,
							slaveValue: ""
						});
					});
				}
				yield put({
					type: "setAttrValue",
					payload: {
						zbInfo: {
							zbName,
							zbFillBackUuid,
							dim1,
							slaveDimsForWeb: slaveDimsData,
							calcType,
							calcField
						}
					}
				});
				yield put({
					type: "setDialogShow",
					payload: {
						searchResultModal: true
					}
				});
			} else {
				message.error(response.message || "请求失败");
			}
		}

	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		initReCall(state, action) {
			let { total, reCallList } = state;
			let data = action.payload.data || null;
			total = data.total;
			reCallList = data["dataList"] || [];
			return {
				...state,
				total,
				reCallList
			};
		}
	}
};

