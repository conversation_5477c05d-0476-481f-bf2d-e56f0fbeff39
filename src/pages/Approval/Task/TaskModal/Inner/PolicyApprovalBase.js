import { PureComponent } from "react";
import { connect } from "dva";
import { Button, Input, Radio, Row, Col, message } from "antd";
import { approvalTaskAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { checkFunctionHasPermission } from "@/utils/permission";
import { approvalTaskLang, commonLang } from "@/constants/lang";

const { TextArea } = Input;

class PolicyBase extends PureComponent {

	constructor(props) {
		super(props);
		this.changeBaseField = this.changeBaseField.bind(this);
		this.submitApproval = this.submitApproval.bind(this);
		this.closeModal = this.closeModal.bind(this);
		this.refreshPolicyEditArea = this.refreshPolicyEditArea.bind(this);
	}

	changeBaseField(field, e) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;

		policyApprovalData[field] = e.target.value;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				policyApprovalData: policyApprovalData
			}
		});
	}

	submitApproval() {
		let { approvalTaskStore, globalStore, dispatch } = this.props;
		let { dialogData, policyPage } = approvalTaskStore;
		let { policyApprovalData } = dialogData;
		let { curPage, pageSize, searchParams } = policyPage;
		let { uuid, desc, status } = policyApprovalData;
		const newSearchParams = { ...searchParams };

		let { currentApp } = globalStore;
		if (currentApp.name !== "all") {
			newSearchParams["appName"] = currentApp.name;
		} else {
			newSearchParams["appName"] = "";
		}

		if (!desc) {
			// lang:请输入审批描述
			message.error(approvalTaskLang.modal("enterDescriptionPlaceholder"));
			return;
		}
		if (desc && desc.length > 200) {
			// lang:审批描述不能超过200个字符
			message.error(approvalTaskLang.modal("descriptionLengthTip"));
			return;
		}
		let params = {
			uuid: uuid,
			status: status,
			desc: desc
		};
		approvalTaskAPI.doPolicyApproval(params).then(res => {
			if (res.success) {
				// lang:策略审批成功
				message.success(approvalTaskLang.message("policyApprovalSuccessfully"));
				// 关闭策略版本提交modal
				this.closeModal();
				// 重新拉取策略审批任务列表
				dispatch({
					type: "approvalTask/getPolicyApprovalTaskList",
					payload: {
						curPage: curPage,
						pageSize: pageSize,
						...newSearchParams
					}
				});

				// 刷新map状态
				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
				// 重新刷新编辑区策略列表
				this.refreshPolicyEditArea();
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	refreshPolicyEditArea() {
		let { policyEditorStore, globalStore, dispatch } = this.props;
		let { curPage, pageSize } = policyEditorStore;
		let { currentApp } = globalStore;
		if (currentApp && currentApp.name) {
			dispatch({
				type: "policyEditor/getPolicySets",
				payload: {
					appName: currentApp.name ? currentApp.name : null,
					curPage: curPage,
					pageSize: pageSize
				}
			});
		}
	}

	closeModal() {
		let { dispatch } = this.props;

		dispatch({
			type: "approvalTask/setDialogShow",
			payload: {
				policyApproval: false
			}
		});
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				policyApprovalData: {
					uuid: null,
					status: "APPROVED",
					desc: null,
					leftNavIndex: 0,
					tabIndex: 0,
					diffDetail: {
						baseDiff: null,
						ruleDiff: null
					}
				}
			}
		});
	}

	render() {
		let { approvalTaskStore, globalStore } = this.props;
		let { menuTreeReady } = globalStore;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;
		let { desc, status } = policyApprovalData;

		return (
			<div className="basic-form">
				<Row gutter={CommonConstants.gutterSpan}>
					<Col span={4} className="basic-info-title">
						{/* lang:审批结果 */}
						{approvalTaskLang.modal("approvalResult")}：
					</Col>
					<Col span={12}>
						<Radio.Group
							value={status || undefined}
							buttonStyle="solid"
							onChange={this.changeBaseField.bind(this, "status")}
						>
							<Radio.Button value="APPROVED">
								{/* lang:通过 */}
								{approvalTaskLang.modal("pass")}
							</Radio.Button>
							<Radio.Button value="REJECTED">
								{/* lang:拒绝 */}
								{approvalTaskLang.modal("reject")}
							</Radio.Button>
						</Radio.Group>
					</Col>
				</Row>
				<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
					<Col span={4} className="basic-info-title">
						{/* lang:审批原因 */}
						{approvalTaskLang.modal("approvalReason")}：
					</Col>
					<Col span={12}>
						<TextArea
							rows={4}
							value={desc || undefined}
							placeholder={approvalTaskLang.modal("enterDescriptionPlaceholder")} // lang:请输入审批描述
							onChange={this.changeBaseField.bind(this, "desc")}
						/>
					</Col>
				</Row>
				<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
					<Col span={4} className="basic-info-title"></Col>
					<Col span={12}>
						{
							menuTreeReady &&
							checkFunctionHasPermission("ZB0401", "changePolicyApprovalStatus") &&
							<Button
								type="primary"
								className="mr10"
								onClick={this.submitApproval.bind(this)}
							>
								{/* lang:确定 */}
								{approvalTaskLang.modal("ok")}
							</Button>
						}
						{
							menuTreeReady &&
							!checkFunctionHasPermission("ZB0401", "changePolicyApprovalStatus") &&
							<Button
								type="primary"
								className="mr10"
								disabled={checkFunctionHasPermission("ZB0401", "changePolicyApprovalStatus")}
							>
								{/* lang:暂无权限 */}
								{commonLang.messageInfo("noPermission")}
							</Button>
						}
						<Button onClick={this.closeModal.bind(this)}>
							{/* lang:取消 */}
							{approvalTaskLang.modal("cancel")}
						</Button>
					</Col>
				</Row>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(PolicyBase);
