import { PureComponent } from "react";
import { connect } from "dva";
import { approvalTaskAPI } from "@/services";
import { Button, Input, Table, Pagination, message, DatePicker } from "antd";
import moment from "moment";
import SalaxyApprovalModal from "../TaskModal/SalaxyApprovalModal";
import { checkFunctionHasPermission } from "@/utils/permission";
import { isJSON } from "@/utils/isJSON";
import { approvalTaskLang, commonLang } from "@/constants/lang";
import { approvalTaskConstants } from "@/constants";

const { RangePicker } = DatePicker;
const { operationTypeMap } = approvalTaskConstants;
class SalaxyTask extends PureComponent {

	constructor(props) {
		super(props);
		this.startSearch = this.startSearch.bind(this);
		this.changePagination = this.changePagination.bind(this);
		this.changeSearchParamField = this.changeSearchParamField.bind(this);
		this.salaxyApprovalHandle = this.salaxyApprovalHandle.bind(this);
	}

	componentDidMount() {
		// 页面初始化数据
		this.timer = setInterval(() => {
			const { globalStore } = this.props;
			const { menuTreeReady } = globalStore;
			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkFunctionHasPermission("ZB0401", "indexApprovalSearch")) {
					this.startSearch();
				}
			}
		}, 100);
	}

	startSearch() {
		let { approvalTaskStore, globalStore, dispatch } = this.props;
		let { salaxyPage } = approvalTaskStore;
		let { pageSize, searchParams } = salaxyPage;
		let { currentApp } = globalStore;
		if (currentApp.name !== "all") {
			searchParams["appName"] = currentApp.name;
		} else {
			searchParams["appName"] = "";
		}
		dispatch({
			type: "approvalTask/getSalaxyApprovalTaskList",
			payload: {
				curPage: 1,
				pageSize: pageSize,
				...searchParams
			}
		});
	}

	componentWillReceiveProps(nextProps) {
		let { approvalTaskStore, dispatch, globalStore } = this.props;
		let { menuTreeReady } = globalStore;
		let { salaxyPage } = approvalTaskStore;
		let { curPage, pageSize, searchParams } = salaxyPage;
		let preCurrentApp = this.props.globalStore.currentApp;
		let nextCurrentApp = nextProps.globalStore.currentApp;
		if (!menuTreeReady || !true) {
			// 这里放权限
			return;
		}
		if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
			if (nextCurrentApp.name !== "all") {
				searchParams["appName"] = nextCurrentApp.name;
			} else {
				searchParams["appName"] = "";
			}

			dispatch({
				type: "approvalTask/getSalaxyApprovalTaskList",
				payload: {
					curPage: curPage,
					pageSize: pageSize,
					...searchParams
				}
			});
		}
	}

	changePagination(curPage, pageSize) {
		let { approvalTaskStore, globalStore, dispatch } = this.props;
		let { salaxyPage } = approvalTaskStore;
		let { searchParams } = salaxyPage;
		let { currentApp } = globalStore;
		if (currentApp.name !== "all") {
			searchParams["app"] = currentApp.name;
		} else {
			searchParams["app"] = "";
		}

		dispatch({
			type: "approvalTask/getSalaxyApprovalTaskList",
			payload: {
				curPage: curPage,
				pageSize: pageSize,
				...searchParams
			}
		});
	}

	changeSearchParamField(field, type, e) {
		let { approvalTaskStore, dispatch } = this.props;
		let { salaxyPage } = approvalTaskStore;
		let { searchParams } = salaxyPage;

		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "date") {
			value = e;
		}
		if (field === "targetName") {
			searchParams[field] = value;
		} else if (field === "selectTs") {
			let startStr = moment(value[0]).format("YYYY-MM-DD") + " 00:00:00";
			let endStr = moment(value[1]).format("YYYY-MM-DD") + " 23:59:59";
			searchParams["startTs"] = value.length === 2 ? Date.parse(startStr) : null;
			searchParams["endTs"] = value.length === 2 ? Date.parse(endStr) : null;
		}
		dispatch({
			type: "approvalTask/setAttrValue",
			payload: {
				salaxyPage: salaxyPage
			}
		});
	}

	salaxyApprovalHandle(item) {
		let { approvalTaskStore, indexRunningStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { salaxyApprovalData } = dialogData;

		let { indexPages } = indexRunningStore;
		let { approvalTask } = indexPages;

		dispatch({
			type: "approvalTask/setDialogShow",
			payload: {
				salaxyApproval: true
			}
		});

		salaxyApprovalData.uuid = item.uuid;
		salaxyApprovalData.id = item.id;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				salaxyApprovalData: salaxyApprovalData
			}
		});

		let params = {
			uuid: item.uuid
		};
		approvalTaskAPI.getSalaxyApprovalTaskDiff(params).then(res => {
			if (res.success) {
				let onlineVersion = res.data && res.data.onlineVersion ? res.data.onlineVersion : null;
				let pendVersion = res.data && res.data.pendVersion ? res.data.pendVersion : null;

				if (onlineVersion && onlineVersion.id) {
					onlineVersion["filterList"] = onlineVersion["filterStr"] && isJSON(onlineVersion["filterStr"]) ? JSON.parse(onlineVersion["filterStr"]) : [];
					onlineVersion["attachFieldsObj"] = onlineVersion["attachFields"] && isJSON(onlineVersion["attachFields"]) ? JSON.parse(onlineVersion["attachFields"]) : [];
					onlineVersion["sceneList"] = onlineVersion["scene"] && isJSON(onlineVersion["scene"]) ? JSON.parse(onlineVersion["scene"]) : [];
				}
				if (pendVersion && pendVersion.id) {
					pendVersion["filterList"] = pendVersion["filterStr"] && isJSON(pendVersion["filterStr"]) ? JSON.parse(pendVersion["filterStr"]) : [];
					pendVersion["attachFieldsObj"] = pendVersion["attachFields"] && isJSON(pendVersion["attachFields"]) ? JSON.parse(pendVersion["attachFields"]) : [];
					pendVersion["sceneList"] = pendVersion["scene"] && isJSON(pendVersion["scene"]) ? JSON.parse(pendVersion["scene"]) : [];
				}

				// console.log(onlineVersion);
				// console.log(pendVersion);

				if (onlineVersion) {
					onlineVersion["approvalStatus"] = "之前的版本";
					approvalTask["indexList"].push(onlineVersion);
				}
				if (pendVersion) {
					pendVersion["approvalStatus"] = "现在的版本";
					approvalTask["indexList"].push(pendVersion);
				}
				dispatch({
					type: "indexRunning/setAttrValue",
					payload: {
						indexPages: indexPages
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { approvalTaskStore } = this.props;
		let { salaxyPage } = approvalTaskStore;
		let { taskList, total, curPage, searchParams } = salaxyPage;
		let { startTs, endTs } = searchParams;
		let columns = [
			{
				title: approvalTaskLang.table("operationType"),		// lang:变更类型
				render: (record) => {
					return (
						<div>
							{
								record["operationType"] &&
								operationTypeMap[record["operationType"]] &&
								operationTypeMap[record["operationType"]]()["text"]
							}
						</div>
					);
				}
			},
			{
				title: approvalTaskLang.table("indicatorName"),		// lang:指标名称
				dataIndex: "targetName"
			},
			{
				title: approvalTaskLang.table("publishDesc"),		// lang:发版描述
				dataIndex: "description"
			},
			{
				title: approvalTaskLang.table("createdBy"),		// lang:提交人
				dataIndex: "createdBy"
			},
			{
				title: approvalTaskLang.table("submitTime"),		// lang:提交时间
				dataIndex: "gmtCreate"
			},
			{
				title: approvalTaskLang.table("operation"),		// lang:操作
				width: 180,
				render: (record) => {
					return (
						<div className="table-action">
							<a
								className={checkFunctionHasPermission("ZB0401", "changeIndexApproval") ? null : "a-disabled"}
								onClick={() => {
									if (checkFunctionHasPermission("ZB0401", "changeIndexApproval")) {
										this.salaxyApprovalHandle(record);
									} else {
										// lang:没有权限
										message.info(commonLang.messageInfo("noPermission"));
									}
								}}
							>
								{/* lang:审批 */}
								{approvalTaskLang.table("approval")}
							</a>
						</div>
					);
				}
			}
		];
		return (
			<div>
				<div className="page-global-body">
					<div className="page-global-body-search">
						<div className="left-info">
							<h2>
								{/* lang:策略审批任务列表 */}
								{approvalTaskLang.common("indicatorTitle2")}
							</h2>
						</div>
						<div className="right-info">
							<div className="right-info-item">
								<RangePicker
									onChange={this.changeSearchParamField.bind(this, "selectTs", "date")}
									style={{ width: "270px" }}
									value={startTs && endTs ? [moment(startTs), moment(endTs)] : []}
									format="YYYY-MM-DD"
									disabled={!checkFunctionHasPermission("ZB0401", "indexApprovalSearch")}
								/>
							</div>
							<div className="right-info-item">
								<Input
									placeholder={approvalTaskLang.searchParams("indicatorNamePlaceholder")} // lang:请输入策略名称
									value={searchParams.targetName || undefined}
									onChange={this.changeSearchParamField.bind(this, "targetName", "input")}
									onPressEnter={this.startSearch.bind(this)}
									disabled={!checkFunctionHasPermission("ZB0401", "indexApprovalSearch")}
								/>
							</div>
							<div className="right-info-item">
								<Button
									type="primary"
									className="search-button"
									onClick={this.startSearch.bind(this)}
									disabled={!checkFunctionHasPermission("ZB0401", "indexApprovalSearch")}
								>
									{/* lang:搜索 */}
									{approvalTaskLang.searchParams("search")}
								</Button>
							</div>
						</div>
					</div>
					<div className="page-global-body-main">
						<Table
							className="table-card-body"
							columns={columns}
							dataSource={taskList}
							pagination={false}
							rowKey="id"
						/>
						<div className="page-global-body-pagination">
							<span className="count">
								{commonLang.getRecords(total)}
							</span>
							<Pagination
								showQuickJumper
								showSizeChanger
								onChange={this.changePagination.bind(this)}
								onShowSizeChange={this.changePagination.bind(this)}
								current={curPage}
								total={total}
							/>
						</div>
					</div>
				</div>
				<SalaxyApprovalModal />
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask,
	indexRunningStore: state.indexRunning
}))(SalaxyTask);
