.circle-process {
	circle {
		stroke-dashoffset: 0;
		transition: stroke-dashoffset 1s linear;
		stroke: #efefef;
		stroke-width: 1em;
	}
	.bar {
		stroke: #f15224;
		&.success{
			stroke: #86d16b;
		}
	}
}
.cont-wrap {
	display: block;
	height: 160px;
	width: 160px;
	margin: 2em auto;
	border-radius: 100%;
	position: relative;
	&:after {
		position: absolute;
		display: block;
		height: 140px;
		width: 130px;
		left: 50%;
		top: 50%;
		content: attr(data-pct)"%";
		margin-top: -60px;
		margin-left: -65px;
		border-radius: 100%;
		line-height: 140px;
		font-size: 2em;
		text-align:center;
	}
	.txt{
		position: absolute;
		left: 50%;
		top: 50%;
		margin-top: -28px;
		margin-left: -65px;
		font-size:12px;
		width:130px;
		text-align: center;
	}
}
.to-effect{
    margin-bottom: -0.6em;
    font-weight: 500;
    margin-top: 4px;
}
.ml5{
	margin-left:5px;
}
