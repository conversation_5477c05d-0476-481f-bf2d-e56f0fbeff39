:global {
    .sort-rule-item {
        & {
            width: auto;
            height: 44px;
            line-height: 44px;
            border: 1px solid #e6e6e6;
            margin-bottom: 10px;
            clear: both;
            padding: 0 10px;
            background: #fff;
            border-radius: 5px;
            cursor: move;
        }

        &:hover {
            background: #f0f0f0;
        }

        .sort-rule-name {
            float: left;
            width: 380px;
            overflow: hidden;
            text-overflow: ellipsis;
            -o-text-overflow: ellipsis;
            -webkit-text-overflow: ellipsis;
            -moz-text-overflow: ellipsis;
            white-space: nowrap;
        }

        .right-info {
            & {
                float: right;
            }

            .sort-rule-id,
            .sort-rule-status {
                float: right;
            }

            .sort-rule-status {
                margin-left: 12px;
            }
        }
    }
}
