import React, { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import "./PolicyDetail.less";
import { policyDetailAPI } from "@/services";
import moment from "moment";
import { isJSON } from "@/utils/isJSON";
import { Button, Menu, message, Icon, Tooltip, Input, Popover, Popconfirm, Alert, Dropdown, Tag } from "antd";

const PublicPolicyBasic = React.lazy(() => import("@/components/PolicyDetail/Basic/BasicPublic"));
const RuleList = React.lazy(() => import("@/components/PolicyDetail/RuleManage/RuleList"));
const TemplateDrawer = React.lazy(() => import("@/components/TemplateDrawer"));
const AddTemplateModal = React.lazy(() => import("@/components/PolicyDetail/AddTemplateModal"));
const AddToCustomListModal = React.lazy(() => import("@/components/PolicyDetail/AddToCustomListModal"));
const PolicyCiteIndexModal = React.lazy(() => import("@/components/PolicyDetail/PolicyCiteIndexModal"));
const PolicyCommitModal = React.lazy(() => import("./Modal/PolicyCommitModal"));
const RuleSortModal = React.lazy(() => import("./Modal/RuleSortModal"));

import { searchToObject } from "@/utils/utils";
import { checkFunctionHasPermission } from "@/utils/permission";
import checkPermissionPublicPolicy from "@/constants/permission/checkPermissionPublicPolicy";
import { PolicyConstants } from "@/constants";
import { policyDetailLang } from "@/constants/lang";
import PolicyVersionDrawer from "@/components/PolicyVersionDrawer";

let policyDetailTimeout = null;

class PublicPolicyDetail extends PureComponent {
	state = {
		historyVersion: []
	};

	constructor(props) {
		super(props);
		this.changeTabIndex = this.changeTabIndex.bind(this);
		this.removePolicyRuleCopyItem = this.removePolicyRuleCopyItem.bind(this);
		this.checkoutLocalStorage = this.checkoutLocalStorage.bind(this);
		this.emptyPolicyRuleCopyBox = this.emptyPolicyRuleCopyBox.bind(this);
		this.rulePaste = this.rulePaste.bind(this);
		this.ruleSortHandle = this.ruleSortHandle.bind(this);
	}

	componentWillMount() {
		let { dispatch, match, location } = this.props;
		let { policyUuid } = match["params"];
		let { search } = location;
		let searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		let tabIndex = searchObj && searchObj.tabIndex ? parseInt(searchObj.tabIndex, 10) : 0;
		// 检测localStorage
		this.checkoutLocalStorage();
		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				policyUuid: policyUuid,
				tabIndex: tabIndex
			}
		});
	}

	checkoutLocalStorage() {
		let { dispatch } = this.props;
		if (localStorage.getItem("policyRuleCopyList")) {
			if (isJSON(localStorage.getItem("policyRuleCopyList"))) {
				let policyRuleCopyListStr = localStorage.getItem("policyRuleCopyList") || "[]";
				dispatch({
					type: "global/setAttrValue",
					payload: {
						policyRuleCopyList: JSON.parse(policyRuleCopyListStr)
					}
				});
			}
		}
	}

	async componentDidMount() {
		let { dispatch, match, location } = this.props;
		let { policyUuid } = match["params"];

		await dispatch({
			type: "policyDetail/getPolicyRules",
			payload: {
				policyUuid: policyUuid
			}
		});

		let { search } = location;
		let searchObj = search && searchToObject(search) ? searchToObject(search) : null;
		let ruleUuid = searchObj && searchObj.ruleUuid ? searchObj.ruleUuid : null;
		if (ruleUuid) {
			dispatch({
				type: "policyDetail/setAttrValue",
				payload: {
					currentRuleUuid: ruleUuid
				}
			});
			policyDetailTimeout = setTimeout(() => {
				dispatch({
					type: "policyDetail/setAttrValue",
					payload: {
						ruleActiveKey: [ruleUuid]
					}
				});
			}, 20);
		} else {
			dispatch({
				type: "policyDetail/setAttrValue",
				payload: {
					ruleActiveKey: [],
					currentRuleUuid: null
				}
			});
		}

		// 获取历史版本
		this.refreshHistoryVersion();
	}

	// 获取历史版本
	refreshHistoryVersion = () => {
		let { match } = this.props;
		let { policyUuid } = match["params"];
		policyDetailAPI.getPublicPolicyVersionList({ policyUuid }).then(res => {
			this.setState({
				historyVersion: res.data || []
			});
			if (!res.success) {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	componentWillUnmount() {
		policyDetailTimeout && clearTimeout(policyDetailTimeout);
	}

	changeTabIndex(index) {
		let { dispatch } = this.props;

		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				tabIndex: index
			}
		});
	}

	removePolicyRuleCopyItem(index) {
		let { globalStore, dispatch } = this.props;
		let { policyRuleCopyList } = globalStore;
		policyRuleCopyList.splice(index, 1);
		dispatch({
			type: "global/setAttrValue",
			payload: {
				policyRuleCopyList: policyRuleCopyList
			}
		});
		localStorage.setItem("policyRuleCopyList", JSON.stringify(policyRuleCopyList));
	}

	emptyPolicyRuleCopyBox() {
		let { dispatch } = this.props;

		dispatch({
			type: "global/setAttrValue",
			payload: {
				policyRuleCopyList: []
			}
		});
		localStorage.setItem("policyRuleCopyList", "[]");
	}

	rulePaste(item, index) {
		let { policyDetailStore, globalStore, dispatch } = this.props;
		let { policyRuleCopyList } = globalStore;
		let { policyDetail, policyRules } = policyDetailStore;
		let params = {
			name: policyDetail.name,
			targetPolicyUuid: policyDetail.uuid,
			srcRuleUuid: item.ruleUuid
		};
		policyDetailAPI.rulePaste(params).then(res => {
			if (res.success) {
				// lang:添加规则成功！
				message.success(policyDetailLang.tipInfo("addSuccess"));
				let ruleData = res.data;
				ruleData["operationActions"] = ruleData["operationActions"] && isJSON(ruleData["operationActions"]) ? JSON.parse(ruleData["operationActions"]) : [];
				ruleData["triggers"] = ruleData["triggers"] && isJSON(ruleData["triggers"]) ? JSON.parse(ruleData["triggers"]) : [];
				if (ruleData["conditions"]) {
					ruleData["conditions"]["params"] = ruleData["conditions"]["params"] && isJSON(ruleData["conditions"]["params"]) ? JSON.parse(ruleData["conditions"]["params"]) : [];
					ruleData["conditions"]["children"] &&
					ruleData["conditions"]["children"].map((conditionItem) => {
						// 变量children
						if (conditionItem.type === "alias") {
							conditionItem["params"] = conditionItem["params"] && isJSON(conditionItem["params"]) ? JSON.parse(conditionItem["params"]) : [];
						}
					});
				}
				policyRules.push(ruleData);
				dispatch({
					type: "policyDetail/setPolicyRule",
					payload: {
						policyRules: policyRules
					}
				});

				// 更新规则成功后刷新策略审批状态
				dispatch({
					type: "policyDetail/changePolicyStatus",
					payload: {
						uuid: policyDetail.uuid
					}
				});
			} else {
				message.error(res.message);
				if (res.message.indexOf("规则不存在") > -1 || res.code.indexOf("400") > -1) {
					policyRuleCopyList[index]["notExist"] = true;
					dispatch({
						type: "global/setAttrValue",
						payload: {
							policyRuleCopyList: policyRuleCopyList
						}
					});
					localStorage.setItem("policyRuleCopyList", JSON.stringify(policyRuleCopyList));
				}
			}
		}).catch(err => {
			console.log(err);
		});
	}

	ruleSortHandle() {
		let { policyDetailStore, dispatch } = this.props;
		let { policyRules, dialogData } = policyDetailStore;
		let { ruleSortData } = dialogData;
		ruleSortData["ruleList"] = policyRules;

		let noRule = true;
		ruleSortData["ruleList"].forEach((item) => {
			if (!item.unSave) {
				noRule = false;
			}
		});

		if (noRule) {
			// lang:暂无已保存规则
			message.warning(policyDetailLang.ruleSortModal("noSaveRule"));
			return;
		}

		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				ruleSort: true
			}
		});

		dispatch({
			type: "policyDetail/setDialogData",
			payload: {
				ruleSortData: ruleSortData
			}
		});
	}

	openPolicyVersionDrawer = () => {
		let { policyDetailStore, dispatch } = this.props;
		let { policyDetail, dialogData } = policyDetailStore;
		let { policyVersionDrawerData } = dialogData;
		let params = {
			policyUuid: policyDetail.uuid
		};

		policyDetailAPI.getPolicyVersionList(params).then(res => {
			if (res.success) {
				policyVersionDrawerData["versionList"] = res.data || [];
				dispatch({
					type: "policyDetail/setDialogData",
					payload: {
						policyVersionDrawerData: policyVersionDrawerData
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});

		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				policyVersionDrawer: true
			}
		});
	}
	render() {
		let { policyDetailStore, globalStore, dispatch, location } = this.props;
		let { policyRuleCopyList, personalMode, allMap } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		const { historyVersion } = this.state;

		let { policyDetail, tabIndex, ruleSearch, policyOriginalAllInfo } = policyDetailStore;
		let { showSearch, searchWord } = ruleSearch;

		let policyRuleCopyDom = [];
		if (policyRuleCopyList.length) {
			policyRuleCopyDom = policyRuleCopyList.map((item, index) => {
				return (
					<li key={index}>
						<div className="line1">
							<h3
								className="text-overflow"
								style={{ maxWidth: "220px" }}
							>
								<Tooltip title={item.ruleName} placement="left">
									{item.ruleName}
								</Tooltip>
							</h3>
							<div className="action-btns">
								<Button.Group size="small">
									{/* 从暂存区移除 */}
									<Tooltip title={policyDetailLang.tipInfo("removeStag")}>
										<Button type="default"
											onClick={this.removePolicyRuleCopyItem.bind(this, index)}>
											<Icon type="delete" />
										</Button>
									</Tooltip>
									{
										!item.notExist &&
										// 添加此规则到当前策略
										<Tooltip title={policyDetailLang.tipInfo("addRuleToPolicy")}>
											<Button type="default" onClick={this.rulePaste.bind(this, item, index)}>
												<Icon type="plus" />
											</Button>
										</Tooltip>
									}
								</Button.Group>
							</div>
						</div>
						{
							!item.notExist &&
							<div className="line2">
								<p className="source">
									<span>
										{/* 来源： */}
										{policyDetailLang.tipInfo("source")}：
                            		</span>{item.policyName}
									<span className="create-ts">{moment(item.createTs).fromNow()}</span>
								</p>
							</div>
						}
						{
							item.notExist &&
							<div className="line2">
								<p className="not-exist">
									{/* 当前规则已被删除，无法粘贴！ */}
									{policyDetailLang.tipInfo("cantPaste")}
								</p>
							</div>
						}
					</li>
				);
			});
		} else {
			policyRuleCopyDom = (
				<div className="none-data">
					<i className="iconfont icon-empty"></i>
					<p>
						{/* 暂存区暂无规则 */}
						{policyDetailLang.tipInfo("stagNoRules")}
					</p>
				</div>
			);
		}
		let ruleStoragePopoverTitle = (
			<div className="title">
				<h3>
					{/* 已复制规则暂存区 */}
					{policyDetailLang.tipInfo("hasCopyStag")}
				</h3>
				{
					policyRuleCopyList.length > 0 &&
					<Popconfirm
						title={policyDetailLang.tipInfo("sureClear")} // 您确认要清空暂存区吗？
						onConfirm={this.emptyPolicyRuleCopyBox.bind(this)}
					>
						<div className="empty-box">
							{/* 清空 */}
							{policyDetailLang.tipInfo("clear")}
						</div>
					</Popconfirm>
				}
			</div>
		);

		let statusMap = lang === "cn" ? PolicyConstants.policyStatusMap : PolicyConstants.policyStatusMap2;
		let policyMenuMapKey = policyDetail.fkPolicySetUuid + "_policys";
		let policyMenu = (
			<Menu className="app-menu-list">
				{
					allMap && allMap[policyMenuMapKey] && allMap[policyMenuMapKey].map((item, index) => {
						return (
							<Menu.Item
								key={index}
								onClick={() => {
									let { dispatch, location } = this.props;
									let pathname = location.pathname;
									let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
									dispatch(routerRedux.push(prefix + "/policy/policyDetail/" + item.name + "?tabIndex=1"));
									dispatch({
										type: "policyDetail/setAttrValue",
										payload: {
											ruleActiveKey: [],
											currentRuleUuid: null
										}
									});
									// TODO 这里稍后统一换接口
									dispatch({
										type: "policyDetail/getPolicyDetail",
										payload: {
											uuid: item.name
										}
									});
									dispatch({
										type: "policyDetail/getPolicyRules",
										payload: {
											policyUuid: item.name
										}
									});
								}}
							>
								{item.dName}
							</Menu.Item>
						);
					})
				}
			</Menu>
		);

		// 编辑区策略跳转到运行区跳转到已上线版本
		const currentVersion = historyVersion && historyVersion.length > 0 && historyVersion.find((v) => { return v.status === "online"; }) || null;
		return (
			<div className="policy-detail-wrap">
				<div className="policy-detail-box">
					<div className="policy-detail-header">
						<div className="policy-detail-guide">
							<div
								className="go-back"
								onClick={() => {
									let { dispatch, location } = this.props;
									let pathname = location.pathname;
									let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
									dispatch(routerRedux.push(prefix + "/policy/publicPolicyList?currentTab=2"));
								}}
							>
								{/* 返回策略集列表 */}
								<Tooltip title={policyDetailLang.tipInfo("goBack")}>
									<Icon type="left" />
								</Tooltip>
							</div>
							<Tooltip title={policyDetail.name}><h3>{policyDetail.name}</h3></Tooltip>
							{
								statusMap[policyDetail.status] &&
								<Tag
									color={statusMap[policyDetail.status]["color"]}
									style={{ marginLeft: "10px" }}
								>
									{statusMap[policyDetail.status]["text"]}
								</Tag>
							}
							{
								currentVersion &&
								<Tooltip title={policyDetailLang.tipInfo("toRunArea")}>
									<Icon
										type="link"
										className="jump-to-running-area"
										onClick={() => {
											let { dispatch, location } = this.props;
											let pathname = location.pathname;
											let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
											const baseUrl = `/policy/versionPublicPolicyDetail/${currentVersion.policyUuid}?tabIndex=1&policyVersion=${currentVersion.policyVersion}`;
											dispatch(routerRedux.push(prefix + baseUrl));
										}}
									/>
								</Tooltip>
							}
						</div>
						{
							<div className="tab-menu">
								<span
									className={tabIndex === 0 ? "first active" : "first"}
									onClick={this.changeTabIndex.bind(this, 0)}
								>
									{/* 基本设置 */}
									{policyDetailLang.common("basicSet")}
								</span>
								<span
									className={tabIndex === 1 ? "second active" : "second"}
									onClick={this.changeTabIndex.bind(this, 1)}
								>
									{/* 规则管理 */}
									{policyDetailLang.common("ruleManagement")}
								</span>
							</div>
						}
						{
							tabIndex === 0 &&
							<div className="rule-tools-bar">
								{/* 策略版本提交 */}
								<Tooltip title={policyDetailLang.tipInfo("stratVersionSub")}>
									<div
										className="tool-item"
										onClick={() => {
											dispatch({
												type: "policyDetail/setDialogShow",
												payload: {
													policyCommit: true
												}
											});
										}}
									>
										<i className="iconfont icon-submit"></i>
									</div>
								</Tooltip>
							</div>
						}
						{
							tabIndex === 1 &&
							<div className="rule-tools-bar">
								{/* lang:查看历史版本 */}
								<Tooltip title={policyDetailLang.tipInfo("viewHistoryVersion")}>
									<div
										className="tool-item"
										onClick={() => {
											if (checkPermissionPublicPolicy.PubPolicyHistory()) {
												this.openPolicyVersionDrawer();
											} else {
												message.warning(policyDetailLang.tipInfo("noPermission")); // 无权限操作
											}
										}}
									>
										<i className="iconfont icon-version"></i>
									</div>
								</Tooltip>
								{/* 策略版本提交 */}
								<Tooltip title={policyDetailLang.tipInfo("stratVersionSub")}>
									<div
										className="tool-item"
										onClick={() => {
											if (checkPermissionPublicPolicy.SubmitPubPolicy()) {
												dispatch({
													type: "policyDetail/setDialogShow",
													payload: {
														policyCommit: true
													}
												});
											} else {
												message.warning(policyDetailLang.tipInfo("noPermission")); // 无权限操作
											}
										}}
									>
										<i className="iconfont icon-submit"></i>
									</div>
								</Tooltip>
								{/* 查看本策略指标 */}
								<Tooltip title={policyDetailLang.tipInfo("lookStrat")}>
									<div
										className="tool-item"
										onClick={() => {
											if (checkFunctionHasPermission("ZB0101", "viewPolicyCiteIndex")) {
												dispatch({
													type: "policyDetail/setDialogShow",
													payload: {
														PolicyCiteIndex: true
													}
												});
												dispatch({
													type: "indexRunning/getPolicyCiteIndexList",
													payload: {
														curPage: 1,
														pageSize: 20,
														policyUuid: policyDetail.uuid
													}
												});
											} else {
												message.warning(policyDetailLang.tipInfo("noPermission")); // 无权限操作
											}
										}}
									>
										<i className="iconfont icon-log"></i>
									</div>
								</Tooltip>
								{/* "已复制规则暂存区" : "管理已复制规则" */}
								<Tooltip
									title={policyRuleCopyList.length ? policyDetailLang.tipInfo("hasCopy") : policyDetailLang.tipInfo("hasCopy2")}>
									<Popover
										popupClassName="rule-storage-popover-wrap"
										placement="bottomRight"
										title={ruleStoragePopoverTitle}
										content={<ul>{policyRuleCopyDom}</ul>}
										trigger={["click"]}
										onClick={() => {
											if (checkFunctionHasPermission("ZB0101", "pasteRule")) {
												this.checkoutLocalStorage();
											} else {
												message.warning(policyDetailLang.tipInfo("noPermission")); // 无权限操作
											}
										}}>
										<div className="tool-item rule-storage">
											<i className="iconfont icon-paste"></i>
											{
												policyRuleCopyList.length > 0 &&
												<span className="badge">
													{policyRuleCopyList.length}
												</span>
											}
										</div>
									</Popover>
								</Tooltip>
								{/* 规则排序 */}
								<Tooltip title={policyDetailLang.tipInfo("sortRule")}>
									<div
										className="tool-item"
										onClick={() => {
											if (checkFunctionHasPermission("ZB0101", "sortRule")) {
												this.ruleSortHandle();
											} else {
												// 无权限操作
												message.warning(policyDetailLang.tipInfo("noPermission"));
											}
										}}
									>
										<i className="iconfont icon-sort"></i>
									</div>
								</Tooltip>
								{/* 快捷键C快速添加规则 */}
								<Tooltip title={policyDetailLang.tipInfo("quickAdd")}>
									<div className="tool-item" onClick={() => {
										if (checkFunctionHasPermission("ZB0101", "addRule")) {
											dispatch({
												type: "policyDetail/setDialogShow",
												payload: {
													templateDrawer: true
												}
											});
										} else {
											message.warning(policyDetailLang.tipInfo("noPermission")); // 无权限操作
										}
									}}>
										<i className="iconfont icon-plus"></i>
									</div>
								</Tooltip>
								{
									!showSearch &&
									// 搜索规则
									<Tooltip title={policyDetailLang.tipInfo("searchRule")}>
										<div
											className="tool-item"
											onClick={() => {
												ruleSearch["showSearch"] = true;
												dispatch({
													type: "policyDetail/setAttrValue",
													payload: {
														ruleSearch: ruleSearch
													}
												});
											}}
										>
											<i className="iconfont icon-search"></i>
										</div>
									</Tooltip>
								}
								{
									showSearch &&
									<div className="tool-item rule-search">
										<Input
											value={searchWord || undefined}
											placeholder={policyDetailLang.tipInfo("inputKey")} // 请输入关键词搜索
											autoFocus="autofocus"
											ref="policyRuleSearchInput"
											onChange={(e) => {
												ruleSearch["searchWord"] = e.target.value;
												dispatch({
													type: "policyDetail/setAttrValue",
													payload: {
														ruleSearch: ruleSearch
													}
												});
											}}
										/>
										<Icon
											type="close"
											onClick={() => {
												ruleSearch["showSearch"] = false;
												ruleSearch["searchWord"] = null;
												dispatch({
													type: "policyDetail/setAttrValue",
													payload: {
														ruleSearch: ruleSearch
													}
												});
											}}
										/>
									</div>
								}
							</div>
						}
					</div>
					<div className="policy-detail-body">
						{
							tabIndex === 0 &&
							<React.Suspense fallback={null}>
								<PublicPolicyBasic location={location} />
							</React.Suspense>
						}
						{
							tabIndex === 1 &&
							policyDetail.policyVersion &&
							<Alert
								className="policy-version-alert mb10"
								message={
									<div>
										{/* lang:当前查看的策略版本 */}
										<span>{policyDetailLang.policyVersion("currentViewVersion")}V{policyDetail.policyVersion}</span>
										<a
											className="ml20"
											onClick={() => {
												let { policyInfo, rules } = policyOriginalAllInfo;
												dispatch({
													type: "policyDetail/setAttrValue",
													payload: {
														policyDetail: policyInfo,
														policyRules: rules || []
													}
												});
											}}
										>
											{/* lang:点击返回当前版本 */}
											{policyDetailLang.policyVersion("returnCurrentVersion")}
										</a>
									</div>
								}
								type="info"
								showIcon
							/>
						}
						{
							tabIndex === 1 &&
							<React.Suspense fallback={null}>
								<RuleList location={location} />
							</React.Suspense>
						}
					</div>
					<React.Suspense fallback={null}>
						<TemplateDrawer pageName="policyDetail" />
					</React.Suspense>
					<React.Suspense fallback={null}>
						<AddTemplateModal />
					</React.Suspense>
					<React.Suspense fallback={null}>
						<AddToCustomListModal />
					</React.Suspense>
					<React.Suspense fallback={null}>
						<PolicyCiteIndexModal />
					</React.Suspense>
					<React.Suspense fallback={null}>
						<PolicyCommitModal
							refreshFun={this.refreshHistoryVersion}
						/>
					</React.Suspense>
					<React.Suspense fallback={null}>
						<RuleSortModal />
					</React.Suspense>
					<PolicyVersionDrawer location={location} isEditor={true} />
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(PublicPolicyDetail);

