import dynamic from "dva/dynamic";
import config from "./config";

// dynamic包装 函数
const dynamicWrapper = (app, models, component) => dynamic({
	app,
	models: () => models.map(m => import(`../models/${m}.js`)),
	component
});

// 抽象化菜单配置
const getNavList = (app) => {
	let navList = {};
	navList.SystemManageChildren = [
		{
			name: "规则模板",
			enName: "Rule management",
			icon: "document",
			path: "ruleTemplate",
			component: dynamicWrapper(app, [], () => import("../pages/RuleTemplate"))
		}
	];
	navList.PollicyListChildren = [
		{
			name: "公共策略",
			enName: "Public Policy set list",
			icon: "celve1",
			path: "publicPolicyList",
			component: dynamicWrapper(app, [], () => import("../pages/Policy/PublicPolicy"))
		},
		{
			name: "策略定义",
			enName: "Policy set list",
			icon: "celve1",
			path: "policyList",
			component: dynamicWrapper(app, [], () => import("../pages/Policy/PolicyList"))
		},
		{
			name: "实时指标",
			enName: "Real-time indicator",
			icon: "zhibiao",
			path: "salaxy",
			component: dynamicWrapper(app, [], () => import("../pages/Policy/Salaxy"))
		},
		{
			name: "策略配置",
			enName: "Policy configuration",
			icon: "zhibiao",
			path: "policyDetail/:policyUuid",
			notRender: true,
			component: dynamicWrapper(app, [], () => import("../pages/Policy/PolicyDetail"))
		},
		{
			name: "公共策略配置",
			enName: "Public Policy configuration",
			icon: "zhibiao",
			path: "publicPolicyDetail/:policyUuid",
			notRender: true,
			component: dynamicWrapper(app, [], () => import("../pages/Policy/PolicyDetail/PublicPolicyDetail"))
		},
		{
			name: "运行区策略配置",
			enName: "Policy configuration",
			icon: "zhibiao",
			path: "versionPolicyDetail/:policyUuid",
			notRender: true,
			component: dynamicWrapper(app, [], () => import("../pages/Policy/VersionPolicyDetail"))
		},
		{
			name: "运行区公共策略配置",
			enName: "public Policy configuration",
			icon: "zhibiao",
			path: "versionPublicPolicyDetail/:policyUuid",
			notRender: true,
			component: dynamicWrapper(app, [], () => import("../pages/Policy/VersionPolicyDetail/VersionPublicPolicyDetail"))
		},
		{
			name: "回测任务",
			enName: "Replay task",
			icon: "document",
			path: "replayTask",
			component: dynamicWrapper(app, [], () => import("../pages/Policy/ReplayTask"))
		},
		{
			name: "回溯任务",
			enName: "ReCall task",
			icon: "document",
			path: "reCallTask",
			component: dynamicWrapper(app, [], () => import("../pages/Policy/ReCallTask"))
		}
	];
	navList.immuneChildren = [
		{
			name: "规则免疫",
			enName: "Immune config",
			icon: "celve1",
			path: "config",
			component: dynamicWrapper(app, [], () => import("../pages/Immune"))
		}
	];
	navList.ApprovalChildren = [
		{
			name: "审批任务",
			enName: "Approval Task",
			icon: "ceshi",
			path: "task",
			component: dynamicWrapper(app, [], () => import("../pages/Approval/Task"))
		},
		{
			name: "审批日志",
			enName: "Approval Log",
			icon: "document",
			path: "log",
			component: dynamicWrapper(app, [], () => import("../pages/Approval/Log"))
		}
	];
	navList.DealTypeChildren = [
		{
			name: "决策触发管理",
			enName: "chuzhi chufa",
			icon: "deal-log",
			path: "triggerManage",
			component: dynamicWrapper(app, [], () => import("../pages/PolicyDeal/TriggerManage"))
		},
		{
			name: "风险决策管理",
			enName: "chuzhi chufa",
			icon: "deal-type",
			path: "dealType",
			component: dynamicWrapper(app, [], () => import("../pages/PolicyDeal/DealType"))
		}
	];
	navList.BlackListChildren = [
		{
			name: "黑名单管理",
			enName: "black List",
			icon: "deal-log",
			path: "blackList",
			component: dynamicWrapper(app, [], () => import("../pages/BlackList"))
		},
		{
			name: "字段集管理",
			enName: "Field Set Management",
			icon: "deal-log",
			path: "fieldSetManagement",
			component: dynamicWrapper(app, [], () => import("../pages/FieldSetManage"))
		}
	];
	navList.exceptionChildren = [
		{
			name: "403",
			path: "403",
			component: dynamicWrapper(app, [], () => import("../pages/Exception/403"))
		},
		{
			name: "404",
			path: "404",
			component: dynamicWrapper(app, [], () => import("../pages/Exception/404"))
		},
		{
			name: "500",
			path: "500",
			component: dynamicWrapper(app, [], () => import("../pages/Exception/500"))
		}
	];
	return navList;
};
// nav data
export const getNavData = app => [
	{
		component: dynamicWrapper(app, [], () => import("../layouts/BasicLayout/")),
		layout: "BasicLayout",
		name: "首页",
		path: "/",
		children: [
			{
				name: "策略管理",
				enName: "Policy management",
				icon: "desktop",
				path: "policy",
				children: getNavList(app).PollicyListChildren
			},
			{
				name: "免疫管理",
				enName: "Immune management",
				icon: "desktop",
				path: "immune",
				children: getNavList(app).immuneChildren
			},
			{
				name: "审批管理",
				enName: "Approval management",
				icon: "desktop",
				path: "approval",
				children: getNavList(app).ApprovalChildren
			},
			{
				name: "策略处置",
				enName: "Policy deal",
				icon: "desktop",
				path: "policyDeal",
				children: getNavList(app).DealTypeChildren
			},
			{
				name: "规则管理",
				enName: "Rule management",
				icon: "desktop",
				path: "system",
				children: getNavList(app).SystemManageChildren
			},
			{
				name: "名单管理",
				enName: "Black List",
				icon: "desktop",
				path: "/",
				children: getNavList(app).BlackListChildren
			},
			{
				name: "other",
				path: "exception",
				icon: "setting",
				notRender: true,
				children: getNavList(app).exceptionChildren
			}
		]
	},
	{
		component: dynamicWrapper(app, [], () => import("../layouts/PublishLayout/")),
		path: "/",
		layout: "PublishLayout",
		children: [
			{
				name: "策略管理",
				icon: "desktop",
				path: `${config.routerPrefix}/policy`,
				children: getNavList(app).PollicyListChildren
			},
			{
				name: "审批管理",
				enName: "Approval management",
				icon: "desktop",
				path: `${config.routerPrefix}/approval`,
				children: getNavList(app).ApprovalChildren
			},
			{
				name: "策略处置",
				enName: "Policy deal",
				icon: "desktop",
				path: `${config.routerPrefix}/policyDeal`,
				children: getNavList(app).DealTypeChildren
			},
			{
				name: "规则管理",
				enName: "Rule management",
				icon: "desktop",
				path: `${config.routerPrefix}/system`,
				children: getNavList(app).SystemManageChildren
			},
			{
				name: "名单管理",
				enName: "Black List",
				icon: "desktop",
				path: `${config.routerPrefix}/`,
				children: getNavList(app).BlackListChildren
			},
			{
				name: "免疫管理",
				enName: "Immune management",
				icon: "desktop",
				path: `${config.routerPrefix}/immune`,
				children: getNavList(app).immuneChildren
			},
			{
				name: "other",
				path: `${config.routerPrefix}/exception`,
				icon: "setting",
				notRender: true,
				children: getNavList(app).exceptionChildren
			}
		]
	}
];
