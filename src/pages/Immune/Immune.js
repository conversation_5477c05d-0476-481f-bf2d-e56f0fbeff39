import React, { PureComponent } from "react";
import { Tabs } from "antd";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import NoPermission from "@/components/NoPermission";
import { immuneLang } from "@/constants/lang";
import { searchToObject } from "@/utils/utils";
import { checkFunctionHasPermission } from "@/utils/permission";
import ImmuneHistory from "./ImmuneHistory";

const TabPane = Tabs.TabPane;
const ImmuneConfig = React.lazy(() => import("./ImmuneConfig"));

class Immune extends PureComponent {
    switchTab = (key) => {
    	const { location, dispatch } = this.props;
    	const { pathname } = location;
    	const search = "?currentTab=" + key;
    	dispatch(routerRedux.push(pathname + search));
    }

    render() {
    	const { globalStore, location } = this.props;
    	const { menuTreeReady } = globalStore;
    	const { search } = location;
    	const searchObj = search && searchToObject(search) ? searchToObject(search) : null;
    	const currentTab = searchObj && searchObj.currentTab ? parseInt(searchObj.currentTab, 10) : 1;

    	return (
    		<div className="main-area-wrap">
    			<div className="page-global-tab">
    				<Tabs
    					activeKey={currentTab.toString()}
    					onChange={this.switchTab}
    					animated={false}
    				>
    					<TabPane tab={immuneLang.common("tabForConfigList")} key="1">
    						{
    							menuTreeReady &&
								checkFunctionHasPermission("ZB0106", "RuleImmunoList") &&
								<React.Suspense fallback={null}>
									<ImmuneConfig location={location} />
								</React.Suspense>
    						}
    						{
    							menuTreeReady && !checkFunctionHasPermission("ZB0106", "RuleImmunoList") &&
								<NoPermission />
    						}
    					</TabPane>
    					<TabPane tab={immuneLang.common("tabForHistory")} key="2">
    						{
    							menuTreeReady &&
								String(currentTab) === "2" &&
								checkFunctionHasPermission("ZB0106", "RuleImmunoLogList") &&
								<ImmuneHistory/>
    						}
    						{
    							menuTreeReady && !checkFunctionHasPermission("ZB0106", "RuleImmunoLogList") &&
								<NoPermission />
    						}
    					</TabPane>
    				</Tabs>
    			</div>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global
}))(Immune);
