import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Icon, Input, message, Row, Col, Tooltip, Modal, Radio, Collapse, Spin } from "antd";
import { policyDetailAPI } from "@/services";
import { isJSON } from "@/utils/isJSON";
import RuleConfig from "../RuleConfig";
import { checkFunctionHasPermission } from "@/utils/permission";
import { policyDetailLang } from "@/constants/lang";
import { cloneDeep } from "lodash";

const Panel = Collapse.Panel;
const confirm = Modal.confirm;

class RuleList extends PureComponent {
	state = {
		showEditRuleCumtomIdModal: false,
		customId: null,
		ruleCustomLoading: false,
		ruleName: null,
		ruleUuid: null,
		indexArr: []
	}
	constructor(props) {
		super(props);
		this.changeRuleStatus = this.changeRuleStatus.bind(this);
		this.deleteRuleHandle = this.deleteRuleHandle.bind(this);
		this.deleteRule = this.deleteRule.bind(this);
		this.copyRuleHandle = this.copyRuleHandle.bind(this);
		this.checkoutLocalStorage = this.checkoutLocalStorage.bind(this);
	}

	checkoutLocalStorage() {
		let { dispatch } = this.props;
		if (localStorage.getItem("policyRuleCopyList")) {
			if (isJSON(localStorage.getItem("policyRuleCopyList"))) {
				let policyRuleCopyListStr = localStorage.getItem("policyRuleCopyList") || "[]";
				dispatch({
					type: "global/setAttrValue",
					payload: {
						policyRuleCopyList: JSON.parse(policyRuleCopyListStr)
					}
				});
			}
		}
	}

	changeRuleStatus(uuid, indexArr, e) {
		let { policyDetailStore, dispatch } = this.props;
		let { policyRules, policyDetail } = policyDetailStore;

		let currentRule = null;
		if (indexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[indexArr[0]] : null;
		} else if (indexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[indexArr[0]]["children"][indexArr[1]] : null;
		}

		let params = {
			valid: Number(e.target.value),
			ruleUuid: uuid
		};
		policyDetailAPI.changeRuleStatus(params).then(res => {
			if (res.success) {
				currentRule["valid"] = e.target.value;
				dispatch({
					type: "policyDetail/setPolicyRule",
					payload: {
						policyRules: policyRules
					}
				});
				message.success(policyDetailLang.ruleList("tip1")); // 更改规则状态成功！
				dispatch({
					type: "policyDetail/changePolicyStatus",
					payload: {
						uuid: policyDetail.uuid
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	addToCustomListHandle = (indexArr, currentRule) => {
		let { dispatch } = this.props;

		dispatch({
			type: "policyDetail/setDialogData",
			payload: {
				addToCustomListData: {
					triggers: cloneDeep(currentRule.triggers) || [],
					ruleIndexArr: indexArr,
					ruleUuid: currentRule.uuid
				}
			}
		});
		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				addToCustomList: true
			}
		});
	}

	deleteRuleHandle(obj, indexArr, e) {
		e.stopPropagation();
		let _this = this;
		confirm({
			title: policyDetailLang.ruleList("tip2"), // 删除规则提醒
			content: policyDetailLang.ruleList("tip3") + obj.name + policyDetailLang.ruleList("tip4"), // 您真的要删除《" + obj.name + "》吗？
			onOk() {
				_this.deleteRule(obj, indexArr);
			},
			onCancel() {
				console.log("Cancel");
			}
		});
	}

	deleteRule(ruleObj, indexArr) {
		let { policyDetailStore, dispatch } = this.props;
		let { policyDetail, policyRules } = policyDetailStore;

		let currentRule = null;
		if (indexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[indexArr[0]] : null;
		} else if (indexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[indexArr[0]]["children"][indexArr[1]] : null;
		}
		if (currentRule.unSave) {
			// 未保存的规则，直接删除
			if (indexArr.length === 1) {
				policyRules.splice(indexArr[0], 1);
			} else if (indexArr.length === 2) {
				policyRules[indexArr[0]]["children"].splice(indexArr[1], 1);
			}
			dispatch({
				type: "policyDetail/setPolicyRule",
				payload: {
					policyRules: policyRules
				}
			});
		} else {
			let params = {
				ruleUuid: currentRule.uuid
			};

			policyDetailAPI.deletePolicyRule(params).then(res => {
				if (res.success) {
					message.success(policyDetailLang.ruleList("tip5")); // 删除规则成功！
					// 删除规则
					if (indexArr.length === 1) {
						policyRules.splice(indexArr[0], 1);
					} else if (indexArr.length === 2) {
						policyRules[indexArr[0]]["children"].splice(indexArr[1], 1);
					}
					dispatch({
						type: "policyDetail/setPolicyRule",
						payload: {
							policyRules: policyRules
						}
					});
					// 更改策略状态
					dispatch({
						type: "policyDetail/changePolicyStatus",
						payload: {
							uuid: policyDetail.uuid
						}
					});
					// 删除规则后检查copy暂存区是否存在
					if (localStorage.getItem("policyRuleCopyList")) {
						if (isJSON(localStorage.getItem("policyRuleCopyList"))) {
							let policyRuleCopyListStr = localStorage.getItem("policyRuleCopyList") || "[]";
							let policyRuleCopyList = JSON.parse(policyRuleCopyListStr);
							if (policyRuleCopyList.filter(item => item.ruleUuid === ruleObj.uuid).length) {
								policyRuleCopyList.filter(item => item.ruleUuid === ruleObj.uuid)[0]["notExist"] = true;
							}
							dispatch({
								type: "global/setAttrValue",
								payload: {
									policyRuleCopyList: JSON.parse(policyRuleCopyListStr)
								}
							});
							localStorage.setItem("policyRuleCopyList", JSON.stringify(policyRuleCopyList));
						}
					}

				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		}
	}

	changeRuleCustomId = () => {
		let { ruleUuid, customId, indexArr } = this.state;
		let { policyDetailStore, dispatch } = this.props;
		let { policyRules, policyDetail } = policyDetailStore;

		let currentRule = null;
		if (indexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[indexArr[0]] : null;
		} else if (indexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[indexArr[0]]["children"][indexArr[1]] : null;
		}

		if (!customId) {
			message.warning("请输入规则编号");
			return;
		}
		if (customId.length > 18) {
			message.warning("规则编号最长18个字符");
			return;
		}
		let textExp = /^[0-9a-zA-Z_]{1,}$/;

		if (!textExp.test(customId)) {
			message.warning("规则编号只支持英文、数字、下划线的组合");
		} else {
			let params = {
				ruleUuid: ruleUuid,
				customId: customId
			};
			this.setState({
				ruleCustomLoading: true
			});
			policyDetailAPI.changeRuleCustomId(params).then(res => {
				if (res.success) {
					message.success("修改规则编号成功");
					// 更改状态
					currentRule["customId"] = customId;

					dispatch({
						type: "policyDetail/setPolicyRule",
						payload: {
							policyRules: policyRules
						}
					});
					// 更新规则成功后刷新策略审批状态
					dispatch({
						type: "policyDetail/changePolicyStatus",
						payload: {
							uuid: policyDetail.uuid
						}
					});
					this.setState({
						showEditRuleCumtomIdModal: false
					});
				} else {
					message.error(res.message);
				}
				this.setState({
					ruleCustomLoading: false
				});
			}).catch(err => {
				console.log(err);
				this.setState({
					ruleCustomLoading: false
				});
			});
		}
	}

	copyRuleHandle(obj) {
		this.checkoutLocalStorage();
		let { policyDetailStore, globalStore, dispatch } = this.props;
		let { policyDetail } = policyDetailStore;
		let { policyRuleCopyList } = globalStore;

		let timestamp = Date.parse(new Date());
		let ruleItem = {
			appName: obj.appName,
			policyUuid: obj.fkPolicyUuid,
			policyName: policyDetail.name,
			policyDescription: policyDetail.description,
			ruleUuid: obj.uuid,
			ruleName: obj.name,
			ruleId: obj.id,
			createTs: timestamp
		};
		let filterResult = policyRuleCopyList.filter(item => item.ruleUuid === obj.uuid);
		if (filterResult.length) {
			// 已经存在
			message.success(policyDetailLang.ruleList("tip6")); // 暂存区已存在此规则，无需重复复制！
		} else {
			// 规则不存在
			message.success(policyDetailLang.ruleList("tip7")); // 规则已经复制到暂存区
			policyRuleCopyList.splice(0, 0, ruleItem);
			dispatch({
				type: "global/setAttrValue",
				payload: {
					policyRuleCopyList: policyRuleCopyList
				}
			});
			localStorage.setItem("policyRuleCopyList", JSON.stringify(policyRuleCopyList));
		}
	}

	render() {
		let { ruleCustomLoading, ruleName } = this.state;
		let { policyDetailStore, globalStore } = this.props;
		const { allMap } = globalStore;
		let { policyRulesReady, policyRules, ruleSearch, ruleActiveKey, currentRuleUuid } = policyDetailStore;
		let { searchWord } = ruleSearch;

		let collapseList = [];
		if (allMap && Object.keys(allMap).length > 0) {
			collapseList = policyRules.filter(fItem => {
				if (ruleSearch && searchWord) {
					return fItem.name.indexOf(searchWord) > -1;
				} else {
					return true;
				}
			}).map((item, index) => {
				let title = (obj, indexArr) => {
					let canCopy = true;
					let copyTip = obj.unSave ? policyDetailLang.ruleList("saveFirst") : policyDetailLang.ruleList("copyRule"); // "如需复制请先保存" : "拷贝规则"
					if (indexArr.length === 1) {
						if (policyRules[indexArr[0]].ifClause === "IF") {
							canCopy = false;
							copyTip = policyDetailLang.ruleList("ifNoSupportCopy"); // IF规则不支持复制
						}
					} else if (indexArr.length === 2) {
						canCopy = false;
						copyTip = policyDetailLang.ruleList("ifsubNoSupportCopy"); // IF子规则不支持复制
					}

					let canAddToCustomList = false;
					if (!(obj["ifClause"] && obj["ifClause"] === "IF") && obj["template"] !== "pattern/terminated") {
						canAddToCustomList = true;
					}

					let addToCustomClassNames;
					if (canAddToCustomList) {
						if (item.triggers && item.triggers.length) {
							addToCustomClassNames = "iconfont icon-log-list blue";
						} else {
							addToCustomClassNames = "iconfont icon-log-list";
						}
					} else {
						addToCustomClassNames = "iconfont icon-log-list disabled";
					}

					return (
						<div
							className={obj.unSave ? "rule-manage-header collapse-header un-save" : "rule-manage-header collapse-header"}>
							<div className="rule-name">
								<Icon
									className={ruleActiveKey.length === 1 && ruleActiveKey[0] === obj.uuid ? "row-collapsed active" : "row-collapsed"}
									type={ruleActiveKey.length === 1 && ruleActiveKey[0] === obj.uuid ? "minus-square" : "plus-square"}
								/>
								{obj.name}
							</div>
							<div className="rule-st">
								{
									obj.unSave &&
									<span className="un-save">{policyDetailLang.ruleList("newNoSave")}</span> // 新添加，未保存
								}
								{
									obj.hasModify &&
									!obj.unSave &&
									<span className="has-modify">{policyDetailLang.ruleList("hasChange")}</span> // 有修改
								}
							</div>
							<div className="rule-action">
								{/* 添加到名单 */}
								<Tooltip title={policyDetailLang.ruleList("addList")}>
									<i
										className={addToCustomClassNames}
										onClick={(e) => {
											e.stopPropagation();
											if (checkFunctionHasPermission("ZB0101", "addToCustomList")) {
												if (canAddToCustomList) {
													this.addToCustomListHandle(indexArr, obj);
												}
											} else {
												message.warning(policyDetailLang.ruleList("noPermission")); // 无权限操作
											}
										}}
									></i>
								</Tooltip>
								<Tooltip title={copyTip}>
									<i
										className={!canCopy ? "iconfont icon-fuzhi1 un-save" : "iconfont icon-fuzhi1"}
										onClick={(e) => {
											e.stopPropagation();
											if (checkFunctionHasPermission("ZB0101", "pasteRule")) {
												if (canCopy) {
													this.copyRuleHandle(obj);
												} else {
													message.warning(copyTip);
												}
											} else {
												message.warning(policyDetailLang.ruleList("noPermission")); // 无权限操作
											}
										}}
									>
									</i>
								</Tooltip>
								{/* <Tooltip title="查看操作日志">*/}
								{/* <i className="iconfont icon-log"></i>*/}
								{/* </Tooltip>*/}
								{/* 删除规则 */}
								<Tooltip title={policyDetailLang.ruleList("deleteRules")}>
									<i
										className="iconfont icon-delete"
										onClick={(e) => {
											if (checkFunctionHasPermission("ZB0101", "deleteRule")) {
												this.deleteRuleHandle(obj, indexArr, e);
											} else {
												message.warning(policyDetailLang.ruleList("noPermission")); // 无权限操作
											}
										}}
									></i>
								</Tooltip>
							</div>
							<div className="rule-status" onClick={(e) => {
								e.stopPropagation();
							}}>
								<Radio.Group
									value={obj.unSave ? undefined : obj.valid.toString()}
									buttonStyle="solid"
									size="small"
									onChange={this.changeRuleStatus.bind(this, obj.uuid, indexArr)}
									disabled={(!!obj.unSave) || !checkFunctionHasPermission("ZB0101", "changeRuleState")}
								>
									<Radio.Button value="0">
										{/* 关闭 */}
										{policyDetailLang.ruleList("close")}
									</Radio.Button>
									<Radio.Button value="2">
										{/* 模拟 */}
										{policyDetailLang.ruleList("simulation")}
									</Radio.Button>
									<Radio.Button value="1">
										{/* 正式 */}
										{policyDetailLang.ruleList("formal")}
									</Radio.Button>
								</Radio.Group>
							</div>
							<div className="rule-number edit-area" onClick={(e) => { e.stopPropagation(); }}>
								{
									obj["unSave"] &&
									<span className="none-data">
										{/* 暂无ID */}
										{policyDetailLang.ruleList("noId")}
									</span>
								}
								{
									!obj["unSave"] &&
									<Fragment>
										<span className="rule-id">
											{obj.customId ? obj.customId.substring(0, 18) : ""}
											<Icon
												type="edit"
												onClick={() => {
													this.setState({
														customId: obj.customId,
														showEditRuleCumtomIdModal: true,
														ruleName: obj.name,
														ruleUuid: obj.uuid,
														indexArr: indexArr
													});
												}}
											/>
										</span>
									</Fragment>
								}
							</div>
							<div className="rule-extra">
								{
									obj["ifClause"] &&
									obj["ifClause"] === "IF" &&
									<a onClick={(e) => {
										let { dispatch } = this.props;
										e.stopPropagation();
										if (obj["unSave"]) {
											message.warning(policyDetailLang.ruleList("tip8")); // 当前IF规则未保存，无法添加子规则！
										} else {
											dispatch({
												type: "policyDetail/setAttrValue",
												payload: {
													addIFChildRuleIndex: index
												}
											});
											dispatch({
												type: "policyDetail/setDialogShow",
												payload: {
													addIFChildRule: true
												}
											});
										}
									}
									}>
										{/* 添加子规则 */}
										{policyDetailLang.ruleList("addSubrule")}
									</a>
								}
							</div>
						</div>
					);
				};
				let renderDom = [];
				let itemObj = (
					<Panel
						header={title(item, [index])}
						key={item.uuid}
						showArrow={false}
						forceRender={true}
					>
						{
							currentRuleUuid &&
							currentRuleUuid === item.uuid &&
							<RuleConfig ruleData={item} ruleIndexArr={[index]} />
						}
					</Panel>
				);
				renderDom.push(itemObj);
				if (item.children && item.children.length) {
					item.children.map((subItem, subIndex) => {
						let itemObj = (
							<Panel
								className={subItem.unSave ? "rule-children un-save" : "rule-children"}
								header={title(subItem, [index, subIndex])}
								key={subItem.uuid}
								showArrow={false}
								forceRender={true}
							>
								<RuleConfig ruleData={subItem} ruleIndexArr={[index, subIndex]} />
							</Panel>
						);
						renderDom.push(itemObj);
					});
				}
				return renderDom;
			});
		} else {
			return <div className="tc"><Spin /></div>;
		}

		return (
			<div className="rule-manage">
				{
					policyRules && policyRules.length > 0 &&
					<div className="rule-manage-header rule-th">
						{/* <div className="checkbox">*/}
						{/* <Checkbox onChange={() => {*/}
						{/* }}></Checkbox>*/}
						{/* </div>*/}
						<div className="rule-name">
							{/* 规则名称 */}
							{policyDetailLang.ruleList("ruleName")}
						</div>
						{/* <div className="rule-empty">*/}

						{/* </div>*/}
						<div className="rule-operator">
							{/* 操作 */}
							{policyDetailLang.ruleList("operation")}
						</div>
						<div className="rule-status">
							{/* 规则状态 */}
							{policyDetailLang.ruleList("ruleState")}
						</div>
						<div className="rule-number">
							{/* 规则编号 */}
							{policyDetailLang.ruleList("ruleCode")}
						</div>
					</div>
				}
				<div className="rule-manage-body">
					<Collapse
						className="rule-manage-collapse"
						activeKey={ruleActiveKey}
						onChange={(value) => {
							let { dispatch } = this.props;
							let uuidList = value.length > 1 ? [value[value.length - 1]] : value;
							dispatch({
								type: "policyDetail/setAttrValue",
								payload: {
									ruleActiveKey: uuidList,
									currentRuleUuid: uuidList[0]
								}
							});
						}}
					>
						{
							collapseList
						}
					</Collapse>
					{
						policyRulesReady && policyRules.length === 0 &&
						<div className="no-rule-tip"></div>
					}
				</div>
				{/* 修改自定义规则编号弹窗 */}
				<Modal
					title="修改规则编号"
					visible={this.state.showEditRuleCumtomIdModal}
					onOk={() => {
						this.changeRuleCustomId();
					}}
					onCancel={() => {
						this.setState({
							showEditRuleCumtomIdModal: false,
							ruleUuid: null,
							customId: null,
							ruleCustomLoading: false
						});
					}}
					confirmLoading={ruleCustomLoading}
				>
					<div className="modal-form">
						<Row gutter={10}>
							{/* lang:策略集名称 */}
							<Col span={6}
								className="basic-info-title">规则名称：</Col>
							<Col span={18}>
								<Input
									type="text"
									value={ruleName}
									disabled={true}
								/>
							</Col>
						</Row>

						<Row gutter={10}>
							<Col span={6} className="basic-info-title">
								规则编号：
    						</Col>
							<Col span={18}>
								<Input
									style={{ marginBottom: "4px" }}
									value={this.state.customId || undefined}
									placeholder="请输入规则编号"
									onChange={(e) => {
										this.setState({
											customId: e.target.value
										});
									}}
								/>
								<span className="tip-text">
									{/* lang:建议输入：英文、数字和下划线的组合 */}
									支持英文、数字、下划线的组合，最长18个字符
    							</span>
							</Col>
						</Row>
					</div>
				</Modal>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(RuleList);
