import React from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Input, Form, Button, message, Select, Row, Col, Tag, Icon, Tooltip } from "antd";
import { policyEditorAPI } from "@/services";
import { CommonConstants } from "@/constants";
import WeightModeEditor from "../../WeightModeEditor";
import { checkFunctionHasPermission } from "@/utils/permission";
import { policyDetailLang } from "@/constants/lang";
import { trim } from "lodash";

const Option = Select.Option;
const { TextArea } = Input;

class Basic extends React.PureComponent {

	constructor(props) {
		super(props);
		this.changeWeightedSlider = this.changeWeightedSlider.bind(this);
		this.savePolicyHandle = this.savePolicyHandle.bind(this);
		this.changeWeightMode = this.changeWeightMode.bind(this);
		this.addDeleteItem = this.addDeleteItem.bind(this);
	}

	changeWeightedSlider(value) {
		let { policyDetailStore, dispatch } = this.props;
		let { policyDetail } = policyDetailStore;

		policyDetail["reviewThreshold"] = value[0];
		policyDetail["denyThreshold"] = value[1];
		dispatch({
			type: "policyDetail/setPolicyDetail",
			payload: {
				policyDetail: policyDetail
			}
		});
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyDetailStore } = this.props;
		let { policyDetail } = policyDetailStore;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		policyDetail[field] = value;
		dispatch({
			type: "policyDetail/setPolicyDetail",
			payload: {
				policyDetail: policyDetail
			}
		});
	}

	savePolicyHandle() {
		let { policyDetailStore, dispatch } = this.props;
		let { policyDetail } = policyDetailStore;

		if (!trim(policyDetail.name)) {
			message.error(policyDetailLang.basicSetup("tip1")); // 请输入策略名称
			return;
		}

		if (trim(policyDetail.name).length >= 50 || trim(policyDetail.name).length <= 1) {
			message.error(policyDetailLang.basicSetup("tip2")); // 策略集名称长度应大于1,小于50
			return;
		}

		if (!/^[A-Za-z0-9\u4E00-\u9FA5\_]{2,50}$/.test(policyDetail.name)) {
			message.error(policyDetailLang.basicSetup("tip3")); // 策略名称：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合
			return;
		}

		let params = {
			uuid: policyDetail.uuid,
			name: policyDetail.name,
			mode: policyDetail.mode,
			tags: policyDetail.tags,
			description: policyDetail.description
		};

		if (policyDetail.mode === "Weighted") {
			params["dealTypeMappings"] = policyDetail.dealTypeMappings ? JSON.stringify(policyDetail.dealTypeMappings, null, 4) : null;
			if (policyDetail.dealTypeMappings && policyDetail.dealTypeMappings.length > 0) {
				let mapHasEmpty = false;
				for (let i = 0; i < policyDetail.dealTypeMappings.length; i++) {
					let mapItem = policyDetail.dealTypeMappings[i];
					if (!mapItem.dealType || !mapItem.score) {
						mapHasEmpty = true;
						break;
					}
				}
				if (mapHasEmpty) {
					message.warning(policyDetailLang.basicSetup("tip4")); // 风险阈值配置存在空值，请补充完整！
					return;
				}
			}
		}

		policyEditorAPI.modifyPolicy(params).then(res => {
			if (res.success) {
				dispatch({
					type: "policyDetail/getPolicyDetail",
					payload: {
						uuid: policyDetail.uuid
					}
				});
				dispatch({
					type: "policyDetail/changePolicyStatus",
					payload: {
						uuid: policyDetail.uuid
					}
				});
				message.success(policyDetailLang.basicSetup("tip5")); // 保存策略成功

				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
			} else {
				message.error(res.message);
			}

		}).catch(err => {
			console.log(err);
		});

	}

	changeWeightMode(field, value, index) {
		let { dispatch, policyDetailStore } = this.props;
		let { policyDetail } = policyDetailStore;

		policyDetail["dealTypeMappings"][index][field] = value;
		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				policyDetail: policyDetail
			}
		});
	}

	addDeleteItem(type, index) {
		let { dispatch, policyDetailStore } = this.props;
		let { policyDetail } = policyDetailStore;

		let temp = {
			"score": null,
			"dealType": null
		};

		if (type === "add") {
			policyDetail["dealTypeMappings"].splice(index + 1, 0, temp);
		} else if (type === "delete") {
			policyDetail["dealTypeMappings"].splice(index, 1);
		}

		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				policyDetail: policyDetail
			}
		});
	}

	render() {
		let { policyDetailStore, globalStore } = this.props;
		let { allMap, policyModel } = globalStore;
		let { policyTags = [] } = allMap;
		let { policyDetail } = policyDetailStore;

		let tagList = [];
		if (policyDetail && policyDetail.tags) {
			tagList = policyDetail.tags.split(",");
		}
		return (
			<div className="basic-form">
				<Form>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 策略名称： */}
							{policyDetailLang.basicSetup("policyName")}：
						</Col>
						<Col span={10}>
							<Input
								type="text"
								placeholder={policyDetailLang.basicSetup("inputPolicyName")} // 请输入策略名称
								value={policyDetail.name}
								onChange={this.changeDialogDataHandle.bind(this, "name", "input")}
							/>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 应用名： */}
							{policyDetailLang.basicSetup("applyName")}：
						</Col>
						<Col span={10}>
							<Select
								disabled={true}
								value={policyDetail.appName || undefined}
							>
								{
									allMap &&
                                    allMap["appNames"] &&
                                    allMap["appNames"].map((item, index) => {
                                    	return (
                                    		<Option value={item.name} key={index}>
                                    			{item.dName}
                                    		</Option>
                                    	);
                                    })
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 事件类型： */}
							{policyDetailLang.basicSetup("eventType")}：
						</Col>
						<Col span={10}>
							<Select disabled={true} value={policyDetail.riskEventType || undefined}>
								{
									allMap &&
                                    allMap["EventType"] &&
                                    allMap["EventType"].map((item, index) => {
                                    	return (
                                    		<Option value={item.name} key={index}>
                                    			{item.dName}
                                    		</Option>
                                    	);
                                    })
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 事件标识： */}
							{policyDetailLang.basicSetup("eventFlag")}：
						</Col>
						<Col span={10}>
							<Input
								type="text"
								placeholder={policyDetailLang.basicSetup("inputEventFlag")} // 请输入事件标识
								value={policyDetail.riskEventId || undefined}
								disabled={true}
							/>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 风险类型： */}
							{policyDetailLang.basicSetup("riskType")}：
						</Col>
						<Col span={10}>
							<Select
								disabled={true}
								value={policyDetail.riskType || undefined}
							>
								{
									allMap &&
                                    allMap[policyDetail.riskEventType] &&
                                    allMap[policyDetail.riskEventType].map((item, index) => {
                                    	return (
                                    		<Option value={item.name} key={index}>
                                    			{item.dName}
                                    		</Option>
                                    	);
                                    })
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 策略模式： */}
							{policyDetailLang.basicSetup("strategyPattern")}：
						</Col>
						<Col span={10}>
							<Select
								disabled={true}
								value={policyDetail.mode || undefined}
							>
								{
									policyModel &&
                                    policyModel.map((item, index) => {
                                    	return (
                                    		<Option value={item.name} key={index}>
                                    			{item.dName}
                                    		</Option>
                                    	);
                                    })
								}
							</Select>
						</Col>
					</Row>
					{
						policyDetail.mode === "Weighted" &&
                        <Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
                        	<Col span={6} className="basic-info-title">
                        		{/* 风险阈值： */}
                        		{policyDetailLang.basicSetup("riskThreshold")}：
                        	</Col>
                        	<Col span={18}>
                        		<WeightModeEditor
                        			dealTypeMappings={policyDetail && policyDetail.dealTypeMappings ? policyDetail.dealTypeMappings : []}
                        			onChange={this.changeWeightMode.bind(this)}
                        			operateItem={this.addDeleteItem.bind(this)}
                        			page="basicPage"
                        		/>
                        	</Col>
                        </Row>
					}
					{/* 策略标签： */}
					{/* <Row
						gutter={CommonConstants.gutterSpan}
						style={{ height: "auto" }}
					>
						<Col span={6} className="basic-info-title">
							{policyDetailLang.basicSetup("tags")}：
						</Col>
						<Col
							span={10}
							style={{ lineHeight: "32px" }}
						>
							<Select
								mode="multiple"
								// placeholder={policyListLang.policyModal("selectEventTags")} // lang:请选择事件标签
								value={tagList || undefined}
								onChange={(value) => {
									let selectList = value.join(",");
									console.log(selectList);
									this.changeDialogDataHandle("tags", "select", selectList);
								}}
							>
								{
									allMap &&
                                    allMap["policyTags"] &&
                                    allMap["policyTags"].map((item, index) => {
                                    	return (
                                    		<Option
                                    			value={item.name}
                                    			key={index}
                                    		>
                                    			{item.dName}
                                    		</Option>
                                    	);
                                    })
								}
							</Select>
						</Col>
					</Row> */}
					<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
						<Col span={6} className="basic-info-title">
							{/* 描述： */}
							{policyDetailLang.basicSetup("describe")}：
						</Col>
						<Col span={12}>
							<TextArea
								rows={4}
								value={policyDetail.description}
								onChange={this.changeDialogDataHandle.bind(this, "description", "input")}
							/>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan} style={{ clear: "both" }}>
						<Col span={6}></Col>
						<Col span={10} className="basic-info-btns">
							<Button type="primary" onClick={() => {
								if (checkFunctionHasPermission("ZB0101", "editorModifyPolicy")) {
									this.savePolicyHandle();
								} else {
									message.warning(policyDetailLang.basicSetup("noPermission")); // 无权限操作
								}
							}}>
								{/* 保存 */}
								{policyDetailLang.basicSetup("save")}
							</Button>
							<Button onClick={() => {
								let { dispatch } = this.props;

								if (process.env.SYS_ENV === "development") {
									dispatch(routerRedux.push("/policy/policyList?currentTab=2"));
								} else {
									dispatch(routerRedux.push("/index/policy/policyList?currentTab=2"));
								}
							}}>
								{/* 返回 */}
								{policyDetailLang.basicSetup("goBack")}
							</Button>
						</Col>
					</Row>
				</Form>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(Basic);
