import { PureComponent } from "react";
import { connect } from "dva";
import RunningArea from "@/pages/Policy/Salaxy/RunningArea";

class SalaxyApprovalDiff extends PureComponent {

	constructor(props) {
		super(props);
		this.changeDiffNav = this.changeDiffNav.bind(this);
	}

	changeDiffNav(index) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;

		policyApprovalData.tabIndex = index;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				policyApprovalData: policyApprovalData
			}
		});
	}

	render() {

		return (
			<div>
				<RunningArea
					isOtherPageCite={true}
					citePageName="approvalTask"
				/>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(SalaxyApprovalDiff);
