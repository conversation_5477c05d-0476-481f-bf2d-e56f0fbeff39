import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Button, Row, Col, Radio, message, Tooltip, Divider } from "antd";
import { blackListAPI } from "@/services";

const RadioGroup = Radio.Group;

class BlackListImportModal extends PureComponent {

	constructor(props) {
		super(props);
		this.changeImportMode = this.changeImportMode.bind(this);
		this.importBlackListData = this.importBlackListData.bind(this);
		this.closeModal = this.closeModal.bind(this);
		this.startSearch = this.startSearch.bind(this);
	}

	changeImportMode(e) {
		let value = e.target.value;
		let { blackListStore, dispatch } = this.props;
		let { dialogData } = blackListStore;
		let { blackListImportData } = dialogData;

		blackListImportData["listName"] = value;
		dispatch({
			type: "blackList/setDialogData",
			payload: {
				blackListImportData: blackListImportData
			}
		});
	}

	importBlackListData() {
		let { blackListStore } = this.props;
		let { dialogData } = blackListStore;
		let { blackListImportData } = dialogData;
		if (!blackListImportData["listName"]) {
			message.warning("请选择名单类型");
			return;
		}
		if (!blackListImportData["file"]) {
			message.warning("请选择要上传的文件");
			return;
		}
		blackListAPI.importBlackListData(blackListImportData).then(res => {
			if (res.success) {
				this.refs.importFile.value = "";
				// 重新刷新指标列表
				setTimeout(() => {
					this.startSearch();
				}, 1000);

				// 关闭弹窗
				this.closeModal();
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	startSearch() {
		let { dispatch, blackListStore } = this.props;
		let { searchParams } = blackListStore;
		// 页面初始化数据
		searchParams["curPage"] = 1;

		dispatch({
			type: "blackList/getBlackListData",
			payload: {
				...searchParams
			}
		});
		dispatch({
			type: "blackList/setAttrValue",
			payload: {
				searchParams: searchParams
			}
		});
	}

	closeModal() {
		let { dispatch } = this.props;
		dispatch({
			type: "blackList/setDialogShow",
			payload: {
				blackListImportModal: false
			}
		});
		dispatch({
			type: "blackList/setDialogData",
			payload: {
				blackListImportData: {
					listName: null,
					file: null
				}
			}
		});
	}

	render() {
		let { blackListStore, globalStore, dispatch } = this.props;
		let { dialogShow, dialogData } = blackListStore;
		let { blackListImportData } = dialogData;
		let { allMap } = globalStore;
		let { blackListSelect } = allMap;

		let radioStyle = {
			display: "block",
			height: "30px"
		};

		let footerButton = [
			<Button onClick={this.closeModal.bind(this)}>取消</Button>,
			<Button type="primary" onClick={this.importBlackListData.bind(this)}>上传</Button>
		];

		return (
			<Modal
				title="黑名单导入"
				visible={dialogShow.blackListImportModal}
				maskClosable={false}
				className="file-import-modal-wrap"
				onCancel={this.closeModal.bind(this)}
				footer={footerButton}
				width={650}
			>
				<div className="basic-form s2">
					<Row
						gutter={10}
						style={{ height: "auto" }}
					>
						<Col span={5} className="basic-info-title">
                            下载模板：
						</Col>
						<Col span={10}>
							<a href="/indexApi/admin/blackList/template">点击下载</a>
						</Col>
					</Row>
					<Divider />

					<Row
						gutter={10}
						style={{ height: "auto" }}
					>
						<Col span={5} className="basic-info-title">
                            名单类型：
						</Col>
						<Col span={10}>
							<RadioGroup
								onChange={this.changeImportMode.bind(this)}
								value={blackListImportData.listName || undefined}
							>
								{
									blackListSelect &&
                                    blackListSelect.map((item, index) => {
                                    	return (
                                    		<Radio
                                    			style={radioStyle}
                                    			value={item.name}
                                    			key={index}
                                    		>
                                    			{item.dName}
                                    		</Radio>
                                    	);
                                    })
								}
							</RadioGroup>
						</Col>
					</Row>
					<Row gutter={10}>
						<Col span={5} className="basic-info-title">
                            选择文件：
						</Col>
						<Col span={10}>
							<Tooltip title="文件大小在100M以内">
								<a className="file">
                                    选择文件
									<input
										type="file"
										ref="importFile"
										onChange={(e) => {
											let file = e.target && e.target.files && e.target.files[0] ? e.target.files[0] : undefined;
											if (blackListImportData["file"]) {
												if (!file) {
													return;
												}
											} else {
												if (!file) {
													// lang:请先选择文件
													message.warning("请先选择文件");
													return;
												}
											}
											let filePath = file.name;
											let fileSize = file.size / 1024;
											let reg = new RegExp(".(xlsx)$", "i");
											if (!reg.test(filePath)) {
												// 校验不通过
												message.warning("只允许上传xlsx格式的文件");
												return;
											}
											if (fileSize > 100000) {
												message.warning("文件大小在100M以内");
												return;
											}

											blackListImportData["file"] = file;
											dispatch({
												type: "blackList/setDialogData",
												payload: {
													blackListImportData: blackListImportData
												}
											});
										}}
									/>
								</a>
							</Tooltip>
							<div>
								{blackListImportData && blackListImportData["file"] ? blackListImportData["file"]["name"] : undefined}
							</div>
						</Col>
					</Row>
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	blackListStore: state.blackList
}))(BlackListImportModal);
