import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";
import Cookies from "universal-cookie";

const auth = async(params) => {
	return request("/bridgeApi/user/getAuthCode", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	}, true);
};

const getAuth = async(param) => {
	return request(getUrl("/auth", param), {
		method: "GET",
		headers: getHeader()
	});
};

const signIn = async(param) => {
	return request("/login", {
		method: "POST",
		headers: getHeader(),
		body: { ...param }
	});
};

// const signOut = async(param) => {
// 	const cookies = new Cookies();
// 	cookies.remove("_td_token_", { path: "/" });
// 	cookies.remove("_uid_", { path: "/" });
// 	return true;
// };
const signOut = async(params) => {
	return request("/bridgeApi/user/logout", {
	   method: "POST",
	   headers: getHeader(),
	   body: { ...params }
	}, true);
};

const getUserInfo = async(param) => {
	return request(getUrl("/bridgeApi/userCenter/getUserInfo", param), {
		method: "GET",
		headers: getHeader()
	}, true);
};

/**
 * @description: 统一登录接口
 * @param params
 * @returns {Promise<Object>}
 */
const userLogin = async(params) => {
	return request(getUrl("/bridgeApi/user/login"), {
		method: "POST",
		headers: getHeader(),
		body: params
	}, true);
};

const getUserMenuTree = async(params) => {
	return request(getUrl("/bridgeApi/user/menuTree", params), {
		method: "GET",
		headers: getHeader()
	}, true);
};

export default {
	auth,
	signIn,
	signOut,
	getUserInfo,
	getAuth,
	getUserMenuTree,
	userLogin
};
