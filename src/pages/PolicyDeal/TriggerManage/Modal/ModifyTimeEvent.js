import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Button, Input, Select, Form, Row, Col, message, Checkbox } from "antd";
import { CommonConstants } from "@/constants";
import { policyDealAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";

const Option = Select.Option;

class ModifyTimeEvent extends PureComponent {

	constructor(props) {
		super(props);
		this.changeDialogDataHandle = this.changeDialogDataHandle.bind(this);
		this.saveTimeEvent = this.saveTimeEvent.bind(this);
		this.deleteTimeEvent = this.deleteTimeEvent.bind(this);
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyDealStore } = this.props;
		let { modifyTimeEvent } = policyDealStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		} else if (type === "check") {
			value = e.target.checked;
		}
		modifyTimeEvent[field] = value;
		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				modifyTimeEvent: modifyTimeEvent
			}
		});
	}

	saveTimeEvent() {
		let { policyDealStore, dispatch } = this.props;
		let { dialogData } = policyDealStore;
		let { modifyTimeEvent, modifyTriggerTimeData } = dialogData;

		let eventData = {
			all: modifyTimeEvent.all,
			color: "#FF6C5C",
			setType: "normal"
		};

		// 校验同日期事件事件段是否重复
		let sameDayEvents = modifyTriggerTimeData.calendarDataList.filter(item => item.start.startsWith(modifyTimeEvent.startDay) && item.id !== modifyTimeEvent.id);
		console.log(sameDayEvents);
		if (modifyTimeEvent.all) {
			if (sameDayEvents.length > 0) {
				message.error("与同日其他日程设置重叠,设置失败");
				return;
			}
		} else {
			let allDayEvents = sameDayEvents.filter(item => item.all);
			if (allDayEvents.length > 0) {
				message.error("与同日其他日程设置重叠,设置失败");
				return;
			}

			let timeSlotEvents = sameDayEvents.filter((item) => {
				if (!item.all) {
					let startTime = item.start.split(" ")[1].split(":")[0];
					let endTime = item.end.split(" ")[1].split(":")[0];
					console.log("startTime : " + startTime);
					console.log("endTime : " + endTime);

					let repeat = (modifyTimeEvent.startHour <= startTime && modifyTimeEvent.endHour >= startTime) ||
                        (modifyTimeEvent.startHour <= endTime && modifyTimeEvent.endHour >= endTime);
					return repeat;
				} else {
					return false;
				}
			});

			if (timeSlotEvents.length > 0) {
				message.error("与同日其他日程设置重叠,设置失败");
				return;
			}
		}

		if (modifyTimeEvent.all) {
			eventData["start"] = modifyTimeEvent.startDay;
			eventData["title"] = "全天不触发";
		} else {

			if (modifyTimeEvent.startHour >= modifyTimeEvent.endHour) {
				message.error("结束时间要大于开始时间");
				return;
			}

			eventData["start"] = modifyTimeEvent.startDay + " " + modifyTimeEvent.startHour + ":00";
			eventData["end"] = modifyTimeEvent.startDay + " " + modifyTimeEvent.endHour + ":00";
			eventData["title"] = modifyTimeEvent.startHour + ":00" + " ~ " + modifyTimeEvent.endHour + ":00";
		}

		let params = {
			setType: modifyTimeEvent.setType,
			dealType: modifyTriggerTimeData.dealType,
			eventData: JSON.stringify(eventData)
		};

		if (modifyTimeEvent.isUpdate) {

			if (modifyTimeEvent.setType === "normal") {
				params["uuid"] = modifyTimeEvent.id;
			} else {
				if (!modifyTimeEvent.all && modifyTimeEvent.setType === "per_day") {

					params["dateString"] = modifyTimeEvent.startDay + "-" + modifyTimeEvent.startHour + "-" + modifyTimeEvent.endHour;
				} else {

					params["dateString"] = modifyTimeEvent.startDay;
				}
			}

			policyDealAPI.modifyTriggerTimeEvent(params).then(res => {
				if (res.success) {

					let deleteIndex = modifyTriggerTimeData["calendarDataList"].findIndex(item => item.id === modifyTimeEvent.id && item.start.startsWith(modifyTimeEvent.startDay));
					modifyTriggerTimeData["calendarDataList"].splice(deleteIndex, 1);
					eventData["id"] = res.data;
					modifyTriggerTimeData["calendarDataList"].push(eventData);
					dispatch({
						type: "policyDeal/setDialogData",
						payload: {
							modifyTriggerTimeData: modifyTriggerTimeData
						}
					});
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							modifyTimeEvent: false
						}
					});
				} else {
					console.log(res.message);
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		} else {
			policyDealAPI.addTriggerTimeEvent(params).then(res => {
				if (res.success) {
					eventData["id"] = res.data;
					modifyTriggerTimeData["calendarDataList"].push(eventData);
					dispatch({
						type: "policyDeal/setDialogData",
						payload: {
							modifyTriggerTimeData: modifyTriggerTimeData
						}
					});
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							modifyTimeEvent: false
						}
					});
				} else {
					console.log(res.message);
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		}

	}

	deleteTimeEvent() {
		let { policyDealStore, dispatch } = this.props;
		let { dialogData } = policyDealStore;
		let { modifyTimeEvent, modifyTriggerTimeData } = dialogData;

		let params = {
			setType: modifyTimeEvent.setType,
			uuid: modifyTimeEvent.id,
			dealType: modifyTriggerTimeData.dealType
		};

		if (!modifyTimeEvent.all && modifyTimeEvent.setType === "per_day") {
			params["dateString"] = modifyTimeEvent.startDay + "-" + modifyTimeEvent.startHour + "-" + modifyTimeEvent.endHour;
		} else {
			params["dateString"] = modifyTimeEvent.startDay;
		}

		policyDealAPI.deleteTriggerTimeEvent(params).then(res => {
			if (res.success) {

				let deleteIndex = modifyTriggerTimeData["calendarDataList"].findIndex(item => item.id === modifyTimeEvent.id && item.start.startsWith(modifyTimeEvent.startDay));
				modifyTriggerTimeData["calendarDataList"].splice(deleteIndex, 1);
				dispatch({
					type: "policyDeal/setDialogData",
					payload: {
						modifyTriggerTimeData: modifyTriggerTimeData
					}
				});
				dispatch({
					type: "policyDeal/setDialogShow",
					payload: {
						modifyTimeEvent: false
					}
				});
			} else {
				console.log(res.message);
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { policyDealStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyDealStore;
		let { modifyTimeEvent } = dialogData;

		let hourSelectDom = [];

		for (let i = 0; i <= 24; i++) {
			hourSelectDom.push(<Option value={i} index={i}>{i}</Option>);
		}

		let title = modifyTimeEvent.isUpdate ? "修改不触发日程" : "添加不触发日程";

		return (
			<Modal
				style={{ zIndex: 2000 }}
				title={title}
				visible={dialogShow.modifyTimeEvent}
				maskClosable={false}
				onCancel={() => {
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							modifyTimeEvent: false
						}
					});
				}}
				footer={[
					modifyTimeEvent.isUpdate &&
                    <Button
                    	type="primary"
                    	onClick={() => {
                    		if (checkFunctionHasPermission("ZB0301", "deleteUnTriggerByTime")) {
                    			this.deleteTimeEvent();
                    		} else {
                    			message.warning("无权限操作");
                    		}
                    	}}
                    >
                        删除配置
                    </Button>,
					<Button
						key="cancel"
						onClick={() => {
							dispatch({
								type: "policyDeal/setDialogShow",
								payload: {
									modifyTimeEvent: false
								}
							});
						}}
					>
                        取消
					</Button>,
					<Button
						key="submit"
						type="primary"
						onClick={() => {
							if (!modifyTimeEvent.isUpdate && checkFunctionHasPermission("ZB0301", "addUnTriggerByTime")) {
								this.saveTimeEvent();
							} else if (modifyTimeEvent.isUpdate && checkFunctionHasPermission("ZB0301", "updateUnTriggerByTime")) {
								this.saveTimeEvent();
							} else {
								message.warning("无权限操作");
							}
						}}
					>
                        确定
					</Button>
				]}
			>
				<Form className="modal-form">
					{
						modifyTimeEvent.all &&
                        <Row gutter={CommonConstants.gutterSpan}>
                        	<Col span={6} className="basic-info-title">时间：</Col>
                        	<Col span={18}>
                        		<Input
                        			disabled={true}
                        			value={modifyTimeEvent.startDay || undefined}
                        			onChange={this.changeDialogDataHandle.bind(this, "startDay", "input")}
                        			placeholder="时间"
                        			style={{ width: "150px" }}
                        		/>
                        	</Col>
                        </Row>
					}
					{
						!modifyTimeEvent.all &&
                        <Row gutter={CommonConstants.gutterSpan}>
                        	<Col span={6} className="basic-info-title">开始时间：</Col>
                        	<Col span={18}>
                        		<Input
                        			disabled={true}
                        			value={modifyTimeEvent.startDay || undefined}
                        			onChange={this.changeDialogDataHandle.bind(this, "startDay", "input")}
                        			placeholder="时间"
                        			style={{ width: "150px" }}
                        		/>
                        		<Select
                        			placeholder="请选择系统字段"
                        			value={modifyTimeEvent.startHour}
                        			onChange={this.changeDialogDataHandle.bind(this, "startHour", "select")}
                        			style={{ width: "100px", marginLeft: "10px", marginRight: "10px", zIndex: 2000 }}
                        		>
                        			{
                        				hourSelectDom
                        			}
                        		</Select>时
                        	</Col>
                        </Row>
					}
					{
						!modifyTimeEvent.all && <Row gutter={CommonConstants.gutterSpan}>
							<Col span={6} className="basic-info-title">结束时间：</Col>
							<Col span={18}>
								<Input
									disabled={true}
									value={modifyTimeEvent.startDay || undefined}
									onChange={this.changeDialogDataHandle.bind(this, "startDay", "input")}
									placeholder="时间"
									style={{ width: "150px" }}
								/>
								<Select
									placeholder="请选择系统字段"
									value={modifyTimeEvent.endHour}
									onChange={this.changeDialogDataHandle.bind(this, "endHour", "select")}
									style={{ width: "100px", marginLeft: "10px", marginRight: "10px", zIndex: 2000 }}
								>
									{
										hourSelectDom
									}
								</Select>时
							</Col>
						</Row>
					}

					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">全天日程：</Col>
						<Col span={18}>
							<Checkbox
								checked={modifyTimeEvent.all || undefined}
								onChange={this.changeDialogDataHandle.bind(this, "all", "check")}
								style={{ marginTop: "5px" }}
							/>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(ModifyTimeEvent);
