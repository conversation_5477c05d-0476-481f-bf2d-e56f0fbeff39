import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Form, Row, Col, message } from "antd";
import { workflowAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { workflowLang } from "@/constants/lang";

const TextArea = Input.TextArea;

class WorkflowCommitModal extends PureComponent {

    state = {
    	desc: null
    };

    constructor(props) {
    	super(props);
    	this.workflowCommit = this.workflowCommit.bind(this);
    }

    workflowCommit() {
    	let { policyEditorStore, policyRunningStore, workflowStore, globalStore, dispatch } = this.props;
    	let { currentApp } = globalStore;
    	let { curPage, pageSize } = policyEditorStore;
    	let { policySetItem } = workflowStore;
    	let desc = this.state.desc;
    	if (!desc) {
    		message.warning(workflowLang.workflowCommitModal("releaseDescriptionPlaceholder")); // lang:请输入发版描述
    		return;
    	}
    	let params = {
    		policySetUuid: policySetItem.uuid,
    		desc: desc
    	};

    	workflowAPI.workflowCommit(params).then(res => {
    		if (res.success) {
    			message.success(res.message);
    			dispatch({
    				type: "workflow/setDialogShow",
    				payload: {
    					workflowCommit: false
    				}
    			});
    			// 根据策略集获取规则流数据
    			dispatch({
    				type: "workflow/getDecisionFlow",
    				payload: {
    					policySetUuid: policySetItem.uuid
    				}
    			});
    			// 重新拉取策略工作区列表
    			dispatch({
    				type: "policyEditor/getPolicySets",
    				payload: {
    					appName: currentApp && currentApp.name ? currentApp.name : null,
    					curPage: curPage,
    					pageSize: pageSize
    				}
    			});
    			// 重新拉取运行区列表
    			dispatch({
    				type: "policyRunning/getPolicySets",
    				payload: {
    					appName: currentApp.name ? currentApp.name : null,
    					curPage: policyRunningStore.curPage,
    					pageSize: policyRunningStore.pageSize
    				}
    			});
    			this.setState({
    				desc: null
    			});
    		} else {
    			message.error(res.message);
    		}
    	});
    }

    render() {
    	let { workflowStore, dispatch } = this.props;
    	let { policySetItem, dialogShow } = workflowStore;

    	return (
    		<Modal
    			title={workflowLang.workflowCommitModal("title")} // lang:"规则流版本提交"
    			visible={dialogShow.workflowCommit}
    			width={650}
    			maskClosable={true}
    			onOk={this.workflowCommit.bind(this)}
    			onCancel={() => {
    				dispatch({
    					type: "workflow/setDialogShow",
    					payload: {
    						workflowCommit: false
    					}
    				});
    				this.setState({
    					desc: null
    				});
    			}}
    		>
    			<div className="basic-form">
    				<Form>
    					<Row gutter={CommonConstants.gutterSpan}>
    						<Col span={6} className="basic-info-title">
    							{/* lang:所属策略集：*/}
    							{workflowLang.workflowCommitModal("policySet")}：
    						</Col>
    						<Col span={18}>
    							<Input
    								type="text"
    								placeholder={workflowLang.workflowCommitModal("policySetName")} // lang:"策略集名称"
    								value={policySetItem && policySetItem.name}
    								disabled={true}
    							/>
    						</Col>
    					</Row>
    					<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
    						<Col span={6} className="basic-info-title">
    							{/* lang:发版描述：*/}
    							{workflowLang.workflowCommitModal("releaseDescription")}：
    						</Col>
    						<Col span={18}>
    							<TextArea
    								placeholder={workflowLang.workflowCommitModal("releaseDescriptionPlaceholder")} // lang:"请输入发版描述"
    								value={this.state.desc || undefined}
    								onChange={(e) => {
    									this.setState({
    										desc: e.target.value
    									});
    								}}
    								rows={4}
    							/>
    						</Col>
    					</Row>
    				</Form>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor,
	policyRunningStore: state.policyRunning,
	workflowStore: state.workflow
}))(WorkflowCommitModal);
