import React, { Fragment } from "react";
import { connect } from "dva";
import { Drawer, Tag, Tooltip } from "antd";
import { routerRedux } from "dva/router";
import "./IndexCiteDrawer.less";
// import ReadOnlyScene from "@/pages/Policy/Salaxy/RunningArea/Inner/Scene";
import FilterScene from "@/pages/Policy/Salaxy/Common/FilterScene";
import { indexListLang } from "@/constants/lang";

class IndexCiteDrawer extends React.PureComponent {
	constructor(props) {
		super(props);
		this.closeDrawerHandle = this.closeDrawerHandle.bind(this);
	}

	closeDrawerHandle() {
		let { dispatch } = this.props;
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				indexCiteDrawer: false
			}
		});
	}

	// 普通策略
	citeDataItem(item, type) {
		let { globalStore } = this.props;
		let { allMap } = globalStore;
		return (
			<dl>
				<dt>
					<span>
						{/* lang:所属策略 */}
						{indexListLang.citeDrawer("policy")}{item.policyName}
					</span>
					{/* lang:所属应用 */}
					<Tooltip title={indexListLang.citeDrawer("appName")}>
						<Tag color="volcano">
							{/* lang:应用：*/}
							{indexListLang.citeDrawer("appName2")}
							{
								allMap && allMap["appNames"] && allMap["appNames"].map((citem, cindex) => {
									if (citem.name === item.appName) {
										return citem.dName;
									}
								})
							}
						</Tag>
					</Tooltip>
					{/* lang:所在策略集 */}
					<Tooltip title={indexListLang.citeDrawer("inPolicySetTip")}>
						<Tag color="cyan">
							{/* lang:策略集：*/}
							{indexListLang.citeDrawer("policySet2")}
							{item.policySetName}
						</Tag>
					</Tooltip>
				</dt>
				{
					item["rules"] && item["rules"].map((rItem, rIndex) => {
						let href;
						let hrefType = type === "runningArea" ? "versionPolicyDetail" : "policyDetail";
						if (process.env.SYS_ENV === "development") {
							href = "/policy/" + hrefType + "/" + item.policyUuid + "?tabIndex=1&ruleUuid=" + rItem.ruleUuid;
						} else {
							href = "/index/policy/" + hrefType + "/" + item.policyUuid + "?tabIndex=1&ruleUuid=" + rItem.ruleUuid;
						}
						if (type === "runningArea") {
							href += "&policyVersion=" + item.policyVersion;
						}
						return (
							<dd key={rIndex}>
								<a
									href={href}
									target="_blank"
								>
									{rItem.ruleName}
								</a>
							</dd>
						);
					})
				}
			</dl>
		);
	}

    openEditArea = async(rItem) => {
    	let { indexEditorStore, dispatch, globalStore, viewIndexItemDetail } = this.props;
    	let { pageSize, searchParams } = indexEditorStore;
    	let { currentApp } = globalStore;
    	let appName = currentApp.name;
    	if (appName !== "all") {
    		searchParams["app"] = appName;
    	} else {
    		searchParams["app"] = "";
    	}
    	searchParams.name = rItem.name;
    	searchParams.calcType = "";
    	// 修改查询条件
    	await dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			searchParams: searchParams
    		}
    	});
    	// 根据当前名称查询列表数据
    	await dispatch({
    		type: "indexEditor/getSalaxyList",
    		payload: {
    			curPage: 1,
    			pageSize: pageSize,
    			...searchParams
    		}
    	});
    	// 默认展开当前指标内容
    	viewIndexItemDetail([rItem.id]);
    }

    openRunningArea = async(rItem) => {
    	const { dispatch, pathname, indexRunningStore, globalStore } = this.props;
    	let { pageSize, searchParams } = indexRunningStore;
    	let { currentApp } = globalStore;
    	let appName = currentApp.name;

    	if (appName !== "all") {
    		searchParams["app"] = appName;
    	} else {
    		searchParams["app"] = "";
    	}
    	searchParams.name = rItem.name;
    	searchParams.calcType = "";
    	await dispatch({
    		type: "indexRunning/setAttrValue",
    		payload: {
    			searchParams: searchParams
    		}
    	});
    	await dispatch({
    		type: "indexRunning/getSalaxyList",
    		payload: {
    			curPage: 1,
    			pageSize: pageSize,
    			...searchParams
    		}
    	});
    	await dispatch({
    		type: "indexRunning/setAttrValue",
    		payload: {
    			indexActiveKey: [rItem.id],
    			currentIndexId: rItem.id,
    			indexActiveIndex: null,
    			editIndexMap: {}
    		}
    	});
    	const search = "?currentTab=1";
    	dispatch(routerRedux.push(pathname + search));
    }

    // 指标公式
    citeFormula = (item, type) => {
    	const { dispatch } = this.props;
    	return (
    		<dl>
    			{
    				item.map((rItem, rIndex) => {
    					return (
    						<dd
    							key={rIndex}
    							onClick={() => {
    								// 清空指标查看弹窗数据
    								dispatch({
    									type: "indexEditor/setDialogShow",
    									payload: {
    										indexCiteDrawer: false
    									}
    								});
    								dispatch({
    									type: "indexEditor/setDialogData",
    									payload: {
    										indexCiteDrawerData: {
    											indexId: null,
    											item: null,
    											dataList: []
    										}
    									}
    								});
    								if (type === "editArea") {
    									this.openEditArea(rItem);
    								} else {
    									this.openRunningArea(rItem);
    								}
    							}}
    						>
    							<a>{rItem.name}</a>
    						</dd>
    					);
    				})
    			}
    		</dl>
    	);
    }

    // 公共策略
    publicCiteDataItem(item, type) {
    	return (
    		<dl>
    			<dt>
    				<span>
    					{/* lang:所属策略 */}
    					{indexListLang.citeDrawer("policy")}{item.policyName}
    				</span>
    			</dt>
    			{
    				item["rules"] && item["rules"].map((rItem, rIndex) => {
    					let href;
    					let hrefType = type === "runningArea" ? "versionPublicPolicyDetail" : "publicPolicyDetail";
    					if (process.env.SYS_ENV === "development") {
    						href = "/policy/" + hrefType + "/" + item.policyUuid + "?tabIndex=1&ruleUuid=" + rItem.ruleUuid;
    					} else {
    						href = "/index/policy/" + hrefType + "/" + item.policyUuid + "?tabIndex=1&ruleUuid=" + rItem.ruleUuid;
    					}
    					if (type === "runningArea") {
    						href += "&policyVersion=" + item.policyVersion;
    					}
    					return (
    						<dd key={rIndex}>
    							<a
    								href={href}
    								target="_blank"
    							>
    								{rItem.ruleName}
    							</a>
    						</dd>
    					);
    				})
    			}
    		</dl>
    	);
    }
    render() {
    	let { indexEditorStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = indexEditorStore;
    	let { indexCiteDrawerData } = dialogData;
    	let { item, dataList = {} } = indexCiteDrawerData;
    	const { online = [], pend = [], onlineFormulaZbs = [], pendFormulaZbs = [], onlineForPub = [], pendForPub = [] } = dataList || {};
    	const hasOnlineDataList = (online && online.length > 0) || (onlineFormulaZbs && onlineFormulaZbs.length > 0) || (onlineForPub && onlineForPub.length > 0);
    	const hasPendDataList = (pend && pend.length > 0) || (pendFormulaZbs && pendFormulaZbs.length > 0) || (pendForPub || pendForPub.length > 0);
    	let { scene } = item || {};
    	if (scene) {
    		scene = JSON.parse(scene) || [];
    	}
    	return (
    		<Drawer
    			title="Basic Drawer"
    			width={550}
    			className="index-cite-drawer-wrap"
    			placement="right"
    			closable={false}
    			onClose={() => {
    				dispatch({
    					type: "indexEditor/setDialogData",
    					payload: {
    						indexCiteDrawerData: {
    							indexId: null,
    							item: null,
    							dataList: {}
    						}
    					}
    				});
    			}}
    			visible={dialogShow.indexCiteDrawer}
    			mask={false}
    		>
    			<div className="index-cite-detail">
    				<div className="index-cite-detail-title">
    					<i className="iconfont icon-zhibiao title-pre"></i>
    					<div className="index-cite-title-text">
    						<span>{item && item.name}</span>
    						<span className="tip">
    							{/* lang:引用详情 */}
    							{indexListLang.citeDrawer("citeDetail")}
    						</span>
    					</div>
    					<i className="iconfont icon-close title-close" onClick={this.closeDrawerHandle.bind(this)}></i>
    				</div>
    				<div className="index-cite-detail-base">
    					{/* {
    						scene &&
                            scene.length > 0 &&
                            <ReadOnlyScene
                            	indexData={{ "sceneList": scene || [] }}
                            	spanWidth={20}
                            	className="td-no-bg"
                            />
    					} */}
    					{
    						scene &&
							scene.length > 0 &&
							<FilterScene
								disabled={true}
								indexData={item}
								colSpanRight={20}
								width="150px"
							/>
    					}
    					<div className="index-cite-detail-group">
    						<label>
    							{/* lang:创建时间 */}
    							{indexListLang.citeDrawer("createTime")}
    						</label>
    						<span>{item && item.gmtCreate}</span>
    					</div>
    					<div className="index-cite-detail-group">
    						<label>
    							{/* lang:修改时间 */}
    							{indexListLang.citeDrawer("modifyTime")}
    						</label>
    						<span>{item && item.gmtModified}</span>
    					</div>
    				</div>
    				<div className="index-cite-detail-sep"></div>
    				<div className="index-cite-list">
    					{
    						hasOnlineDataList &&
                            <Fragment>
                            	<div className='index-cite-list-item-title'>
                            		{/* 运行区 */}
                            		{indexListLang.citeDrawer("runningArea")}
                            	</div>
                            	{
                            		online && online.map((item, index) => {
                            			return (
                            				<Fragment key={index}>
                            					<div className="sub-item">
                            						{this.citeDataItem(item, "runningArea")}
                            					</div>
                            				</Fragment>
                            			);
                            		})
                            	}
                            	{
                            		onlineFormulaZbs && onlineFormulaZbs.length > 0 &&
                                    <div className="sub-item">
                                    	<div className="sub-item-title">
                                    		{/* 公式指标 */}
                                    		{indexListLang.citeDrawer("formulaIndex")}
                                    	</div>
                                    	{this.citeFormula(onlineFormulaZbs, "runningArea")}
                                    </div>
                            	}

                            	{
                            		onlineForPub && onlineForPub.length > 0 &&
                                    <div className="sub-item">
                                    	<div className="sub-item-title">
                                    		{/* 公共策略 */}
                                    		{indexListLang.citeDrawer("publicStrategy")}
                                    	</div>
                                    	{
                                    		onlineForPub.map((item)=>{
                                    			return this.publicCiteDataItem(item, "runningArea");
                                    		})
                                    	}
                                    </div>
                            	}
                            </Fragment>
    					}
    					{
    						hasPendDataList &&
                            <Fragment>
                            	<div className='index-cite-list-item-title'>
                            		{/* 编辑区 */}
                            		{indexListLang.citeDrawer("editArea")}
                            	</div>
                            	{
                            		pend && pend.map((item, index) => {
                            			return (
                            				<Fragment key={index}>
                            					<div className="sub-item">
                            						{this.citeDataItem(item, "editArea")}
                            					</div>
                            				</Fragment>
                            			);
                            		})
                            	}
                            	{
                            		pendFormulaZbs && pendFormulaZbs.length > 0 &&
                                    <div className="sub-item">
                                    	<div className="sub-item-title">
                                    		{/* 公式指标 */}
                                    		{indexListLang.citeDrawer("formulaIndex")}
                                    	</div>
                                    	{this.citeFormula(pendFormulaZbs, "editArea")}
                                    </div>
                            	}
                            	{
                            		pendForPub && pendForPub.length > 0 &&
                                    <div className="sub-item">
                                    	<div className="sub-item-title">
                                    		{/* 公共策略 */}
                                    		{indexListLang.citeDrawer("publicStrategy")}
                                    	</div>
                                    	{
                                    		pendForPub.map((item)=>{
                                    			return this.publicCiteDataItem(item, "editArea");
                                    		})
                                    	}
                                    </div>
                            	}
                            </Fragment>
    					}
    					{
    						!(hasOnlineDataList || hasPendDataList) &&
                            <div class="none-data">
                            	<i class="iconfont icon-empty"></i>
                            	<p>
                            		{/* lang:当前指标还没有被引用 */}
                            		{indexListLang.citeDrawer("noneCiteText")}
                            	</p>
                            </div>
    					}
    				</div>
    			</div>
    		</Drawer>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	indexRunningStore: state.indexRunning
}))(IndexCiteDrawer);
