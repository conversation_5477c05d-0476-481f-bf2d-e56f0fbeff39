import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Form, Row, Col, message, Toolt<PERSON>, Button } from "antd";
import "./RulesImportModal.less";
import { policyEditorAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { imExportModeLang, commonLang } from "@/constants/lang";
import ImportMode from "@/components/ImportModal/FileImportCondition/ImportMode";
import SelectScene from "@/components/ImportModal/FileImportCondition/SelectScene";
import FileImportResult from "@/components/ImportModal/FileImportResult";
import { cloneDeep } from "lodash";

class RulesImportModal extends PureComponent {
	state = {
		hasSuccess: false,
		successData: {},
		uploadLoading: false
	};

	constructor(props) {
		super(props);
	}

	changeImport = (e, { type, valueType = "input" }) => {
		let value = "";
		if (valueType === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		let { policyEditorStore, dispatch } = this.props;
		let { dialogData } = policyEditorStore;
		let { rulesImportData } = dialogData;
		const newRulesImportData = cloneDeep(rulesImportData);

		newRulesImportData[type] = value;
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				rulesImportData: newRulesImportData
			}
		});
	}

	importPolicyRules = async() => {
		let { policyEditorStore, globalStore, dispatch } = this.props;
		let { dialogData, curPage, pageSize } = policyEditorStore;
		let { rulesImportData } = dialogData;

		if (!rulesImportData["ruleMode"]) {
			// lang:请选择规则导入模式
			message.warning(imExportModeLang.importPolicyModal("ruleModeRequired"));
			return;
		}
		if (!rulesImportData["zbMode"]) {
			// lang:请选择指标导入模式
			message.warning(imExportModeLang.importPolicyModal("zbModeRequired"));
			return;
		}
		if (!rulesImportData["file"]) {
			// lang:请选择要上传的文件
			message.warning(imExportModeLang.importPolicyModal("selectFileFirst"));
			return;
		}
		const newRulesImportData = cloneDeep(rulesImportData);
		if (!newRulesImportData.replaceScene) {
			delete newRulesImportData.replaceScene;
		} else {
			newRulesImportData.replaceScene = JSON.stringify(newRulesImportData.replaceScene);
		}
		await this.setState({
			uploadLoading: true
		});
		await policyEditorAPI.importRules(newRulesImportData).then(res => {
			this.setState({
				uploadLoading: false
			});
			if (res.success) {
				this.refs.rulesImportFile.value = "";
				this.setState({
					hasSuccess: true,
					successData: res.data || {}
				});
				// 导入成功后
				// 1. 刷新allMap
				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
				// 2. 重新刷新编辑区策略列表
				let { currentApp } = globalStore;
				if (currentApp.name) {
					dispatch({
						type: "policyEditor/getPolicySets",
						payload: {
							appName: currentApp.name ? currentApp.name : null,
							curPage: curPage,
							pageSize: pageSize
						}
					});
				}
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			this.setState({
				uploadLoading: false
			});
			console.log(err);
		});
	}

	closeModal = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				RulesImport: false
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				rulesImportData: {
					uuid: null,
					importMode: null,
					file: null,
					replaceScene: "",
					zbMode: "",
					ruleMode: ""
				}
			}
		});
		if (this.refs.rulesImportFile) {
			this.refs.rulesImportFile.value = "";
		}
		this.setState({
			hasSuccess: false,
			successData: {},
			uploadLoading: false
		});
	}
	render() {
		const { successData = {}, hasSuccess, uploadLoading } = this.state;
		let { policyEditorStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyEditorStore;
		let { rulesImportData } = dialogData;
		const { replaceScene } = rulesImportData || {};
		let footerButton = [];
		if (hasSuccess) {
			footerButton = [
				<Button type="primary" onClick={this.closeModal.bind(this)}>
					{/* lang:确定 */}
					{commonLang.base("ok")}
				</Button>
			];
		} else {
			footerButton = [
				<Button onClick={this.closeModal.bind(this)}>
					{/* lang:取消 */}
					{commonLang.base("cancel")}
				</Button>,
				<Button
					type="primary"
					onClick={this.importPolicyRules.bind(this)}
					loading={uploadLoading}
					disabled={uploadLoading}
				>
					{/* lang:上传 */}
					{commonLang.base("upload")}
				</Button>
			];
		}
		return (
			<Modal
				title={imExportModeLang.importPolicyModal("ruleImport")} // lang:规则导入
				visible={dialogShow.RulesImport}
				maskClosable={false}
				className="rules-import-modal-wrap"
				width={700}
				footer={footerButton}
				onCancel={this.closeModal.bind(this)}
			>
				<div className="basic-form s2">
					{
						!hasSuccess &&
						<Form>
							{/* 导入场景 */}
							<ImportMode
								changeImport={this.changeImport}
								data={rulesImportData}
								showMode={{ "zbMode": true, "ruleMode": true }}
							/>

							<Row gutter={CommonConstants.gutterSpan}>
								<Col span={5} className="basic-info-title line-height-32">
									{/* lang:选择文件 */}
									{imExportModeLang.importPolicyModal("selectFile")}：
    						</Col>
								<Col span={19}>
									{/* lang:文件大小在100M以内 */}
									<Tooltip title={imExportModeLang.importPolicyModal("fileSizeTip")}>
										<a className="file line-height-32">
											{/* lang:选择文件 */}
											{imExportModeLang.importPolicyModal("selectFile")}
											<input
												type="file"
												ref="rulesImportFile"
												onChange={(e) => {
													let file = e.target && e.target.files && e.target.files[0] ? e.target.files[0] : undefined;
													if (rulesImportData["file"]) {
														if (!file) {
															return;
														}
													} else {
														if (!file) {
															// lang:请先选择文件
															message.warning(imExportModeLang.importPolicyModal("selectFileFirst"));
															return;
														}
													}
													let filePath = file.name;
													let fileSize = file.size / 1024;
													let reg = new RegExp(".(ply)$", "i");
													if (!reg.test(filePath)) { // 校验不通过
														// lang:只允许上传ply格式的文件
														message.warning(imExportModeLang.importPolicyModal("fileAllowedTip"));
														return;
													}
													if (fileSize > 100000) {
														// lang:文件大小在100M以内
														message.warning(imExportModeLang.importPolicyModal("fileSizeTip"));
														return;
													}

													rulesImportData["file"] = file;
													dispatch({
														type: "policyEditor/setDialogData",
														payload: {
															rulesImportData: rulesImportData
														}
													});
												}}
											/>
										</a>
									</Tooltip>
									<div className="mb10">
										{rulesImportData && rulesImportData["file"] ? rulesImportData["file"]["name"] : undefined}
									</div>
								</Col>
							</Row>

							{/* 选择场景 */}
							<SelectScene
								changeImport={this.changeImport}
								data={rulesImportData}
								type="rules"
							/>
						</Form>
					}
					{
						hasSuccess &&
						<FileImportResult
							type="rules"
							showMode={{ "zbMode": true, "ruleMode": true }}
							successData={successData}
							replaceScene={replaceScene}
						/>
					}
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(RulesImportModal);
