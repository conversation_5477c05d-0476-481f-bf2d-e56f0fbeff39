import React, { PureComponent, Component } from "react";
import { connect } from "dva";
import { Modal, Button, Input, Select, Form, Row, Col, Slider, message } from "antd";
import { userAPI } from "../../../services";
import { isJSON } from "../../../utils/isJSON";
import Cookies from "universal-cookie";
import { rsaPwd } from "../../../utils/user";

const TextArea = Input.TextArea;
const Option = Select.Option;

class DevelopmentLoginModal extends PureComponent {
    state = {
    	account: null,
    	password: null
    };

    constructor(props) {
    	super(props);
    	this.submitModal = this.submitModal.bind(this);
    }

    componentWillMount() {
    	let developmentLoginData = localStorage.getItem("developmentLoginData");
    	if (developmentLoginData && isJSON(developmentLoginData)) {
    		let developmentLoginJson = JSON.parse(developmentLoginData);

    		this.setState({
    			account: developmentLoginJson.account || "admin",
    			password: developmentLoginJson.password || "Td@123"
    		});
    	}
    }

    async submitModal(needLogin) {
    	let { account, password } = this.state;
    	let { dispatch, onCancel } = this.props;
    	let params = { account, password };
    	const rsaPassword = rsaPwd(params.password);
    	let {tempRandom, authSuccess, authMessage} = ["", false, ""];
    	try {
    		// 获取加盐随机数
    		const authResult = await userAPI.auth({
    			...params,
    			password: rsaPassword
    		}) || {};
    		tempRandom = authResult.data;
    		authSuccess = authResult.success;
    		authMessage = authResult.message;

    		if (authSuccess && tempRandom) {
    			userAPI.userLogin({
    				...params,
    				tempRandom,
    				password: rsaPassword
    			}).then(res => {
    				if (res.success) {
    					message.success("模拟登陆成功");

    					setTimeout(() => {
    						// let token = res.data.tdToken;
    						// let userId = res.data.userId;
    						// cookies.set("_td_token_", token, { path: "/" });
    						// cookies.set("_uid_", userId, { path: "/" });
    						// sessionStorage.setItem("_td_token_", token);
    						// localStorage.setItem("_td_token_", token);

    						const csrfToken = res.data.csrfToken;
    						sessionStorage.setItem("_csrf_", csrfToken);
    						localStorage.setItem("_sync_qjt_csrf_", csrfToken); // 新的csrf同步到其他页面

    						localStorage.setItem("developmentLoginData", JSON.stringify(params));
    						// 刷新menuTree
    						dispatch({
    							type: "global/getUserMenuTree",
    							payload: {}
    						});
    						dispatch({
    							type: "global/getAllMap",
    							payload: {}
    						});
    						onCancel();
    					}, 500);
    				} else {
    					message.error(res.message);
    				}
    			}).catch(err => {
    				console.log(err);
    			});
    		} else {
    			message.error(authMessage);
    		}
    	} catch (e) {
    		message.error(e.message);
    	}
    };

    changeFieldValue(field, e) {
    	let { account, password } = this.state;
    	let params = { account, password };
    	let value = e.target.value;

    	params[field] = value;
    	this.setState(params);
    }

    render() {
    	let { showDevelopmentLoginModal, onCancel, dispatch } = this.props;
    	let { account, password } = this.state;

    	return (
    		<Modal
    			title="开发者模拟登陆"
    			visible={showDevelopmentLoginModal}
    			maskClosable={false}
    			onOk={this.submitModal.bind(this)}
    			onCancel={() => {
    				onCancel();
    			}}
    		>
    			<Form className="basic-form">
    				<Row gutter={10}>
    					<Col span={5} className="basic-info-title">
                            用户名：
    					</Col>
    					<Col span={18}>
    						<Input
    							type="text"
    							placeholder="请输入用户名"
    							value={account || undefined}
    							onChange={this.changeFieldValue.bind(this, "account")}
    						/>
    					</Col>
    				</Row>
    				<Row gutter={10}>
    					<Col span={5} className="basic-info-title">
                            密码：
    					</Col>
    					<Col span={18}>
    						<Input
    							type="text"
    							placeholder="请输入用户名"
    							value={password || undefined}
    							onChange={this.changeFieldValue.bind(this, "password")}
    						/>
    					</Col>
    				</Row>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global
}))(DevelopmentLoginModal);
