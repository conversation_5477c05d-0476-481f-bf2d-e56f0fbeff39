import { uniqBy, cloneDeep } from "lodash";

// 去除规则params重复数据
export const ruleRemoveSameParams = (policyRules, ruleIndexArr) => {
	let params = null;
	let currentChild = null;
	if (ruleIndexArr.length === 1) {
		currentChild = policyRules[ruleIndexArr[0]]["conditions"].children[0];
		params = currentChild["params"];
	} else if (ruleIndexArr.length === 2) {
		currentChild = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"].children[0];
		params = currentChild["params"];
	}
	console.log(policyRules);
};
