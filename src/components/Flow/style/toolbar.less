:global {
    .flow-toolbar {
        & {
            line-height: 48px;
            padding-left: 20px;
        }

        .ant-divider {
            float: left;
            position: relative;
            top: 18px;
        }

        .command {
            & {
                float: left;
                position: relative;
                top: 8px;
                width: 32px;
                height: 32px;
                line-height: 32px;
                text-align: center;
                margin-right: 5px;
            }

            &:hover {
                i {
                    color: #2f80f7;
                }
            }

            &.disable i {
                cursor: not-allowed;
                color: rgba(0, 0, 0, 0.25);
            }

            i {
                position: relative;
                top: 2px;
                cursor: pointer;
                display: inline-block;
                text-align: center;
                font-size: 18px;
            }
        }
    }

    .flow-editor-hd .toolbar-left {
        & {
            position: absolute;
            left: 12px;
        }

        .ant-alert {
            position: relative;
            min-width: 300px;
            top: 6px;
            padding-top: 0;
            padding-bottom: 0;
            padding-left: 30px;
            line-height: 32px;

            i {
                top: 9px;
                left: 10px;
            }
        }
    }

    .flow-editor-hd .toolbar-right {
        & {
            position: absolute;
            right: 12px;
        }

        .toolbar-right-item {
            & {
                float: left;
                margin-left: 12px;
            }

            .ant-tag {
                width: auto;
                height: 32px;
                line-height: 32px;
            }

            .ant-select {
                position: relative;
                width: 200px;
            }
        }
    }
}
