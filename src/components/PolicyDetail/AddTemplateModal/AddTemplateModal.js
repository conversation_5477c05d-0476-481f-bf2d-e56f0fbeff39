import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Table } from "antd";
import { policyDetailLang } from "@/constants/lang";

class AddTemplateModal extends PureComponent {

    state = {
    	currentGroupIndex: 0
    };

    constructor(props) {
    	super(props);
    	this.selectTemplate = this.selectTemplate.bind(this);
    }

    componentWillMount() {
    	this.setState({
    		currentGroupIndex: 0
    	});
    }

    selectTemplate(item) {
    	let { policyDetailStore, dispatch } = this.props;
    	let { policyRules, addIFChildRuleIndex, policyUuid, dialogShow, ruleIndexArr } = policyDetailStore;
    	let { addRuleTemplate, addIFChildRule } = dialogShow;

    	let timestamp = Date.parse(new Date());
    	let tempObj = {
    		unSave: true,
    		hasModify: false,
    		template: item.name,
    		uuid: timestamp.toString(),
    		weightRatio: 0,
    		valid: 0,
    		baseWeight: 1,
    		name: item.displayName,
    		id: timestamp,
    		operationActions: [],
    		applyStatus: "Approved",
    		fkPolicyUuid: policyUuid,
    		operateCode: "Accept",
    		displayOrder: ruleIndexArr.length === 1 ? ruleIndexArr[0] + 1 : ruleIndexArr[1] + 1,
    		conditions: {
    			logicOperator: "&&",
    			operator: "",
    			value: "",
    			type: "context",
    			description: "",
    			fkRuleUuid: policyUuid,
    			propertyDataType: "",
    			children: []
    		}
    	};
    	let aliasTempObj = {
    		uuid: timestamp.toString(),
    		logicOperator: "&&",
    		property: item.name,
    		operator: "==",
    		value: "1",
    		type: "alias",
    		description: "",
    		params: [],
    		propertyDataType: "",
    		children: [],
    		describe: item.description
    	};
    	if (addIFChildRule) {
    		if (item.name !== "common/custom") {
    			tempObj.conditions.children.push(aliasTempObj);
    		}

    		dispatch({
    			type: "policyDetail/setAttrValue",
    			payload: {
    				ruleActiveKey: [timestamp.toString()],
    				currentRuleUuid: timestamp.toString()
    			}
    		});
    	}

    	if (addIFChildRule) {
    		if (policyRules[addIFChildRuleIndex]["children"]) {
    			policyRules[addIFChildRuleIndex]["children"].push(tempObj);
    		} else {
    			policyRules[addIFChildRuleIndex]["children"] = [tempObj];
    		}
    	}
    	if (addRuleTemplate) {
    		if (ruleIndexArr.length === 1) {
    			policyRules[ruleIndexArr[0]]["conditions"]["children"].push(aliasTempObj);
    		} else if (ruleIndexArr.length === 2) {
    			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"].push(aliasTempObj);
    		}
    	}

    	dispatch({
    		type: "policyDetail/setPolicyRule",
    		payload: {
    			policyRules: policyRules
    		}
    	});
    	dispatch({
    		type: "policyDetail/setDialogShow",
    		payload: {
    			addRuleTemplate: false,
    			addIFChildRule: false
    		}
    	});
    }

    render() {
    	let { policyDetailStore, globalStore, templateStore, dispatch } = this.props;
    	const { personalMode } = globalStore;
    	const { lang } = personalMode;
    	let { currentGroupIndex } = this.state;
    	let { dialogShow } = policyDetailStore;
    	let { ruleTemplateList } = templateStore;
    	let { addRuleTemplate, addIFChildRule } = dialogShow;

    	let title = addRuleTemplate ? policyDetailLang.addTemplateModal("title1") : policyDetailLang.addTemplateModal("title2"); // "添加内置模板" : "添加子规则"

    	let templateList = ruleTemplateList;
    	let groupChildren = templateList && templateList[currentGroupIndex] && templateList[currentGroupIndex]["templateDataList"] ? templateList[currentGroupIndex]["templateDataList"] : [];
    	groupChildren = groupChildren && groupChildren.length > 0 && groupChildren.filter((child)=>{
    		return child.name !== "common/script";
    	});

    	let columns = [
    		{
    			title: policyDetailLang.addTemplateModal("ruleName"), // 规则名称
    			dataIndex: "displayName",
    			render: (text, records) => {
    				let name = records.displayName;
    				if (lang === "en") name = records.enDisplayName;
    				return name;
    			}
    		},
    		{
    			title: policyDetailLang.addTemplateModal("operation"), // 操作
    			width: 150,
    			dataIndex: "",
    			render: (record, records, index) => {

    				let disabled = false;
    				if (addIFChildRule) {
    					if (records.displayName === "IF") {
    						disabled = true;
    					}
    				}
    				if (addRuleTemplate) {
    					if (records.name === "common/custom" || records.name === "pattern/terminated" || records.name === "pattern/policyResult") {
    						disabled = true;
    					}
    				}
    				return (
    					<div
    						className="oper-list"
    						key={index}
    					>
    						<a
    							className={disabled ? "disabled" : ""}
    							onClick={() => {
    								if (!disabled) {
    									this.selectTemplate(records);
    								}
    							}}
    						>
    							{/* 选择当前模板 */}
    							{policyDetailLang.addTemplateModal("selectTemplate")}
    						</a>
    					</div>
    				);
    			}
    		}
    	];

    	return (
    		<Modal
    			title={title}
    			visible={addRuleTemplate || addIFChildRule}
    			width={750}
    			className="select-template-wrap"
    			maskClosable={false}
    			onOk={() => {
    			}}
    			onCancel={() => {
    				dispatch({
    					type: "policyDetail/setDialogShow",
    					payload: {
    						addRuleTemplate: false,
    						addIFChildRule: false
    					}
    				});
    			}}
    		>
    			<div className="select-template-content">
    				<div className="template-group-list">
    					{
    						ruleTemplateList && ruleTemplateList.map((item, index) => {
    							// console.log(item);
    							return (
    								<li
    									className={this.state.currentGroupIndex === index ? "group-item current" : "group-item"}
    									key={index}
    									onClick={() => {
    										this.setState({
    											currentGroupIndex: index
    										});
    									}}
    								>
    									{lang === "en" ? item["groupName"] : item["groupDisplayName"]}
    								</li>
    							);
    						})
    					}
    				</div>
    				<div className="template-rule-list">
    					<Table
    						columns={columns}
    						dataSource={groupChildren}
    						pagination={false}
    						size="middle"
    						bordered={true}
    					/>
    				</div>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	templateStore: state.template,
	policyDetailStore: state.policyDetail
}))(AddTemplateModal);
