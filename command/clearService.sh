#!/bin/bash
ssh kehong.lin@192.168.6.67<< remotessh
echo "移除atreus-react"
cd /home/<USER>/projects/frontEnd/atreus-react
rm -rf dist
mkdir dist
echo "移除bifrost-react"
cd /home/<USER>/projects/frontEnd/bifrost-react
rm -rf dist
mkdir dist
echo "移除dc-react"
cd /home/<USER>/projects/frontEnd/dc-react
rm -rf dist
mkdir dist
echo "移除holmes-react"
cd /home/<USER>/projects/frontEnd/holmes-react
rm -rf dist
mkdir dist
echo "移除preserver-react"
cd /home/<USER>/projects/frontEnd/preserver-react
rm -rf dist
mkdir dist
echo "移除salaxy-react"
cd /home/<USER>/projects/frontEnd/salaxy-react
rm -rf dist
mkdir dist
echo "移除静态文件目录"
cd /home/<USER>/projects/frontEnd/
rm -rf static
mkdir static

echo "清空服务器前端文件成功。"
exit
remotessh
