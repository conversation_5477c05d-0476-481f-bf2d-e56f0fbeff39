import { PureComponent } from "react";
import { connect } from "dva";
import { Mo<PERSON>, Button } from "antd";
import { policyListLang, commonLang } from "@/constants/lang";
import "./ViewOutputParams.less";

class ViewOutputParams extends PureComponent {
	onCancel = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyRunning/setDialogShow",
			payload: {
				outputParams: false
			}
		});
		dispatch({
			type: "policyRunning/setDialogData",
			payload: {
				outputParamsData: {
					publicSetUuid: "",
					returnFields: [""],
					isRunning: false
				}
			}
		});
	}
	render() {
		const { globalStore, policyRunningStore } = this.props;
		const { allMap } = globalStore;
		const { ruleAndIndexFieldList = [] } = allMap || {};
		const { dialogData, dialogShow } = policyRunningStore || {};
		const { outputParams } = dialogShow;
		const { outputParamsData = {} } = dialogData;
		const { returnFields = []} = outputParamsData || {};
		const returnFieldsName = [];
		ruleAndIndexFieldList.filter(v=>{
			returnFields.forEach(field=>{
				if (v.name === field) {
					returnFieldsName.push(v.dName);
				}
			});
		});
		return (
			<Modal
				title={policyListLang.outputModal("viewTitle")} // 查看策略集出参
				visible={outputParams}
				maskClosable={true}
				onCancel={this.onCancel}
				footer={[
					<Button onClick={this.onCancel}>
						{/* 取消 */}
						{commonLang.base("cancel")}
					</Button>
				]}
			>
				{
					returnFieldsName &&
					returnFieldsName.length > 0
						? <table className="show-scene-table-wrap">
							<tbody>
								<tr>
									<td>
										{/* 出参字段 */}
										{policyListLang.outputModal("outputField")}
									</td>
									<td>
										{returnFieldsName.join("  |  ")}
									</td>
								</tr>
							</tbody>
						</table>
						: <div className="tc">
							<i className="iconfont icon-empty output-icon-no-data"></i>
							<p>
								{/* 暂无出参信息 */}
								{policyListLang.outputModal("noData")}
							</p>
						</div>
				}
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyRunningStore: state.policyRunning
}))(ViewOutputParams);
