import store from "../../app";

const base = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		policySetDebug: {
			cn: "策略集测试",
			en: "Policy Set Debug"
		},
		partnerCode: {
			cn: "合作方",
			en: "Partner Code"
		},
		partnerCodePlaceholder: {
			cn: "请输入合作方",
			en: "Please enter partner name"
		},
		appName: {
			cn: "应用标识",
			en: "App Name"
		},
		eventId: {
			cn: "事件标识",
			en: "Event ID"
		},
		eventType: {
			cn: "事件类型",
			en: "Event Type"
		},
		eventTime: {
			cn: "事件发生时间",
			en: "Event Time"
		},
		eventTimePlaceholder: {
			cn: "请填写事件发生时间",
			en: "Please enter event Time"
		},
		customField: {
			cn: "自定义字段",
			en: "Custom Field"
		},
		selectCustomField: {
			cn: "请选择字段",
			en: "Please select custom field"
		},
		enterFieldValuePlaceholder: {
			cn: "请输入传值",
			en: "Please enter field value"
		},
		addField: {
			cn: "添加",
			en: "Add Field"
		},
		sendTimes: {
			cn: "发送次数",
			en: "Send Times"
		},
		clickDebug: {
			cn: "点击测试",
			en: "Debug"
		}
	};
	return params[field][lang];
};
const result = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		debugResult: {
			cn: "测试结果",
			en: "Debug Result"
		},
		callSuccess: {
			cn: "调用成功",
			en: "The call is successful"
		},
		riskDecisionResult: {
			cn: "风险决策结果",
			en: "Risk decision result"
		},
		riskScore: {
			cn: "风险分数",
			en: "Risk score"
		},
		requestTime: {
			cn: "请求用时",
			en: "Request Time"
		},
		errorCode: {
			cn: "错误代码",
			en: "The error code"
		},
		hitList: {
			cn: "命中列表",
			en: "hit List"
		},
		originalContent: {
			cn: "原始内容",
			en: "The original content"
		},
		resultMsg1: {
			cn: "接口执行共",
			en: "The interface was executed "
		},
		resultMsg2: {
			cn: "次，引擎执行耗时：",
			en: " times, and the engine took "
		},
		resultMsg3: {
			cn: "毫秒",
			en: " milliseconds to execute"
		},
		policy: {
			cn: "策略",
			en: "Policy"
		},
		rule: {
			cn: "规则",
			en: "Rule"
		},
		flowExecutePath: {
			cn: "流程执行顺序",
			en: "Rule flow execute path"
		}
	};
	return params[field][lang];
};

const message = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"customFieldHasEmptyTip": {
			"cn": "存在未填写的自定义字段，请补充完整",
			"en": "There are unfilled custom fields, please complete"
		},
		"deleteCurrentRow": {
			"cn": "删除当前行",
			"en": "Delete current row"
		},
		"removeResultSuccess": {
			"cn": "移除结果成功",
			"en": "Remove result successful"
		}
	};
	return params[field][lang];
};

export default {
	base,
	result,
	message
};
