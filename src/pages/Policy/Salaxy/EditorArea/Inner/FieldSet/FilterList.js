import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Row, Col } from "antd";
import { CommonConstants, PolicyConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import cloneDeep from "lodash.clonedeep";
import "./Less/FilterList.less";

const InputGroup = Input.Group;
const Option = Select.Option;

let addressSetOperator = [
	{
		name: "similarityMatch",
		dName: "匹配",
		enDName: "Match"
	},
	{
		name: "similarityUnMatch",
		dName: "不匹配",
		enDName: "UnMatch"
	}
];

let commonSetOperator = [
	{
		name: "match",
		dName: "匹配",
		enDName: "match"
	},
	{
		name: "unmatch",
		dName: "不匹配",
		enDName: "unmatch"
	}
];

let belongOperator = [
	// {
	// 	name: "belong",
	// 	dName: "属于",
	// 	enDName: "belong",
	// },
	// {
	// 	name: "unbelong",
	// 	dName: "不属于",
	// 	enDName: "unbelong",
	// },
];

class FilterList extends PureComponent {
    state = {
    	fieldList: [
    		{
    			"fieldType": "systemField",
    			"leftPropertyName": "N_DC_VB_WORKADDRCO",
    			"operator": "==",
    			"rightValue": "123",
    			"rightValueType": "input",
    			"type": "INT",
    			"similarity": null
    		}
    	]
    };

    constructor(props) {
    	super(props);
    	this.changeOperationField = this.changeOperationField.bind(this);
    	this.addOperationActions = this.addOperationActions.bind(this);
    	this.deleteOperationActions = this.deleteOperationActions.bind(this);
    }

    changeOperationField(field, type, index, e) {
    	let { indexEditorStore, globalStore, dispatch } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let { allMap } = globalStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
    	let filterList = currentIndexObj && currentIndexObj.filterList ? currentIndexObj.filterList : [];

    	let value = "";
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}

    	filterList[index][field] = value;

    	if (field === "fieldType") {
    		filterList[index]["leftPropertyName"] = null;
    		filterList[index]["operator"] = null;
    		filterList[index]["rightValue"] = null;
    		filterList[index]["rightValueType"] = null;
    	}

    	let thisLineObj = filterList[index];
    	// 如果前置fieldType是系统字段
    	if (thisLineObj && thisLineObj["fieldType"] && thisLineObj["fieldType"] === "systemField") {
    		let ruleFieldItem = allMap && allMap["ruleFieldList"] && allMap["ruleFieldList"].find(item => item.name === value);

    		if (field === "leftPropertyName") {
    			filterList[index]["type"] = ruleFieldItem.type ? ruleFieldItem.type : null;
    			filterList[index]["rightValue"] = null;
    			filterList[index]["operator"] = null;
    			filterList[index]["rightValueType"] = "input";
    		}
    		if (field === "rightValue") {
    			if (thisLineObj["operator"] === "belong" || thisLineObj["operator"] === "unbelong") {
    				filterList[index]["rightValueType"] = "list";
    			}
    		}
    		if (field === "rightValueType") {
    			filterList[index]["rightValue"] = null;
    		}
    	}

    	// 如果前置fieldType是字段集
    	if (filterList[index] && filterList[index]["fieldType"] && filterList[index]["fieldType"] === "fieldSet") {
    		let fieldSetSelectItem = allMap && allMap["fieldSetSelect"] && allMap["fieldSetSelect"].find(item => item.name === value);

    		if (field === "leftPropertyName") {
    			filterList[index]["type"] = fieldSetSelectItem.type ? fieldSetSelectItem.type : null;
    			filterList[index]["rightValue"] = null;
    			filterList[index]["operator"] = null;
    		}
    		if (field === "rightValueType") {
    			filterList[index]["rightValue"] = null;
    		}
    		if (field === "rightValue") {
    			filterList[index]["rightValueType"] = fieldSetSelectItem.type;
    		}
    	}

    	currentIndexObj["hasModify"] = true;

    	dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			editIndexMap: editIndexMap
    		}
    	});
    }

    addOperationActions() {
    	let { indexEditorStore, dispatch } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
    	let filterList = currentIndexObj && currentIndexObj.filterList ? currentIndexObj.filterList : [];
    	let operationTemplate = {
    		"fieldType": "systemField",
    		"leftPropertyName": null,
    		"operator": "==",
    		"rightValue": null,
    		"rightValueType": "input",
    		"type": "STRING",
    		"similarity": null
    	};

    	filterList.push(operationTemplate);
    	currentIndexObj["hasModify"] = true;

    	dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			editIndexMap: editIndexMap
    		}
    	});
    }

    deleteOperationActions(index) {
    	let { indexEditorStore, dispatch } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
    	let filterList = currentIndexObj && currentIndexObj.filterList ? currentIndexObj.filterList : [];

    	filterList.splice(index, 1);
    	currentIndexObj["hasModify"] = true;

    	dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			editIndexMap: editIndexMap
    		}
    	});
    }

    render() {
    	let { indexEditorStore, globalStore, disabled } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let { allMap, personalMode } = globalStore;
    	let lang = personalMode.lang === "cn" ? "cn" : "en";

    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
    	let filterList = currentIndexObj && currentIndexObj.filterList ? currentIndexObj.filterList : [];

    	console.log(currentIndexObj);

    	return (
    		<Fragment>
    			<Row gutter={CommonConstants.gutterSpan}>
    				<Col span={4} className="basic-info-title">
    					{/* lang:过滤条件 */}
    					{indexListLang.ruleAttr("filterConditions")}
    				</Col>
    				<Col
    					span={20}
    					className="add-new-filter"
    				>
    					<span
    						onClick={this.addOperationActions.bind(this)}
    					>
    						<Icon type="plus-square-o" />
    						{/* lang:新增过滤 */}
    						{indexListLang.ruleAttr("addFilter")}
    					</span>
    				</Col>
    			</Row>
    			<div className="mb20">
    				{
    					filterList && filterList.map((item, index) => {
    						let conditionArr = PolicyConstants.conditionOperator[item.type ? item.type : "STRING"] || [];
    						let newBelongOperator = cloneDeep(belongOperator);
    						conditionArr.map((cItem, cIndex) => {
    							newBelongOperator.push(cItem);
    						});

    						return (
    							<Row
    								gutter={CommonConstants.gutterSpan}
    								className="rule-operation-item"
    								key={index}
    							>
    								<Col span={4} className="basic-info-title"></Col>
    								<Col span={3}>
    									<Select
    										value={item["fieldType"] || undefined}
    										placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
    										onChange={this.changeOperationField.bind(this, "fieldType", "select", index)}
    									>
    										<Option value="systemField">系统字段</Option>
    										<Option value="fieldSet">字段集</Option>
    									</Select>
    								</Col>

    								{/* 如果选择的字段类型是系统字段 */}
    								{
    									item["fieldType"] &&
                                        item["fieldType"] === "systemField" &&
                                        <Col span={4}>
                                        	<Select
                                        		value={item["leftPropertyName"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		onChange={this.changeOperationField.bind(this, "leftPropertyName", "select", index)}
                                        		showSearch
                                        		optionFilterProp="children"
                                        		disabled={disabled}
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["ruleFieldList"] &&
                                                    allMap["ruleFieldList"].map((item, index) => {
                                                    	return (
                                                    		<Option value={item.name} key={index} title={item.dName}>
                                                    			{
                                                    				item.dName
                                                    			}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
    								}
    								{/* 如果选择的字段类型是字段集 */}
    								{
    									item["fieldType"] &&
                                        item["fieldType"] === "fieldSet" &&
                                        <Col span={4}>
                                        	<Select
                                        		value={item["leftPropertyName"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		onChange={this.changeOperationField.bind(this, "leftPropertyName", "select", index)}
                                        		showSearch
                                        		optionFilterProp="children"
                                        		disabled={disabled}
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["fieldSetSelect"] &&
                                                    allMap["fieldSetSelect"].map((item, index) => {
                                                    	return (
                                                    		<Option
                                                    			value={item.name}
                                                    			key={index}
                                                    			title={item.dName}
                                                    		>
                                                    			{item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
    								}
    								{
    									item["fieldType"] === "systemField" &&
                                        item["leftPropertyName"] &&
                                        <Col span={3} className="basic-info-text">
                                        	<Select
                                        		value={item["operator"] || undefined}
                                        		disabled={disabled}
                                        		onChange={this.changeOperationField.bind(this, "operator", "select", index)}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        	>
                                        		{
                                        			newBelongOperator.map((item, index) => {
                                        				return (
                                        					<Option
                                        						value={item.name}
                                        						key={index}
                                        						title={lang === "cn" ? item.dName : item.enDName}
                                        					>
                                        						{lang === "cn" ? item.dName : item.enDName}
                                        					</Option>
                                        				);
                                        			})
                                        		}
                                        	</Select>
                                        </Col>
    								}
    								{/* 这里判断operator */}
    								{
    									item["fieldType"] === "fieldSet" &&
                                        item["type"] === "fieldSet" &&
                                        <Col span={3} className="basic-info-text">
                                        	<Select
                                        		value={item["operator"] || undefined}
                                        		disabled={disabled}
                                        		onChange={this.changeOperationField.bind(this, "operator", "select", index)}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		{
                                        			commonSetOperator.map((item, index) => {
                                        				return (
                                        					<Option
                                        						value={item.name}
                                        						key={index}
                                        						title={lang === "cn" ? item.dName : item.enDName}
                                        					>
                                        						{lang === "cn" ? item.dName : item.enDName}
                                        					</Option>
                                        				);
                                        			})
                                        		}
                                        	</Select>
                                        </Col>
    								}
    								{
    									item["fieldType"] === "fieldSet" &&
                                        item["type"] === "addressSet" &&
                                        <Col span={3} className="basic-info-text">
                                        	<Select
                                        		value={item["operator"] || undefined}
                                        		disabled={disabled}
                                        		onChange={this.changeOperationField.bind(this, "operator", "select", index)}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		{
                                        			addressSetOperator.map((item, index) => {
                                        				return (
                                        					<Option
                                        						value={item.name}
                                        						key={index}
                                        						title={lang === "cn" ? item.dName : item.enDName}
                                        					>
                                        						{lang === "cn" ? item.dName : item.enDName}
                                        					</Option>
                                        				);
                                        			})
                                        		}
                                        	</Select>
                                        </Col>
    								}
    								{/* 如果选择的是字段集，并且字段集类型是fieldSet */}
    								{
    									item["fieldType"] &&
                                        item["fieldType"] === "fieldSet" &&
                                        item["type"] === "fieldSet" &&
                                        <Col span={4} className="basic-info-text">
                                        	<Select
                                        		value={item["rightValue"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
                                        		showSearch
                                        		optionFilterProp="children"
                                        		disabled={disabled}
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["fieldSetSelect"] &&
                                                    allMap["fieldSetSelect"].map((item, index) => {
                                                    	return (
                                                    		<Option value={item.name} key={index} title={item.dName}>
                                                    			{indexListLang.ruleAttr("current") + item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
    								}
    								{
    									item["fieldType"] &&
                                        item["fieldType"] === "fieldSet" &&
                                        item["type"] === "addressSet" &&
                                        <Col span={3} className="basic-info-text">
                                        	<Input
                                        		className="similarity-warp"
                                        		addonBefore="相似度"
                                        		addonAfter="%"
                                        		value={item.similarity}
                                        		onChange={this.changeOperationField.bind(this, "similarity", "input", index)}
                                        	/>
                                        </Col>
    								}
    								{
    									item["fieldType"] &&
                                        item["fieldType"] === "fieldSet" &&
                                        item["type"] === "addressSet" &&
                                        <Col span={4} className="basic-info-text">
                                        	<Select
                                        		value={item["rightValue"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
                                        		showSearch
                                        		optionFilterProp="children"
                                        		disabled={disabled}
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["fieldSetSelect"] &&
                                                    allMap["fieldSetSelect"].map((item, index) => {
                                                    	return (
                                                    		<Option
                                                    			value={item.name}
                                                    			key={index}
                                                    			title={item.dName}
                                                    		>
                                                    			{indexListLang.ruleAttr("current") + item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
    								}
    								{
    									item["fieldType"] &&
                                        item["fieldType"] === "systemField" &&
                                        item["operator"] !== "belong" &&
                                        item["operator"] !== "unbelong" &&
                                        <Col span={7} className="basic-info-text">
                                        	{
                                        		item.rightValueType === "input" &&
                                                item.type !== "ENUM" &&
                                                <InputGroup compact>
                                                	<Select
                                                		value={item["rightValueType"] || undefined}
                                                		style={{ width: "30%" }}
                                                		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                                		onChange={this.changeOperationField.bind(this, "rightValueType", "select", index)}
                                                		disabled={disabled}
                                                	>
                                                		{/* lang:常量变量*/}
                                                		<Option value="input">
                                                			{indexListLang.ruleAttr("constant")}
                                                		</Option>
                                                		<Option
                                                			value="context">
                                                			{indexListLang.ruleAttr("variable")}
                                                		</Option>
                                                	</Select>
                                                	<Input
                                                		style={{ width: "70%" }}
                                                		value={item.rightValue || undefined}
                                                		placeholder={indexListLang.ruleAttr("constantInputPlaceholder")} // lang:请填写常量内容
                                                		onChange={this.changeOperationField.bind(this, "rightValue", "input", index)}
                                                		disabled={disabled}
                                                	/>
                                                </InputGroup>
                                        	}
                                        	{
                                        		item.rightValueType === "context" &&
                                                item.type !== "ENUM" &&
                                                <InputGroup compact>
                                                	<Select
                                                		value={item["rightValueType"] || undefined}
                                                		style={{ width: "30%" }}
                                                		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                                		onChange={this.changeOperationField.bind(this, "rightValueType", "select", index)}
                                                		disabled={disabled}
                                                	>
                                                		{/* lang:常量变量*/}
                                                		<Option value="input">
                                                			{indexListLang.ruleAttr("constant")}
                                                		</Option>
                                                		<Option
                                                			value="context">
                                                			{indexListLang.ruleAttr("variable")}
                                                		</Option>
                                                	</Select>
                                                	<Select
                                                		style={{ width: "70%" }}
                                                		value={item.rightValue || undefined}
                                                		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                                		onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
                                                		showSearch
                                                		optionFilterProp="children"
                                                		disabled={disabled}
                                                		dropdownMatchSelectWidth={false}
                                                	>
                                                		{
                                                			allMap &&
                                                            allMap["ruleFieldList"] &&
                                                            allMap["ruleFieldList"].filter(fItem => {
                                                            	return (
                                                            		fItem.type === item.type ||
																	(["DOUBLE", "INT"].indexOf(fItem.type) > -1 && ["DOUBLE", "INT"].indexOf(item.type) > -1)
                                                            	);
                                                            }).map((item, index) => {
                                                            	return (
                                                            		<Option
                                                            			value={item.name}
                                                            			key={index}
                                                            			title={item.dName}
                                                            		>
                                                            			{/* lang:当前*/}
                                                            			{indexListLang.ruleAttr("current") + item.dName}
                                                            		</Option>
                                                            	);
                                                            })
                                                		}
                                                	</Select>
                                                </InputGroup>
                                        	}
                                        	{
                                        		item.type === "ENUM" &&
                                                <Select
                                                	value={item.rightValue || undefined}
                                                	placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                                	onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
                                                	showSearch
                                                	optionFilterProp="children"
                                                	disabled={disabled}
                                                	dropdownMatchSelectWidth={false}
                                                >
                                                	{
                                                		allMap &&
                                                        allMap["fieldEnumObj"] &&
                                                        allMap["fieldEnumObj"][item.leftPropertyName] &&
                                                        allMap["fieldEnumObj"][item.leftPropertyName].map((item, index) => {
                                                        	return (
                                                        		<Option
                                                        			value={item.value}
                                                        			key={index}
                                                        			title={item.value}
                                                        		>
                                                        			{item.description}
                                                        		</Option>
                                                        	);
                                                        })
                                                	}
                                                </Select>
                                        	}
                                        </Col>
    								}
    								{
    									item["fieldType"] &&
                                        item["fieldType"] === "systemField" &&
                                        (item["operator"] === "belong" || item["operator"] === "unbelong") &&
                                        <Col span={4}>
                                        	<Select
                                        		value={item["rightValue"] || undefined}
                                        		placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
                                        		onChange={this.changeOperationField.bind(this, "rightValue", "select", index)}
                                        		showSearch
                                        		optionFilterProp="children"
                                        		disabled={disabled}
                                        		dropdownMatchSelectWidth={false}
                                        	>
                                        		{
                                        			allMap &&
                                                    allMap["customList"] &&
                                                    allMap["customList"].map((item, index) => {
                                                    	return (
                                                    		<Option
                                                    			value={item.name}
                                                    			key={index}
                                                    			title={item.dName}
                                                    		>
                                                    			{item.dName}
                                                    		</Option>
                                                    	);
                                                    })
                                        		}
                                        	</Select>
                                        </Col>
    								}
    								{
    									!disabled &&
                                        <Col span={2} className="basic-info-oper">
                                        	<Tooltip
                                        		title={indexListLang.ruleAttr("deleteCurrentRow")} // lang:删除当前行
                                        		placement="right"
                                        	>
                                        		<Icon
                                        			className="delete"
                                        			type="delete"
                                        			onClick={this.deleteOperationActions.bind(this, index)}
                                        		/>
                                        	</Tooltip>
                                        </Col>
    								}
    							</Row>
    						);
    					})
    				}
    			</div>
    		</Fragment>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(FilterList);

