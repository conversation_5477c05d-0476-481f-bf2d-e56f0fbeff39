import React from "react";
import { connect } from "dva";
import { Radio, Row, Col, Popover, Icon } from "antd";
import OneCondition from "../../OneCondition";
import RuleConditonTemplate from "./RuleConditonTemplate";
import FormulaScriptEditor from "@/components/PolicyDetail/FormulaScriptEditor";
import { CommonConstants } from "@/constants";
import { policyDetailLang } from "@/constants/lang";

const RadioGroup = Radio.Group;

class RuleConditon extends React.PureComponent {
	constructor(props) {
		super(props);
		this.changeLogicOperator = this.changeLogicOperator.bind(this);
		this.addSingleCondition = this.addSingleCondition.bind(this);
		this.addGroupCondition = this.addGroupCondition.bind(this);
		this.addRuleTemplateHandle = this.addRuleTemplateHandle.bind(this);
	}

	modifyTemplate = (field, e) => {
		let { policyDetailStore, dispatch, ruleIndexArr } = this.props;
		let { policyRules, ruleErrMsg } = policyDetailStore;
		if (ruleErrMsg) {
			dispatch({
				type: "policyDetail/setAttrValue",
				payload: {
					ruleErrMsg: ""
				}
			});
		}
		let value = e;
		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]][field] = value;
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]][field] = value;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}
		console.log(field, e);
		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}
	changeLogicOperator(e) {
		let { policyDetailStore, dispatch, ruleIndexArr } = this.props;
		let { policyRules } = policyDetailStore;
		let value = e.target.value;

		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["conditions"]["logicOperator"] = value;
			policyRules[ruleIndexArr[0]]["conditions"]["type"] = "context";
			policyRules[ruleIndexArr[0]]["hasModify"] = true;
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["logicOperator"] = value;
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["type"] = "context";
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["hasModify"] = true;
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	addRuleTemplateHandle() {
		let { dispatch, ruleIndexArr } = this.props;
		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				addRuleTemplate: true
			}
		});
		dispatch({
			type: "policyDetail/setAttrValue",
			payload: {
				ruleIndexArr: ruleIndexArr
			}
		});
	}

	addSingleCondition() {
		let { policyDetailStore, dispatch, ruleIndexArr } = this.props;
		let { policyRules } = policyDetailStore;

		let singleConditionTemp = {
			property: null,
			operator: "==",
			value: null,
			propertyDataType: null,
			type: "context",
			rightValueType: "context"
		};
		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["conditions"]["children"].push(singleConditionTemp);
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"].push(singleConditionTemp);
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	addGroupCondition() {
		let { policyDetailStore, dispatch, ruleIndexArr } = this.props;
		let { policyRules } = policyDetailStore;

		let groupConditionTemp = {
			"logicOperator": "&&",
			"type": "context",
			"children": [
				{
					property: null,
					operator: "==",
					value: null,
					propertyDataType: null,
					type: "context",
					rightValueType: "context"
				}
			]
		};
		if (ruleIndexArr.length === 1) {
			policyRules[ruleIndexArr[0]]["conditions"]["children"].push(groupConditionTemp);
		} else if (ruleIndexArr.length === 2) {
			policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"].push(groupConditionTemp);
		}

		dispatch({
			type: "policyDetail/setPolicyRule",
			payload: {
				policyRules: policyRules
			}
		});
	}

	render() {
		let { policyDetailStore, ruleIndexArr } = this.props;
		let { policyRules, ruleErrMsg, policyDetail } = policyDetailStore;
		let currentRule = null;
		if (ruleIndexArr.length === 1) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]] : null;
		} else if (ruleIndexArr.length === 2) {
			currentRule = policyRules && policyRules.length ? policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]] : null;
		}
		let currentCondition = currentRule && currentRule.conditions ? currentRule.conditions : null;
		const { appName } = policyDetail || {};
		return (
			<div>
				{
					currentRule && currentRule.template && currentRule.template === "common/custom" &&
					<Row gutter={CommonConstants.gutterSpan} className="mb10">
						<Col span={0} className="basic-info-title"></Col>
						<Col span={4} className="basic-info-title">
							{/* 执行条件： */}
							{policyDetailLang.ruleCondition("performsConditional")}
						</Col>
						<Col span={18}>
							<RadioGroup
								onChange={this.changeLogicOperator.bind(this)}
								value={currentCondition && currentCondition["logicOperator"] ? currentCondition["logicOperator"] : null}
							>
								<Radio value="&&">
									{/* 满足以下所有条件 */}
									{policyDetailLang.ruleCondition("mztj1")}
								</Radio>
								<Radio value="||">
									{/* 满足以下任意条件 */}
									{policyDetailLang.ruleCondition("mztj2")}
								</Radio>
								{
									currentRule["ifClause"] && currentRule["ifClause"] === "IF" &&

									<Radio value="!||">
										{/* 以下条件均不满足 */}
										{policyDetailLang.ruleCondition("mztj3")}
									</Radio>

								}
								{
									currentRule["ifClause"] && currentRule["ifClause"] === "IF" &&
									<Radio value="!&&">
										{/* 以下条件至少一条不满足 */}
										{policyDetailLang.ruleCondition("mztj4")}
									</Radio>
								}
							</RadioGroup>
						</Col>
						<Col span={2}></Col>
					</Row>
				}
				{
					currentRule && currentRule.template && currentRule.template !== "common/script" &&
					<Row gutter={CommonConstants.gutterSpan}>
						<div className="custom-condition">
							{
								currentCondition && currentCondition.type !== "alias" && currentCondition.children &&
								currentCondition.children.map((item, index) => {
									if (item.children && item.children.length) {
										return (
											<div className="custom-condition-item group-condition has-line" key={index}>
												{
													item.children.map((groupItem, groupIndex) => {
														return (
															<OneCondition
																conditionData={item}
																conditionSingleData={groupItem}
																conditionType="group"
																ruleIndexArr={ruleIndexArr}
																conditionArr={[index, groupIndex]}
																key={groupIndex}
															/>
														);
													})
												}
											</div>
										);
									} else if (item.type !== "alias") {
										return (
											<div className="custom-condition-item one-condition has-line" key={index}>
												<OneCondition
													conditionData={null}
													conditionSingleData={item}
													ruleIndexArr={ruleIndexArr}
													conditionType="single"
													conditionArr={[index]}
												/>
											</div>
										);
									} else if (item.type === "alias") {
										// console.log("===================================>>>>>>>>>>>>>>>RuleConditonTemplate");
										// console.log(item);
										return (
											<RuleConditonTemplate
												property={item.property}
												ruleIndexArr={ruleIndexArr}
												conditionIndex={index}
												key={index}
											/>
										);
									}
								})
							}
							{
								currentRule && currentRule.template === "common/custom" &&
								<div className="custom-condition-item add-condition">
									<Col span={4} className="basic-info-title"></Col>
									<Col span={18} className="add-condition-handle">
										<a onClick={this.addRuleTemplateHandle.bind(this)}>
											{/* 添加规则模板 */}
											{policyDetailLang.ruleCondition("add1")}
										</a>
										<a onClick={this.addSingleCondition.bind(this)}>
											{/* 添加单条条件 */}
											{policyDetailLang.ruleCondition("add2")}
										</a>
										<a onClick={this.addGroupCondition.bind(this)}>
											{/* 添加条件组 */}
											{policyDetailLang.ruleCondition("add3")}
										</a>
									</Col>
								</div>
							}
						</div>
					</Row>
				}
				{
					currentRule && currentRule.template && currentRule.template === "common/script" &&
					<Row gutter={CommonConstants.gutterSpan} className="mb10">
						<Col span={4} className="basic-info-title">
							{/* 脚本规则 */}
							{policyDetailLang.ruleConfig("scriptRule")}
						</Col>
						<Col span={16}>
							<FormulaScriptEditor
								appName={appName}
								ruleErrMsg={ruleErrMsg}
								value={currentRule.script}
								onChange={this.modifyTemplate.bind(this, "script")}
							/>
						</Col>
						<Col span={1}>
							<Popover
								// popupClassName="rule-param-pop-tip"
								placement="right"
								title={policyDetailLang.ruleAttr("scriptRule")} // 脚本规则
								content={
									<pre>
										{ policyDetailLang.ruleAttr("scriptDemo") }
									</pre>
								} // 脚本规则介绍
							>
								<Icon
									type="question-circle-o"
									className="param-tip-icon"
								/>
							</Popover>
						</Col>
					</Row>
				}
			</div>
		);
	}
}

export default connect(state => ({
	policyDetailStore: state.policyDetail
}))(RuleConditon);
