import store from "../../app";
import moment from "moment";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		title: {
			"cn": "回测任务列表",
			"en": "Replay Task List"
		}
	};
	return params[field][lang];
};
const operatorModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		addReplay: {
			"cn": "添加回测",
			"en": "Add Replay"
		},
		modifyReplay: {
			"cn": "修改回测",
			"en": "Modify Replay"
		},
		addReplayTaskSetting: {
			"cn": "新增回测任务配置",
			"en": "Add Replay Task Setting"
		},
		modifyReplayTaskSetting: {
			"cn": "修改回测任务配置",
			"en": "Modify Replay Task Setting"
		},
		runningTaskModifyTip: {
			"cn": "运行中的任务无法修改",
			"en": "The running task cannot be modified"
		},
		replayName: {
			"cn": "回测任务名称",
			"en": "Name"
		},
		replayNamePlaceholder: {
			"cn": "请输入回测任务名称",
			"en": "Please enter replay name"
		},
		replayDataType: {
			"cn": "回测数据类型",
			"en": "Back test data type"
		},
		replayDataTypePlaceholder: {
			"cn": "请选择回测数据类型",
			"en": "Please select back test data type"
		},
		policySet: {
			"cn": "回测数据策略集",
			"en": "Policy set"
		},
		policySetPlaceholder: {
			"cn": "请选择回测策略集",
			"en": "Please select replay policy set"
		},
		timeInterval: {
			"cn": "回测数据区间",
			"en": "The time interval"
		},
		timeIntervalPlaceholder: {
			"cn": "请选择回测数据区间",
			"en": "Please select time interval"
		},
		timeMaxSelect: {
			"cn": "日期区间最长选择3个月，最大支持500W条数据。",
			"en": "The maximum date interval is 3 months, and the maximum support is 500W pieces of data."
		},
		historyReplaySet: {
			"cn": "回测目标策略",
			"en": "Replay target police"
		},
		historyReplaySetPlaceHolder: {
			"cn": "请选择回测目标策略",
			"en": "Please select the backtesting target policy"
		},
		taskExecuteType: {
			"cn": "任务执行方式",
			"en": "Task execute type"
		},
		rightNow: {
			"cn": "立即执行",
			"en": "Right now"
		},
		timing: {
			"cn": "定时执行",
			"en": "Timing"
		},
		taskExecuteTime: {
			"cn": "任务执行时间",
			"en": "Task execute time"
		},
		taskExecuteTimePleaseholder: {
			"cn": "请选择任务执行时间",
			"en": "Please select task execute time"
		},
		replayTargetPolice: {
			"cn": "请选择回测目标策略",
			"en": "Please select replay target police"
		},
		addSuccessTip: {
			"cn": "新增回测任务成功",
			"en": "Add replay task successfully"
		},
		modifySuccessTip: {
			"cn": "修改回测任务成功",
			"en": "Modify replay task successfully"
		},
		selectField: {
			"cn": "请选择",
			"en": "Select Field"
		},
		selectPlaceholder: {
			"cn": "请选择",
			"en": "Please Select"
		},
		inputPlaceholder: {
			"cn": "请输入",
			"en": "Please Input"
		},
		addOne: {
			"cn": "添加一项",
			"en": "Add One"
		},
		removeCurrentField: {
			"cn": "移除当前行",
			"en": "Remove Current Field"
		},
		policyFilter: {
			"cn": "回测数据过滤",
			"en": "Policy Filter"
		},
		selectPoliceFilter: {
			"cn": "请选择回测数据过滤",
			"en": "Please select replay data filter"
		},
		notSupportRepayTask: {
			"cn": "暂不支持回测",
			"en": "Backtesting is not supported temporarily"
		},
		replayProcessView: {
			"cn": "查看回测进度",
			"en": "View backtesting progress"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		allStatus: {
			"cn": "全部状态",
			"en": "All Status"
		},
		taskStatusPleaseholder: {
			"cn": "请选择任务状态",
			"en": "Please select task status"
		},
		allPolicySet: {
			"cn": "全部策略集",
			"en": "All PolicySet"
		},
		clear: {
			"cn": "清空",
			"en": "Clear"
		},
		search: {
			"cn": "搜索",
			"en": "Search"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		taskNo: {
			"cn": "报告编号",
			"en": "Task No"
		},
		taskName: {
			"cn": "任务名称",
			"en": "Task Name"
		},
		policySetName: {
			"cn": "策略集名称",
			"en": "PolicySet Name"
		},
		totalCount: {
			"cn": "总数",
			"en": "Total"
		},
		taskProgress: {
			"cn": "进度",
			"en": "Progress"
		},
		taskStatus: {
			"cn": "任务状态",
			"en": "Status"
		},
		taskStartTime: {
			"cn": "执行开始时间",
			"en": "Task Start Time"
		},
		taskEndTime: {
			"cn": "执行结束时间",
			"en": "Task End Time"
		},
		startTime: {
			"cn": "回测数据开始时间",
			"en": "Start Time"
		},
		endTime: {
			"cn": "回测数据结束时间",
			"en": "End Time"
		},
		operation: {
			"cn": "操作",
			"en": "Operation"
		},
		viewReport: {
			"cn": "查看报告",
			"en": "View Report"
		},
		noReport: {
			"cn": "查看报告",
			"en": "No Report"
		},
		exportReport: {
			"cn": "导出报告",
			"en": "Export Report"
		},
		terminateTask: {
			"cn": "终止任务",
			"en": "Terminate Task"
		},
		replayCount: {
			"cn": "回测总数",
			"en": "Replay count"
		},
		hasCompleted: {
			"cn": "已完成",
			"en": "Has completed"
		},
		terminateTaskTip: {
			"cn": "终止回测任务提醒",
			"en": "Terminate task tip"
		}
	};
	return params[field][lang];
};

const terminateTaskTipText = (taskName) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;

	let cn = "确定要终止回测任务" + taskName + "吗？";
	let en = "Are you sure you want to terminate the replay task " + taskName + "?";
	return personalMode && personalMode.lang === "cn" ? cn : en;
};

const date = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		dataError: {
			"cn": "开始日期必须小于或者等于结束日期",
			"en": "The start date must be less than or equal to the end date"
		},
		timeRangeError: {
			"cn": "时间范围不能超过90天",
			"en": "The time range cannot exceed 90 days"
		},
		dateMap: {
			"cn": {
				"今天": [moment(`${moment().format("YYYY-MM-DD")} 00:00:00`), moment()],
				"昨天": [moment(`${moment().subtract(1, "days").format("YYYY-MM-DD")} 00:00:00`), moment(`${moment().format("YYYY-MM-DD")} 00:00:00`)],
				"最近七天": [moment(`${moment().subtract(6, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"最近三十天": [moment(`${moment().subtract(29, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"本月": [moment(`${moment().startOf("month").format("YYYY-MM-DD")} 00:00:00`), moment()]
			},
			"en": {
				"Today": [moment(`${moment().format("YYYY-MM-DD")} 00:00:00`), moment()],
				"Yesterday": [moment(`${moment().subtract(1, "days").format("YYYY-MM-DD")} 00:00:00`), moment(`${moment().format("YYYY-MM-DD")} 00:00:00`)],
				"Recent 7 days": [moment(`${moment().subtract(6, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"Recent 30 days": [moment(`${moment().subtract(29, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"This month": [moment(`${moment().startOf("month").format("YYYY-MM-DD")} 00:00:00`), moment()]
			}
		}
	};
	return params[field][lang];
};

const report = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		replayTaskReport: {
			"cn": "回测任务报告",
			"en": "Replay Task Report"
		},
		taskNo: {
			"cn": "报告编号",
			"en": "Task No"
		},
		taskTime: {
			"cn": "任务时间",
			"en": "Task Time"
		},
		ruleHitDetail: {
			"cn": "规则命中明细",
			"en": "Rule Hit Detail"
		},
		ruleName: {
			"cn": "规则名称",
			"en": "Rule Name"
		},
		runningCount: {
			"cn": "运行总数",
			"en": "Running Count"
		},
		hitCount: {
			"cn": "命中数量",
			"en": "Hit Count"
		},
		rulePercent: {
			"cn": "命中比例",
			"en": "Hit Percent"
		},
		ruleHitReport: {
			"cn": "规则命中报告",
			"en": "Rule Hit Report"
		},
		hitCount2: {
			"cn": "命中数",
			"en": "Hit Count"
		},
		totalCount: {
			"cn": "总数",
			"en": "Total Count"
		},
		rule: {
			"cn": "规则",
			"en": "Rule"
		}
	};
	return params[field][lang];
};

export default {
	common,
	operatorModal,
	searchParams,
	table,
	terminateTaskTipText,
	date,
	report
};
