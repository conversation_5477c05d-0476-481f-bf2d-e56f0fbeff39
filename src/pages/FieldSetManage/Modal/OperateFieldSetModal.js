/**
 * @Description:
 * <AUTHOR>
 * @date 2018/12/28
 */
import { PureComponent } from "react";
import { connect } from "dva";
import { Row, Col, Select, Modal, Input, message, Table, Popconfirm } from "antd";
import { fieldSetAPI } from "@/services";
import { fieldSetManagementLang } from "@/constants/lang";

const Option = Select.Option;

class OperateFieldSetModal extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    }

    handleCancel = () => {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "fieldSet/setAttrValue",
    		payload: {
    			dialogShow: {
    				addFieldSet: false,
    				modifyFieldSet: false
    			},
    			dialogData: {
    				operateFieldSetData: {
    					id: null,
    					name: null,
    					displayName: null,
    					type: null,
    					fields: []
    				}
    			}
    		}
    	});
    };

    changeFieldValue(field, type, e) {
    	let { fieldSetStore, dispatch } = this.props;
    	let { dialogData } = fieldSetStore;
    	let { operateFieldSetData } = dialogData;
    	let value;

    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "select") {
    		value = e;
    	}

    	operateFieldSetData[field] = value;
    	dispatch({
    		type: "fieldSet/setAttrValue",
    		payload: {
    			dialogData: {
    				operateFieldSetData
    			}
    		}
    	});
    }

    /**
	 * 添加或修改字段集
	 */
    operateFieldSet() {
    	let { fieldSetStore, dispatch, initSearch } = this.props;
    	let { dialogData, dialogShow } = fieldSetStore;
    	let { operateFieldSetData } = dialogData;

    	if (!operateFieldSetData.name) {
    		// lang:请输入字段集标识
    		message.warn(fieldSetManagementLang.operateFieldSetModal("fieldSetIdPlaceholder"));
    		return;
    	}
    	if (operateFieldSetData.name.length > 32) {
    		// lang:字段集长度不能超过32个字符
    		message.warn(fieldSetManagementLang.operateFieldSetModal("fieldSetIdLength"));
    		return;
    	}

    	if (!operateFieldSetData.displayName) {
    		// lang:请输入字段集显示名
    		message.warn(fieldSetManagementLang.operateFieldSetModal("fieldSetNamePlaceholder"));
    		return;
    	}
    	if (operateFieldSetData.displayName.length > 24) {
    		// lang:字段集长度不能超过32个字符
    		message.warn(fieldSetManagementLang.operateFieldSetModal("fieldSetNameLength"));
    		return;
    	}

    	if (!operateFieldSetData.type) {
    		// lang:请选择字段集类型
    		message.warn(fieldSetManagementLang.operateFieldSetModal("fieldSetTypePlaceholder"));
    		return;
    	}
    	if (!operateFieldSetData.fields || operateFieldSetData.fields.length === 0) {
    		// lang:请选择字段
    		message.warn(fieldSetManagementLang.operateFieldSetModal("fieldsPlaceholder"));
    		return;
    	}

    	let params = {
    		displayName: operateFieldSetData.displayName,
    		type: operateFieldSetData.type,
    		fields: JSON.stringify(operateFieldSetData.fields)
    	};
    	if (dialogShow.addFieldSet) {
    		params = {
    			name: operateFieldSetData.name,
    			...params
    		};
    		fieldSetAPI.addFieldSet(params).then(res => {
    			if (res.success) {
    				message.success(fieldSetManagementLang.operateFieldSetModal("addSuccess")); // lang:新增成功
    				this.handleCancel();
    				initSearch.call(this);
    				// 刷新allMap
    				dispatch({
    					type: "global/getAllMap"
    				});
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	} else {
    		params = {
    			id: operateFieldSetData.id,
    			...params
    		};
    		fieldSetAPI.modifyFieldSet(params).then(res => {
    			if (res.success) {
    				message.success(fieldSetManagementLang.operateFieldSetModal("modifySuccess")); // lang:修改成功
    				this.handleCancel();
    				dispatch({
    					type: "fieldSet/getFieldSetList"
    				});
    			} else {
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	}
    }

    deleteField(name) {
    	const { fieldSetStore, dispatch } = this.props;
    	const { dialogData } = fieldSetStore;
    	const { operateFieldSetData } = dialogData;
    	const index = operateFieldSetData.fields.indexOf(name);
    	if (index > -1) {
    		operateFieldSetData.fields.splice(index, 1);
    	}
    	dispatch({
    		type: "fieldSet/setAttrValue",
    		payload: {
    			dialogData: {
    				operateFieldSetData
    			}
    		}
    	});
    }

    render() {
    	const { fieldSetStore, globalStore } = this.props;
    	const { allMap = {}, personalMode } = globalStore;
    	const lang = personalMode.lang === "cn" ? "cn" : "en";
    	const { ruleFieldList, fieldSetTypeSelect } = allMap;
    	const { dialogData, dialogShow } = fieldSetStore;
    	const { operateFieldSetData } = dialogData;
    	const cols = [
    		{
    			title: fieldSetManagementLang.table("id"), // lang:"标识",
    			dataIndex: "name",
    			key: "name"
    		},
    		{
    			title: fieldSetManagementLang.table("name"), // lang:"显示名",
    			dataIndex: "dName",
    			key: "dName"
    		},
    		{
    			title: fieldSetManagementLang.table("operation"), // lang: "操作",
    			key: "operation",
    			width: 80,
    			render: (text, record, index) => {
    				return (
    					<div className="operation-box" key={index}>
    						<Popconfirm title={fieldSetManagementLang.operateFieldSetModal("deleteFieldConfirm")}// lang:"确认要删除此字段吗？"
    							onConfirm={() => {
    								this.deleteField(record.name);
    							}}
    							okText={fieldSetManagementLang.table("delete")}
    							cancelText={fieldSetManagementLang.table("cancel")}
    						>
    							<a>
    								{/* lang:删除 */}
    								{fieldSetManagementLang.table("delete")}
    							</a>
    						</Popconfirm>
    					</div>
    				);
    			}
    		}
    	];
    	const dataSource = [];
    	operateFieldSetData.fields &&
            operateFieldSetData.fields.forEach(ele => {
            	let obj = ruleFieldList && ruleFieldList.length > 0 && ruleFieldList.find(item => item.name === ele);
            	if (obj) {
            		dataSource.push(obj);
            	}
            });

    	return (
    		<Modal
    			width={600}											// lang:新增字段集  : 修改字段集
    			title={dialogShow.addFieldSet ? fieldSetManagementLang.operateFieldSetModal("addFieldSet") : fieldSetManagementLang.operateFieldSetModal("modifyFieldSet")}
    			visible={dialogShow.addFieldSet || dialogShow.modifyFieldSet}
    			maskClosable={false}
    			onCancel={this.handleCancel.bind(this)}
    			onOk={this.operateFieldSet.bind(this)}
    		>
    			<div className="basic-form">
    				<Row gutter={10}>
    					<Col span={5} className="basic-info-title must-input">
    						{/* lang: 字段集标识 */}
    						{fieldSetManagementLang.operateFieldSetModal("fieldSetId")}:
    					</Col>
    					<Col span={18}>
    						<Input
    							value={operateFieldSetData.name || undefined}
    							disabled={dialogShow.modifyFieldSet}
    							onChange={this.changeFieldValue.bind(this, "name", "input")}
    							placeholder={fieldSetManagementLang.operateFieldSetModal("fieldSetIdPlaceholder")} // lang:请输入字段集标识
    						/>
    					</Col>
    				</Row>
    				<Row gutter={10}>
    					<Col span={5} className="basic-info-title must-input">
    						{/* lang: 字段集显示名 */}
    						{fieldSetManagementLang.operateFieldSetModal("fieldSetName")}:
    					</Col>
    					<Col span={18}>
    						<Input
    							value={operateFieldSetData.displayName || undefined}
    							onChange={this.changeFieldValue.bind(this, "displayName", "input")}
    							placeholder={fieldSetManagementLang.operateFieldSetModal("fieldSetNamePlaceholder")} // lang:"请输入显示名"
    						/>
    					</Col>
    				</Row>
    				<Row gutter={10}>
    					<Col span={5} className="basic-info-title must-input">
    						{/* lang: 字段集类型 */}
    						{fieldSetManagementLang.operateFieldSetModal("fieldSetType")}:
    					</Col>
    					<Col span={18}>
    						<Select
    							value={operateFieldSetData.type || undefined}
    							onChange={this.changeFieldValue.bind(this, "type", "select")}
    							placeholder={fieldSetManagementLang.operateFieldSetModal("fieldSetTypePlaceholder")} // lang:请选择字段集类型
    						>
    							{
    								fieldSetTypeSelect &&
									fieldSetTypeSelect.length > 0 &&
    								fieldSetTypeSelect.map((item, index) =>
    									<Option value={item.name} key={index}>
    										{lang === "cn" ? item.dName || item.enDName : item.enDName || item.dName}
    									</Option>
    								)
    							}
    						</Select>
    					</Col>
    				</Row>
    				<Row gutter={10} style={{ height: "auto" }}>
    					<Col span={5} className="basic-info-title must-input">
    						{/* lang: 字段 */}
    						{fieldSetManagementLang.operateFieldSetModal("fields")}:
    					</Col>
    					<Col span={18}>
    						<Select
    							mode="multiple"
    							placeholder={fieldSetManagementLang.operateFieldSetModal("fieldsPlaceholder")} // lang:请选择字段
    							value={operateFieldSetData.fields || []}
    							showSearch
    							optionFilterProp="children"
    							onChange={(e) => {
    								console.log(e);
    								this.changeFieldValue("fields", "select", e);
    							}}
    						>
    							{
    								ruleFieldList &&
                                    ruleFieldList.length > 0 &&
                                    ruleFieldList.map((item, index) =>
                                    	<Option value={item.name} key={index}>
                                    		{item.dName}
                                    	</Option>
                                    )
    							}
    						</Select>
    					</Col>
    				</Row>
    				<Row gutter={10} style={{ height: "auto" }}>
    					<Table
    						columns={cols}
    						size="middle"
    						dataSource={dataSource}
    						pagination={false}
    						rowKey="name"
    						className="mt20"
    						bordered={true}
    					/>
    				</Row>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	fieldSetStore: state.fieldSet
}))(OperateFieldSetModal);
