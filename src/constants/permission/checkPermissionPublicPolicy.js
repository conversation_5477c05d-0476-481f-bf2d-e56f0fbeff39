import { checkFunctionHasPermission } from "@/utils/permission";
const CODE = "ZB0105";

export default {
	// 删除公共策略
	DelPubPolicy: () => {
		return checkFunctionHasPermission(CODE, "DelPubPolicy");
	},
	// 策略规则详情
	PolicyInfoAndRule: () => {
		return checkFunctionHasPermission(CODE, "PolicyInfoAndRule");
	},
	// 运行区公共策略列表
	PubPolicyListOnline: () => {
		return checkFunctionHasPermission(CODE, "PubPolicyListOnline");
	},
	// 公共策略导入
	PublicPolicyImport: () => {
		return checkFunctionHasPermission(CODE, "PublicPolicyImport");
	},
	// 编辑区公共策略详情
	PubPolicyDetail: () => {
		return checkFunctionHasPermission(CODE, "PubPolicyDetail");
	},
	// 公共策略历史版本
	PubPolicyHistory: () => {
		return checkFunctionHasPermission(CODE, "PubPolicyHistory");
	},
	// 公共策略被规则流引用
	PubPolicyFlowRef: () => {
		return checkFunctionHasPermission(CODE, "PubPolicyFlowRef");
	},
	// 公共策略下线
	OfflinePubPolicy: () => {
		return checkFunctionHasPermission(CODE, "OfflinePubPolicy");
	},
	// 新增公共策略
	AddPublicPolicy: () => {
		return checkFunctionHasPermission(CODE, "AddPublicPolicy");
	},
	// 切换公共策略版本
	SwitchPubPolicyVersion: () => {
		return checkFunctionHasPermission(CODE, "SwitchPubPolicyVersion");
	},
	// 运行区公共策略详情
	OnlinePubPolicyDetail: () => {
		return checkFunctionHasPermission(CODE, "OnlinePubPolicyDetail");
	},
	// 提交公共策略版本
	SubmitPubPolicy: () => {
		return checkFunctionHasPermission(CODE, "SubmitPubPolicy");
	},
	// 编辑区公共策略列表
	PubPolicyList: () => {
		return checkFunctionHasPermission(CODE, "PubPolicyList");
	},
	// 更新公共策略
	UpdatePubPolicy: () => {
		return checkFunctionHasPermission(CODE, "UpdatePubPolicy");
	},
	// 公共策略导出
	PublicPolicyExport: () => {
		return checkFunctionHasPermission(CODE, "PublicPolicyExport");
	}
};
