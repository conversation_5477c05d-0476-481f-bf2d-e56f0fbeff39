@configLeftWidth: 400px;

:global {

	.config-step-wrap {
		& {
			position: relative;
			width: 100%;
		}
		.left-nav {
			& {
				float: left;
				width: 40%;
				min-height: 300px;
				max-height: 90%;
				overflow: scroll;
				&::-webkit-scrollbar {
					display: none;
				}
			}
			ul {
				& {
					padding: 0;
				}
				li {
					& {
						position: relative;
						height: 40px;
						line-height: 40px;
						margin-bottom: 10px;
						border-radius: 3px;
						background: #fff;
						color: #666;
						padding: 0 16px;
						border: 1px solid #dcdcdc;
						box-sizing: border-box;
						cursor: pointer;
					}
					&:hover {
						background: #f5f5f5;
					}
					&.current.empty {
						background: #ff9954;
						border: 1px solid #e6874d;
					}
					.param-left {
						& {
							float: left;
							overflow: hidden;
							text-overflow: ellipsis;
							-o-text-overflow: ellipsis;
							-webkit-text-overflow: ellipsis;
							-moz-text-overflow: ellipsis;
							white-space: nowrap;
						}
						.param-type {
							position: relative;
							top: -1px;
							min-width: 70px;
							padding: 4px 10px;
							background: #5cb85c;
							line-height: 1;
							color: #fff;
							display: inline-block;
							margin-right: 10px;
							font-size: 14px;
							text-align: center;
							-webkit-border-radius: 3px;
							-moz-border-radius: 3px;
							border-radius: 3px;
						}
						.param-label {
							& {
								color: #555;
								font-size: 14px;
							}
							&.no-label {
								color: #e94228;
							}
						}
						.param-name {
							& {
								color: #777;
								font-size: 14px;
							}
							&.no-name {
								color: #e94228;
							}
						}
					}
					.param-oper-list {
						& {
							position: absolute;
							top: 10px;
							right: 10px;
							line-height: initial;
						}
						i {
							font-size: 20px;
							margin-left: 8px;
							opacity: 0.65;
							color: #333 !important;
						}
						i.disabled {
							cursor: not-allowed;
							opacity: 0.35;
						}
						i:hover {
							opacity: 1;
						}
					}
				}
				li.current {
					& {
						background: #2da5ff;
						color: #fff;
						border: 1px solid #2da5ff;
					}
					.param-type {
						background: rgba(255, 255, 255, .45);
						color: #063d67;
					}
					.param-label {
						& {
							color: #fff;
							opacity: .85;
						}
						&.no-label {
							color: #fff;
							opacity: .65;
						}
					}
					.param-name {
						& {
							color: #fff;
							opacity: .85;
						}
						&.no-name {
							color: #fff;
							opacity: .65;
						}
					}
				}
			}
			.add-control {
				& {
					text-align: center;
					cursor: pointer;
				}
				&:hover {
					background: #dcdcdc;
				}
			}
		}
		.right-content {
			& {
				float: right;
				width: 58%;
				//margin-left: 20px;
				max-height: 90%;
				overflow: scroll;
			}
			&::-webkit-scrollbar {
				display: none;
			}
		}

        .boder-1{
            border: 1px solid #dcdcdc;
        }
		.add-control {
			& {
				width: 100%;
				height: 36px;
				line-height: 35px;
				background: #fff;
                flex:1;
                &:last-of-type{
                    border-left: 1px solid #dcdcdc;
                }
			}
			span {
				& {
					// width: 16.66667%;
					// display: inline-block;
					// text-align: center;
					// border-right: 1px solid #e6e6e6;
					// cursor: pointer;
					// line-height: 36px;
				}
				&:hover {
					background: #1d8bd8;
					color: #fff;
				}
				&:last-child {
					border-right: none;
				}
			}
		}
	}
    .model-copy-drop-down{
        &.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item{
            height: 30px;
            line-height: 30px;
            padding: 0 0 0 10px;
        }
        &.ant-dropdown .ant-dropdown-menu{
            width:250px;
        }
    }
    .model-copy-item{
        >div{
            max-width:200px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        i{
            font-size:12px;
        }
    }
}
