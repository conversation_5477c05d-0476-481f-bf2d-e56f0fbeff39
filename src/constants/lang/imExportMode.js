import store from "../../app";
const importInfo = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		warn: {
			cn: "提示",
			en: "Warn Tip"
		},
		systemField: {
			cn: "系统字段缺失",
			en: "System field missing"
		},
		offlineZb: {
			cn: "离线指标缺失",
			en: "Offline indicators missing"
		},
		multiNameList: {
			cn: "多维度名单",
			en: "Multidimensional list"
		},
		nameList: {
			cn: "单维度名单",
			en: "Single dimension list"
		},
		thirdName: {
			cn: "三方缺失",
			en: "Absence of the three party"
		},
		zbSceneMiss: {
			cn: "部分场景缺失指标",
			en: "Missing indicators in some scenes"
		},
		zbSceneAllMiss: {
			cn: "全场景缺失指标",
			en: "Missing indicators in the whole scene"
		},
		holmesName: {
			cn: "模型缺失",
			en: "Model deletion"
		},
		appMissInfo: {
			cn: "应用缺失",
			en: "Application deficiency"
		},
		eventMissInfo: {
			cn: "策略缺失",
			en: "Missing policy"
		},
		totalImport: {
			cn: "共导入",
			en: "Co import"
		},
		policySet: {
			cn: "个策略",
			en: "strategies"
		},
		indicator: {
			cn: "个指标",
			en: "indicators"
		},
		rules: {
			cn: "条规则",
			en: "rules"
		},
		eachTotal: {
			cn: (number) => ("共" + number + "条"),
			en: (number) => ("A total of " + number)
		},
		name: {
			cn: "名称",
			en: "Name"
		},
		identification: {
			cn: "标识",
			en: "Identification"
		},
		app: {
			cn: "应用标识",
			en: "Application identification"
		},
		appName: {
			cn: "应用名称",
			en: "Application name"
		},
		eventId: {
			cn: "策略标识",
			en: "Policy identity"
		},
		policySetName: {
			cn: "策略集名称",
			en: "Policy set name"
		},
		policyName: {
			cn: "策略名称",
			en: "Policy name"
		},
		version: {
			cn: "版本",
			en: "Version"
		},
		importDetail: {
			cn: "下载导入明细",
			en: "Download import details"
		},
		missType: {
			cn: "缺失类型",
			en: "Deletion type"
		},
		exceptType: {
			cn: "异常类型",
			en: "Exception type"
		},
		misId: {
			cn: "缺失项标识",
			en: "Missing item ID"
		},
		misName: {
			cn: "缺失项名称",
			en: "Missing item name"
		},
		misVersion: {
			cn: "缺失项版本号",
			en: "Missing item version number"
		},
		completion: {
			cn: "补全",
			en: "completion"
		},
		resultTitle: {
			cn: "处理结果",
			en: "Processing result"
		},
		missField: {
			cn: "缺失项",
			en: "Missing item"
		},
		exceptionField: {
			cn: "异常项",
			en: "Exception term"
		},
		policyNo: {
			cn: "策略编号",
			en: "Policy No"
		},
		mode: {
			cn: "处理模式",
			en: "Processing mode"
		},
		ruleNo: {
			cn: "规则编号",
			en: "Rule No"
		},
		ruleName: {
			cn: "规则名称",
			en: "Rule name"
		},
		zbNo: {
			cn: "指标编号",
			en: "Index No"
		},
		zbName: {
			cn: "指标名称",
			en: "Index Name"
		},
		eventTypeUnMatch: {
			cn: "事件类型",
			en: "Event type"
		},
		policyModeUnMatch: {
			cn: "策略模式",
			en: "Policy mode"
		},
		exportScene: {
			cn: "导出环境",
			en: "Export environment"
		},
		importScene: {
			cn: "导入环境",
			en: "Import environment"
		},
		FirstMatch: {
			cn: "首次匹配",
			en: "FirstMatch"
		},
		WorstMatch: {
			cn: "最坏匹配",
			en: "WorstMatch"
		}
	};
	return params[field][lang];
};

const exportInfo = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		warn: {
			cn: "提示",
			en: "Warn Tip"
		},
		tipPolicySet: {
			cn: "系统默认将策略集下的策略/规则及引用到的指标一并导出",
			en: "By default, the system exports the policies / rules under the policy set and the referenced indicators together"
		},
		tipPolicy: {
			cn: "系统默认将策略下的规则及引用到的指标一并导出",
			en: "By default, the system exports the rules under the policy and the indicators it references"
		},
		tipWorkFlow: {
			cn: "系统默认将规则流中引用策略/规则/指标一并导出",
			en: "By default, the system exports the reference policies / rules / indicators in the rule flow"
		},
		tipPublicPolicy: {
			cn: "系统默认将公共策略下的规则及引用的指标一并导出",
			en: "By default, the system exports the rules and referenced indicators under the public policy"
		}
	};
	return params[field][lang];
};
const importPolicyModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"previousStep": {
			"cn": "上一步",
			"en": "Previous step"
		},
		"policyImport": {
			"cn": "策略导入",
			"en": "Policy Import"
		},
		"ruleImport": {
			"cn": "规则导入",
			"en": "Rule Import"
		},
		"policySet": {
			"cn": "策略集",
			"en": "PolicySet"
		},
		"policyName": {
			"cn": "策略名称",
			"en": "Policy Name"
		},
		"importMode": {
			"cn": "导入模式",
			"en": "Import mode"
		},
		"zbNoScene": {
			"cn": "以下指标无场景",
			"en": "No scenario for the following indicators"
		},
		"selectFile": {
			"cn": "选择文件",
			"en": "Select file"
		},
		"fileName": {
			"cn": "文件名称",
			"en": "File name"
		},
		"selectFileFirst": {
			"cn": "请先选择文件",
			"en": "Please select the file first"
		},
		"samePolicySkipping": {
			"cn": "相同策略(策略名称)跳过",
			"en": "Skip same policy (policy name)"
		},
		"samePolicyCoverage": {
			"cn": "相同策略(策略名称)覆盖",
			"en": "Same policy (policy name) override"
		},
		"samePolicyInterrupt": {
			"cn": "相同策略(策略名称)中断",
			"en": "Same policy (policy name) interrupt"
		},
		"sameRuleSkipping": {
			"cn": "相同规则(规则名称)跳过",
			"en": "Same rule (rule name) skipped"
		},
		"sameRuleCoverage": {
			"cn": "相同规则(规则名称)覆盖",
			"en": "Same rule (rule name) override"
		},
		"sameRuleInterrupt": {
			"cn": "相同规则(规则名称)中断",
			"en": "Same rule (rule name) interrupt"
		},
		"sameIndicatorSkipping": {
			"cn": "相同指标(指标ID)跳过",
			"en": "Skip the same indicator (indicator ID)"
		},
		"sameIndicatorCoverage": {
			"cn": "相同指标(指标ID)覆盖",
			"en": "Same indicator (indicator ID) coverage"
		},
		"sameIndicatorInterrupt": {
			"cn": "相同指标(指标ID)中断",
			"en": "Same indicator (indicator ID) interrupt"
		},
		"plsFileAllowedTip": {
			"cn": "只允许上传pls格式的文件",
			"en": "Only files in pls format are allowed"
		},
		"plyFileAllowedTip": {
			"cn": "只允许上传ply格式的文件",
			"en": "Only files in ply format are allowed"
		},
		"pldFileAllowedTip": {
			"cn": "只允许上传pld格式的文件",
			"en": "Only uploading files in pld format is allowed"
		},
		"zbFileAllowedTip": {
			"cn": "只允许上传zb格式的文件",
			"en": "Only files in zb format are allowed"
		},
		"fileSizeTip": {
			"cn": "文件大小请100M内",
			"en": "File size in 100M please"
		},
		//	other
		"importStatus": {
			"cn": "导入状态",
			"en": "Import status"
		},
		"reason": {
			"cn": "原因",
			"en": "Reason"
		},
		"success": {
			"cn": "成功",
			"en": "Success"
		},
		"fail": {
			"cn": "失败",
			"en": "Fail"
		},
		"policy": {
			"cn": "策略",
			"en": "Policy"
		},
		"rule": {
			"cn": "规则",
			"en": "Rule"
		},
		"indicator": {
			"cn": "指标",
			"en": "Indicator"
		},
		"workflow": {
			"cn": "规则流",
			"en": "Workflow"
		},
		"overWorkflow": {
			"cn": "覆盖已有规则流",
			"en": "Overwrite existing rule flow"
		},
		"policyModeRequired": {
			"cn": "请选择策略导入模式",
			"en": "Please select policy import mode"
		},
		"zbModeRequired": {
			"cn": "请选择指标导入模式",
			"en": "Please select indicator import mode"
		},
		"ruleModeRequired": {
			"cn": "请选择规则导入模式",
			"en": "Please select rule import mode"
		},
		"exceptDealMode": {
			"cn": "异常处理模式",
			"en": "Exception handling mode"
		},
		"eventTypeNotSame": {
			"cn": "事件类型不一致，以导入环境为准，风险类型默认事件类型中的第一个。",
			"en": "The event types are inconsistent. The import environment shall prevail. The risk type defaults to the first event type."
		},
		"eventNotSame": {
			"cn": "事件标识不一致，以导入环境为准。",
			"en": "Inconsistent event identification, subject to import environment."
		},
		"policyModeUnMatch": {
			"cn": "策略模式不一致默认导入环境数据。",
			"en": "Inconsistent policy mode default import environment data."
		}
	};
	return params[field][lang];
};
export default {
	importPolicyModal,
	importInfo,
	exportInfo
};
