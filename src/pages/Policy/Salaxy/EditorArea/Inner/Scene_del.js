import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Icon, Row, Col, Checkbox, Button, Input, Tooltip } from "antd";
import { CommonConstants } from "@/constants";
import { trim } from "lodash";
import { indexListLang } from "@/constants/lang";
import "./Scene.less";

class Scene extends PureComponent {
    state = {
    	currentSelectAppIndex: 0,
    	searchIng: false,
    	searchWord: null
    }
    constructor(props) {
    	super(props);
    }

    changeSceneEvent = (appItem = {}, eventId, checked) => {
    	let { indexEditorStore, dispatch } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
    	let { sceneList } = currentIndexObj;

    	let sceneObj = sceneList.find(scene => scene.appName === appItem.name);
    	let sceneObjIndex = sceneList.findIndex(scene => scene.appName === appItem.name);

    	if (checked) {
    		// add
    		if (sceneObj) {
    			// app scene对象已经存在
    			sceneList[sceneObjIndex]["appName"] = appItem.name;
    			if (Array.isArray(sceneList[sceneObjIndex]["eventList"])) {
    				sceneList[sceneObjIndex]["eventList"].push(eventId);
    			} else {
    				sceneList[sceneObjIndex]["eventList"] = [eventId];
    			}

    		} else {
    			// add
    			let obj = {
    				appName: appItem.name,
    				eventList: [eventId]
    			};
    			sceneList.push(obj);
    		}
    	} else {
    		// remove
    		if (sceneObj) {
    			let { eventList } = sceneObj;

    			if (sceneObjIndex > -1) {
    				let eventIdIndex = eventList.findIndex(event => event === eventId);
    				eventList.splice(eventIdIndex, 1);
    				if (eventList.length > 0) {
    					// 更新
    					sceneList[sceneObjIndex]["appName"] = appItem.name;
    					sceneList[sceneObjIndex]["eventList"] = sceneObj.eventList || [];
    				} else {
    					// 移除一整个
    					sceneList.splice(sceneObjIndex, 1);
    				}

    			}
    		}
    	}
    	currentIndexObj["hasModify"] = true;

    	dispatch({
    		type: "indexEditor/setAttrValue",
    		payload: {
    			editIndexMap: editIndexMap
    		}
    	});
    }

    render() {
    	let { currentSelectAppIndex, searchIng, searchWord } = this.state;
    	let { globalStore: { allMap = {} } } = this.props;
    	let { indexEditorStore, unSave } = this.props;
    	let { currentIndexId, editIndexMap } = indexEditorStore;
    	let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;

    	let { sceneList = [] } = currentIndexObj;

    	let newAppList = [];
    	searchWord = trim(searchWord);

    	allMap["appNames"] && allMap["appNames"].forEach((item) => {
    		if (searchIng && searchWord) {
    			if (item.name !== "all" && item.dName) {
    				if (item.dName.indexOf(searchWord) === -1) {
    					// 如果没有匹配应用，继续检索应用下的策略集
    					let policySetMapName = item.name ? item.name + "_scenes" : null;
    					let policySetList = policySetMapName && allMap[policySetMapName] ? allMap[policySetMapName] : [];
    					let hasMatchSearchWord = false;
    					policySetList.forEach(setItem => {
    						if (setItem.dName && setItem.dName.indexOf(searchWord) > -1) {
    							hasMatchSearchWord = true;
    						}
    					});
    					if (hasMatchSearchWord) {
    						newAppList.push(item);
    					}
    				} else {
    					// 匹配应用
    					newAppList.push(item);
    				}
    			}
    		} else {
    			if (item.name !== "all") {
    				newAppList.push(item);
    			}
    		}
    	});
    	console.log(newAppList);

    	let currentAppObj = newAppList.length > 0 && newAppList[currentSelectAppIndex] ? newAppList[currentSelectAppIndex] : null;
    	let policySetMapName = currentAppObj && currentAppObj.name ? currentAppObj.name + "_scenes" : null;
    	let hasSelectSceneObj = currentAppObj ? sceneList.find(sceneItem => sceneItem.appName === currentAppObj.name) : null;

    	if (!hasSelectSceneObj) {
    		hasSelectSceneObj = {
    			appName: currentAppObj && currentAppObj.name,
    			eventList: []
    		};
    	}

    	let disabled = false;
    	// if (currentIndexObj && !currentIndexObj["unSave"]) {
    	if (currentIndexObj && !unSave) {
    		disabled = true;
    	}
    	// if (currentIndexObj && !currentIndexObj["unSave"]) {
    	// 	disabled = true;
    	// }

    	return (
    		<Fragment>
    			<Row
    				gutter={CommonConstants.gutterSpan}
    				style={{ marginBottom: "10px" }}
    			>
    				<Col span={4} className="basic-info-title">
    					{/* lang:场景 */}
    					{indexListLang.ruleConfig("scene")}
    				</Col>
    				<Col span={8}>
    					<div className="scene-wrap">
    						{
    							searchIng &&
                                <div className="scene-search-wrap">
                                	<Input
                                		value={searchWord || undefined}
                                		placeholder={indexListLang.ruleConfig("searchSceneTip")} // lang:搜索应用、策略集
                                		prefix={
                                			<Icon
                                				type="search"
                                				style={{ color: "rgba(0,0,0,.25)" }}
                                			/>
                                		}
                                		suffix={
                                			<Tooltip
                                				title={indexListLang.ruleConfig("exitSearch")} // lang:退出搜索
                                			>
                                				<Icon
                                					type="close"
                                					style={{ color: "rgba(0,0,0,.45)" }}
                                					onClick={() => {
                                						this.setState({
                                							searchIng: false,
                                							searchWord: null
                                						});
                                					}}
                                				/>
                                			</Tooltip>
                                		}
                                		style={{ width: "100%" }}
                                		onChange={(e) => {
                                			this.setState({
                                				searchWord: e.target.value
                                			});
                                		}}
                                	/>
                                </div>
    						}
    						<div className="left">
    							<div className="scene-content-wrap">
    								<div className="scene-content-header">
    									<h3>
    										{/* lang:应用 */}
    										{indexListLang.ruleConfig("application")}
    									</h3>
    								</div>
    								<div className="scene-content-body">
    									<ul className="scene-app-list">
    										{
    											newAppList.map((item, index) => {
    												let hasSelect = !!sceneList.find(sceneItem => {
    													return sceneItem && sceneItem.eventList && sceneItem.appName === item.name && sceneItem.eventList.length > 0;
    												});

    												return (
    													<li
    														className={currentSelectAppIndex === index ? "scene-app-item active" : "scene-app-item"}
    														key={index}
    														onClick={() => {
    															this.setState({
    																currentSelectAppIndex: index
    															});
    														}}
    													>
    														<Icon
    															style={{ marginRight: "5px" }}
    															type={hasSelect ? "check-square" : "border"}
    														/>
    														<span>{item.dName}</span>
    													</li>
    												);
    											})
    										}
    									</ul>
    								</div>
    							</div>
    						</div>
    						<div className="right">
    							<div className="scene-content-wrap">
    								<div className="scene-content-header">
    									<h3>
    										{/* lang:策略集 */}
    										{indexListLang.ruleConfig("policySet")}
    									</h3>
    									<div className="search-handle">
    										<Tooltip
    											title={indexListLang.ruleConfig("searchSceneTip")} // lang:搜索应用、策略集
    										>
    											<Button
    												type="dashed"
    												shape="circle"
    												icon="search"
    												size="small"
    												onClick={() => {
    													this.setState({
    														searchIng: true
    													});
    												}}
    											/>
    										</Tooltip>
    									</div>
    								</div>
    								<div className="scene-content-body">
    									<ul className="scene-event-list">
    										{
    											policySetMapName &&
                                                allMap &&
                                                allMap[policySetMapName] &&
                                                allMap[policySetMapName].map((item, index) => {
                                                	let eventList = hasSelectSceneObj.eventList;

                                                	let Dom = (
                                                		<li
                                                			className="scene-event-item"
                                                			key={index}
                                                		>
                                                			<Checkbox
                                                				checked={!!(eventList && eventList.find(event => event === item.eventId))}
                                                				onClick={(e) => {
                                                					let checked = e.target.checked;

                                                					console.log(sceneList);
                                                					this.changeSceneEvent(currentAppObj, item.eventId, checked);
                                                				}}
                                                				disabled={disabled}
                                                			>
                                                				{item.dName}
                                                			</Checkbox>
                                                		</li>
                                                	);

                                                	if (searchIng && searchWord) {
                                                		if (currentAppObj.dName.indexOf(searchWord) > -1) {
                                                			return Dom;
                                                		} else if (item.dName && item.dName.indexOf(searchWord) > -1) {
                                                			return Dom;
                                                		}
                                                	} else {
                                                		return Dom;
                                                	}
                                                })
    										}
    									</ul>
    								</div>
    							</div>
    						</div>
    					</div>
    				</Col>
    			</Row>
    		</Fragment>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(Scene);
