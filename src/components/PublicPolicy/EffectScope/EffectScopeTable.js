import { Fragment, PureComponent } from "react";
import { connect } from "dva";
import { publicPolicyListLang } from "@/constants/lang";
import "./EffectScopeTable.less";

class EffectScopeTable extends PureComponent {
	state = {
		totalEventList: []
	}
	constructor(props) {
		super(props);
	}

	componentWillMount() {
		const { globalStore } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		const totalEventList = publicPolicyScene.reduce((totalScenes, cur) => {
			totalScenes = totalScenes.concat(cur.eventList);
			return totalScenes;
		}, []);
		this.setState({
			totalEventList
		});
	}
	render() {
		const { scene, globalStore, styleCss, className } = this.props;
		const { allMap } = globalStore || {};
		const { publicPolicyScene = [] } = allMap || {};
		const { totalEventList } = this.state;
		let newScene = [];
		if (scene && scene.length > 0) {
			newScene = scene.filter((v) => {
				const { appName } = v || {};
				const newAppName = (publicPolicyScene.find((publicAppName) => {
					if (publicAppName.name === appName) {
						return publicAppName;
					}
				}) || {}).dName;
				if (newAppName) {
					v.newAppName = newAppName;
					return v;
				}
			});
		}

		return (
			<div className={className || ""}>
				{
					newScene &&
					newScene.length > 0 &&
					<table className="effect-scope" style={styleCss}>
						<thead>
							<tr>
								<th>
									{/* lang:应用 */}
									{publicPolicyListLang.common("application")}
								</th>
								<th>
									{/* lang:策略集 */}
									{publicPolicyListLang.common("policySet")}
								</th>
							</tr>
						</thead>
						<tbody>
							{
								newScene.map((v) => {
									const { eventList = [], newAppName } = v || {};
									const currentEventListName = totalEventList.filter((scene) => {
										if (eventList.indexOf(scene.name) > -1) {
											return scene;
										}
									}).map(sceneName => {
										return sceneName.dName;
									});
									return (
										<tr>
											<td width={120}>
												{newAppName}
											</td>
											<td>
												{currentEventListName.join(" | ")}
											</td>
										</tr>
									);
								})

							}

						</tbody>
					</table>
				}
			</div>
		);
	}
};
export default connect(state => ({
	globalStore: state.global
}))(EffectScopeTable);
