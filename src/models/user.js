import { userAPI } from "@/services";
import { message } from "antd";

export default {
	namespace: "user",
	state: {
		currentUser: {}
	},

	effects: {
		*fetchCurrent({ payload }, { call, put }) {
			const response = yield call(userAPI.getUserInfo, payload);
			console.log(response);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "saveCurrentUser",
				payload: response.data
			});
		}
	},

	reducers: {
		saveCurrentUser(state, action) {
			return {
				...state,
				currentUser: action.payload
			};
		}
	}
};
