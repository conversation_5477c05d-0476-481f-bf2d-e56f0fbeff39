import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Button } from "antd";
import { policyDetailLang } from "@/constants/lang";
import ImmuneConfig from "@/pages/Immune/ImmuneConfig";

class ImmuneConfigListModal extends PureComponent {
	onCancel = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyDetail/setDialogData",
			payload: {
				viewImmuneData: {
					ruleUuid: ""
				}
			}
		});
		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				viewImmune: false
			}
		});
	}
	render() {
		const { policyDetailStore, refreshCallBack } = this.props;
		const { dialogShow, dialogData } = policyDetailStore;
		const { viewImmune } = dialogShow;
		const { ruleUuid } = dialogData.viewImmuneData || {};
    	return (
    		<Modal
    			title= {policyDetailLang.immuneListModal("immuneListTitle")} // "查看规则免疫列表"
    			visible={viewImmune}
    			maskClosable={false}
    			width="1024px"
				onCancel={this.onCancel}
				footer={[
					<Button key="back" onClick={this.onCancel}>
					  	{/* 关闭 */}
						{policyDetailLang.immuneListModal("close")}
					</Button>
				  ]
				}
    		>
				{
					viewImmune &&
					<ImmuneConfig
						refreshCallBack={refreshCallBack}
						isOtherPageCite={true}
						ruleUuid={ruleUuid}
					/>
				}
    		</Modal>
    	);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(ImmuneConfigListModal);

