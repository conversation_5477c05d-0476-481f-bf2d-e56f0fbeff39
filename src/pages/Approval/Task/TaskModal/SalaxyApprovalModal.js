import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, message } from "antd";
import { policyDetailAPI } from "@/services";
import SalaxyApprovalBase from "./Inner/SalaxyApprovalBase";
import SalaxyApprovalDiff from "./Inner/SalaxyApprovalDiff";
import "./Less/ApprovalModal.less";

class SalaxyApprovalModal extends PureComponent {

    state = {
    	desc: null
    };

    constructor(props) {
    	super(props);
    	this.policyCommit = this.policyCommit.bind(this);
    	this.changeLeftNav = this.changeLeftNav.bind(this);
    }

    policyCommit() {
    	let { policyDetailStore, dispatch } = this.props;
    	let { policyDetail } = policyDetailStore;
    	let desc = this.state.desc;
    	if (!desc) {
    		message.warning("请输入发版描述");
    		return;
    	}
    	let params = {
    		policyUuid: policyDetail.uuid,
    		desc: desc
    	};

    	policyDetailAPI.policyCommit(params).then(res => {
    		if (res.success) {
    			message.success("指标版本提交成功");
    			dispatch({
    				type: "policyDetail/setDialogShow",
    				payload: {
    					policyCommit: false
    				}
    			});
    			this.setState({
    				desc: null
    			});
    		} else {
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    changeLeftNav(index) {
    	let { approvalTaskStore, dispatch } = this.props;
    	let { dialogData } = approvalTaskStore;
    	let { salaxyApprovalData } = dialogData;

    	salaxyApprovalData.leftNavIndex = index;
    	dispatch({
    		type: "approvalTask/setDialogData",
    		payload: {
    			salaxyApprovalData: salaxyApprovalData
    		}
    	});
    }

    render() {
    	let { approvalTaskStore, indexRunningStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = approvalTaskStore;
    	let { salaxyApproval } = dialogShow;
    	let { salaxyApprovalData } = dialogData;
    	let { leftNavIndex } = salaxyApprovalData;

    	let { indexPages } = indexRunningStore;
    	return (
    		<Modal
    			title="指标版本审核"
    			visible={salaxyApproval}
    			className="approval-modal-wrap"
    			maskClosable={true}
    			width={1180}
    			onOk={this.policyCommit.bind(this)}
    			onCancel={() => {
    				indexPages["approvalTask"] = {
    					indexList: [],
    					indexListReady: false,
    					curPage: 1,
    					pageSize: 20,
    					total: 0,
    					pages: 0
    				};
    				dispatch({
    					type: "approvalTask/setDialogShow",
    					payload: {
    						salaxyApproval: false
    					}
    				});
    				setTimeout(() => {
    					dispatch({
    						type: "approvalTask/setDialogData",
    						payload: {
    							salaxyApprovalData: {
    								id: null,
    								uuid: null,
    								status: "APPROVED",
    								desc: null,
    								leftNavIndex: 0,
    								tabIndex: 0,
    								diffDetail: {
    									onlineVersion: null,
    									pendVersion: null
    								}
    							}
    						}
    					});
    					dispatch({
    						type: "indexRunning/setAttrValue",
    						payload: {
    							indexPages: {
    								...indexPages,
    								approvalTask: {
    									indexList: [],
    									indexListReady: false,
    									curPage: 1,
    									pageSize: 20,
    									total: 0,
    									pages: 0
    								}
    							}
    						}
    					});
    				}, 300);
    				dispatch({
    					type: "indexRunning/setAttrValue",
    					payload: {
    						indexPages: indexPages
    					}
    				});
    			}}
    			footer={null}
    		>
    			<div className="approval-wrap">
    				<div className="left-nav">
    					<ul>
    						<li
    							className={leftNavIndex === 0 ? "active" : ""}
    							onClick={this.changeLeftNav.bind(this, 0)}
    						>
                                策略审批
    						</li>
    						<li
    							className={leftNavIndex === 1 ? "active" : ""}
    							onClick={this.changeLeftNav.bind(this, 1)}
    						>
                                修改详情
    						</li>
    					</ul>
    				</div>
    				<div className="right-content">
    					{
    						leftNavIndex === 0 && <SalaxyApprovalBase />
    					}
    					{
    						leftNavIndex === 1 && <SalaxyApprovalDiff />
    					}
    				</div>
    			</div>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask,
	indexRunningStore: state.indexRunning
}))(SalaxyApprovalModal);
