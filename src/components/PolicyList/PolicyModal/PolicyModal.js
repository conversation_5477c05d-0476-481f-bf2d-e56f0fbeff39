import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Select, Form, Row, Col, message } from "antd";
import { policyEditorAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { policyListLang } from "@/constants/lang";
import WeightModeEditor from "../../WeightModeEditor";

const TextArea = Input.TextArea;
const Option = Select.Option;

class PolicyModal extends PureComponent {
	constructor(props) {
		super(props);
		this.addPolicy = this.addPolicy.bind(this);
		this.changeDialogDataHandle = this.changeDialogDataHandle.bind(this);
		this.changeWeightMode = this.changeWeightMode.bind(this);
		this.changeDealTypeCount = this.changeDealTypeCount.bind(this);
		this.addDeleteItem = this.addDeleteItem.bind(this);
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyEditorStore } = this.props;
		let { addPolicyData } = policyEditorStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		addPolicyData[field] = value;
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				addPolicyData: addPolicyData
			}
		});
	}

	changeWeightMode(field, value, index) {
		let { dispatch, policyEditorStore } = this.props;
		let { addPolicyData } = policyEditorStore.dialogData;
		addPolicyData["dealTypeMappings"][index][field] = value;
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				addPolicyData: addPolicyData
			}
		});
	}

	changeDealTypeCount(value) {
		let { dispatch, policyEditorStore } = this.props;
		let { addPolicyData } = policyEditorStore.dialogData;

		addPolicyData["dealTypeCount"] = value;
		let dealTypeLen = addPolicyData.dealTypeMappings.length;

		let temp = {
			"score": null,
			"dealType": null
		};
		if (value > dealTypeLen) {
			// 新增
			for (let i = 0; i < value - dealTypeLen; i++) {
				addPolicyData["dealTypeMappings"].push(temp);
			}
		} else {
			// 移除
			for (let i = 0; i < dealTypeLen - value; i++) {
				addPolicyData["dealTypeMappings"].push(temp);
				addPolicyData["dealTypeMappings"].splice(addPolicyData["dealTypeMappings"].length - 1, 1);
			}
		}

		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				addPolicyData: addPolicyData
			}
		});
	}

	addDeleteItem(type, index) {
		let { dispatch, policyEditorStore } = this.props;
		let { addPolicyData } = policyEditorStore.dialogData;

		let temp = {
			"score": null,
			"dealType": null
		};

		if (type === "add") {
			addPolicyData["dealTypeMappings"].splice(index + 1, 0, temp);
		} else if (type === "delete") {
			addPolicyData["dealTypeMappings"].splice(index, 1);
		}

		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				addPolicyData: addPolicyData
			}
		});
	}

	addPolicy() {
		let { policyEditorStore } = this.props;
		let { dialogData } = policyEditorStore;
		let { addPolicyData } = dialogData;

		if (!addPolicyData.name) {
			// lang:策略名称不能为空
			message.error(policyListLang.policyModal("policyNameIsEmptyTip"));
			return;
		} else if (!addPolicyData.riskType) {
			// lang:请选择风险类型
			message.error(policyListLang.policyModal("enterRiskType"));
			return;
		} else if (!addPolicyData.mode) {
			// lang:请选择策略模式
			message.error(policyListLang.policyModal("selectPolicyMode"));
			return;
		}

		if (addPolicyData.name.length >= 50 || addPolicyData.name.length <= 1) {
			// lang:策略名称长度应大于1,小于50
			message.error(policyListLang.policyModal("policyLenTip"));
			return;
		}

		if (!/^[A-Za-z0-9\u4E00-\u9FA5\_]{2,50}$/.test(addPolicyData.name)) {
			// lang:策略名称：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合
			message.error(policyListLang.policyModal("policyRegTip"));
			return;
		}

		// 检测权重模式下风险阈值配置是否有空值
		if (addPolicyData.mode === "Weighted") {
			if (addPolicyData.dealTypeMappings && addPolicyData.dealTypeMappings.length > 0) {
				let mapHasEmpty = false;
				for (let i = 0; i < addPolicyData.dealTypeMappings.length; i++) {
					let mapItem = addPolicyData.dealTypeMappings[i];
					if (!mapItem.dealType || !mapItem.score) {
						mapHasEmpty = true;
						break;
					}
				}
				if (mapHasEmpty) {
					// lang:风险阈值配置存在空值，请补充完整！
					message.error(policyListLang.policyModal("riskThresholdEmptyTip"));
					return;
				}
			}
		}

		let tags;
		if (addPolicyData.tags && addPolicyData.tags.length) {
			tags = addPolicyData.tags.join(",");
		}
		let params = {
			partner: addPolicyData.partner,
			fkPolicySetUuid: addPolicyData.fkPolicySetUuid,
			name: addPolicyData.name,
			riskType: addPolicyData.riskType,
			mode: addPolicyData.mode,
			riskEventType: addPolicyData.riskEventType,
			riskEventId: addPolicyData.riskEventId,
			appName: addPolicyData.appName,
			description: addPolicyData.description,
			level: addPolicyData.level || 0,
			tags: tags,
			dealTypeMappings: addPolicyData.dealTypeMappings ? JSON.stringify(addPolicyData.dealTypeMappings) : null
		};

		policyEditorAPI.addPolicy(params).then(res => {
			let { policyEditorStore, globalStore, dispatch } = this.props;
			let { curPage, pageSize } = policyEditorStore;
			let { currentApp } = globalStore;

			if (res.success) {
				dispatch({
					type: "policyEditor/setDialogShow",
					payload: {
						policyModal: false
					}
				});
				// lang:新增策略成功
				message.success(policyListLang.policyModal("newPolicySuccessTip"));
				dispatch({
					type: "policyEditor/getPolicySets",
					payload: {
						appName: currentApp.name ? currentApp.name : null,
						curPage: curPage,
						pageSize: pageSize
					}
				});

				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
			} else {
				console.log(res.message);
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});

	}

	render() {
		let { policyEditorStore, globalStore, dispatch } = this.props;
		let { dialogShow, modalType, dialogData } = policyEditorStore;
		let { allMap, policyModel } = globalStore;
		let { addPolicyData } = dialogData;
		let title = modalType === "addPolicy" ? policyListLang.policyModal("addPolicy") : policyListLang.policyModal("modifyPolicy");	// 添加策略 or 修改策略

		return (
			<Modal
				title={title}
				width={600}
				visible={dialogShow.policyModal}
				maskClosable={false}
				onOk={this.addPolicy.bind(this)}
				onCancel={() => {
					dispatch({
						type: "policyEditor/setDialogShow",
						payload: {
							policyModal: false
						}
					});
					dispatch({
						type: "policyEditor/setAttrValue",
						payload: {
							modalType: null
						}
					});
				}}
			>
				<Form className="modal-form">
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={5} className="basic-info-title">
							{/* lang:策略名称 */}
							{policyListLang.policyModal("policyName")}
						</Col>
						<Col span={18}>
							<Input
								type="text"
								placeholder={policyListLang.policyModal("enterPolicyName")} // lang:请输入策略名称
								value={addPolicyData.name}
								onChange={this.changeDialogDataHandle.bind(this, "name", "input")}
							/>
							<span className="tip-text">
								{/* lang:建议输入：中文、英文、数字和下划线的组合 */}
								{policyListLang.common("inputSuggest1")}
							</span>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={5} className="basic-info-title">
							{/* lang:风险类型 */}
							{policyListLang.policyModal("riskType")}
						</Col>
						<Col span={18}>
							<Select
								placeholder={policyListLang.policyModal("enterRiskType")} // lang:请选择风险类型
								value={addPolicyData.riskType || undefined}
								onChange={this.changeDialogDataHandle.bind(this, "riskType", "select")}
							>
								{
									allMap &&
                                    allMap[addPolicyData.riskEventType] &&
                                    allMap[addPolicyData.riskEventType].map((item, index) => {
                                    	return (
                                    		<Option value={item.name} key={index}>
                                    			{item.dName}
                                    		</Option>
                                    	);
                                    })
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={5} className="basic-info-title">
							{/* lang:策略模式 */}
							{policyListLang.policyModal("policyMode")}
						</Col>
						<Col span={18}>
							<Select
								placeholder={policyListLang.policyModal("selectPolicyMode")} // lang:请选择策略模式
								value={addPolicyData.mode || undefined}
								onChange={this.changeDialogDataHandle.bind(this, "mode", "select")}
							>
								{
									policyModel && policyModel.map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					</Row>
					{
						addPolicyData.mode === "Weighted" &&
                        <Row gutter={CommonConstants.gutterSpan}>
                        	<Col span={5} className="basic-info-title">
                        		{/* lang:风险阈值 */}
                        		{policyListLang.policyModal("RiskThreshold")}
                        	</Col>
                        	<Col span={18}>
                        		<WeightModeEditor
                        			dealTypeMappings={addPolicyData.dealTypeMappings}
                        			onChange={this.changeWeightMode.bind(this)}
                        			operateItem={this.addDeleteItem.bind(this)}
                        		/>
                        	</Col>
                        </Row>
					}
					{/* lang:事件标签 */}
					{/* <Row gutter={CommonConstants.gutterSpan}>
                        <Col span={5} className="basic-info-title">
                            {policyListLang.policyModal("eventTags")}：
						</Col>
                        <Col span={18}>
                            <Select
                                mode="multiple"
                                placeholder={policyListLang.policyModal("selectEventTags")} // lang:请选择事件标签
                                value={addPolicyData.tags || undefined}
                                onChange={this.changeDialogDataHandle.bind(this, "tags", "select")}
                            >
                                {
                                    allMap &&
                                    allMap["policyTags"] &&
                                    allMap["policyTags"].map((item, index) => {
                                        return (
                                            <Option
                                                value={item.name}
                                                key={index}
                                            >
                                                {item.dName}
                                            </Option>
                                        );
                                    })
                                }
                            </Select>
                        </Col>
                    </Row> */}
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={5} className="basic-info-title">
							{/* lang:描述 */}
							{policyListLang.policyModal("description")}
						</Col>
						<Col span={18}>
							<TextArea
								rows={4}
								value={addPolicyData.description}
								onChange={this.changeDialogDataHandle.bind(this, "description", "input")}
								placeholder={policyListLang.policyModal("enterPolicyDescription")} // lang: 请输入策略描述
							/>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(PolicyModal);
