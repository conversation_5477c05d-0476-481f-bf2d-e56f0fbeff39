@import "../base/variables";

@commonMenuHeight: 40px;
:global {
	.basic-layout {
		.left-menu {
			& {
				padding-left: 0;
				margin-bottom: 0;
				margin-top: 18px;
			}
			.group-item {
				h3 {
					& {
						position: relative;
						margin-bottom: 0;
						height: @commonMenuHeight;
						line-height: @commonMenuHeight;
						font-size: 15px;
						color: #7e8081;
						cursor: pointer;
						font-weight: normal;
						overflow: hidden;
					}

					.icon1 {
						position: relative;
						top: -2px;
						vertical-align: middle;
						margin-left: 24px;
						margin-right: 12px;
						font-size: 18px;
						color: #737b87;
					}
					.icon2 {
						position: absolute;
						top: 13px;
						right: 20px;
						font-size: 14px;
						color: #8b94ab;
					}
				}
				.menu-list {
					& {
						padding-left: 0;
						margin-bottom: 0;
						border-bottom: 1px solid #e6e6e6;
					}
					li {
						& {
							position: relative;
							height: @commonMenuHeight;
							line-height: @commonMenuHeight;
							cursor: pointer;
						}
						&.active:before {
							position: absolute;
							left: 0;
							top: 0;
							width: 4px;
							height: 100%;
							content: "";
							background: @blue;
						}
						&.active {
							a {
								background: #f0f0f0;
								span {
									color: @blue;
								}
							}
						}
						a {
							& {
								display: block;
								text-decoration: none;
							}
							&:hover {
								text-decoration: none;
								background: #f5f5f5;
								span {
									color: @blue;
								}
							}
							span {
								display: block;
								margin-left: 56px;
								font-size: 14px;
								color: #7e8081;
							}
						}
					}
				}
			}
			.menu-arrow-right {
				position: absolute;
				top: 13px;
				right: 10px;
				font-size: 14px;
				color: #8b94ab;
			}
		}
	}
	.ant-menu-submenu-popup.ant-menu-submenu > .ant-menu {
		background-color: #fff;
	}
	.ant-menu-submenu-title {
		padding-left: 0 !important;
	}

	.ant-layout-sider-children .ant-menu {
		.ant-menu-submenu {
			.ant-menu-submenu-title {
				height: @commonMenuHeight;
				line-height: @commonMenuHeight;
				margin-bottom: 0;
				margin-top: 0;
			}
			.ant-menu {
				.ant-menu-item {
					& {
						margin-bottom: 0;
						margin-top: 0;
						height: @commonMenuHeight;
						line-height: @commonMenuHeight;
					}
				}
			}
		}
	}

	.ant-layout-sider-children >
	.ant-menu-inline-collapsed >
	.ant-menu-submenu .ant-menu-submenu-title {
		padding: 0 24px 0 8px !important;
	}
	.basic-layout .left-menu {
		&:hover {
			background: rgba(92, 140, 255, 0.10);
		}
	}
	.ant-menu-submenu.ant-menu-submenu-popup.ant-menu-light.left-menu {
		left: 83px !important;
	}
	.ant-menu-submenu.ant-menu-submenu-popup.ant-menu-light.left-menu.ant-menu-submenu-placement-rightTop {
		left: 83px !important;
	}
}
