import { PureComponent } from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Input, Form, Button, Select, Row, Col, Tag } from "antd";
import { CommonConstants } from "@/constants";
import { policyDetailLang } from "@/constants/lang";
import WeightModeEditor from "../../WeightModeEditor";

const Option = Select.Option;
const { TextArea } = Input;

class Basic extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { globalStore, policyDetail } = this.props;
		let { allMap, policyModel } = globalStore;
		let { policyTags = [] } = allMap;

		let tagList = [];
		if (policyDetail && policyDetail.tags) {
			tagList = policyDetail.tags.split(",");
		}
		return (
			<div className="basic-form">
				<Form>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 策略名称： */}
							{policyDetailLang.basicSetup("policyName")}：
						</Col>
						<Col span={10}>
							<Input
								type="text"
								placeholder={policyDetailLang.basicSetup("inputPolicyName")} // 请输入策略名称
								value={policyDetail.name}
								disabled={true}
							/>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 应用名： */}
							{policyDetailLang.basicSetup("applyName")}：
						</Col>
						<Col span={10}>
							<Select
								disabled={true}
								value={policyDetail.appName || undefined}
							>
								{
									allMap && allMap["appNames"] && allMap["appNames"].map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 事件类型： */}
							{policyDetailLang.basicSetup("eventType")}：
						</Col>
						<Col span={10}>
							<Select disabled={true} value={policyDetail.riskEventType || undefined}>
								{
									allMap && allMap["EventType"] && allMap["EventType"].map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 事件标识： */}
							{policyDetailLang.basicSetup("eventFlag")}：
						</Col>
						<Col span={10}>
							<Input
								type="text"
								placeholder={policyDetailLang.basicSetup("inputEventFlag")} // 请输入事件标识
								value={policyDetail.riskEventId || undefined}
								disabled={true}
							/>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 风险类型： */}
							{policyDetailLang.basicSetup("riskType")}：
						</Col>
						<Col span={10}>
							<Select disabled={true} value={policyDetail.riskType || undefined}>
								{
									allMap && allMap[policyDetail.riskEventType] && allMap[policyDetail.riskEventType].map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* 策略模式： */}
							{policyDetailLang.basicSetup("strategyPattern")}：
						</Col>
						<Col span={10}>
							<Select disabled={true} value={policyDetail.mode || undefined}>
								{
									policyModel && policyModel.map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					</Row>
					{
						policyDetail.mode === "Weighted" &&
                        <Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
                        	<Col span={6} className="basic-info-title">
                        		{/* 风险阈值： */}
                        		{policyDetailLang.basicSetup("riskThreshold")}：
                        	</Col>
                        	<Col span={18}>
                        		<WeightModeEditor
                        			dealTypeMappings={policyDetail && policyDetail.dealTypeMappings ? policyDetail.dealTypeMappings : []}
                        			page="basicPage"
                        			disabled={true}
                        		/>
                        	</Col>
                        </Row>
					}
					{
						tagList.length > 0 &&
                        <Row gutter={CommonConstants.gutterSpan}>
                        	<Col span={6} className="basic-info-title">
                        		{/* 策略标签： */}
                        		{policyDetailLang.basicSetup("tags")}：
                        	</Col>
                        	<Col span={10} style={{ lineHeight: "32px" }}>
                        		{
                        			tagList.map((tag, index) => {
                        				let tagObj = policyTags.find(policyTag => policyTag.name === tag);
                        				if (tagObj) {
                        					return (
                        						<Tag
                        							key={index}
                        							color={CommonConstants.tagColor[index] ? CommonConstants.tagColor[index] : "blue"}
                        						>
                        							{tagObj.dName}
                        						</Tag>
                        					);
                        				}
                        			})
                        		}
                        	</Col>
                        </Row>
					}
					<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
						<Col span={6} className="basic-info-title">
							{/* 描述： */}
							{policyDetailLang.basicSetup("describe")}：
						</Col>
						<Col span={12}>
							<TextArea
								rows={4}
								value={policyDetail.description}
								disabled={true}
							/>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan} style={{ clear: "both" }}>
						<Col span={6}></Col>
						<Col span={10} className="basic-info-btns">
							<Button type="primary" disabled={true}>
								{/* 保存 */}
								{policyDetailLang.basicSetup("save")}
							</Button>
							<Button onClick={() => {
								let { dispatch } = this.props;
								if (process.env.SYS_ENV === "development") {
									dispatch(routerRedux.push("/policy/policyList"));
								} else {
									dispatch(routerRedux.push("/index/policy/policyList"));
								}
							}}>
								{/* 返回 */}
								{policyDetailLang.basicSetup("goBack")}
							</Button>
						</Col>
					</Row>
				</Form>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(Basic);
