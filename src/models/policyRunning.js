import { message } from "antd";
import { policyRunningAPI, policyEditorAP<PERSON> } from "@/services";

export default {
	namespace: "policyRunning",
	state: {
		policySetsListLoad: true,
		policySetsList: [],
		curPage: 1,
		pageSize: 10,
		total: 0,
		pages: 0,
		expandedRowKeys: [],
		dialogShow: {
			addPolicyReplay: false,
			modifyPolicyReplay: false,
			publicPolicy: false,
			outputParams: false
		},
		searchData: {
			searchField: "name",
			searchValue: null,
			tag: ""
		},
		dialogData: {
			policyReplayData: {
				policySetUuid: null,
				taskName: null,
				taskStartAt: null,
				taskFrom: null,
				taskTo: null,
				taskTarget: null,
				taskExecuteType: "RIGHTNOW",
				replayTaskStatus: null,
				testTarget: {},
				fieldFilters: null,
				dataSourceType: null
			},
			policyRulesSet: {},
			publicPolicyData: {},
			outputParamsData: {
				returnFields: []
			}
		},
		curUuid: ""
	},
	effects: {
		*getPolicySets({ payload }, { call, put }) {
			yield put({
				type: "setAttrValue",
				payload: {
					policySetsListLoad: true
				}
			});
			let response = yield call(policyRunningAPI.getPolicySets, payload);
			yield put({
				type: "setAttrValue",
				payload: {
					policySetsListLoad: false
				}
			});
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initPolicySets",
				payload: response
			});
		},
		*getVersionPolicys({ payload }, { call, put }) {
			let response = yield call(policyRunningAPI.getVersionPolicys, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let { data = [] } = response;
			data = data && data.map((parent = {}) => {
				if (parent.rules) {
					parent.rules = parent.rules.filter((child = {}) => {
						return Number(child.valid) === 1;
					});
				}
				return parent;
			});
			yield put({
				type: "setDialogData",
				payload: {
					policyRulesSet: data || []
				}
			});
		},
		*changePolicySetStatus({ payload }, { call, put, select }) {
			const { taskUuid } = payload;
			const {policySetsList} = yield select(state => state.policyRunning);
			const response = yield call(policyRunningAPI.getReplayTaskDetail, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const obj = policySetsList.find(item => String(item.taskUuid) === String(taskUuid));
			const { data } = response || {};
			const {taskStatus, totalAmount, totalCount, completeCount} = data || {};
			if (obj) {
				obj["replayTaskStatus"] = taskStatus;
				obj["totalAmount"] = totalAmount;
				obj["totalCount"] = totalCount;
				obj["completeCount"] = completeCount;
			}
			yield put({
				type: "setAttrValue",
				payload: {
					policySetsList
				}
			});
		},
		*getCustomReturnFields({ payload }, { call, put }) {
			let response = yield call(policyEditorAPI.customReturnFieldsList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			const { data} = response;
			yield put({
				type: "setDialogData",
				payload: {
					outputParamsData: {
						returnFields: data || []
					}
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setSearchData(state, action) {
			let { payload } = action;
			let { searchData } = state;

			for (let key in payload) {
				if (payload[key] !== undefined) {
					searchData[key] = payload[key];
				}
			}

			return {
				...state,
				searchData: searchData
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		initPolicySets(state, action) {
			let { policySetsList, curPage, pageSize, total, pages } = state;
			let data = action.payload.data || null;
			let list = data["dataList"] || [];
			curPage = data.curPage;
			pageSize = data.pageSize;
			total = data.total;
			pages = data.pages;

			policySetsList = list || [];

			return {
				...state,
				policySetsList: policySetsList,
				curPage: curPage,
				pageSize: pageSize,
				total: total,
				pages: pages
			};
		}
	}
};
