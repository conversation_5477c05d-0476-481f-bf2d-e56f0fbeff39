import { getApiDetailHeader, getHeader, getUrl, deleteEmptyObjItem } from "./common";
import { downloadFileHandle } from "../utils/request";
import request, { PostForm } from "../utils/request";

const getPolicySets = async(params) => {
	return request(getUrl("/admin/policySets", params), {
		method: "GET",
		headers: getHeader()
	});
};
const deletePolicySet = async(uuid) => {
	let params = {
		uuid: uuid
	};
	return request(getUrl("/admin/policySets/single", params), {
		method: "DELETE",
		headers: getHeader()
	});
};
const deletePolicy = async(params) => {
	return request(getUrl("/admin/policys/single", params), {
		method: "DELETE",
		headers: getHeader()
	});
};
const getPolicySetDetail = async(params) => {
	return request(getUrl("/admin/policySets/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};
const modifyPolicySet = async(params) => {
	return request("/admin/policySets/detail", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const addPolicy = async(params) => {
	return request("/admin/policys", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const modifyPolicy = async(params) => {
	return request("/admin/policys/detail", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const addPolicySet = async(params) => {
	return request("/admin/policySets", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
// 导出规则列表的测试
const exportRulesTest = async(params) => {
	return request(getUrl("/admin/policys/export", params), {
		method: "GET",
		headers: getHeader()
	});
};
// 导出规则列表
const exportRules = async(params) => {
	return downloadFileHandle(getUrl("/admin/policys/export", params), {
		method: "GET",
		headers: getHeader()
	}, params.fileName, params.fileType);
};
// 导入规则列表
const importRules = async(params) => {
	const res = await PostForm("/admin/policys/import", "post", { ...params });
	return res;
};
// 导出策略列表前的测试
const exportPolicySetsTest = async(params) => {
	return request(getUrl("/admin/policySets/export", params), {
		method: "GET",
		headers: getHeader()
	});
};
// 导出策略列表
const exportPolicySets = async(params) => {
	return downloadFileHandle(getUrl("/admin/policySets/export", params), {
		method: "GET",
		headers: getHeader()
	}, params.fileName, params.fileType);
};
// 导入策略列表
const importPolicySets = async(params) => {
	const res = await PostForm("/admin/policySets/import", "post", { ...params });
	return res;
};

const getPolicyDetail = async(params) => {
	return request(getUrl("/admin/policys/detail", params), {
		method: "GET",
		headers: getHeader()
	});
};
const getVersionPolicyDetail = async(params) => {
	return request(getUrl("/admin/policys/versionPolicyDetail", params), {
		method: "GET",
		headers: getHeader()
	});
};

const getTestFields = async(params) => {
	return request(getUrl("/admin/policySets/testFields", params), {
		method: "GET",
		headers: getApiDetailHeader()
	});
};
const policyTest = async(params) => {
	return request(getUrl("/riskService", params), {
		method: "GET",
		headers: {
			...getApiDetailHeader(),
			DETAILRESPONSE: 1
		}
	});
};
// 查询策略集自定义出参
const customReturnFieldsList = async(params) => {
	return request(getUrl("/admin/policySets/customReturnFields", params), {
		method: "GET",
		headers: getApiDetailHeader()
	});
};
// 修改策略集自定义出参
const customReturnFields = async(params) => {
	return request("/admin/policySets/customReturnFields", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
export default {
	getPolicySets,
	getPolicySetDetail,
	modifyPolicySet,
	deletePolicySet,
	modifyPolicy,
	deletePolicy,
	getPolicyDetail,
	getVersionPolicyDetail,
	addPolicySet,
	addPolicy,
	exportRulesTest,
	exportRules,
	importRules,
	importPolicySets,
	exportPolicySetsTest,
	exportPolicySets,
	getTestFields,
	policyTest,
	customReturnFieldsList,
	customReturnFields
};
