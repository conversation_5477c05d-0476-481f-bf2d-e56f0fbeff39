import { PureComponent } from "react";
import { Row, Col, message, Tooltip } from "antd";
import { CommonConstants, ImportConstants } from "@/constants";
import { imExportModeLang } from "@/constants/lang";

const { importFileType } = ImportConstants;

class CheckImportFile extends PureComponent {
	resetFile = () => {
		this.refs.policySetImportFile.value = "";
	};
	render() {
		const { importType, fileValue, changeFileValue } = this.props;
		return (
			<Row gutter={CommonConstants.gutterSpan} style={{ "height": "auto" }}>
				<Col span={5} className="basic-info-title line-height-32">
					{/* lang:选择文件 */}
					{imExportModeLang.importPolicyModal("selectFile")}：
				</Col>
				<Col span={19}>
					{/* lang:文件大小在100M以内 */}
					<Tooltip title={imExportModeLang.importPolicyModal("fileSizeTip")}>
						<a className="file line-height-32">
							{/* lang:选择文件 */}
							{imExportModeLang.importPolicyModal("selectFile")}
							<input
								type="file"
								ref="policySetImportFile"
								onChange={(e) => {
									let file = e.target && e.target.files && e.target.files[0] ? e.target.files[0] : undefined;
									if (fileValue) {
										if (!file) {
											return;
										}
									} else {
										if (!file) {
											// lang:请先选择文件
											message.warning(imExportModeLang.importPolicyModal("selectFileFirst"));
											return;
										}
									}
									let filePath = file.name;
									let fileSize = file.size / 1024;
									let reg = new RegExp(".(" + importFileType[importType] + ")$", "i");
									if (!reg.test(filePath)) { // 校验不通过
										// lang:只允许上传pls格式的文件
										message.warning(imExportModeLang.importPolicyModal(importFileType[importType] + "FileAllowedTip"));
										return;
									}
									if (fileSize > 100000) {
										// lang:文件大小请100M内
										message.warning(imExportModeLang.importPolicyModal("fileSizeTip"));
										return;
									}
									changeFileValue(file);
								}}
							/>
						</a>
					</Tooltip>
					<div>
						{fileValue ? fileValue["name"] : undefined}
					</div>
				</Col>
			</Row>
		);
	}
}

export default CheckImportFile;
