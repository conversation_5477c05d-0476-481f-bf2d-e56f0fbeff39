import { getApiDetailHeader, getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request, { PostForm, downloadFileHandle } from "../utils/request";

const getWorkflow = async(params) => {
	return request(getUrl("/admin/policySets/decisionFlow", params), {
		method: "GET",
		headers: getApiDetailHeader()
	});
};

const getVersionList = async(params) => {
	return request(getUrl("/admin/policySets/decisionFlow/history", params), {
		method: "GET",
		headers: getApiDetailHeader()
	});
};

const addWorkflow = async(params) => {
	return request("/admin/policySets/decisionFlow", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const modifyWorkflow = async(params) => {
	return request("/admin/policySets/decisionFlow", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};

const deleteWorkflow = async(params) => {
	return request(getUrl("/admin/policySets/decisionFlow", params), {
		method: "DELETE",
		headers: getApiDetailHeader()
	});
};

const getModelParamsByUuid = async(params) => {
	return request(getUrl("/admin/selectors/modelParam", params), {
		method: "GET",
		headers: getApiDetailHeader()
	});
};

const getWorkflowByVersion = async(params) => {
	return request(getUrl("/admin/policySets/decisionFlow/version", params), {
		method: "GET",
		headers: getApiDetailHeader()
	});
};

const workflowCommit = async(params) => {
	return request("/admin/policySets/decisionFlow/version", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

const switchWorkflowVersion = async(params) => {
	return request("/admin/policySets/decisionFlow/switch", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const exportWorkflowTest = async(params) => {
	return request(getUrl("/admin/policySets/decisionFlow/export", params), {
		method: "GET",
		headers: getHeader()
	});
};

const exportWorkflow = async(params) => {
	return downloadFileHandle(getUrl("/admin/policySets/decisionFlow/export", params), {
		method: "GET",
		headers: getHeader()
	}, params.fileName, params.fileType);
};

// 导入决策流
const importWorkflow = async(params) => {
	const res = await PostForm("/admin/policySets/decisionFlow/import", "post", { ...params });
	return res;
};

// ======================== service

const getServiceParamsByName = async(params) => {
	return request(getUrl("/admin/selectors/dataParam", params), {
		method: "GET",
		headers: getApiDetailHeader()
	});
};

// 申请下线规则流
const workFlowOffLine = async(params)=>{
	return request("/admin/policySets/offlineDecisionFlow", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
export default {
	getWorkflow,
	getVersionList,
	addWorkflow,
	modifyWorkflow,
	deleteWorkflow,
	getWorkflowByVersion,
	getModelParamsByUuid,
	workflowCommit,
	switchWorkflowVersion,
	exportWorkflowTest,
	exportWorkflow,
	importWorkflow,
	getServiceParamsByName,
	workFlowOffLine
};
