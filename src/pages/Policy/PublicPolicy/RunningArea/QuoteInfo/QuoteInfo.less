@header-height: 60px;

.quote-drawer-wrap {
	& {
		z-index: 99 !important;
	}

	.ant-drawer-content-wrapper {
		margin-top: @header-height;
	}

	.ant-drawer-header {
		display: none;
	}

	.quote-detail {
		& {
			top: @header-height;
			bottom: 0;
			right: 0;
			z-index: 99;
			background-color: #fff;
			overflow-x: hidden;
			overflow-y: auto;
		}

		.quote-detail-title {
			& {
				font-size: 20px;
				margin-bottom: 10px;
			}
			.title-close {
				float: right;
				position: relative;
				top: 7px;
				font-size: 14px;
				cursor: pointer;
				-webkit-transition: all 0.2s ease-out;
				-moz-transition: all 0.2s ease-out;
				-ms-transition: all 0.2s ease-out;
				-o-transition: all 0.2s ease-out;
			}

			.title-close:hover {
				font-size: 15px;
				color: #c90000;
				font-weight: bold;
			}

			.quote-status {
				position: relative;
				vertical-align: text-bottom;
				margin-left: 10px;
			}
		}

	}

	.quote-detail-base {
		border-top: 1px solid #ededed;
		padding-top: 10px;
		padding-bottom: 10px;
	}

	.quote-detail-group {
		& {
			padding: 3px 0;
			font-size: 14px;
		}

		label {
			color: #b4b4b4;
			display: inline-block;
			min-width: 80px;
			min-height: 21px;
			margin-right: 20px;
		}

		span {
			white-space: nowrap;
			width: 400px;
			overflow: hidden;
			text-overflow: ellipsis;
			-o-text-overflow: ellipsis;
			-webkit-text-overflow: ellipsis;
			-moz-text-overflow: ellipsis;
			white-space: nowrap;
			display: inline-block;
			vertical-align: bottom;
		}
	}


	.quote-detail-rule {
		& {
			margin-top:10px;
			margin-bottom: 20px;
		}
		h4 {
			margin-bottom: 12px;
			font-size: 15px;
		}
	}
	.effect-scope{
		max-width: 100%;
	}
	.no-quote{
		font-size:18px;
		color:#b4b4b4;
	}
}
