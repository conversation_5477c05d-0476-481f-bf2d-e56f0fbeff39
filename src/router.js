import { Router, Route, Switch } from "dva/router";
import { Spin } from "antd";
import dynamic from "dva/dynamic";
import cloneDeep from "lodash/cloneDeep";
import { getNavData } from "./common/nav";
import { getPlainNode } from "./utils/utils";
import Cookies from "universal-cookie";
import "./styles/index.less";
import config from "./common/config";

// 设置默认的加载组件
dynamic.setDefaultLoadingComponent(() => {
	return <Spin size="large" className="globalSpin" />;
});

function getRouteData(navData, path) {
	if (!navData.some(item => item.layout === path) ||
        !(navData.filter(item => item.layout === path)[0].children)) {
		return null;
	}
	let route = cloneDeep(navData.filter(item => {
		return item.layout === path;
	})[0]);
	let nodeList = getPlainNode(route.children);
	return nodeList;
}

function getLayout(navData, path) {
	if (!navData.some(item => item.layout === path) ||
        !(navData.filter(item => item.layout === path)[0].children)) {
		return null;
	}
	let route = navData.filter(item => item.layout === path)[0];
	return {
		component: route.component,
		layout: route.layout,
		name: route.name,
		path: route.path
	};
}

// 登录验证
function requireAuth(Layout, props, passProps) {
	// const cookies = new Cookies();
	// let token = cookies.get("_td_token_");
	// if (token) {
	// 	return <Layout {...props} {...passProps} />;
	// } else {
	// 	if (process.env.SYS_ENV === "development") {
	// 		return <Layout {...props} {...passProps} />;
	// 	} else {
	// 		const origin = window.location.origin;
	// 		const pathname = window.location.pathname;
	// 		const search = window.location.search;
	// 		const callbackUrl = origin + pathname + encodeURIComponent(search);
	// 		window.location = "/user/login?callbackUrl=" + callbackUrl;

	// 	}
	// }
	return <Layout {...props} {...passProps} />;
}

function RouterConfig({ history, app }) {
	let navData = getNavData(app);
	let BasicLayout = getLayout(navData, "BasicLayout").component;
	let PublishLayout = getLayout(navData, "PublishLayout").component;
	let passProps = {
		app,
		navData: navData.filter((item) => {
			return item.layout !== "UserLayout" && item.layout !== "PublishLayout";
		}), // 剔除掉无需登录模块
		getRouteData: (path) => {
			return getRouteData(navData, path);
		}
	};

	return (
		<Router history={history}>
			<Switch>
				<Route path={config.routerPrefix} render={props => requireAuth(PublishLayout, props, passProps)} />
				{
					process.env.SYS_ENV === "development" &&
                    <Route path="/" render={props => requireAuth(BasicLayout, props, passProps)} />
				}
			</Switch>
		</Router>
	);
}

export default RouterConfig;
