import { PureComponent } from "react";
import { connect } from "dva";
import { indexEditorAPI } from "@/services";
import { indexListLang } from "@/constants/lang";
import ImportCommon from "./ImportCommon";

class IndexImportModal extends PureComponent {
	changeImport = (e, fileName, type = "input") => {
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		let { indexEditorStore, dispatch } = this.props;
		let { dialogData } = indexEditorStore;
		let { indexImportData } = dialogData;
		const newIndexImportData = { ...indexImportData };

		newIndexImportData[fileName] = value;
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				indexImportData: newIndexImportData
			}
		});
	}

	importCallback = () => {
		let { indexEditorStore, dispatch, globalStore } = this.props;
		let { currentApp } = globalStore;
		let { curPage, pageSize, searchParams } = indexEditorStore;
		// 导入成功后
		// 1.重新刷新编辑区指标列表
		let appName;
		if (currentApp.name) {
			appName = currentApp.name;
		}
		searchParams["app"] = appName;
		dispatch({
			type: "indexEditor/getSalaxyList",
			payload: {
				curPage: curPage,
				pageSize: pageSize,
				...searchParams
			}
		});
		// 2.清空每个策略详情
		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				indexActiveKey: [],
				currentIndexId: null,
				indexActiveKeyLoading: false,
				currentLoadingId: null,
				indexActiveIndex: null,
				editIndexMap: {}
			}
		});

	}

	closeModal() {
		let { dispatch } = this.props;
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				indexImport: false
			}
		});
		dispatch({
			type: "indexEditor/setDialogData",
			payload: {
				indexImportData: {
					importMode: "SKIP",
					file: null,
					replaceScene: "",
					zbMode: ""
				}
			}
		});
	}

	render() {
		let { indexEditorStore } = this.props;
		let { dialogShow, dialogData } = indexEditorStore;
		let { indexImportData } = dialogData;
		return (
			<ImportCommon
				title={indexListLang.importIndexModal("indexImport")} // lang:指标导入
				modalData={indexImportData}
				importType="index"
				visible={dialogShow.indexImport}
				changeField={this.changeImport.bind(this)}
				onCancel={this.closeModal.bind(this)}
				importCallback={this.importCallback.bind(this)}
				fetchMethod={indexEditorAPI.importIndex}
			/>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor
}))(IndexImportModal);
