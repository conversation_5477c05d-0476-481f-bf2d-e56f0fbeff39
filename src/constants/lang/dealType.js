import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"title": {
			"cn": "风险决策列表",
			"en": "Deal Type List"
		},
		"add": {
			"cn": "新增风险决策",
			"en": "Add Deal Type"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"name": {
			"cn": "风险决策名称",
			"en": "Deal Type Name"
		},
		"identifier": {
			"cn": "风险决策标识",
			"en": "Deal Type Identifier"
		},
		"level": {
			"cn": "风险决策等级",
			"en": "Deal Type Level"
		},
		"isDefault": {
			"cn": "是否默认",
			"en": "Whether the default"
		},
		"color": {
			"cn": "颜色",
			"en": "Color"
		},
		"operator": {
			"cn": "操作",
			"en": "Operator"
		},
		"yes": {
			"cn": "是",
			"en": "Yes"
		},
		"no": {
			"cn": "否",
			"en": "No"
		},
		"edit": {
			"cn": "编辑",
			"en": "Edit"
		},
		"delete": {
			"cn": "删除",
			"en": "Delete"
		},
		"editTip": {
			"cn": "编辑风险决策",
			"en": "Edit deal type"
		},
		"deleteTip": {
			"cn": "删除风险决策",
			"en": "Delete deal type"
		}
	};
	return params[field][lang];
};
const modal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"deleteTip": {
			"cn": "删除风险决策提醒",
			"en": "Remove deal type reminders"
		},
		"addSuccessTip": {
			"cn": "新增风险决策成功",
			"en": "Add deal type successfully"
		},
		"deleteSuccessTip": {
			"cn": "删除风险决策成功",
			"en": "Remove deal type successfully"
		},
		"modifySuccessTip": {
			"cn": "修改风险决策成功",
			"en": "Modify deal type successfully"
		},
		"isDefaultDealTypeTip": {
			"cn": "当前为默认风险决策无法删除",
			"en": "The current default deal type cannot be delete"
		},
		"isDefaultDealTypeTip2": {
			"cn": "当前为默认风险决策无法进行修改",
			"en": "The current default deal type cannot be modified"
		},
		"addDealType": {
			"cn": "新增风险决策",
			"en": "Add Deal Type"
		},
		"modifyDealType": {
			"cn": "修改风险决策",
			"en": "Modify Deal Type"
		},
		"name": {
			"cn": "风险决策名称：",
			"en": "Name:"
		},
		"namePlaceholder": {
			"cn": "请输入风险决策名称",
			"en": "Please enter deal type name"
		},
		"identifier": {
			"cn": "风险决策标识：",
			"en": "Identifier:"
		},
		"identifierPlaceholder": {
			"cn": "请输入风险决策标识",
			"en": "Please enter deal type identifier"
		},
		"level": {
			"cn": "风险决策等级：",
			"en": "Level:"
		},
		"levelPlaceholder": {
			"cn": "请输入风险决策等级",
			"en": "Please enter deal type level"
		},
		"levelTip": {
			"cn": "注：风险决策等级数字越大等级越高",
			"en": "Note: the higher the number of risk decision grade, the higher the grade"
		},
		"color": {
			"cn": "颜色值：",
			"en": "Color:"
		},
		"colorPlaceholder": {
			"cn": "请选择颜色值",
			"en": "Please select a color value"
		},
		// 新增或修改判空
		"nameCannotEmpty": {
			"cn": "风险决策名称不能为空",
			"en": "Name cannot be empty"
		},
		"nameLengthTip": {
			"cn": "风险决策名称长度不能超过24个字符",
			"en": "Name cannot be longer than 24 characters"
		},
		"identifierCannotEmpty": {
			"cn": "风险决策标识不能为空",
			"en": "Identifier cannot be empty"
		},
		"identifierLengthTip": {
			"cn": "风险决策标识长度不能超过32个字符",
			"en": "Identifier cannot be longer than 24 characters"
		},
		"levelCannotEmpty": {
			"cn": "风险决策等级不能为空",
			"en": "Level cannot be empty"
		},
		"intType": {
			"cn": "风险决策等级只能输入正整数",
			"en": "Only positive integer can be entered for risk decision level"
		},
		"colorCannotEmpty": {
			"cn": "颜色值不能为空",
			"en": "Color cannot be empty"
		},
		"color1": {
			"cn": "纯色",
			"en": "Pure color"
		},
		"color2": {
			"cn": "渐变色",
			"en": "Gradation"
		}
	};
	return params[field][lang];
};

const deleteDescription = (title) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;

	let cn = "您真的要删除《" + title + "》吗？";
	let en = "Do you really want to delete " + title + "?";
	return personalMode && personalMode.lang === "cn" ? cn : en;
};

const colors = [
	"#4D4D4D", "#999999", "#ECECEC", "#F44E3B", "#FE9200", "#FCDC00", "#DBDF00", "#A4DD00", "#68CCCA", "#73D8FF", "#AEA1FF", "#FDA1FF",
	"#333333", "#808080", "#cccccc", "#D33115", "#E27300", "#FCC400", "#B0BC00", "#05BE88", "#16A5A5", "#1890ff", "#7B64FF", "#FA28FF",
	"#000000", "#666666", "#B3B3B3", "#9F0500", "#C45100", "#F8A618", "#808900", "#194D33", "#0C797D", "#0062B1", "#653294", "#AB149E"
];
const linerColors = [
	"linear-gradient(to right, rgb(5, 190, 136), rgb(18, 213, 168))", "linear-gradient(to right, rgb(252, 82, 75), rgb(254, 114, 118))",
	"linear-gradient(to right, rgb(251, 130, 60), rgb(249, 200, 52))", "linear-gradient(to right, rgb(243, 112, 37), rgb(249, 215, 112))",
	"linear-gradient(to right, rgb(11, 97, 219), rgb(104, 165, 249))", "linear-gradient(to right, rgb(248, 166, 24), rgb(248, 224, 94))",
	"linear-gradient(to right, rgb(20, 192, 57), rgb(81, 236, 131))", "linear-gradient(to right, rgb(241, 17, 8), rgb(232, 176, 189))",
	"linear-gradient(to right, rgb(174, 95, 255), rgb(185, 157, 255))"
];
export default {
	common,
	table,
	modal,
	deleteDescription,
	colors,
	linerColors
};
