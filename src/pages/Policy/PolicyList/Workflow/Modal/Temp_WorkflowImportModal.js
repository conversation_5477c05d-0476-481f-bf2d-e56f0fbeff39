import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Modal, Button, Row, Col, message, Tooltip } from "antd";
import { workflowAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { workflowLang } from "@/constants/lang";
import ImportMode from "@/components/ImportModal/FileImportCondition/ImportMode";
import SelectScene from "@/components/ImportModal/FileImportCondition/SelectScene";
import FileImportResult from "@/components/ImportModal/FileImportResult";

class WorkflowImportModal extends PureComponent {
	state = {
		stateFile: null,
		replaceScene: "",
		policyMode: "",
		zbMode: "",
		hasSuccess: false,
		successData: {},
		importLoad: false
	};

	constructor(props) {
		super(props);
		this.importWorkflow = this.importWorkflow.bind(this);
		this.closeModal = this.closeModal.bind(this);
	}
	changeImport = (e, { type, valueType = "input" }) => {
		let value = "";
		if (valueType === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		this.setState({
			[type]: value
		});
	}
	importWorkflow = async () => {
		let { stateFile, zbMode, policyMode, replaceScene } = this.state;
		let { workflowStore, policyEditorStore, refreshWorkflow, dispatch, globalStore } = this.props;
		let { policySetItem } = workflowStore;

		if (!policyMode) {
			message.warning(workflowLang.workflowImportModal("policyModeRequired")); // lang:请选择导入策略模式
			return;
		}
		if (!zbMode) {
			message.warning(workflowLang.workflowImportModal("zbModeRequired")); // lang:请选择导入指标模式
			return;
		}
		if (!stateFile) {
			message.warning(workflowLang.workflowImportModal("selectFileMessage4")); // lang:请选择要上传的文件！
			return;
		}
		let params = {
			policySetUuid: policySetItem.uuid,
			file: stateFile,
			zbMode,
			policyMode
		};
		if (replaceScene) {
			params.replaceScene = JSON.stringify(replaceScene);
		}

		await this.setState({
			importLoad: true
		});
		await workflowAPI.importWorkflow(params).then(res => {
			this.setState({
				importLoad: false
			});
			if (res.success) {
				this.refs.workflowImportFile.value = "";
				this.setState({
					stateFile: null,
					hasSuccess: true,
					successData: res.data || {}
				});
				// 导入成功后
				// 1. 刷新工作流流程
				refreshWorkflow();
				// 2. 刷新allMap
				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
				// 3. 刷新编辑区策略列表
				let { currentApp } = globalStore;
				if (currentApp.name) {
					let { curPage, pageSize } = policyEditorStore;
					dispatch({
						type: "policyEditor/getPolicySets",
						payload: {
							appName: currentApp.name ? currentApp.name : null,
							curPage: curPage,
							pageSize: pageSize
						}
					});
				}
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			this.setState({
				importLoad: false
			});
			console.log(err);
		});
	}

	closeModal() {
		let { dispatch } = this.props;

		dispatch({
			type: "workflow/setDialogShow",
			payload: {
				WorkflowImport: false
			}
		});
		this.setState({
			stateFile: null,
			replaceScene: "",
			policyMode: "",
			zbMode: "",
			hasSuccess: false,
			successData: {},
			importLoad: false
		});
	}

	render() {
		const { state } = this;
		let { stateFile, successData = {}, hasSuccess, importLoad } = state;
		let { workflowStore } = this.props;
		let { dialogShow } = workflowStore;
		const { replaceScene } = dialogShow || {};
		let footerButton = [];
		if (hasSuccess) {
			footerButton = [
				<Button type="primary" onClick={this.closeModal.bind(this)}>
					{/* lang:确定 */}
					{workflowLang.common("ok")}
				</Button>
			];
		} else {
			footerButton = [
				<Button onClick={this.closeModal.bind(this)}>
					{/* lang:取消*/}
					{workflowLang.common("cancel")}
				</Button>,
				<Button
					type="primary"
					onClick={this.importWorkflow.bind(this)}
					loading={importLoad}
					disabled={importLoad}
				>
					{/* lang:上传*/}
					{workflowLang.workflowImportModal("upload")}
				</Button>
			];
		}

		return (
			<Modal
				title={workflowLang.workflowImportModal("title")} // lang:"规则流导入"
				visible={dialogShow.WorkflowImport}
				maskClosable={false}
				onCancel={this.closeModal.bind(this)}
				className="file-import-modal-wrap"
				footer={footerButton}
				width={650}
			>
				<div className="basic-form s2">
					{
						!hasSuccess &&
						<Fragment>
							{/* 导入场景 */}
							<ImportMode
								changeImport={this.changeImport}
								data={{ ...state }}
								showMode={{ "zbMode": true, "policyMode": true, "workflowMode": true }}
							/>
							<Row gutter={CommonConstants.gutterSpan}>
								<Col span={5} className="basic-info-title line-height-32">
									{/* lang:选择文件：*/}
									{workflowLang.workflowImportModal("selectFile")}：
    					        </Col>
								<Col span={19}>
									<Tooltip
										title={workflowLang.workflowImportModal("selectFileMessage5")} // lang:"文件大小在100M以内"
									>
										<a className="file line-height-32">
											{/* lang:选择文件：*/}
											{workflowLang.workflowImportModal("selectFile")}
											<input
												type="file"
												ref="workflowImportFile"
												onChange={(e) => {
													let file = e.target && e.target.files && e.target.files[0] ? e.target.files[0] : undefined;
													if (stateFile) {
														if (!file) {
															return;
														}
													} else {
														if (!file) {
															message.warning(workflowLang.workflowImportModal("selectFileMessage1")); // lang:请先选择文件
															return;
														}
													}
													let filePath = file.name;
													let fileSize = file.size / 1024;
													let reg = new RegExp(".(pld)$", "i");
													if (!reg.test(filePath)) { // 校验不通过
														message.warning(workflowLang.workflowImportModal("selectFileMessage2")); // lang:"只允许上传pld格式的文件");
														return;
													}
													if (fileSize > 100000) {
														message.warning(workflowLang.workflowImportModal("selectFileMessage3")); // lang:"文件大小请100M内");
														return;
													}

													this.setState({
														stateFile: file
													});
												}}
											/>
										</a>
									</Tooltip>
									<div className="mb10">
										{stateFile ? stateFile["name"] : undefined}
									</div>
								</Col>
							</Row>
							{/* 选择场景 */}
							<SelectScene
								changeImport={this.changeImport}
								data={{ ...state }}
								type="workflow"
							/>
						</Fragment>
					}
					{
						hasSuccess &&
						<FileImportResult
							type="workflow"
							showMode={{ "zbMode": true, "policyMode": true, "ruleMode": true }}
							successData={successData}
							replaceScene={replaceScene}
						/>
					}
				</div>

			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor,
	workflowStore: state.workflow
}))(WorkflowImportModal);
