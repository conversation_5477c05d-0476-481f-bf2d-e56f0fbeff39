import { Icon, Popover } from "antd";
import Zmage from "react-zmage";
import { indexListLang } from "@/constants/lang";
import "./index.less";

export default (props) => {
	const { demo, demoPic } = props.advanceConfig || {};
	return (
		<Popover
			placement="right"
			arrowPointAtCenter={false}
			title={
				<div className="text-overflow index-demo-title">
					{/* 指标样例 */}
					{indexListLang.example("indexExample")}
				</div>
			}
			content={
				<div className="index-demo-wrap">
					<div className="index-demo-detail">
						<h5>
							{/* 指标示例： */}
							{indexListLang.example("indexDemo")}
						</h5>
						<div className="demo-text">{demo}</div>
					</div>
					<div className="index-demo-detail">
						<h5>
							{/* 样例图片： */}
							{indexListLang.example("sampleImg")}
						</h5>
						<Zmage
							backdrop="linear-gradient(50deg, rgba(0,0,0,0.8) 0%, rgba(200,200,200,0.3) 100%)"
							zIndex={1500}
							src={demoPic}
						/>
					</div>
				</div>
			}
		>
			<Icon type="read" className="param-tip-icon" />
		</Popover>
	);
};
