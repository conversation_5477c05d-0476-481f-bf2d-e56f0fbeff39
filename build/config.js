const path = require("path");
const assetsPublicPath = "/";
const PORT = process.env.PORT || 8000;
const sourcePrefix = "salaxy-resource/";
const publicPath = "/salaxy-resource/";

module.exports = {
	common: {
		htmlTemplatePath: path.resolve(__dirname, "../src/index.ejs"),
		sourcePrefix: sourcePrefix
	},
	dev: {
		hot: true,
		assetsSubDirectory: sourcePrefix + "/static",
		assetsPublicPath: "/",
		// assetsPublicPath: publicPath,
		proxyTable: {
			"/bridgeApi": {
				// "target": "http://*************:8089",
				// "target": "http://************:8089",
				"target": "http://***********:30080",
				"changeOrigin": true,
				"pathRewrite": {
					// "^/bridgeApi": "/bridgeApi"
				}
			},
			"/indexApi": {
				// "target": "http://*************:8089",
				// "target": "http://************:8089",
				"target": "http://***********:30080",
				"changeOrigin": true,
				"pathRewrite": {
					// "^/indexApi": "/indexApi"
				}
			}

		},
		port: PORT,
		autoOpenBrowser: true,
		devtool: "eval-source-map",
		publicPath: publicPath,
		host: "0.0.0.0"
	},
	build: {
		assetsRoot: path.resolve(__dirname, "../dist"),
		assetsSubDirectory: sourcePrefix + "/static",
		assetsPublicPath,
		devtool: "source-map"
	}
};
