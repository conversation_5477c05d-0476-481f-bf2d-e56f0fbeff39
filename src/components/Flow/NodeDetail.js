import { PureComponent } from "react";
import { connect } from "dva";
import { Card, Button, Input, Row, Col, Select, Radio, Table, Tooltip, Icon } from "antd";
import ModelSetting from "./Modal/ModelSetting";
import ServiceSetting from "./Modal/ServiceSetting";
import "./style/model-setting-modal.less";
import { withPropsAPI } from "gg-editor";
import { workflowLang } from "@/constants/lang";

const Option = Select.Option;

const nodeTypeMap = {
	"flow-start": "START",
	"flow-end": "END",
	"flow-exclusivity": "CONDITION_START",
	"flow-parallel": "CONCURRENT_START",
	"flow-policy": "POLICY",
	"flow-model": "MODEL",
	"flow-data": "DATA",
	"flow-public-policy": "PUBLIC_POLICY"
};

class NodeDetail extends PureComponent {
	state = {
		changeLabelText: null,
		// 普通策略
		policyConfig: {
			policyUuid: null
		},
		// 公共策略
		publicPolicyConfig: {
			policyUuid: null
		},
		dataConfig: {
			invokeType: "byInterface", 		// 调用方式  byInterface(按接口) byType(按类型)
			interfaceName: null, // 接口标识  按接口时必填
			interfaceType: null, // 接口类型  按类型时必填
			invokeAccording: "priConfidence", // priCost(成本优先) priConfidence(置信度优先) 按类型时必填
			modelInput: [],
			modelOutput: []
		},
		modelConfig: {
			modelId: null,
			modelInput: [],
			modelOutput: []
		}
	};

	constructor(props) {
		super(props);
		this.modelSetting = this.modelSetting.bind(this);
		this.serviceSetting = this.serviceSetting.bind(this);
		this.changeNodeBase = this.changeNodeBase.bind(this);
		this.changeNodeConfigField = this.changeNodeConfigField.bind(this);
	}

	componentDidMount() {
		let { propsAPI } = this.props;
		let { getSelected, update, executeCommand } = propsAPI;
		let { policyConfig, modelConfig, dataConfig, publicPolicyConfig } = this.state;
		let item = getSelected()[0];

		if (!item) {
			return null;
		}

		let { shape, nodeType, nodeConfig } = item.getModel();

		let params = {};
		if (shape === "flow-policy") {
			if (!nodeConfig) {
				nodeConfig = policyConfig;
			}
			params["nodeConfig"] = nodeConfig;
			this.setState({
				policyConfig: nodeConfig
			});
		} else if (shape === "flow-model") {
			// 模型节点
			if (!nodeConfig) {
				nodeConfig = modelConfig;
			}
			params["nodeConfig"] = nodeConfig;
			this.setState({
				modelConfig: nodeConfig
			});
		} else if (shape === "flow-data") {
			if (!nodeConfig) {
				nodeConfig = dataConfig;
			}
			params["nodeConfig"] = nodeConfig;

			this.setState({
				dataConfig: nodeConfig
			});
		} else if (shape === "flow-public-policy") {
			if (!nodeConfig) {
				nodeConfig = publicPolicyConfig;
			}
			params["nodeConfig"] = nodeConfig;
			this.setState({
				publicPolicyConfig: nodeConfig
			});
		}

		if (!nodeType) {
			params["nodeType"] = nodeTypeMap[shape];
		}
		executeCommand(() => {
			update(item, {
				...params
			});
		});
	}
	// 策略集文字较多的场景拉伸输入框
	optimizeMultilineText = (text, font, maxWidth = 250) => {
		const canvas = document.createElement("canvas");
		const canvasContext = canvas.getContext("2d");
		canvasContext.font = font;
		const textLen = canvasContext.measureText(text);
		if (textLen <= maxWidth) {
			return text;
		}
		let multilineText = "";
		let multilineTextWidth = 0;
		let multilineTextWidthTemp = 0;
		let lineNum = 0;
		for (const char of text) {
			const { width } = canvasContext.measureText(char);
			if (multilineTextWidth + width >= maxWidth) {
				multilineText += "\n";
				multilineTextWidth = 0;
				lineNum++;
			}
			multilineText += char;
			multilineTextWidth += width;
			if (lineNum === 0) {
				multilineTextWidthTemp = multilineTextWidth;
			}
		}
		// 保持最小100宽
		let enterMaxWidth = (multilineTextWidthTemp + 40) > 100 ? (multilineTextWidthTemp + 80) : 100;
		let enterHei = 40;
		if (lineNum === 1) {
			enterHei += 10;
		}
		if (lineNum > 1) {
			enterHei += (lineNum - 1) * 20;
		}
		return {label: multilineText, size: [enterMaxWidth, enterHei]};
	}

	changeNodeBase(field, type, e) {
		let { propsAPI } = this.props;
		let { getSelected, executeCommand, update } = propsAPI;

		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		let item = getSelected()[0];
		if (!item) {
			return;
		}

		let params = {};
		// 针对label名称的场景处理，需要处理颜色显示
		if (field === "label" && typeof item.model.label !== "string") {
			params[field] = {
				...item.model.label,
				text: value
			};
			console.log(params);
		} else {
			params[field] = value;
		}

		let newParams = {};
		// 策略集才自动伸缩
		if (["PUBLIC_POLICY", "POLICY"].indexOf(item.model.nodeType) > -1) {
			let labelText = params.label;
			if (typeof labelText !== "string") {
				labelText = labelText.text;
			}
			newParams = this.optimizeMultilineText(labelText, "12px", 250);
		}
		executeCommand(() => {
			update(item, {
				...params,
				...newParams
			});
		});

		if (field === "nodeConfig" && e.modelId) {
			this.setState({
				modelConfig: value
			});
		}
		if (field === "nodeConfig" && e.serviceName) {
			this.setState({
				dataConfig: value
			});
		}
		// 如果修改的是节点名称，那修改state里面的changeLabelText
		if (field === "label") {
			this.setState({
				changeLabelText: value
			});
		}
	}

	changeNodeConfigField(field, type, e) {
		let { propsAPI, globalStore } = this.props;
		let { getSelected, executeCommand, update } = propsAPI;
		let { allMap } = globalStore;
		let { thirdDataSelect } = allMap;
		let { policyConfig, modelConfig, dataConfig, publicPolicyConfig } = this.state;

		let value;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		this.setState({
			test: value
		});

		let item = getSelected()[0];
		if (!item) {
			return;
		}
		let { shape, nodeConfig } = item.getModel();
		nodeConfig[field] = value;
		let params = {
			nodeConfig: nodeConfig
		};

		executeCommand(() => {
			update(item, {
				...params
			});
		});

		console.log(arguments);
		let selectObj;
		if (shape === "flow-policy") {
			policyConfig[field] = value; // 这种历史写法感觉有点问题，先标注但不删除
			this.setState({
				policyConfig: nodeConfig
			});
		} else if (shape === "flow-model") {
			modelConfig[field] = value;
			this.setState({
				modelConfig: nodeConfig
			});
		} else if (shape === "flow-data") {
			selectObj = thirdDataSelect && thirdDataSelect.find(item => item.name === value);
			dataConfig[field] = value;
			this.setState({
				dataConfig: dataConfig
			});
		} else if (shape === "flow-public-policy") {
			// publicPolicyConfig[field] = value;
			this.setState({
				publicPolicyConfig: nodeConfig
			});
		}

		if (selectObj) {
			this.changeNodeBase("label", "select", selectObj.dName);
			this.setState({
				changeLabelText: selectObj.dName
			});
		}
	}

	modelSetting() {
		let { dispatch } = this.props;

		dispatch({
			type: "workflow/setDialogShow",
			payload: {
				modelSetting: true
			}
		});
	}

	serviceSetting() {
		let { dispatch } = this.props;

		dispatch({
			type: "workflow/setDialogShow",
			payload: {
				serviceSetting: true
			}
		});
	}

	render() {
		let { globalStore, workflowStore, propsAPI, disabled } = this.props;
		let { getSelected } = propsAPI;
		let { policyConfig, modelConfig, dataConfig, changeLabelText, publicPolicyConfig } = this.state;
		let { allMap } = globalStore;
		let { modelSelect, thirdDataSelect } = allMap;
		let { policySetItem, dialogShow } = workflowStore;
		let item = getSelected()[0];
		const publicPolicyList = allMap[policySetItem.uuid + "_public_policys"] || []; // 公共策略列表数据源

		if (!item) {
			return null;
		}
		let { label, shape, nodeType } = item.getModel();
		let policySetUuid = policySetItem && policySetItem.uuid ? policySetItem.uuid : null;
		let nodeMap = {
			"flow-start": workflowLang.nodeName("start"), // lang:"开始节点",
			"flow-end": workflowLang.nodeName("end"), // lang:"结束节点",
			"flow-exclusivity": workflowLang.nodeName("exclusive"), // lang:"排他节点",
			"flow-parallel": workflowLang.nodeName("parallel"), // lang:"并行节点",
			"flow-policy": workflowLang.nodeName("policy"), // lang:"策略节点",
			"flow-model": workflowLang.nodeName("model"), // lang:"模型节点"
			"flow-data": workflowLang.nodeName("tripartite"), // lang:"三方节点"
			"flow-public-policy": workflowLang.nodeName("publicPolicy") // lang:"公共策略节点"
		};

		let nodeConfig;
		let selectServiceName;
		let selectPolicyName;
		if (shape && shape === "flow-policy") {
			nodeConfig = policyConfig;
			// 这里的判断逻辑是为了保证已经选择的列表项被删除后，不显示他的uuid等类似的标识
			let policyList = allMap && allMap[policySetUuid + "_policys_publish"] && allMap[policySetUuid + "_policys_publish"] ? allMap[policySetUuid + "_policys_publish"] : [];
			let policyUuid = nodeConfig && nodeConfig.policyUuid ? nodeConfig.policyUuid : null;
			if (policyList.find(fItem => fItem.name === policyUuid)) {
				selectPolicyName = policyUuid;
			}
		} else if (shape && shape === "flow-model") {
			nodeConfig = modelConfig;
		} else if (shape && shape === "flow-data") {
			nodeConfig = dataConfig;
			// 这里的判断逻辑是为了保证已经选择的列表项被删除后，不显示他的uuid等类似的标识
			if (allMap && allMap["thirdDataSelect"] && nodeConfig && nodeConfig.interfaceName) {
				if (allMap["thirdDataSelect"].find(fItem => fItem.name === nodeConfig.interfaceName)) {
					selectServiceName = nodeConfig.interfaceName;
				}
			}
		} else if (shape && shape === "flow-public-policy") {
			nodeConfig = publicPolicyConfig;
			// 这里的判断逻辑是为了保证已经选择的列表项被删除后，不显示他的uuid等类似的标识
			let policyUuid = nodeConfig && nodeConfig.policyUuid ? nodeConfig.policyUuid : null;
			if (publicPolicyList.find(fItem => fItem.name === policyUuid)) {
				selectPolicyName = policyUuid; // selectPolicyName为公共策略id 直接沿用了以前的写法，虽然命名有点歧义
			}
		}

		console.log(nodeConfig);
		let labelText = "";
		if (label && typeof label === "string") {
			labelText = label;
		} else {
			labelText = label.text;
		}
		return (
			<Card type="inner" title={nodeMap[shape]} bordered="false">
				<div className="param-list">
					{
						shape !== "flow-exclusivity" &&
						shape !== "flow-parallel" &&
						<Row className="param-item">
							<Col span={24} className="param-title">
								{/* lang:节点名称*/}
								{workflowLang.nodeDetail("nodeName")}
							</Col>
							<Col span={24} className="param-content">
								<Input
									placeholder={workflowLang.nodeDetail("nodeNamePlaceholder")} // lang:"请输入节点名称"
									defaultValue={labelText}
									value={changeLabelText || labelText}
									onChange={this.changeNodeBase.bind(this, "label", "input")}
									disabled={disabled}
								/>
							</Col>
						</Row>
					}
					{
						shape &&
						shape === "flow-exclusivity" &&
						<Row className="param-item">
							<Col span={24} className="param-title">
								{/* lang:排他类型*/}
								{workflowLang.nodeDetail("exclusiveType")}
							</Col>
							<Col span={24} className="param-content">
								<Radio.Group
									defaultValue={nodeType || "CONDITION_START"}
									buttonStyle="solid"
									className="radio-group-double"
									onChange={(e) => {
										let value = e.target.value;
										this.changeNodeBase("nodeType", "input", e);

										if (value === "CONDITION_START") {
											this.changeNodeBase("label", "select", "排他开始");
										} else {
											this.changeNodeBase("label", "select", "排他结束");
										}
									}}
									disabled={disabled}
								>
									<Radio.Button value="CONDITION_START">
										{/* lang:排他开始*/}
										{workflowLang.nodeDetail("exclusiveStart")}
									</Radio.Button>
									<Radio.Button value="CONDITION_END">
										{/* lang:排他结束*/}
										{workflowLang.nodeDetail("exclusiveEnd")}
									</Radio.Button>
								</Radio.Group>
							</Col>
						</Row>
					}
					{
						shape &&
						shape === "flow-parallel" &&
						<Row className="param-item">
							<Col span={24} className="param-title">
								{/* lang:并行类型*/}
								{workflowLang.nodeDetail("parallelType")}
							</Col>
							<Col span={24} className="param-content">
								<Radio.Group
									defaultValue={nodeType || "CONCURRENT_START"}
									buttonStyle="solid"
									className="radio-group-double"
									disabled={disabled}
									onChange={(e) => {
										let value = e.target.value;
										this.changeNodeBase("nodeType", "input", e);

										if (value === "CONCURRENT_START") {
											this.changeNodeBase("label", "select", "并行开始");
										} else {
											this.changeNodeBase("label", "select", "并行结束");
										}
									}}
								>
									<Radio.Button value="CONCURRENT_START">
										{/* lang:并行开始*/}
										{workflowLang.nodeDetail("parallelStart")}
									</Radio.Button>
									<Radio.Button value="CONCURRENT_END">
										{/* lang:并行结束*/}
										{workflowLang.nodeDetail("parallelEnd")}
									</Radio.Button>
								</Radio.Group>
							</Col>
						</Row>
					}
					{
						shape &&
						shape === "flow-policy" &&
						<Row className="param-item">
							<Col span={24} className="param-title">
								{/* lang:策略*/}
								{workflowLang.nodeDetail("policy")}
							</Col>
							<Col span={24} className="param-content">
								<Select
									placeholder={workflowLang.nodeDetail("policyPlaceholder")} // lang:"选择策略"
									value={selectPolicyName ? selectPolicyName : undefined}
									showSearch
									optionFilterProp="children"
									disabled={disabled}
									onChange={(e) => {
										this.changeNodeConfigField("policyUuid", "select", e);
										let policyList = allMap[policySetUuid + "_policys_publish"] ? allMap[policySetUuid + "_policys_publish"] : [];
										let policyObj = policyList.find(pItem => pItem.name === e);

										if (policyObj) {
											this.changeNodeBase("label", "select", policyObj.dName);
											this.setState({
												changeLabelText: policyObj.dName
											});
										}
									}}
								>
									{
										allMap &&
										allMap[policySetUuid + "_policys_publish"] &&
										allMap[policySetUuid + "_policys_publish"].map((item, index) => {
											return (
												<Option
													value={item.name}
													key={index}
												>
													{item.dName}
												</Option>
											);
										})
									}
								</Select>
							</Col>
						</Row>
					}
					{
						shape &&
						shape === "flow-model" &&
						<div className="model-node-detail">
							<Button
								style={{ width: "100%" }}
								onClick={this.modelSetting.bind(this)}
								className="mb20"
							>
								{disabled ? workflowLang.nodeDetail("viewModelConfig") : workflowLang.nodeDetail("clickConfigModel")}
							</Button>
							{
								nodeConfig.modelInput &&
								nodeConfig.modelInput.length > 0 &&
								<div className="model-section-wrap">
									<div className="model-section-header">
										<h3>
											{/* lang:入参配置*/}
											{workflowLang.nodeDetail("inputConfiguration")}
										</h3>
									</div>
									<div className="model-section-body">
										<Table
											columns={
												[
													{
														title: workflowLang.nodeDetail("fieldName"), // lang:"字段名",
														width: 150,
														dataIndex: "systemField",
														render: (text, record) => {
															return (
																<Tooltip title={record.systemFieldDName}>
																	{text}
																</Tooltip>
															);
														}
													},
													{
														title: workflowLang.nodeDetail("direction"), // lang:"方向",
														width: 100,
														render: (text, record, index) => {
															return (
																<Icon type="arrow-right" />
															);
														}
													},
													{
														title: workflowLang.nodeDetail("elementName"), // lang:"要素名",
														width: 150,
														dataIndex: "modelVar",
														render: (text, record) => {
															return (
																<Tooltip title={record.dName}>
																	{text}
																</Tooltip>
															);
														}
													}
												]
											}
											dataSource={nodeConfig.modelInput || []}
											pagination={false}
											rowKey="name"
											size="middle"
										/>
									</div>
								</div>
							}
							{
								nodeConfig.modelOutput &&
								nodeConfig.modelOutput.length > 0 &&
								<div className="model-section-wrap">
									<div className="model-section-header">
										<h3>
											{/* lang:出参配置*/}
											{workflowLang.nodeDetail("outputConfiguration")}
										</h3>
									</div>
									<div className="model-section-body">
										<Table
											columns={
												[
													{
														title: workflowLang.nodeDetail("fieldName"), // lang:"字段名",
														width: 150,
														dataIndex: "systemField",
														render: (text, record) => {
															return (
																<Tooltip title={record.systemFieldDName}>
																	{text}
																</Tooltip>
															);
														}
													},
													{
														title: workflowLang.nodeDetail("direction"), // lang:"方向",
														width: 100,
														render: (text, record, index) => {
															return (
																<Icon type="arrow-left" />
															);
														}
													},
													{
														title: workflowLang.nodeDetail("elementName"), // lang:"要素名"
														width: 150,
														dataIndex: "modelVar",
														render: (text, record) => {
															return (
																<Tooltip title={record.dName}>
																	{text}
																</Tooltip>
															);
														}
													}
												]
											}
											dataSource={nodeConfig.modelOutput || []}
											pagination={false}
											rowKey="name"
											size="middle"
										/>
									</div>
								</div>
							}
						</div>
					}

					{
						shape &&
						shape === "flow-data" &&
						<div className="model-node-detail">
							<Button
								style={{ width: "100%" }}
								onClick={this.serviceSetting.bind(this)}
								className="mb20"
							>
								{/* lang:点击配置服务 */}
								{disabled ? workflowLang.nodeDetail("viewServiceConfig") : workflowLang.nodeDetail("clickConfigService")}
							</Button>
							{
								nodeConfig.dataInput &&
								nodeConfig.dataInput.length > 0 &&
								<div className="model-section-wrap">
									<div className="model-section-header">
										<h3>
											{/* lang:入参配置*/}
											{workflowLang.nodeDetail("inputConfiguration")}
										</h3>
									</div>
									<div className="model-section-body">
										<Table
											columns={
												[
													{
														title: workflowLang.nodeDetail("fieldName"), // lang:"字段名",
														width: 150,
														dataIndex: "systemField",
														render: (text, record) => {
															return (
																<Tooltip title={record.systemFieldDName}>
																	{text}
																</Tooltip>
															);
														}
													},
													{
														title: workflowLang.nodeDetail("direction"), // lang:"方向",
														width: 100,
														render: (text, record, index) => {
															return (
																<Icon type="arrow-right" />
															);
														}
													},
													{
														title: workflowLang.nodeDetail("elementName"), // lang:"要素名",
														width: 150,
														dataIndex: "dataVar",
														render: (text, record) => {
															return (
																<Tooltip title={record.dName}>
																	{text}
																</Tooltip>
															);
														}
													}
												]
											}
											dataSource={nodeConfig.dataInput || []}
											pagination={false}
											rowKey="name"
											size="middle"
										/>
									</div>
								</div>
							}
							{
								nodeConfig.dataOutput &&
								nodeConfig.dataOutput.length > 0 &&
								<div className="model-section-wrap">
									<div className="model-section-header">
										<h3>
											{/* lang:出参配置*/}
											{workflowLang.nodeDetail("outputConfiguration")}
										</h3>
									</div>
									<div className="model-section-body">
										<Table
											columns={
												[
													{
														title: workflowLang.nodeDetail("fieldName"), // lang:"字段名",
														width: 150,
														dataIndex: "systemField",
														render: (text, record) => {
															return (
																<Tooltip title={record.systemFieldDName}>
																	{text}
																</Tooltip>
															);
														}
													},
													{
														title: workflowLang.nodeDetail("direction"), // lang:"方向",
														width: 100,
														render: (text, record, index) => {
															return (
																<Icon type="arrow-left" />
															);
														}
													},
													{
														title: workflowLang.nodeDetail("elementName"), // lang:"要素名"
														width: 150,
														dataIndex: "dataVar",
														render: (text, record) => {
															return (
																<Tooltip title={record.dName}>
																	{text}
																</Tooltip>
															);
														}
													}
												]
											}
											dataSource={nodeConfig.dataOutput || []}
											pagination={false}
											rowKey="name"
											size="middle"
										/>
									</div>
								</div>
							}
						</div>
					}
					{
						shape &&
						shape === "flow-public-policy" &&
						<Row className="param-item">
							<Col span={24} className="param-title">
								{/* lang:公共策略*/}
								{workflowLang.nodeDetail("publicPolicy")}
							</Col>
							<Col span={24} className="param-content">
								<Select
									placeholder={workflowLang.nodeDetail("policyPlaceholder")} // lang:"选择策略"
									value={selectPolicyName ? selectPolicyName : undefined}
									showSearch
									optionFilterProp="children"
									disabled={disabled}
									className="wid-100-percent"
									onChange={(e) => {
										this.changeNodeConfigField("policyUuid", "select", e);
										let policyObj = publicPolicyList.find(pItem => pItem.name === e);
										if (policyObj) {
											this.changeNodeBase("label", "select", policyObj.dName);
											this.setState({
												changeLabelText: policyObj.dName
											});
										}
									}}
								>
									{
										publicPolicyList && publicPolicyList.length > 0 &&
										publicPolicyList.map(v => {
											return <Option value={v.name}>{v.dName}</Option>;
										})
									}
								</Select>
							</Col>
						</Row>
					}
				</div>
				{
					shape === "flow-model" &&
					dialogShow.modelSetting &&
					<ModelSetting
						nodeConfig={nodeConfig}
						onChange={(nodeConfig) => {
							this.changeNodeBase("nodeConfig", "select", nodeConfig);
							let modelObj = modelSelect && modelSelect.find(mItem => mItem.uuid === nodeConfig.modelId && mItem.version === nodeConfig.modelVersion);

							if (modelObj) {
								this.changeNodeBase("label", "select", modelObj.name);
								this.setState({
									changeLabelText: modelObj.name
								});
							}
						}}
						disabled={disabled}
					/>
				}
				{
					shape === "flow-data" &&
					dialogShow.serviceSetting &&
					<ServiceSetting
						nodeConfig={nodeConfig}
						onChange={(nodeConfig) => {
							this.changeNodeBase("nodeConfig", "select", nodeConfig);
							let serviceObj = thirdDataSelect && thirdDataSelect.find(mItem => mItem.name === nodeConfig.serviceName);

							if (serviceObj) {
								this.changeNodeBase("label", "select", serviceObj.dName);
								this.setState({
									changeLabelText: serviceObj.dName
								});
							}
						}}
						disabled={disabled}
					/>
				}
			</Card>
		);
	}
}

export default withPropsAPI(connect(state => ({
	globalStore: state.global,
	workflowStore: state.workflow
}))(NodeDetail));
