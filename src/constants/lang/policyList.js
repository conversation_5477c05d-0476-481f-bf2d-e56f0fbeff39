import store from "../../app";
import moment from "moment";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		tabForEditor: {
			"cn": "编辑区",
			"en": "Editing Area"
		},
		tabForRunning: {
			"cn": "运行区",
			"en": "Running Area"
		},
		titleForEditor: {
			"cn": "编辑区列表",
			"en": "Editing Area List"
		},
		titleForRunning: {
			"cn": "运行区列表",
			"en": "Running Area List"
		},
		addNewPolicySet: {
			"cn": "新建策略集",
			"en": "Add policy set"
		},
		policySetNoPolicy: {
			"cn": "当前策略集暂无策略",
			"en": "The current policy set does not have a policy"
		},
		inputSuggest1: {
			"cn": "建议输入：中文、英文、数字和下划线的组合",
			"en": "Suggested input: combination of Chinese, English, Numbers and underlining"
		},
		selectDealType: {
			"cn": "选择处置方式",
			"en": "Select a deal type"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		policySetName: {
			"cn": "策略集名称",
			"en": "Policy set name"
		},
		policyName: {
			"cn": "策略名称",
			"en": "Policy Name"
		},
		event: {
			"cn": "事件标识",
			"en": "Event Id"
		},
		ruleName: {
			"cn": "规则名称",
			"en": "Rule Name"
		},
		ruleCustomId: {
			"cn": "规则编号",
			"en": "Custom Id"
		},
		search: {
			"cn": "搜索",
			"en": "Search"
		},
		searchPlaceholder: {
			"cn": "请输入搜索内容",
			"en": "Search content"
		},
		allTags: {
			"cn": "全部标签",
			"en": "All tags"
		}
	};
	return params[field][lang];
};

const replay = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		dataError: {
			"cn": "开始日期必须小于或者等于结束日期",
			"en": "The start date must be less than or equal to the end date"
		},
		timeRangeError: {
			"cn": "时间范围不能超过90天",
			"en": "The time range cannot exceed 90 days"
		},
		dateMap: {
			"cn": {
				"今天": [moment(`${moment().format("YYYY-MM-DD")} 00:00:00`), moment()],
				"昨天": [moment(`${moment().subtract(1, "days").format("YYYY-MM-DD")} 00:00:00`), moment(`${moment().format("YYYY-MM-DD")} 00:00:00`)],
				"最近七天": [moment(`${moment().subtract(6, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"最近三十天": [moment(`${moment().subtract(29, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"本月": [moment(`${moment().startOf("month").format("YYYY-MM-DD")} 00:00:00`), moment()]
			},
			"en": {
				"Today": [moment(`${moment().format("YYYY-MM-DD")} 00:00:00`), moment()],
				"Yesterday": [moment(`${moment().subtract(1, "days").format("YYYY-MM-DD")} 00:00:00`), moment(`${moment().format("YYYY-MM-DD")} 00:00:00`)],
				"Recent 7 days": [moment(`${moment().subtract(6, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"Recent 30 days": [moment(`${moment().subtract(29, "days").format("YYYY-MM-DD")} 00:00:00`), moment()],
				"This month": [moment(`${moment().startOf("month").format("YYYY-MM-DD")} 00:00:00`), moment()]
			}
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		"policySet": {
			"cn": "策略集/策略",
			"en": "PolicySet/Policy"
		},
		"app": {
			"cn": "所属应用",
			"en": "Application"
		},
		"description": {
			"cn": "描述",
			"en": "Description"
		},
		"operation": {
			"cn": "操作",
			"en": "Operation"
		},
		"version": {
			"cn": "版本号",
			"en": "Version"
		},
		"hasNonePolicy": {
			"cn": "当前策略集暂无策略",
			"en": "Current policy set has none policy"
		},
		"policyA": {
			"cn": "策略_",
			"en": "policy_"
		},
		"publicPolicyA": {
			"cn": "公共策略_",
			"en": "Public strategy_"
		},
		"exportRuleListSuccessfully": {
			"cn": "导出策略规则列表成功",
			"en": "Export rule list was successful"
		},
		"policySetA": {
			"cn": "策略集_",
			"en": "policySet_"
		},
		"exportPolicyListSuccessfully": {
			"cn": "导出策略列表成功",
			"en": "Export policy list was successful"
		},
		"policyReplay": {
			"cn": "策略回测",
			"en": "Policy Replay"
		},
		"viewRuleStream": {
			"cn": "查看规则流",
			"en": "View Rule Stream"
		},
		"noConfigRuleStream": {
			"cn": "当前策略集还没有配置规则流",
			"en": "The current policy set does not yet configure the rule flow"
		},
		"policyOffLineTitle": {
			"cn": "策略下线",
			"en": "Strategy offline"
		},
		"policyOffLineContent": {
			"cn": (name)=>(`确认下线《${name}》`),
			"en": (name)=>(`Confirm offline《${name}》`)
		},
		"policyOffLineSuccess": {
			"cn": (name)=>(`策略《${name}》下线成功`),
			"en": (name)=>(`Policy ${name}》offline success`)
		},
		"replayStatus": {
			"cn": "回测状态",
			"en": "Replay status"
		}
	};
	return params[field][lang];
};

const tooltip = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"setUpTheExecutionProcess": {
			"cn": "设置执行流程",
			"en": "Set up the execution process"
		},
		"importPolicyList": {
			"cn": "导入策略列表",
			"en": "Import policy list"
		},
		"exportPolicySet": {
			"cn": "导出策略列表",
			"en": "Export policy list"
		},
		"addPolicy": {
			"cn": "添加策略",
			"en": "Add policy"
		},
		"editPolicySet": {
			"cn": "编辑策略集",
			"en": "Edit policySet"
		},
		"deletePolicySet": {
			"cn": "删除策略集",
			"en": "Delete policySet"
		},
		"addOutput": {
			"cn": "新增出参",
			"en": "Add new output"
		},
		"searchOutput": {
			"cn": "查询出参",
			"en": "Search output"
		},
		"testPolicySet": {
			"cn": "测试策略集",
			"en": "Test policySet"
		},
		"importRules": {
			"cn": "导入规则",
			"en": "Import rules"
		},
		"exportRules": {
			"cn": "导出规则",
			"en": "Export rules"
		},
		"editPolicy": {
			"cn": "编辑策略",
			"en": "Edit policy"
		},
		"deletePolicy": {
			"cn": "删除策略",
			"en": "Delete policy"
		},
		"publicPolicy": {
			"cn": "公共策略",
			"en": "Public policy"
		},
		"offLinePolicy": {
			"cn": "申请下线",
			"en": "Application for offline"
		}
	};
	return params[field][lang];
};

const policySetModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"newPolicySet": {
			"cn": "新建策略集",
			"en": "New PolicySet"
		},
		"modifyPolicySet": {
			"cn": "修改策略集",
			"en": "Modify PolicySet"
		},
		"policySetName": {
			"cn": "策略集",
			"en": "PolicySet"
		},
		"enterPolicyName": {
			"cn": "请输入策略集名称",
			"en": "Enter policySet Name"
		},
		"appName": {
			"cn": "应用名",
			"en": "App Name"
		},
		"selectAppName": {
			"cn": "请选择应用",
			"en": "Select app name"
		},
		"inputSuggest": {
			"cn": "建议输入：中文、英文、数字和下划线的组合",
			"en": "Suggested input: combination of Chinese, English, Numbers and underlining"
		},
		"inputSuggest2": {
			"cn": "建议输入：英文、数字和下划线的组合",
			"en": "Suggested input: English, Numbers and underlining"
		},
		"eventType": {
			"cn": "事件类型",
			"en": "Event Type"
		},
		"selectEventType": {
			"cn": "请选择事件类型",
			"en": "Select Event Type"
		},
		"eventId": {
			"cn": "事件标识",
			"en": "Event Id"
		},
		"selectEventId": {
			"cn": "请选择事件标识",
			"en": "Select Event Id"
		},
		"description": {
			"cn": "描述",
			"en": "Description"
		},
		"enterPolicyDescription": {
			"cn": "请输入策略描述",
			"en": "Enter policy description"
		},
		//	operator
		"policySetNameIsEmptyTip": {
			"cn": "策略集名称不能为空",
			"en": "The policy set name cannot be empty"
		},
		"eventIdIsEmptyTip": {
			"cn": "事件标识不能为空",
			"en": "Event id cannot be empty"
		},
		"policySetLenTip": {
			"cn": "策略集名称长度应大于1,小于50",
			"en": "The policy set name length should be greater than 1 and less than 50"
		},
		"eventIdLenTip": {
			"cn": "事件标识长度应大于1,小于50",
			"en": "Event id length should be greater than 1 and less than 50"
		},
		"policySetRegTip": {
			"cn": "策略集名称：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合",
			"en": "Policy set name: please do not enter any illegal characters such as full Angle characters and Chinese characters. It is recommended to enter a combination of Chinese, English, Numbers and underscores"
		},
		"eventIdRegTip": {
			"cn": "事件标识：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合",
			"en": "Event id: please do not enter any illegal characters such as full Angle characters and Chinese characters. It is recommended to enter a combination of Chinese, English, Numbers and underscores"
		},
		"newPolicySetSuccessTip": {
			"cn": "新建策略集成功",
			"en": "The new policy set was successful"
		},
		"modifyPolicySetSuccessTip": {
			"cn": "修改策略集成功",
			"en": "Modify policy set was successful"
		}
	};
	return params[field][lang];
};
const policyModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"addPolicy": {
			"cn": "新建策略",
			"en": "Add Policy"
		},
		"modifyPolicy": {
			"cn": "修改策略",
			"en": "Modify Policy"
		},
		"policyName": {
			"cn": "策略名称",
			"en": "Policy Name"
		},
		"enterPolicyName": {
			"cn": "请输入策略名称",
			"en": "Please enter policy Name"
		},
		"riskType": {
			"cn": "风险类型",
			"en": "Risk Type"
		},
		"enterRiskType": {
			"cn": "请选择风险类型",
			"en": "Please select Risk Type"
		},
		"policyMode": {
			"cn": "策略模式",
			"en": "Policy Mode"
		},
		"selectPolicyMode": {
			"cn": "请选择策略模式",
			"en": "Please select Policy Mode"
		},
		"RiskThreshold": {
			"cn": "风险阈值",
			"en": "Risk threshold"
		},
		"description": {
			"cn": "描述",
			"en": "Description"
		},
		"enterPolicyDescription": {
			"cn": "请输入策略描述",
			"en": "Please enter policy description"
		},
		"eventTags": {
			"cn": "事件标签",
			"en": "Event Tags"
		},
		"selectEventTags": {
			"cn": "请选择事件标签",
			"en": "Select Event Tags"
		},
		//	operator
		"policyNameIsEmptyTip": {
			"cn": "策略名称不能为空",
			"en": "The policy name cannot be empty"
		},
		"policyLenTip": {
			"cn": "策略名称长度应大于1,小于50",
			"en": "The policy name length should be greater than 1 and less than 50"
		},
		"policyRegTip": {
			"cn": "策略名称：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合",
			"en": "Policy name: please do not enter any illegal characters such as full Angle characters and Chinese characters. It is recommended to enter a combination of Chinese, English, Numbers and underscores"
		},
		"newPolicySuccessTip": {
			"cn": "新建策略成功",
			"en": "The new policy was successful"
		},
		"modifyPolicySetSuccessTip": {
			"cn": "修改策略集成功",
			"en": "Modify policy set was successful"
		},
		"riskThresholdEmptyTip": {
			"cn": "风险阈值配置存在空值，请补充完整！",
			"en": "The risk threshold configuration has a null value, please complete!"
		}
	};
	return params[field][lang];
};

const deleteModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"deletePolicyTip": {
			"cn": "删除策略提醒",
			"en": "Delete policy tip"
		},
		"deletePolicySetTip": {
			"cn": "删除策略集提醒",
			"en": "Delete policy set tip"
		},
		"deletePolicyDesc1": {
			"cn": "您真的要删除《",
			"en": "Do you really want to delete "
		},
		"deletePolicyDesc2": {
			"cn": "》吗？",
			"en": "?"
		},
		"deletePolicySetRegTip": {
			"cn": "当前策略集下存在策略，不能删除！",
			"en": "A policy exists under the current policy set and cannot be deleted!"
		}
	};
	return params[field][lang];
};

const detailDrawer = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"appName": {
			"cn": "所属应用",
			"en": "App Name"
		},
		"eventType": {
			"cn": "事件类型",
			"en": "Event Type"
		},
		"eventId": {
			"cn": "事件标识",
			"en": "Event ID"
		},
		"riskType": {
			"cn": "风险类型",
			"en": "Risk Type"
		},
		"policyMode": {
			"cn": "策略模式",
			"en": "Policy Mode"
		},
		"policyDescription": {
			"cn": "策略描述",
			"en": "Description"
		},
		"noDescription": {
			"cn": "暂无描述",
			"en": "No Description"
		},
		"lastModify": {
			"cn": "最后修改",
			"en": "Last Modify"
		},
		"modifyTime": {
			"cn": "修改时间",
			"en": "Modify Time"
		},
		"ruleList": {
			"cn": "规则列表",
			"en": "Rule List"
		},
		"tag": {
			"cn": "策略标签",
			"en": "Tag"
		}
	};
	return params[field][lang];
};

const outputModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";

	let params = {
		"modifyTitle": {
			"cn": "操作策略集出参",
			"en": "Opera policy set out parameter"
		},
		"outputField": {
			"cn": "出参字段",
			"en": "Field"
		},
		"opera": {
			"cn": "操作",
			"en": "Opera"
		},
		"systemFieldPlaceHolder": {
			"cn": "请选择系统字段",
			"en": "Please select system field"
		},
		"existNullField": {
			"cn": (i)=>`第${i + 1}组参数未选择系统字段`,
			"en": (i)=>`The ${i + 1} group parameter has no system field selected`
		},
		"operaSuc": {
			"cn": "操作策略集出参成功",
			"en": "Operation policy set out successfully"
		},
		"operaFail": {
			"cn": "操作策略集出参失败",
			"en": "Operation policy set out parameter failed"
		},
		"viewTitle": {
			"cn": "操作策略集出参",
			"en": "Opera policy set out parameter"
		},
		"noData": {
			"cn": "暂无出参信息",
			"en": "No reference information"
		}
	};
	return params[field][lang];
};

const ruleRecords = (total, x, y, z) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;

	let cn = "当前策略规则总计：" + total + "条，启用：" + x + "条，禁用：" + y + "条，模拟：" + z + "条";
	let en = "Total number of current policy rules: " + total + ", enabled: " + x + ", disabled: " + y + ", simulated: " + z;
	return personalMode && personalMode.lang === "cn" ? cn : en;
};

export default {
	common,
	searchParams,
	replay,
	table,
	tooltip,
	policySetModal,
	policyModal,
	deleteModal,
	detailDrawer,
	ruleRecords,
	outputModal
};
