import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Select, Row, Col } from "antd";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import { isJSON } from "@/utils/isJSON";
import FilterList from "./FieldSet/FilterList";
import CrossMatchFilterList from './FieldSet/CrossMatchFilterList';
import FilterScene from "../../Common/FilterScene";
// import Scene from "./Scene";
// import ReadOnlyScene from "../../RunningArea/Inner/Scene";
import CommonFilter from "../../Common/CommonFilter";

const Option = Select.Option;
const TextArea = Input.TextArea;

class IndexMore extends PureComponent {
	constructor(props) {
		super(props);
		this.changeBaseField = this.changeBaseField.bind(this);
		this.deleteOperationActions = this.deleteOperationActions.bind(this);
	}

	changeBaseField(field, e, type, noModify) {
		let { indexEditorStore, dispatch } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}

		currentIndexObj[field] = value;

		// 场景初始化时无需刷新保存
		if (!noModify) {
			currentIndexObj["hasModify"] = true;

		}

		if (field === "app") {
			currentIndexObj["event"] = null;
		}

		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				editIndexMap: editIndexMap
			}
		});
	}

	changeCommonFilter = (filterArr) => {
		let { indexEditorStore, dispatch } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;

		currentIndexObj.filterList = filterArr;
		currentIndexObj["hasModify"] = true;

		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				editIndexMap: editIndexMap
			}
		});
	}

	deleteOperationActions(index) {
		let { indexEditorStore, dispatch } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		let filterList = currentIndexObj && currentIndexObj.filterList ? currentIndexObj.filterList : {};

		filterList.splice(index, 1);
		currentIndexObj["hasModify"] = true;

		dispatch({
			type: "indexEditor/setAttrValue",
			payload: {
				editIndexMap: editIndexMap
			}
		});
	}

	render() {
		let { indexEditorStore, templateStore, templateName, globalStore } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let { allMap, personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";

		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		let filterObj = currentIndexObj && currentIndexObj.filterList ? currentIndexObj.filterList : {};
		let { indexTemplateListObj } = templateStore;

		let currentTemplate = indexTemplateListObj && templateName ? indexTemplateListObj[templateName] : null;
		let cfgJson = currentTemplate && currentTemplate.cfgJson && isJSON(currentTemplate.cfgJson) ? JSON.parse(currentTemplate.cfgJson) : null;
		let { advanceConfig = {} } = currentTemplate || {};

		let noFilterFunction = false;
		let noScene = false;
		if (advanceConfig.hasOwnProperty("noFilterFunction") && advanceConfig.noFilterFunction) {
			noFilterFunction = true;
		}
		if (advanceConfig.hasOwnProperty("noScene") && advanceConfig.noScene) {
			noScene = true;
		}

		let eventSelectOption = [];
		let policySetMapName = currentIndexObj && currentIndexObj.app ? currentIndexObj.app + "_policySets" : null;

		if (allMap && allMap["eventIdSelect"]) {
			for (let i in allMap["eventIdSelect"]) {
				eventSelectOption.push(
					<Option value={i} key={i}>
						{
							allMap["eventIdSelect"][i]
						}
					</Option>
				);
			}
		}

		let disabled = false;

		let hasFieldSet = false;
		cfgJson && cfgJson.params && cfgJson.params.forEach((item) => {
			if (item && item.children) {
				item.children.forEach((cItem) => {
					if (cItem.componentType === "fieldSet") {
						hasFieldSet = true;
					}
				});
			}
		});

		let unSave = true;
		const { hasCommited } = currentIndexObj || {};
		if (hasCommited === "1") {
			unSave = false;
		}
		// if (currentIndexObj && !currentIndexObj["unSave"]) {
		// 	unSave = false;
		// }

		return (
			<div className="ml-1">
				{
					!noScene &&
					unSave &&
					<FilterScene
						width="150px"
						disabled={currentIndexObj && !unSave}
						indexData={currentIndexObj}
						onChange={this.changeBaseField.bind(this)}
					/>
					// <Scene unSave={unSave} />
				}
				{
					!noScene &&
					!unSave &&
					<FilterScene
						width="150px"
						disabled={true}
						indexData={currentIndexObj}
					/>
				}
				{
					!noFilterFunction &&
					!hasFieldSet &&
					<CommonFilter
						disabled={disabled}
						filterObj={filterObj}
						onChange={this.changeCommonFilter}
					/>
				}
				{
					hasFieldSet &&
					templateName &&
					templateName !== "crossMatch" && templateName !== "newCrossMatch" &&
					<FilterList
						disabled={disabled}
					/>
				}
				{/* 交叉匹配和自定义专属 */}
				{
					templateName &&
					(templateName == "crossMatch" || templateName == "newCrossMatch") &&
					<CrossMatchFilterList
						disabled={disabled}
						filterObj={filterObj}
						templateName={templateName}
						onChange={this.changeCommonFilter}
					/>
				}
				<Row
					className="mb10"
					gutter={CommonConstants.gutterSpan}
				>
					<Col span={4} className="basic-info-title">
						{/* lang:指标说明 */}
						{indexListLang.ruleAttr("indexDescription")}
					</Col>
					<Col span={8}>
						<TextArea
							rows={4}
							placeholder={indexListLang.ruleAttr("indexDescriptionPlaceholder")} // lang:请输入指标说明
							value={currentIndexObj && currentIndexObj["remark"] ? currentIndexObj["remark"] : undefined}
							onChange={(e) => this.changeBaseField("remark", e, "input")}
							disabled={disabled}
						/>
					</Col>
				</Row>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(IndexMore);
