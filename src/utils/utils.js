import moment from "moment";
import { message } from "antd";

export function fixedZero(val) {
	return val * 1 < 10 ? `0${val}` : val;
}

export function getTimeDistance(type) {
	const now = new Date();
	const oneDay = 1000 * 60 * 60 * 24;

	if (type === "today") {
		now.setHours(0);
		now.setMinutes(0);
		now.setSeconds(0);
		return [moment(now), moment(now.getTime() + (oneDay - 1000))];
	}

	if (type === "week") {
		let day = now.getDay();
		now.setHours(0);
		now.setMinutes(0);
		now.setSeconds(0);

		if (day === 0) {
			day = 6;
		} else {
			day -= 1;
		}

		const beginTime = now.getTime() - (day * oneDay);

		return [moment(beginTime), moment(beginTime + ((7 * oneDay) - 1000))];
	}

	if (type === "month") {
		const year = now.getFullYear();
		const month = now.getMonth();
		const nextDate = moment(now).add(1, "months");
		const nextYear = nextDate.year();
		const nextMonth = nextDate.month();

		return [moment(`${year}-${fixedZero(month + 1)}-01 00:00:00`), moment(moment(`${nextYear}-${fixedZero(nextMonth + 1)}-01 00:00:00`).valueOf() - 1000)];
	}

	if (type === "year") {
		const year = now.getFullYear();

		return [moment(`${year}-01-01 00:00:00`), moment(`${year}-12-31 23:59:59`)];
	}
}

export function getPlainNode(nodeList, parentPath = "") {
	const arr = [];
	nodeList.forEach((node) => {
		const item = node;
		item.path = `${parentPath}/${item.path || ""}`.replace(/\/+/g, "/");
		item.exact = true;
		if (item.children && !item.component) {
			arr.push(...getPlainNode(item.children, item.path));
		} else {
			if (item.children && item.component) {
				item.exact = false;
			}
			arr.push(item);
		}
	});
	return arr;
}

export function digitUppercase(n) {
	const fraction = ["角", "分"];
	const digit = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
	const unit = [
		["元", "万", "亿"],
		["", "拾", "佰", "仟"]
	];
	let num = Math.abs(n);
	let s = "";
	fraction.forEach((item, index) => {
		s += (digit[Math.floor(num * 10 * (10 ** index)) % 10] + item).replace(/零./, "");
	});
	s = s || "整";
	num = Math.floor(num);
	for (let i = 0; i < unit[0].length && num > 0; i += 1) {
		let p = "";
		for (let j = 0; j < unit[1].length && num > 0; j += 1) {
			p = digit[num % 10] + unit[1][j] + p;
			num = Math.floor(num / 10);
		}
		s = p.replace(/(零.)*零$/, "").replace(/^$/, "零") + unit[0][i] + s;
	}

	return s.replace(/(零.)*零元/, "元").replace(/(零.)+/g, "零").replace(/^整$/, "零元整");
}

// 弹窗未完全关闭禁止再次提交
export function messageError(payload) {
	return new Promise((resolve) => {
		message.error(payload, () => {
			resolve(false);
		});
	});
}

/**
 * @description: 报告对象数组去重
 */
export function unique(array) {
	let result = {};
	let finalResult = [];

	for (let i = 0; i < array.length; i++) {
		result[array[i].id] = array[i];
	}

	for (let item in result) {
		finalResult.push(result[item]);
	}

	return finalResult;
}

export function searchToObject(search) {
	let pairs = search.substring(1).split("&");
	let obj = {};
	let pair;
	let i;
	for (i in pairs) {
		if (pairs[i] === "") { continue; }

		pair = pairs[i].split("=");
		obj[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
	}
	return obj;
}

// JS判断字符串长度（英文占1个字符，中文汉字占2个字符）
export function getStrLen(str) {
	let len = 0;
	for (let i = 0; i < str.length; i++) {
		let c = str.charCodeAt(i);
		// 单字节加1
		if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
			len++;
		} else {
			len += 2;
		}
	}
	return len;
}

export function judgeExistVal(value, optionList, type) {
	if (value) {
		optionList = optionList || [];
		const haveVal = optionList && optionList.length > 0 &&
			optionList.filter((v) => {
				return String(v[type || "name"]) === String(value);
			});
		if (!(haveVal && haveVal.length > 0)) {
			value = undefined;
		}
	}
	return value;
}

export function isPublicPolicy() {
	return window.location.href.indexOf("/policy/publicPolicyDetail") > -1 || window.location.href.indexOf("/policy/versionPublicPolicyDetail") > -1;
}

export function isPublicVersionPolicy() {
	return window.location.href.indexOf("/policy/versionPublicPolicyDetail") > -1;
}
