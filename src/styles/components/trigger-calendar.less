@import "../base/variables";

:global {
	.trigger-calendar {
		& {
			clear: both;
		}
		.ant-fullcalendar-date {
			height: 106px !important;
			.ant-fullcalendar-content {
				height: auto;
			}
		}
		.ant-fullcalendar-table {
			& {
				border-collapse: collapse;
				border: 1px solid #e6e6e6;
			}
			.ant-fullcalendar-column-header {
				& {
					text-align: center;
					height: 36px;
					line-height: 36px;
					margin: 0;
					padding: 0;
				}
				th {
					border: 1px dashed #e6e6e6;
				}
			}
		}
		.ant-fullcalendar-header {
			display: none;
		}
		.ant-fullcalendar-calendar-body {
			& {
				padding: 0;
			}
			td {
				& {
					border: 1px solid #e6e6e6;
					vertical-align: middle;
				}
				.add-event, .remove-event {
					display: none;
					color: #999;
				}
				.add-event {
				}
				.remove-event {
					position: relative;
					top: 5px;
				}
				&:hover {
					.add-event, .remove-event {
						display: block;
					}
				}
				.add-event, .remove-event {
					&:hover {
						text-decoration: underline;
					}
				}
				.add-event:hover {
					color: @blue2
				}
				.remove-event:hover {
					color: @red;
				}
			}
			.ant-fullcalendar-month,
			.ant-fullcalendar-date {
				& {
					position: relative;
					padding: 0;
					margin: 0;
					border: none;
				}
				.ant-fullcalendar-value {
					position: absolute;
					right: 0;
					top: 0;
					font-size: 17px;
					font-family: sans-serif;
					margin: 10px 10px 0 0;
					font-weight: normal;
				}
				.ant-fullcalendar-content {
					& {
						position: relative;
						top: 40px;
						overflow: inherit
					}
					ul {
						& {
							padding: 0;
							margin: 0;
							text-align: center;
						}
						li {
							list-style: none;
							width: auto;
							height: 24px;
							line-height: 24px;
							margin-left: 10px;
							margin-right: 10px;
							text-align: center;
							background: rgb(255, 108, 92);
							border-radius: 4px;
							color: #fff;
						}
					}
				}
			}
		}
	}
	.trigger-calendar-toolbar {
		& {
			position: relative;
			height: 32px;
			line-height: 32px;
			margin-bottom: 20px;
		}
		.left-info {
			position: absolute;
			left: 0;
			top: 0;
			z-index: 10;
		}
		.center-info {
			position: absolute;
			left: 0;
			right: 0;
			text-align: center;
			font-size: 20px;
			font-weight: normal;
			font-family: sans-serif;
			color: #000;
			z-index: 9;
		}
		.right-info {
			position: absolute;
			right: 0;
			z-index: 10;
		}
	}
}
