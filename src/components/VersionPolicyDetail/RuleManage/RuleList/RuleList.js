import { PureComponent } from "react";
import { connect } from "dva";
import { Icon, message, Tooltip, Radio, Collapse, Spin } from "antd";
import RuleConfig from "../RuleConfig";
import { policyDetailLang } from "@/constants/lang";
import { policyDetailAPI } from "@/services";
import moment from "moment";
import { cloneDeep } from "lodash";
import "./RuleList.less";

const Panel = Collapse.Panel;

class RuleList extends PureComponent {
	constructor(props) {
		super(props);
		this.operatorRuleImmnuo = this.operatorRuleImmnuo.bind(this);
	}

	addToCustomListHandle = (indexArr, currentRule) => {
		let { dispatch } = this.props;

		dispatch({
			type: "policyDetail/setDialogData",
			payload: {
				addToCustomListData: {
					triggers: cloneDeep(currentRule.triggers) || [],
					ruleIndexArr: indexArr,
					ruleUuid: currentRule.uuid
				}
			}
		});
		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				addToCustomList: true
			}
		});
	}

	viewImmune = (record) => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyDetail/setDialogData",
			payload: {
				viewImmuneData: {
					ruleUuid: record.uuid
				}
			}
		});
		dispatch({
			type: "policyDetail/setDialogShow",
			payload: {
				viewImmune: true
			}
		});
	}

	operatorRuleImmnuo(obj, e) {
		let { dispatch, policyDetailStore } = this.props;
		let { dialogData } = policyDetailStore;
		let { ruleImmunoData } = dialogData;
		e.stopPropagation();

		if (obj.hasImmuno) {
			let params = {
				ruleUuid: obj.uuid
			};
			policyDetailAPI.getRuleImmuno(params).then(res => {
				if (res.success && res.data) {
					let data = res.data;

					ruleImmunoData["ruleUuid"] = obj.uuid;
					ruleImmunoData["ruleName"] = obj.name;
					ruleImmunoData["logicOperator"] = data.logicOperator;
					ruleImmunoData["expireAt"] = data.expireAt ? moment(data.expireAt).valueOf() : null;
					ruleImmunoData["description"] = data.description;
					ruleImmunoData["conditions"] = data.conditions;

					dispatch({
						type: "policyDetail/setDialogShow",
						payload: {
							addRuleImmuno: false,
							modifyRuleImmuno: true
						}
					});
					dispatch({
						type: "policyDetail/setDialogData",
						payload: {
							ruleImmunoData: ruleImmunoData
						}
					});
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		} else {
			dispatch({
				type: "policyDetail/setDialogShow",
				payload: {
					addRuleImmuno: true,
					modifyRuleImmuno: false
				}
			});
			ruleImmunoData["ruleUuid"] = obj.uuid;
			ruleImmunoData["ruleName"] = obj.name;
			dispatch({
				type: "policyDetail/setDialogData",
				payload: {
					ruleImmunoData: ruleImmunoData
				}
			});
		}
	}

	render() {
		let { policyRulesReady, policyRules, mode, ruleSearch, searchWord, isOtherPageCite, policyDetailStore, dispatch, globalStore } = this.props;
		const { allMap } = globalStore;
		const { ruleActiveKey = [], currentRuleUuid } = policyDetailStore || {};
		let collapseList = [];
		console.log(mode);
		if (allMap && Object.keys(allMap).length > 0) {
			collapseList = policyRules && policyRules.filter(fItem => {
				if (ruleSearch && searchWord) {
					return fItem.name.indexOf(searchWord) > -1;
				} else {
					return true;
				}
			}).map((item, index) => {
				let title = (obj, indexArr) => {
					let addToCustomClassNames;
					if (item.triggers && item.triggers.length) {
						addToCustomClassNames = "iconfont icon-log-list blue";
					} else {
						addToCustomClassNames = "iconfont icon-log-list";
					}

					return (
						<div
							className={obj.unSave ? "rule-manage-header collapse-header un-save" : "rule-manage-header collapse-header"}>
							<div className="rule-name">
								<Icon
									className={ruleActiveKey.length === 1 && ruleActiveKey[0] === obj.uuid ? "row-collapsed active" : "row-collapsed"}
									type={ruleActiveKey.length === 1 && ruleActiveKey[0] === obj.uuid ? "minus-square" : "plus-square"}
								/>
								{obj.name}
							</div>
							<div className="rule-action">
								{/* 添加到名单 */}
								{
									// 如果是其他页面引用了规则列表，那么添加到自定义名单按钮隐藏
									!isOtherPageCite &&
									<Tooltip title={policyDetailLang.ruleList("addList")}>
										<i
											className={addToCustomClassNames}
											onClick={(e) => {
												e.stopPropagation();
												this.addToCustomListHandle(indexArr, obj);
											}}
										></i>
									</Tooltip>
								}
								{
									// 如果是其他页面引用了规则列表，那么免打扰查看按钮隐藏
									!isOtherPageCite &&
									obj.hasImmuno &&
									<Tooltip title={policyDetailLang.ruleList("viewImmuno")}>
										<i
											className="salaxy-iconfont salaxy-miandarao blue"
											onClick={(e) => {
												e.stopPropagation();
												this.viewImmune(obj);
											}}
										></i>
									</Tooltip>
								}
							</div>
							<div
								className="rule-status" onClick={(e) => {
									e.stopPropagation();
								}}
							>
								<Radio.Group
									className="rule-disabled-radio"
									value={obj.valid.toString()}
									buttonStyle="solid"
									size="small"
									disabled={true}
								>
									<Radio.Button value="0">
										{/* 关闭 */}
										{policyDetailLang.ruleList("close")}
									</Radio.Button>
									<Radio.Button value="2">
										{/* 模拟 */}
										{policyDetailLang.ruleList("simulation")}
									</Radio.Button>
									<Radio.Button value="1">
										{/* 正式 */}
										{policyDetailLang.ruleList("formal")}
									</Radio.Button>
								</Radio.Group>
							</div>
							{/* 如果是其他页面引用了规则列表，那么规则免打扰按钮隐藏 */}
							{/* <div className="rule-immuno">
								{
									!isOtherPageCite &&
									<a onClick={this.operatorRuleImmnuo.bind(this, obj)}>
										{obj.hasImmuno ? ruleImmunoLang.common("modify") : ruleImmunoLang.common("add")}
									</a>
								}
							</div> */}
							<div className="rule-number">
								{obj.customId ? obj.customId.substring(0, 18) : ""}
							</div>
						</div>
					);
				};
				let renderDom = [];
				let itemObj = (
					<Panel
						header={title(item, [index])}
						key={item.uuid}
						showArrow={false}
						forceRender={true}
					>
						{
							currentRuleUuid &&
							currentRuleUuid === item.uuid &&
							<RuleConfig
								mode={mode}
								currentRule={item}
								ruleIndexArr={[index]}
								changeActiveKey={(ruleActiveKey) => {
									dispatch({
										type: "policyDetail/setAttrValue",
										payload: {
											ruleActiveKey: ruleActiveKey
										}
									});
								}}
							/>
						}
					</Panel>
				);
				renderDom.push(itemObj);
				if (item.children && item.children.length) {
					item.children.map((subItem, subIndex) => {
						let itemObj = (
							<Panel
								className={subItem.unSave ? "rule-children un-save" : "rule-children"}
								header={title(subItem, [index, subIndex])}
								key={subItem.uuid}
								showArrow={false}
								forceRender={true}
							>
								<RuleConfig
									mode={mode}
									currentRule={subItem}
									ruleIndexArr={[index, subIndex]}
									changeActiveKey={(ruleActiveKey) => {
										dispatch({
											type: "policyDetail/setAttrValue",
											payload: {
												ruleActiveKey: ruleActiveKey
											}
										});
									}}
								/>
							</Panel>
						);
						renderDom.push(itemObj);
					});
				}
				return renderDom;
			});
		} else {
			return <div className="tc"><Spin /></div>;
		}

		return (
			<div className="rule-manage">
				{
					policyRules &&
					policyRules.length > 0 &&
					<div className="rule-manage-header rule-th">
						<div className="rule-name">
							{/* 规则名称 */}
							{policyDetailLang.ruleList("ruleName")}
						</div>
						<div className="rule-operator">
							{/* 操作 */}
							{policyDetailLang.ruleList("operation")}
						</div>
						<div className="rule-status">
							{/* 规则状态 */}
							{policyDetailLang.ruleList("ruleState")}
						</div>
						{/* 规则免打扰 */}
						{/* <div className="rule-immuno">
							{policyDetailLang.ruleList("ruleImmuno")}
						</div> */}
						<div className="rule-number">
							{/* 规则编号 */}
							{policyDetailLang.ruleList("ruleCode")}
						</div>
					</div>
				}
				<div className="rule-manage-body">
					<Collapse
						className="rule-manage-collapse"
						activeKey={ruleActiveKey}
						onChange={(value) => {
							let uuidList = value.length > 1 ? [value[value.length - 1]] : value;
							dispatch({
								type: "policyDetail/setAttrValue",
								payload: {
									ruleActiveKey: uuidList,
									currentRuleUuid: uuidList[0]
								}
							});
						}}
					>
						{collapseList}
					</Collapse>
					{
						policyRulesReady &&
						policyRules.length === 0 &&
						<div className="no-rule-tip"></div>
					}
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(RuleList);
