import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Form, Row, Col, message } from "antd";
import { policyDealAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { dealTypeLang } from "@/constants/lang";
import ColorPick from "../ColorPick";

class ModifyDeal extends PureComponent {
	constructor(props) {
		super(props);
		this.changeDialogDataHandle = this.changeDialogDataHandle.bind(this);
		this.modifyDealType = this.modifyDealType.bind(this);
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyDealStore } = this.props;
		let { modifyDealTypeData } = policyDealStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		} else if (type === "color") {
			value = e.hex;
		}
		modifyDealTypeData[field] = value;
		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				modifyDealTypeData: modifyDealTypeData
			}
		});
	}

	modifyDealType() {
		let { dispatch, policyDealStore } = this.props;
		let { modifyDealTypeData } = policyDealStore.dialogData;
		let { dealType } = policyDealStore;
		let { curPage, pageSize } = dealType;
		const reg = /^[1-9]\d*$/;
		if (!modifyDealTypeData.grade) {
			// lang:风险决策等级不能为空
			message.warning(dealTypeLang.modal("levelCannotEmpty"));
			return;
		}
		if (!reg.test(modifyDealTypeData.grade)) {
			// lang:风险决策等级只能输入正整数
			message.warning(dealTypeLang.modal("intType"));
			return;
		}
		if (!modifyDealTypeData.color) {
			// lang:颜色值不能为空
			message.warning(dealTypeLang.modal("colorCannotEmpty"));
			return;
		}

		policyDealAPI.modifyDealType(modifyDealTypeData).then(res => {
			if (res.success) {
				dispatch({
					type: "policyDeal/setDialogShow",
					payload: {
						modifyDealType: false
					}
				});
				// lang:修改风险决策成功
				message.success(dealTypeLang.modal("modifySuccessTip"));
				dispatch({
					type: "policyDeal/getDealTypes",
					payload: {
						curPage: curPage,
						pageSize: pageSize
					}
				});
			} else {
				console.log(res.message);
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
    	let { policyDealStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = policyDealStore;
    	let { modifyDealTypeData } = dialogData;
    	return (
    		<Modal
    			title={dealTypeLang.modal("modifyDealType")} // lang:修改风险决策
    			visible={dialogShow.modifyDealType}
    			maskClosable={false}
    			onOk={this.modifyDealType.bind(this)}
    			onCancel={() => {
    				dispatch({
    					type: "policyDeal/setDialogShow",
    					payload: {
    						modifyDealType: false
    					}
    				});
    				dispatch({
    					type: "policyDeal/setDialogData",
    					payload: {
    						modifyDealTypeData: {
    							uuid: null,
    							dealType: null,
    							dealName: null,
    							grade: null
    						}
    					}
    				});
    			}}
    		>
    			<Form className="modal-form">
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={6} className="basic-info-title">
    						{/* lang:风险决策名称：*/}
    						{dealTypeLang.modal("name")}
    					</Col>
    					<Col span={18}>
    						<Input
    							value={modifyDealTypeData.dealName || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "dealName", "input")}
    							disabled={true}
    							placeholder={dealTypeLang.modal("identifierPlaceholder")} // lang:请输入风险决策标识
    						/>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={6} className="basic-info-title">
    						{/* lang:风险决策标识： */}
    						{dealTypeLang.modal("identifier")}
    					</Col>
    					<Col span={18}>
    						<Input
    							value={modifyDealTypeData.dealType || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "dealType", "input")}
    							disabled={true}
    							placeholder={dealTypeLang.modal("identifierPlaceholder")} // lang:请输入风险决策标识
    						/>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={6} className="basic-info-title">
    						{/* lang:风险决策等级： */}
    						{dealTypeLang.modal("level")}
    					</Col>
    					<Col span={18}>
    						<Input
    							value={modifyDealTypeData.grade || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "grade", "input")}
    							placeholder={dealTypeLang.modal("levelPlaceholder")} // lang:请输入风险决策等级
    						/>
    						<span className="tip-text">
    							{/* lang:注：风险决策等级数字越大等级越高 */}
    							{dealTypeLang.modal("levelTip")}
    						</span>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={6} className="basic-info-title">
    						{/* lang:颜色值 */}
    						{dealTypeLang.modal("color")}
    					</Col>
    					<Col span={18}>
							{
								dialogShow.modifyDealType &&
								<ColorPick
									changeDialogDataHandle={this.changeDialogDataHandle.bind(this)}
									color={modifyDealTypeData.color}
								/>
							}
    					</Col>
    				</Row>
    			</Form>
    		</Modal>
    	);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(ModifyDeal);
