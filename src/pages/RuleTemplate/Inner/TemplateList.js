import { PureComponent } from "react";
import { connect } from "dva";
import { <PERSON><PERSON>, Popconfirm, message, Icon, Popover, Menu, Dropdown } from "antd";

const ButtonGroup = Button.Group;

class TemplateList extends PureComponent {

	constructor(props) {
		super(props);
		this.changeTemplateType = this.changeTemplateType.bind(this);
	}

	changeTemplateType(type) {
		let { dispatch } = this.props;

		dispatch({
			type: "template/getTemplateList",
			payload: {
				type: type
			}
		});
		dispatch({
			type: "template/setAttrValue",
			payload: {
				currentType: type,
				activeGroupIndex: 0
			}
		});
	}

	render() {
		let { templateStore, dispatch } = this.props;
		let { currentType, activeGroupIndex, ruleTemplateList, indexTemplateList, dialogData } = templateStore;

		let templateList = currentType === 1 ? ruleTemplateList : indexTemplateList;
		const groupMenu = (
			<Menu>
				<Menu.Item key="1" onClick={this.changeTemplateType.bind(this, 1)}>
					<Icon type="bars" />规则模板
    			</Menu.Item>
				<Menu.Item key="2" onClick={this.changeTemplateType.bind(this, 2)}>
					<Icon type="bars" />指标模板
    			</Menu.Item>
			</Menu>
		);

		return (
			<div style={{ position: "relative", height: "100%" }}>
				<div className="box-item-header">
					<Dropdown overlay={groupMenu}>
						<h3>
							<span>
								{currentType === 1 ? "规则模板分组列表" : "指标模板分组列表"}
							</span>
							<Icon type="caret-down" />
						</h3>
					</Dropdown>
					<ButtonGroup className="btns">
						<Button
							size="small"
							onClick={() => {
								let { addGroupData } = dialogData;
								dispatch({
									type: "template/setDialogShow",
									payload: {
										addGroup: true
									}
								});
								addGroupData["type"] = currentType === 1 ? 3 : 4;
								dispatch({
									type: "template/setDialogData",
									payload: {
										addGroupData: addGroupData
									}
								});
							}}
						>
                            添加分组
						</Button>
					</ButtonGroup>
				</div>
				<div className="box-item-body">
					<ul className="system-menu-list">
						{
							templateList &&
                            templateList.map((item, index) => {
                            	return (
                            		<li
                            			className={activeGroupIndex === index ? "system-menu-item active" : "system-menu-item"}
                            			onClick={() => {
                            				if (activeGroupIndex !== index) {
                            					dispatch({
                            						type: "template/setAttrValue",
                            						payload: {
                            							activeGroupIndex: index
                            						}
                            					});
                            				}
                            			}}
                            			key={index}
                            		>
                            			<div className="system-menu-title">
                            				<span>{item["groupDisplayName"]}</span>
                            				<div className="oper-list">
                            					<Popconfirm
                            						title="确定要删除当前分组吗？"
                            						onConfirm={() => {
                            							message.error("暂时不提供组删除");
                            						}}
                            						okText="删除"
                            						cancelText="取消"
                            					>
                            						<Icon type="delete" />
                            					</Popconfirm>
                            					<Popover placement="left"
                            						content={
                            							<div>
                            								<p>中文显示名：{item.groupDisplayName}</p>
                            								<p>英文显示名：{item.groupEnDisplayName}</p>
                            								<p>标识：{item.groupName}</p>
                            								<p>排序：{item.sort}</p>
                            								<p>唯一Id：{item.groupId}</p>
                            							</div>
                            						}
                            						title={item.groupDisplayName + "信息"}
                            					>
                            						<Icon type="profile" />
                            					</Popover>
                            					<Icon
                            						type="edit"
                            						onClick={() => {
                            							let { modifyGroupData } = dialogData;
                            							dispatch({
                            								type: "template/setDialogShow",
                            								payload: {
                            									modifyGroup: true
                            								}
                            							});
                            							modifyGroupData["type"] = currentType === 1 ? 3 : 4;
                            							modifyGroupData["name"] = item.groupName;
                            							modifyGroupData["displayName"] = item.groupDisplayName;
                            							modifyGroupData["enDisplayName"] = item.groupEnDisplayName;
                            							modifyGroupData["sort"] = item.sort;
                            							modifyGroupData["id"] = item.groupId;
                            							dispatch({
                            								type: "template/setDialogData",
                            								payload: {
                            									modifyGroupData: modifyGroupData
                            								}
                            							});
                            						}}
                            					/>
                            				</div>
                            			</div>
                            		</li>
                            	);
                            })
						}
					</ul>
				</div>
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(TemplateList);
