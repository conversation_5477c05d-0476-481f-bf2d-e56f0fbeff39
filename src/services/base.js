import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

const getAllAppName = async(params) => {
	return request(getUrl("/getAllAppName", params), {
		method: "GET",
		headers: getHeader()
	});
};
const getAllMap = async() => {
	return request("/admin/selectors/all", {
		method: "GET",
		headers: getHeader()
	});
};
const getAllMap1 = async() => {
	return request("/admin/selectors/all/part1", {
		method: "GET",
		headers: getHeader()
	});
};
const getAllMap2 = async() => {
	return request("/admin/selectors/all/part2", {
		method: "GET",
		headers: getHeader()
	});
};
const getConfigs = async(params) => {
	return request(getUrl("/admin/configs", params), {
		method: "GET",
		headers: getHeader()
	});
};
const updateConfig = async(params) => {
	return request("/admin/configs", {
		method: "PUT",
		headers: getHeader(),
		body: params
	});
};

export default {
	getAllAppName,
	getAllMap,
	getAllMap1,
	getAllMap2,
	getConfigs,
	updateConfig
};
