import dva from "dva";
import "moment/locale/zh-cn";
import browserHistory from "history/createBrowserHistory";
import { message } from "antd";
import router from "./router";
import "./styles/style.less";

// 创建应用，返回 dva 实例

const app = dva({
	history: browserHistory(),
	onError(e) {
		message.error(e.message, 3);
	}
});

// 配置 hooks 或者注册插件
// app.use(nprogressDva());
app.model(require("./models/global").default);
app.model(require("./models/user").default);
app.model(require("./models/template").default);

// 公共策略
app.model(require("./models/publicPolicyRunning").default);
app.model(require("./models/publicPolicyEditor").default);

app.model(require("./models/policyEditor").default);
app.model(require("./models/policyRunning").default);
app.model(require("./models/replayTask").default);

app.model(require("./models/policyDetail").default);
app.model(require("./models/policyDeal").default);
app.model(require("./models/indexEditor").default);
app.model(require("./models/indexRunning").default);
app.model(require("./models/login").default);
app.model(require("./models/approvalTask").default);
app.model(require("./models/approvalLog").default);
app.model(require("./models/blackList").default);
app.model(require("./models/fieldSet").default);

app.model(require("./models/workflow").default);

app.model(require("./models/immuneConfig").default);
app.model(require("./models/immuneConfigList").default);
app.model(require("./models/immuneHistory").default);

app.model(require("./models/reCallTask").default);

app.router(router);
app.start("#root");

export default app._store;
