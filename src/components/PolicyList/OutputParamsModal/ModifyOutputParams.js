import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Modal, Select, Row, Col, Icon, message, Button } from "antd";
import { checkFunctionHasPermission } from "@/utils/permission";
import { policyEditorAPI } from "@/services";
import { policyListLang, commonLang } from "@/constants/lang";
import "./ModifyOutputParams.less";

const { Option } = Select;
class ModifyOutputParams extends PureComponent {
	state = {
		errInd: -1
	}
	submitParams = () => {
		const { policyEditorStore } = this.props;
		const { dialogData } = policyEditorStore || {};
		const { outputParamsData = {} } = dialogData;
		const { returnFields = [] } = outputParamsData || {};
		let warnMsg = "";
		returnFields.forEach((v, i)=>{
			if (v === "" && !warnMsg) {
				this.setState({
					errInd: i
				});
				warnMsg = policyListLang.outputModal("existNullField")(i);// 第${i + 1}组参数未选择系统字段
				return;
			};
		});
		if (warnMsg) {
			message.warning(warnMsg);
			return;
		}
		policyEditorAPI.customReturnFields({
			...outputParamsData,
			returnFields: JSON.stringify(returnFields)
		}).then(data=>{
			if (data.success) {
				message.success(policyListLang.outputModal("operaSuc")); // 操作策略集出参成功
				this.onCancel();
			} else {
				message.error(data.message || policyListLang.outputModal("operaFail")); // 操作策略集出参失败
			}
		}).catch(e=>{
			message.error(e.message || policyListLang.outputModal("operaFail"));
		});
	}
	onCancel = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				outputParams: false
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				outputParamsData: {
					publicSetUuid: "",
					returnFields: [""]
				}
			}
		});
		this.setState({
			errInd: -1
		});
	}
	operaOutput = (index) => {
		const { policyEditorStore, dispatch } = this.props;
		const { dialogData } = policyEditorStore || {};
		const { outputParamsData = {} } = dialogData;
		const { returnFields: returnFieldsOld = [] } = outputParamsData || {};
		let returnFields = [...returnFieldsOld];
		if (index || index === 0) {
			returnFields.splice(index, 1);
		} else {
			returnFields.push("");
		}
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				outputParamsData: {
					...outputParamsData,
					returnFields
				}
			}
		});
	}
	changeField = (value, index) => {
		const { policyEditorStore, dispatch } = this.props;
		const { dialogData } = policyEditorStore || {};
		const { outputParamsData = {} } = dialogData;
		const { returnFields: returnFieldsOld = [] } = outputParamsData || {};
		let returnFields = [...returnFieldsOld];
		returnFields[index] = value;
		this.setState({
			errInd: -1
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				outputParamsData: {
					...outputParamsData,
					returnFields
				}
			}
		});
	}
	render() {
		const { globalStore, policyEditorStore } = this.props;
		const { allMap } = globalStore;
		const { ruleAndIndexFieldList = [] } = allMap || {};
		const { dialogData, dialogShow } = policyEditorStore || {};
		const { outputParams } = dialogShow;
		const { outputParamsData = {} } = dialogData;
		const { returnFields = []} = outputParamsData || {};
		const { errInd } = this.state;
		const footer = [
			<Button onClick={this.onCancel}>
				{/* 取消 */}
				{commonLang.base("cancel")}
			</Button>
		];
		if (checkFunctionHasPermission("ZB0101", "addResponseConfig")) {
			footer.push(
				<Button onClick={this.submitParams} type="primary">
					{/* 确定 */}
					{commonLang.base("ok")}
				</Button>
			);
		}
		return (
			<Modal
				title={ policyListLang.outputModal("modifyTitle") } // 新增策略集出参
				visible={outputParams}
				maskClosable={true}
				footer={footer}
				onCancel={this.onCancel}
			>
				<Fragment>
					<Row gutter={36} className="mb10">
						<Col span={19}>
							{/* 出参字段 */}
							{policyListLang.outputModal("outputField")}
						</Col>
						<Col span={5}>
							{/* 操作 */}
							{policyListLang.outputModal("opera")}
						</Col>
					</Row>
					{
						returnFields &&
						returnFields.length > 0 &&
						returnFields.map((v, index)=>{
							return (
								<Row gutter={36} className={`output-field ${errInd === index ? "output-field-error" : ""}`} type="flex" align="middle">
									<Col span={19}>
										<Select
											placeholder={policyListLang.outputModal("systemFieldPlaceHolder")} // 请选择系统字段
											className="wid-100-percent"
											value={v || undefined}
											onChange={(e)=>this.changeField(e, index)}
											filterOption={ (input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 }
											showSearch
										>
											{
												ruleAndIndexFieldList &&
												ruleAndIndexFieldList.length > 0 &&
												ruleAndIndexFieldList.map((item)=>{
													return (
														<Option
															disabled={returnFields.indexOf(item.name) > -1}
															value={item.name}
															key={item.name}
															title={item.dName}
														>
															{item.dName}
														</Option>
													);
												})
											}
										</Select>
									</Col>
									<Col span={5}>
										{
											returnFields.length > 1 &&
											<Icon
												type="minus-square"
												className="output-icon"
												onClick={()=>this.operaOutput(index)}
											/>
										}
										{
											returnFields.length === (index + 1) &&
											<Icon
												type="plus-square"
												className="output-icon"
												onClick={()=>this.operaOutput()}
											/>
										}
									</Col>
								</Row>
							);
						})
					}
				</Fragment>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(ModifyOutputParams);
