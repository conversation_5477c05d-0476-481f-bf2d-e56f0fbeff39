import React, { PureComponent, Fragment } from "react";
import { connect } from "dva";
import moment from "moment";
import { isJSON } from "@/utils/isJSON";
import { Button, Menu, message, Icon, Tooltip, Input, Popover, Popconfirm, Dropdown, Tag } from "antd";
import { cloneDeep } from "lodash";
import MultiDimList from "./MultiDimList";

class CustomRuleConfig extends PureComponent {
    state = {
    	params: [
    		{
    			"name": "defineType",
    			"type": "string",
    			"value": ""
    		},
    		{
    			"name": "definitionList",
    			"type": "string",
    			"value": ""
    		},
    		{
    			"name": "score",
    			"type": "int",
    			"value": ""
    		},
    		{
    			"name": "conditions",
    			"type": "string",
    			"value": [
    				{
    					"leftValue": "",
    					"right": "",
    					"weight": 10
    				},
    				{
    					"leftValue": "",
    					"right": "",
    					"weight": 10
    				}
    			]
    		}
    	]
    };

    constructor(props) {
    	super(props);
    }

    render() {
    	let { globalStore, currentParamInfo, simpleCfgList, onChange, disabled } = this.props;
    	let { personalMode } = globalStore;
    	let lang = personalMode.lang === "cn" ? "cn" : "en";

    	disabled = !!disabled;

    	return (
    		<Fragment>
    			{
    				currentParamInfo.property &&
                    currentParamInfo.property === "multiDimList/customList" &&
                    <MultiDimList
                    	currentParamInfo={currentParamInfo}
                    	simpleCfgList={simpleCfgList}
                    	lang={lang}
                    	params={currentParamInfo && currentParamInfo.params || []}
                    	onChange={onChange}
                    	disabled={disabled}
                    />
    			}
    		</Fragment>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(CustomRuleConfig);

