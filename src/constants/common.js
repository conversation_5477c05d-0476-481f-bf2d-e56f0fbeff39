const conditionTypeMap = {
	"&&": "满足所有条件",
	"||": "满足任意条件",
	"!&&": "条件均不满足"
};

const gutterSpan = 10;

const fieldSetTypeOption = [
	{
		name: "mobile",
		dName: "手机号"
	},
	{
		name: "ID",
		dName: "身份证"
	},
	{
		name: "address",
		dName: "地址"
	},
	{
		name: "company",
		dName: "公司名称"
	}
];

const tagColor = ["blue", "geekblue", "purple", "cyan", "gold", "orange", "volcano", "red", "magenta"];

export const compareSignList = [
	{
		name: "等于",
		enName: "equal to",
		value: "=="
	},
	{
		name: "不等于",
		enName: "unequal to",
		value: "!="
	},
	{
		name: "大于",
		enName: "greater than",
		value: ">"
	},
	{
		name: "小于",
		enName: "less than",
		value: "<"
	},
	{
		name: "大于等于",
		enName: "greater than or equal to",
		value: ">="
	},
	{
		name: "小于等于",
		enName: "less than or equal to",
		value: "<="
	}
];

// 计算公式函数
export const formulaMethodList = [
	{ name: "平均值", value: "平均值(,)", realValue: "avg" },
	{ name: "最大值", value: "最大值(,)", realValue: "max" },
	{ name: "最小值", value: "最小值(,)", realValue: "min" },
	{ name: "求和", value: "求和(,)", realValue: "sum" },
	{ name: "空赋值", value: "空赋值(,)", realValue: "nvl" }
];

export default {
	conditionTypeMap,
	gutterSpan,
	fieldSetTypeOption,
	tagColor
};
