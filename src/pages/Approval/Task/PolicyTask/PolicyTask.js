import { PureComponent } from "react";
import { connect } from "dva";
import { approvalTaskAPI } from "@/services";
import { Button, Input, Table, Pagination, message, DatePicker, Tag, Select } from "antd";
import moment from "moment";
import PolicyApprovalModal from "../TaskModal/PolicyApprovalModal";
import { formatPolicyRules } from "@/utils/formatPolicyRules";
import { checkFunctionHasPermission } from "@/utils/permission";
import { approvalTaskLang, commonLang } from "@/constants/lang";
import { approvalTaskConstants } from "@/constants";

const { RangePicker } = DatePicker;
const { Option } = Select;
const { operationTypeMap } = approvalTaskConstants;
class PolicyTask extends PureComponent {
	state = {};

	constructor(props) {
		super(props);
		this.startSearch = this.startSearch.bind(this);
		this.changePagination = this.changePagination.bind(this);
		this.changeSearchParamField = this.changeSearchParamField.bind(this);
		this.policyApprovalHandle = this.policyApprovalHandle.bind(this);
	}

	componentDidMount() {
		// 页面初始化数据
		this.timer = setInterval(() => {
			const { globalStore } = this.props;
			const { menuTreeReady } = globalStore;
			if (menuTreeReady) {
				clearInterval(this.timer);
				if (checkFunctionHasPermission("ZB0401", "policyApprovalSearch")) {
					this.startSearch();
				}
			}
		}, 100);
	}

	startSearch() {
		let { approvalTaskStore, globalStore, dispatch } = this.props;
		let { policyPage } = approvalTaskStore;
		let { pageSize, searchParams } = policyPage;
		let { currentApp } = globalStore;
		if (currentApp.name !== "all") {
			searchParams["appName"] = currentApp.name;
		} else {
			searchParams["appName"] = "";
		}
		dispatch({
			type: "approvalTask/getPolicyApprovalTaskList",
			payload: {
				curPage: 1,
				pageSize: pageSize,
				...searchParams
			}
		});
	}

	componentWillReceiveProps(nextProps) {
		let { approvalTaskStore, dispatch, globalStore } = this.props;
		let { menuTreeReady } = globalStore;
		let { policyPage } = approvalTaskStore;
		let { curPage, pageSize, searchParams } = policyPage;
		let preCurrentApp = this.props.globalStore.currentApp;
		let nextCurrentApp = nextProps.globalStore.currentApp;
		if (!menuTreeReady || !true) {
			// 这里放权限
			return;
		}
		if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
			if (nextCurrentApp.name !== "all") {
				searchParams["appName"] = nextCurrentApp.name;
			} else {
				searchParams["appName"] = "";
			}

			dispatch({
				type: "approvalTask/getPolicyApprovalTaskList",
				payload: {
					curPage: curPage,
					pageSize: pageSize,
					...searchParams
				}
			});
		}
	}

	changePagination(curPage, pageSize) {
		let { approvalTaskStore, dispatch } = this.props;
		let { policyPage } = approvalTaskStore;
		let { searchParams } = policyPage;

		dispatch({
			type: "approvalTask/getPolicyApprovalTaskList",
			payload: {
				curPage: curPage,
				pageSize: pageSize,
				...searchParams
			}
		});
	}
	changeSearchParamField(field, type, e) {
		let { approvalTaskStore, dispatch } = this.props;
		let { policyPage } = approvalTaskStore;
		let { searchParams } = policyPage;

		let value = e;
		if (type === "input") {
			value = e.target.value;
		} else if (type === "date") {
			value = e;
		}
		if (field === "targetName") {
			searchParams[field] = value;
		} else if (field === "selectTs") {
			let startStr = moment(value[0]).format("YYYY-MM-DD") + " 00:00:00";
			let endStr = moment(value[1]).format("YYYY-MM-DD") + " 23:59:59";
			searchParams["startTs"] = value.length === 2 ? Date.parse(startStr) : null;
			searchParams["endTs"] = value.length === 2 ? Date.parse(endStr) : null;
		} else {
			searchParams[field] = value;
		}
		dispatch({
			type: "approvalTask/setAttrValue",
			payload: {
				policyPage: policyPage
			}
		});
	}

	policyApprovalHandle(item) {
		let { approvalTaskStore, dispatch } = this.props;
		let { dialogData } = approvalTaskStore;
		let { policyApprovalData } = dialogData;

		dispatch({
			type: "approvalTask/setDialogShow",
			payload: {
				policyApproval: true
			}
		});

		policyApprovalData.uuid = item.uuid;
		dispatch({
			type: "approvalTask/setDialogData",
			payload: {
				policyApprovalData: policyApprovalData
			}
		});

		let params = {
			uuid: item.uuid,
			h: "hi"
		};
		approvalTaskAPI.getPolicyApprovalTaskDiff(params).then(res => {
			if (res.success) {
				let onlineVersion = res.data && res.data.onlineVersion ? res.data.onlineVersion : null;
				let pendVersion = res.data && res.data.pendVersion ? res.data.pendVersion : null;

				if (onlineVersion && onlineVersion.rules) {
					onlineVersion["rules"] = formatPolicyRules(onlineVersion["rules"]);
					if (onlineVersion["dealTypeMapping"]) {
						onlineVersion["dealTypeMapping"] = JSON.parse(onlineVersion["dealTypeMapping"]);
					}
				}
				if (pendVersion && pendVersion.rules) {
					pendVersion["rules"] = formatPolicyRules(pendVersion["rules"]);
					if (pendVersion["dealTypeMapping"]) {
						pendVersion["dealTypeMapping"] = JSON.parse(pendVersion["dealTypeMapping"]);
					}
				}

				policyApprovalData["diffDetail"]["onlineVersion"] = onlineVersion;
				policyApprovalData["diffDetail"]["pendVersion"] = pendVersion;
				policyApprovalData.publicPolicy = item.publicPolicy;
				dispatch({
					type: "approvalTask/setDialogData",
					payload: {
						policyApprovalData: policyApprovalData
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { approvalTaskStore } = this.props;
		let { policyPage } = approvalTaskStore;
		let { taskList, total, curPage, searchParams } = policyPage;
		let { startTs, endTs } = searchParams;
		let columns = [
			{
				title: approvalTaskLang.table("operationType"),		// lang:变更类型
				render: (record) => {
					return (
						<div>
							{
								record["operationType"] &&
								operationTypeMap[record["operationType"]] &&
								operationTypeMap[record["operationType"]]()["text"]
							}
						</div>
					);
				}
			},
			{
				title: approvalTaskLang.table("policyType"),		// lang:策略类型
				dataIndex: "publicPolicy",
				render: (publicPolicy) => {
					return (
						<Tag color={publicPolicy ? "green" : "gold"}>
							{/* 公共策略  普通策略 */}
							{publicPolicy ? approvalTaskLang.table("publicPolicy") : approvalTaskLang.table("normalPolicy")}
						</Tag>
					);
				}
			},
			{
				title: approvalTaskLang.table("policyName"),		// lang:策略名称
				dataIndex: "targetName"
			},
			{
				title: approvalTaskLang.table("publishDesc"),		// lang:发版描述
				dataIndex: "description"
			},
			{
				title: approvalTaskLang.table("createdBy"),		// lang:提交人
				dataIndex: "createdBy"
			},
			{
				title: approvalTaskLang.table("submitTime"),		// lang:提交时间
				dataIndex: "gmtCreate"
			},
			{
				title: approvalTaskLang.table("operation"),		// lang:操作
				width: 180,
				render: (record) => {
					return (
						<div className="table-action">
							<a
								className={checkFunctionHasPermission("ZB0401", "changePolicyApproval") ? null : "a-disabled"}
								onClick={() => {
									if (checkFunctionHasPermission("ZB0401", "changePolicyApproval")) {
										this.policyApprovalHandle(record);
									} else {
										// lang:没有权限
										message.info(commonLang.messageInfo("noPermission"));
									}
								}}
							>
								{/* lang:审批 */}
								{approvalTaskLang.table("approval")}
							</a>
						</div>
					);
				}
			}
		];
		return (
			<div>
				<div className="page-global-body">
					<div className="page-global-body-search">
						<div className="left-info">
							<h2>
								{/* lang:策略审批任务列表 */}
								{approvalTaskLang.common("policyTitle2")}
							</h2>
						</div>
						<div className="right-info">
							<div className="right-info-item">
								<Select
									className="w200"
									allowClear
									value={searchParams.isPublicPolicy || undefined}
									onChange={this.changeSearchParamField.bind(this, "isPublicPolicy", "select")}
									placeholder={approvalTaskLang.searchParams("policyType")} // lang:请选择策略类型
									disabled={!checkFunctionHasPermission("ZB0401", "policyApprovalSearch")}
								>
									{/* 公共策略 */}
									<Option value="1">{approvalTaskLang.table("publicPolicy")}</Option>
									{/* 普通策略 */}
									<Option value="0">{approvalTaskLang.table("normalPolicy")}</Option>
								</Select>
							</div>
							<div className="right-info-item">
								<RangePicker
									onChange={this.changeSearchParamField.bind(this, "selectTs", "date")}
									style={{ width: "270px" }}
									value={startTs && endTs ? [moment(startTs), moment(endTs)] : []}
									format="YYYY-MM-DD"
									disabled={!checkFunctionHasPermission("ZB0401", "policyApprovalSearch")}
								/>
							</div>
							<div className="right-info-item">
								<Input
									placeholder={approvalTaskLang.searchParams("policyNamePlaceholder")} // lang:请输入策略名称
									value={searchParams.targetName || undefined}
									onChange={this.changeSearchParamField.bind(this, "targetName", "input")}
									onPressEnter={this.startSearch.bind(this)}
									disabled={!checkFunctionHasPermission("ZB0401", "policyApprovalSearch")}
								/>
							</div>
							<div className="right-info-item">
								<Button
									type="primary"
									className="search-button"
									onClick={this.startSearch.bind(this)}
									disabled={!checkFunctionHasPermission("ZB0401", "policyApprovalSearch")}
								>
									{/* lang:搜索 */}
									{approvalTaskLang.searchParams("search")}
								</Button>
							</div>
						</div>
					</div>
					<div className="page-global-body-main">
						<Table
							className="table-card-body"
							columns={columns}
							dataSource={taskList}
							pagination={false}
							rowKey="id"
						/>
						<div className="page-global-body-pagination">
							<span className="count">
								{commonLang.getRecords(total)}
							</span>
							<Pagination
								showQuickJumper
								showSizeChanger
								onChange={this.changePagination.bind(this)}
								onShowSizeChange={this.changePagination.bind(this)}
								current={curPage}
								total={total}
							/>
						</div>
					</div>
				</div>
				<PolicyApprovalModal />
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	approvalTaskStore: state.approvalTask
}))(PolicyTask);
