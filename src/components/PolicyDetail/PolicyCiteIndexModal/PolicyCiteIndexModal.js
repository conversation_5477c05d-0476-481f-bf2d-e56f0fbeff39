import { PureComponent } from "react";
import { connect } from "dva";
import { Modal } from "antd";
import "./PolicyCiteIndexModal.less";
import RunningArea from "@/pages/Policy/Salaxy/RunningArea";
import { policyDetailLang } from "@/constants/lang";

class PolicyCiteIndexModal extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { policyDetailStore, dispatch } = this.props;
		let { dialogShow } = policyDetailStore;
		let { PolicyCiteIndex } = dialogShow;

		return (
			<Modal
				title={policyDetailLang.policyCiteIndexModal("title")} // 当前策略引用指标列表
				visible={PolicyCiteIndex}
				width={1100}
				className="policy-cite-index-wrap"
				maskClosable={true}
				onOk={() => {
				}}
				onCancel={() => {
					dispatch({
						type: "policyDetail/setDialogShow",
						payload: {
							PolicyCiteIndex: false
						}
					});
				}}
				footer={null}
			>
				<div className="select-template-content">
					<RunningArea
						isOtherPageCite={true}
						citePageName="policyDetail"
					/>
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	templateStore: state.template,
	policyDetailStore: state.policyDetail
}))(PolicyCiteIndexModal);
