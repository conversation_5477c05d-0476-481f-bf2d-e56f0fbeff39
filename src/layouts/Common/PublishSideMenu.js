import { PureComponent } from "react";
import { connect } from "dva";
import { Link } from "dva/router";
import { Icon, Layout, Menu } from "antd";
import pathToRegexp from "path-to-regexp";
import { getStrLen } from "@/utils/utils";
import config from "@/common/config";

const { Sider } = Layout;

// 解决不在菜单中的路径在左边菜单栏中失去选中项
const notInMenuPath = [
	{
		curPath: "/index/policy/policyDetail/:policyUuid",
		parentPath: "/index/policy/policyList"
	},
	{
		curPath: "/index/policy/versionPolicyDetail/:policyUuid",
		parentPath: "/index/policy/policyList"
	},
	{
		curPath: "/index/policy/publicPolicyDetail/:policyUuid",
		parentPath: "/index/policy/publicPolicyList"
	},
	{
		curPath: "/index/policy/versionPublicPolicyDetail/:policyUuid",
		parentPath: "/index/policy/publicPolicyList"
	}
];
class PublishSideMenu extends PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			activeGroupCode: null
		};
		this.triggerGroupHandle = this.triggerGroupHandle.bind(this);
		this.switchGroup = this.switchGroup.bind(this);
	}

	componentWillMount() {
		let { dispatch } = this.props;
		dispatch({
			type: "global/setAttrValue",
			payload: {
				sideMenu: {
					openKeys: this.getInitKey().openKeys,
					activeGroupCode: this.getInitKey().activeGroupCode
				}
			}
		});
	}
	triggerGroupHandle(item) {
		let { globalStore, dispatch } = this.props;
		let { sideMenu } = globalStore;
		let { activeGroupCode } = sideMenu;

		let groupCode = item.code;
		if (activeGroupCode === groupCode) {
			groupCode = null;
		}

		dispatch({
			type: "global/setAttrValue",
			payload: {
				sideMenu: {
					activeGroupCode: groupCode
				}
			}
		});
	}

    onOpenChange = (openKeys) => {
    	let { globalStore, dispatch } = this.props;
    	let { sideMenu } = globalStore;
    	let openKey = sideMenu.openKeys;
    	let latestOpenKey = openKeys.find(key => openKey.indexOf(key) === -1);
    	dispatch({
    		type: "global/setAttrValue",
    		payload: {
    			sideMenu: {
    				openKeys: latestOpenKey ? [latestOpenKey] : []
    			}
    		}
    	});
    };

    switchGroup(item, { key }) {
    	let { globalStore, dispatch } = this.props;
    	let { sideMenu } = globalStore;
    	let { openKeys } = sideMenu;

    	if (openKeys.length > 0 && openKeys[0] === key) {
    		dispatch({
    			type: "global/setAttrValue",
    			payload: {
    				sideMenu: {
    					activeGroupCode: null
    				}
    			}
    		});
    	} else {
    		dispatch({
    			type: "global/setAttrValue",
    			payload: {
    				sideMenu: {
    					activeGroupCode: item.code
    				}
    			}
    		});
    	}
    };

    getInitKey = () => {
    	const { globalStore: { customTree }, location: { pathname } } = this.props;
    	let obj = {
    		openKeys: [],
    		activeGroupCode: null
    	};

    	customTree["menuTree"] && customTree["menuTree"].forEach((item, index) => {
    		item.children && item.children.length > 0 && item.children.forEach((subItem) => {
    			if (subItem.path.indexOf(pathname) > -1) {
    				obj.openKeys = [(index + 1).toString()];
    				obj.sortNo = (index + 1).toString();
    				obj.activeGroupCode = item.code;
    			}
    		});
    	});
    	return obj;
    };

    render() {
    	let { globalStore, location: { pathname } } = this.props;
    	let { customTree, personalMode, sideMenu} = globalStore;
    	let { collapsed, openKeys, activeGroupCode } = sideMenu;
    	let { lang } = personalMode;

    	let productNameLen = getStrLen(customTree.name ? customTree.name : "");
    	let fontSize = "22px";
    	if (productNameLen <= 12) {
    		fontSize = "22px";
    	} else if (productNameLen === 13) {
    		fontSize = "21px";
    	} else if (productNameLen === 14) {
    		fontSize = "20px";
    	} else if (productNameLen === 15) {
    		fontSize = "19px";
    	} else if (productNameLen >= 16) {
    		fontSize = "18px";
    	}

    	// 匹配当前路径是否在左侧菜单中
    	let selectedPathName = pathname;
    	let findPath = false;
    	const {menuTree = []} = customTree || {};
    	menuTree && menuTree.length > 0 && menuTree.map((v)=>{
    		const { children = [] } = v;
    		children && children.length > 0 && children.map(subItem=>{
    			if (subItem.path === pathname) {
    				findPath = true;
    			}
    		});
    	});
    	if (!findPath) {
    		notInMenuPath.find((v)=>{
    			if (pathToRegexp(v.curPath).test(pathname)) {
    				selectedPathName = v.parentPath;
    			}
    		});
    	}
    	return (
    		<Sider
    			collapsible
    			collapsed={collapsed}
    			trigger={null}
    			style={{ backgroundColor: "#fff" }}
    			width={220}
    		>
    			<div className="logo-shadow-mask"></div>
    			<div className={collapsed ? "logo collapsed" : "logo"}>
    				{
    					customTree.name &&
                        <div className="logo-icon">
                        	<img
                        		style={{ opacity: customTree.logo && customTree.logo.indexOf("white") ? 0.85 : 1 }}
                        		src={`/salaxy-resource/logo/${customTree.logo}`}
                        		onError={(e) => {
                        			e.target.onerror = null;
                        			e.target.src = "/salaxy-resource/logo/logo-custom.svg";
                        		}}
                        	/>
                        </div>
    				}
    				{
    					!collapsed &&
                        <span style={{ fontSize: fontSize }}>
                        	{
                        		customTree.name ? lang === "en" ? customTree.enName : customTree.name : ""
                        	}
                        </span>
    				}
    			</div>
    			<Menu
    				mode="inline"
    				defaultSelectedKeys={[pathname]}
    				openKeys={openKeys}
    				onOpenChange={this.onOpenChange}
    				selectedKeys={[selectedPathName]}
    			>
    				{
    					customTree["menuTree"] && customTree["menuTree"].map((item, index) => {
    						let groupName;
    						if (personalMode.lang === "en") {
    							groupName = item["enName"] ? item["enName"] : "No Group Name";
    						} else {
    							groupName = item["groupName"] ? item["groupName"] : "暂无分组";
    						}

    						return (
    							<Menu.SubMenu
    								className="left-menu"
    								expandIcon={
    									!collapsed ? <Icon
    										type={activeGroupCode === item.code ? "minus" : "plus"}
    										className="menu-arrow-right icon2"
    									/> : null
    								}
    								key={index + 1}
    								title={
    									<div className="group-item">
    										<h3>
    											<i className={item.groupIcon ? "icon1 iconfont icon-" + item.groupIcon : "icon1 iconfont icon-hecha"}></i>
    											<span>{groupName}</span>
    										</h3>
    									</div>
    								}
    								onTitleClick={this.switchGroup.bind(this, item)}
    							>
    								{
    									item.children && item.children.length > 0 && item.children.map((subItem, subIndex) => {
    										let menuName;
    										if (personalMode.lang === "en") {
    											menuName = subItem["enName"] ? subItem["enName"] : "No Menu Name";
    										} else {
    											menuName = subItem["menuName"] ? subItem["menuName"] : "暂无菜单";
    										}

    										return (
    											<Menu.Item
    												key={subItem.path}
    												className="menu-list"
    												onClick={({ key }) => {
    													/*
														* path==>key
														* group index==>index
														* group info==>item
														* */

    													let { dispatch, globalStore } = this.props;
    													let { sideMenu } = globalStore;
    													let openKeysNumber = index + 1;
    													sideMenu["activeGroupCode"] = item.code;

    													if (!collapsed) {
    														sideMenu["openKeys"] = [openKeysNumber.toString()];
    														sideMenu["beforeOpenKeys"] = [openKeysNumber.toString()];
    													} else {
    														sideMenu["openKeys"] = [openKeysNumber.toString()];
    													}

    													dispatch({
    														type: "global/setAttrValue",
    														payload: {
    															sideMenu: sideMenu
    														}
    													});
    													// 将结果备份到localStorage
    													localStorage.setItem("sideMenu", JSON.stringify(sideMenu));
    												}}
    											>
    												{
    													subItem.path.indexOf(`${config.routerPrefix}/`) > -1 &&
                                                        <Link
                                                        	to={subItem.path}
                                                        	target={subItem.target}
                                                        	style={{ paddingLeft: "8px" }}
                                                        >
                                                        	<span>{menuName}</span>
                                                        </Link>
    												}
    												{
    													subItem.path.indexOf(`${config.routerPrefix}/`) === -1 &&
                                                        <a
                                                        	href={subItem.path}
                                                        	target={subItem.target}
                                                        	style={{ paddingLeft: "8px" }}
                                                        >
                                                        	<span>{menuName}</span>
                                                        </a>
    												}
    											</Menu.Item>
    										);
    									})
    								}
    							</Menu.SubMenu>
    						);
    					})
    				}
    			</Menu>
    		</Sider>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global
}))(PublishSideMenu);

