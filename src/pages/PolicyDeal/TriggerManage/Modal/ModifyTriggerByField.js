import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Select, Form, Row, Col, message } from "antd";
import { CommonConstants } from "@/constants";
import { policyDealAPI } from "@/services";
import { checkFunctionHasPermission } from "@/utils/permission";

const Option = Select.Option;

class ModifyTriggerByField extends PureComponent {
	constructor(props) {
		super(props);
		this.changeDialogDataHandle = this.changeDialogDataHandle.bind(this);
		this.saveTrigger = this.saveTrigger.bind(this);
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyDealStore } = this.props;
		let { modifyTriggerFieldData } = policyDealStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		modifyTriggerFieldData[field] = value;
		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				modifyTriggerFieldData: modifyTriggerFieldData
			}
		});
	}

	saveTrigger() {
		let { dispatch, policyDealStore } = this.props;
		let { modifyTriggerFieldData } = policyDealStore.dialogData;
		let { trigger } = policyDealStore;
		let { searchParams } = trigger;
		searchParams.manageType = "untrigger";

		if (!modifyTriggerFieldData.dealType) {
			message.warning("请选择风险决策");
			return;
		}

		if (!modifyTriggerFieldData.matchField) {
			message.warning("请选择系统字段");
			return;
		}

		if (!modifyTriggerFieldData.matchValue) {
			message.warning("请选择匹配值");
			return;
		}

		let params = {
			dealType: modifyTriggerFieldData.dealType,
			fieldConfig: JSON.stringify({
				matchField: modifyTriggerFieldData.matchField,
				matchValue: modifyTriggerFieldData.matchValue
			})
		};

		if (modifyTriggerFieldData.isUpdate) {
			policyDealAPI.modifyTriggerField(params).then(res => {
				if (res.success) {
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							modifyTriggerByField: false
						}
					});
					message.success("修改触发配置成功");
					dispatch({
						type: "policyDeal/getTriggers",
						payload: searchParams
					});
				} else {
					console.log(res.message);
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		} else {
			policyDealAPI.addTriggerField(params).then(res => {
				if (res.success) {
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							modifyTriggerByField: false
						}
					});
					message.success("新建触发配置成功");
					dispatch({
						type: "policyDeal/getTriggers",
						payload: searchParams
					});
				} else {
					console.log(res.message);
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
			});
		}
	}

	render() {
		let { policyDealStore, globalStore, dispatch } = this.props;
		let { dialogShow } = policyDealStore;
		let { modifyTriggerFieldData } = policyDealStore.dialogData;
		let { allMap } = globalStore;
		let title = modifyTriggerFieldData.isUpdate ? "修改按字段触发" : "新建按字段触发";

		return (
			<Modal
				title={title}
				visible={dialogShow.modifyTriggerByField}
				maskClosable={false}
				onOk={() => {
					if (!modifyTriggerFieldData.isUpdate && checkFunctionHasPermission("ZB0301", "addUnTriggerByField")) {
						this.saveTrigger();
					} else if (modifyTriggerFieldData.isUpdate && checkFunctionHasPermission("ZB0301", "updateUnTriggerByField")) {
						this.saveTrigger();
					} else {
						message.warning("无权限操作");
					}
				}}
				onCancel={() => {
					dispatch({
						type: "policyDeal/setDialogShow",
						payload: {
							modifyTriggerByField: false
						}
					});
				}}
			>
				<Form className="modal-form">
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
                            系统字段：
						</Col>
						<Col span={18}>
							<Select
								placeholder="请选择系统字段"
								value={modifyTriggerFieldData.matchField || undefined}
								onChange={this.changeDialogDataHandle.bind(this, "matchField", "select")}
								showSearch
								optionFilterProp="children"
								dropdownMatchSelectWidth={false}
							>
								{
									allMap &&
                                    allMap["ruleFieldList"] &&
                                    allMap["ruleFieldList"].map((item, index) => {
                                    	return (<Option value={item.name} index={index}>{item.dName}</Option>);
                                    })
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">匹配值：</Col>
						<Col span={18}>
							<Input
								value={modifyTriggerFieldData.matchValue || undefined}
								onChange={this.changeDialogDataHandle.bind(this, "matchValue", "input")}
								placeholder="请输入匹配值"
							/>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(ModifyTriggerByField);
