import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Input, Icon, Tooltip, Select, Row, Col, Radio } from "antd";
import { CommonConstants, PolicyConstants } from "@/constants";
import { indexListLang, policyDetailLang } from "@/constants/lang";
import { isJSON } from "@/utils/isJSON";
import OneCondition from "./OneCondition";
import "./index.less";

const InputGroup = Input.Group;
const Option = Select.Option;
const TextArea = Input.TextArea;

class CommonFilter extends PureComponent {

	constructor(props) {
		super(props);
	}

    addCondition = (type) => {
    	let { onChange, filterObj = {} } = this.props;

    	let conditionTemp;
    	if (filterObj && (!filterObj.logicOperator || !(filterObj.children && filterObj.children.length > 0))) {
    		filterObj = {
    			logicOperator: filterObj.logicOperator || "&&",
    			children: []
    		};
    	}
    	if (type === "single") {
    		conditionTemp = {
    			operator: "==",
    			leftValueType: "STRING",
    			leftVarType: "context",
    			leftVar: null,
    			rightVarType: "input",
    			rightVar: null
    		};
    	} else if (type === "group") {
    		conditionTemp = {
    			logicOperator: "&&",
    			children: [
    				{
    					operator: "==",
    					leftValueType: "STRING",
    					leftVarType: "context",
    					leftVar: null,
    					rightVarType: "input",
    					rightVar: null
    				}
    			]
    		};
    	}
    	filterObj.children.push(conditionTemp);
    	onChange(filterObj);
    }
    changeRadio(e) {
    	const { value } = e.target;
    	const { filterObj, onChange } = this.props;
    	const newFilterObj = Object.assign({}, filterObj, { logicOperator: value });
    	onChange(newFilterObj);
    }
    render() {
    	let { globalStore, filterObj, onChange, disabled } = this.props;
    	let { allMap, personalMode } = globalStore;
    	let lang = personalMode.lang === "cn" ? "cn" : "en";
    	return (
    		<Fragment>
    			<Row
    				gutter={CommonConstants.gutterSpan}
    			>
    				<Col span={4} className="basic-info-title">
    					{/* lang:过滤条件 */}
    					{indexListLang.ruleAttr("filterConditions")}
    				</Col>
    				<Col span={20}>
    					<Radio.Group
    						value={(filterObj && filterObj.logicOperator) || "&&"}
    						onChange={this.changeRadio.bind(this)}
    						disabled={disabled}
    					>
    						<Radio value={"&&"}>{policyDetailLang.ruleCondition("mztj1")}</Radio>
    						<Radio value={"||"}>{policyDetailLang.ruleCondition("mztj2")}</Radio>
    						<Radio value={"!&&"}>{policyDetailLang.ruleCondition("mztj3")}</Radio>
    					</Radio.Group>
    				</Col>
    			</Row>

    			{
    				filterObj &&
                    filterObj.children &&
                    <div className="multiple-condition-list mt10">
                    	{
                    		filterObj.children.map((item, index) => {
                    			if (item.logicOperator) {
                    				// 条件组
                    				return (
                    					<div
                    						className="multiple-condition-item has-line"
                    						key={index}
                    					>
                    						{
                    							item.children.map((groupItem, groupIndex) => {
                    								return (
                    									<OneCondition
                    										conditionData={item}
                    										conditionSingleData={groupItem}
                    										conditionType="group"
                    										conditionArr={[index, groupIndex]}
                    										key={groupIndex}
                    										onChange={onChange}
                    										filterObj={filterObj}
                    										disabled={disabled}
                    									/>
                    								);
                    							})
                    						}
                    					</div>
                    				);
                    			} else {
                    				// 单条条件
                    				return (
                    					<div
                    						className="multiple-condition-item has-line"
                    						key={index}
                    					>
                    						<OneCondition
                    							conditionData={null}
                    							conditionSingleData={item}
                    							conditionType="single"
                    							conditionArr={[index]}
                    							onChange={onChange}
                    							filterObj={filterObj}
                    							disabled={disabled}
                    						/>
                    					</div>
                    				);
                    			}
                    		})
                    	}
                    </div>
    			}
    			<Row
    				gutter={CommonConstants.gutterSpan}
    				style={{ marginBottom: "10px" }}
    			>
    				<Col span={4} className="basic-info-title"></Col>
    				<Col
    					span={20}
    					className="add-new-filter"
    				>
    					<a
    						className={disabled ? "disabled" : ""}
    						style={{ marginRight: "10px" }}
    						onClick={() => {
    							if (!disabled) {
    								this.addCondition("single");
    							}
    						}}
    					>
                            添加单条条件
    					</a>
    					<a
    						className={disabled ? "disabled" : ""}
    						onClick={() => {
    							if (!disabled) {
    								this.addCondition("group");
    							}
    						}}
    					>
                            添加条件组
    					</a>
    				</Col>
    			</Row>
    		</Fragment>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(CommonFilter);
