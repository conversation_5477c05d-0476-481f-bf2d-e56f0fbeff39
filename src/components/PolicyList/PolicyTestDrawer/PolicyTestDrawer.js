import React from "react";
import { connect } from "dva";
import { <PERSON><PERSON>, <PERSON>ton, Row, Col, Input, Icon, Select, Tooltip, Alert, InputNumber } from "antd";
import "./PolicyTestDrawer.less";
import { policyEditorAPI } from "@/services";
import PolicyTestDrawerChild from "./PolicyTestDrawerChild";
import { CommonConstants, StandFieldConstants } from "@/constants";
import { debugLang } from "@/constants/lang";
import { randomMap } from "./randomData";

const Option = Select.Option;
const S_DT_VS_PARTNERCODE = StandFieldConstants("S_DT_VS_PARTNERCODE");
const S_DT_VS_APPNAME = StandFieldConstants("S_DT_VS_APPNAME");
const S_DT_VS_EVENTID = StandFieldConstants("S_DT_VS_EVENTID");
const D_T_VB_EVENTOCCURTIME = StandFieldConstants("D_T_VB_EVENTOCCURTIME");

class PolicyTestDrawer extends React.PureComponent {

    state = {
    	errTip: false,
    	errTipText: null,
    	sendTimes: 1
    };

    constructor(props) {
    	super(props);
    	this.closeDrawerHandle = this.closeDrawerHandle.bind(this);
    	this.setRandomData = this.setRandomData.bind(this);
    	this.policyTest = this.policyTest.bind(this);
    	this.changeFieldValue = this.changeFieldValue.bind(this);
    	this.addSelectFieldItem = this.addSelectFieldItem.bind(this);
    	this.removeSelectFieldItem = this.removeSelectFieldItem.bind(this);
    	this.changeSelectField = this.changeSelectField.bind(this);
    }

    closeDrawerHandle() {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "policyEditor/setDialogShow",
    		payload: {
    			policyDrawer: false
    		}
    	});
    }

    setRandomData(field, type) {
    	let { policyEditorStore, dispatch } = this.props;
    	let { dialogData } = policyEditorStore;
    	let { policyTestDrawer } = dialogData;

    	let result = randomMap(type);
    	let index = Math.floor(Math.random() * result.length);
    	let key = result[index];
    	policyTestDrawer["policyDetail"][field] = key;
    	dispatch({
    		type: "policyEditor/setAttrValue",
    		payload: {
    			policyTestDrawer: policyTestDrawer
    		}
    	});
    }

    policyTest() {
    	let { policyEditorStore, dispatch } = this.props;
    	let { dialogData } = policyEditorStore;
    	let { policyTestDrawer } = dialogData;
    	let params = policyTestDrawer.policyDetail;
    	let { sendTimes } = this.state;

    	if (!sendTimes || sendTimes === 0) {
    		sendTimes = 1;
    	}
    	let newParams = {
    		operationType: "send",
    		[S_DT_VS_PARTNERCODE]: params[S_DT_VS_PARTNERCODE],
    		[S_DT_VS_APPNAME]: params[S_DT_VS_APPNAME],
    		[S_DT_VS_EVENTID]: params[S_DT_VS_EVENTID],
    		[D_T_VB_EVENTOCCURTIME]: params[D_T_VB_EVENTOCCURTIME]
    	};
    	params["selectFieldList"] && params["selectFieldList"].map(item => {
    		if (item.key && item.value) {
    			newParams[item.key] = item.value;
    		}
    	});
    	for (let i = 0; i < sendTimes; i++) {
    		policyEditorAPI.policyTest(newParams).then(res => {
    			if (res.success) {
    				policyTestDrawer.resultList.push(res);
    				dispatch({
    					type: "policyEditor/setDialogData",
    					payload: {
    						policyTestDrawer: policyTestDrawer
    					}
    				});
    				if (i === 0) {
    					dispatch({
    						type: "policyEditor/setDialogShow",
    						payload: {
    							policyTestDrawerChild: true
    						}
    					});
    				}
    			} else {
    				this.setState({
    					errTip: true,
    					errTipText: res.reason_code
    				});

    			}
    		}).catch(err => {
    			console.log(err);
    		});
    	}
    }

    changeFieldValue(field, type, e) {
    	let { policyEditorStore, dispatch } = this.props;
    	let { dialogData } = policyEditorStore;
    	let { policyTestDrawer } = dialogData;

    	let value = "";
    	if (type === "input") {
    		value = e.target.value;
    	}

    	policyTestDrawer["policyDetail"][field] = value;

    	dispatch({
    		type: "policyEditor/setDialogData",
    		payload: {
    			policyTestDrawer: policyTestDrawer
    		}
    	});
    }

    addSelectFieldItem() {
    	let { policyEditorStore, dispatch } = this.props;
    	let { dialogData } = policyEditorStore;
    	let { policyTestDrawer } = dialogData;
    	let { selectFieldList } = policyTestDrawer["policyDetail"];

    	let tempObj = {
    		key: null,
    		value: null
    	};

    	selectFieldList.push(tempObj);

    	dispatch({
    		type: "policyEditor/setDialogData",
    		payload: {
    			policyTestDrawer: policyTestDrawer
    		}
    	});
    }

    removeSelectFieldItem(index) {
    	let { policyEditorStore, dispatch } = this.props;
    	let { dialogData } = policyEditorStore;
    	let { policyTestDrawer } = dialogData;
    	let { selectFieldList } = policyTestDrawer["policyDetail"];

    	selectFieldList.splice(index, 1);
    	dispatch({
    		type: "policyEditor/setDialogData",
    		payload: {
    			policyTestDrawer: policyTestDrawer
    		}
    	});
    }

    changeSelectField(field, index, e) {
    	let { policyEditorStore, dispatch } = this.props;
    	let { dialogData } = policyEditorStore;
    	let { policyTestDrawer } = dialogData;
    	let { selectFieldList } = policyTestDrawer["policyDetail"];

    	let value;
    	if (field === "key") {
    		value = e;
    	} else if (field === "value") {
    		value = e.target.value;
    	}

    	selectFieldList[index][field] = value;
    	dispatch({
    		type: "policyEditor/setDialogData",
    		payload: {
    			policyTestDrawer: policyTestDrawer
    		}
    	});
    }

    render() {
    	let { policyEditorStore, globalStore, dispatch } = this.props;
    	let { dialogShow, dialogData } = policyEditorStore;
    	let { allMap } = globalStore;
    	let { errTip, errTipText, sendTimes } = this.state;

    	let { policyTestDrawer } = dialogData;
    	let { policyDetail } = policyTestDrawer;
    	let eventName = policyDetail["S_DT_VS_EVENTTYPE"] && allMap && allMap["EventType"] ? allMap["EventType"].find(item => item.name === policyDetail["S_DT_VS_EVENTTYPE"]) : undefined;
    	eventName = eventName ? eventName["dName"] : undefined;
    	return (
    		<div>
    			<Drawer
    				title={debugLang.base("policySetDebug")} // lang:策略集测试
    				width={650}
    				closable={true}
    				className="policy-test-wrap"
    				onClose={() => {
    					dispatch({
    						type: "policyEditor/setDialogShow",
    						payload: {
    							policyTestDrawer: false
    						}
    					});
    					this.setState({
    						errTip: false,
    						errTipText: null
    					});
    					policyTestDrawer.policyDetail = {
    						operationType: "send",
    						[S_DT_VS_PARTNERCODE]: null,
    						[S_DT_VS_APPNAME]: null,
    						[S_DT_VS_EVENTID]: null,
    						S_DT_VS_EVENTTYPE: null,
    						[D_T_VB_EVENTOCCURTIME]: null,
    						times: 1,
    						selectFieldList: []
    					};
    					dispatch({
    						type: "policyEditor/setDialogData",
    						payload: {
    							policyTestDrawer: policyTestDrawer
    						}
    					});
    				}}
    				visible={dialogShow.policyTestDrawer}
    			>
    				<PolicyTestDrawerChild />
    				<div className="policy-test-main">
    					<div className="policy-test-header">
    						<i className="iconfont icon-spider title-spider"></i>
    						{/* lang:策略集测试 */}
    						<span className="policy-detail-title-text">{debugLang.base("policySetDebug")}</span>
    						<i className="iconfont icon-close title-close"
    							onClick={() => {
    								dispatch({
    									type: "policyEditor/setDialogShow",
    									payload: {
    										policyTestDrawer: false
    									}
    								});
    							}}>
    						</i>
    					</div>
    					<div className="policy-test-body">
    						<div className="basic-form">
    							{
    								errTip &&
                                    <Alert
                                    	message="Error"
                                    	type="error"
                                    	showIcon
                                    	description={errTipText}
                                    	closable
                                    	onClose={() => {
                                    		this.setState({
                                    			errTip: false,
                                    			errTipText: null
                                    		});
                                    	}}
                                    />
    							}
    							<Row gutter={CommonConstants.gutterSpan}>
    								<Col span={5} className="basic-info-title">
    									{/* lang:合作方*/}
    									{debugLang.base("partnerCode")}
    									<em>*</em>：</Col>
    								<Col span={8}>
    									<Input
    										type="text"
    										placeholder={debugLang.base("partnerCodePlaceholder")} // lang:请输入合作方名称
    										value={policyDetail[S_DT_VS_PARTNERCODE]}
    										onChange={this.changeFieldValue.bind(this, S_DT_VS_PARTNERCODE, "input")}
    										disabled={true}
    									/>
    								</Col>
    							</Row>
    							<Row gutter={CommonConstants.gutterSpan}>
    								<Col span={5} className="basic-info-title">
    									{/* lang:应用标识 */}
    									{debugLang.base("appName")}
    									<em>*</em>：</Col>
    								<Col span={8}>
    									<Input
    										type="text"
    										value={policyDetail[S_DT_VS_APPNAME]}
    										disabled={true}
    									/>
    								</Col>
    							</Row>
    							<Row gutter={CommonConstants.gutterSpan}>
    								<Col span={5} className="basic-info-title">
    									{/* lang:事件类型 */}
    									{debugLang.base("eventType")}
    									<em>*</em>：</Col>
    								<Col span={8}>
    									<Input
    										type="text"
    										value={eventName}
    										disabled={true}
    									/>
    								</Col>
    							</Row>
    							<Row gutter={CommonConstants.gutterSpan}>
    								<Col span={5} className="basic-info-title">
    									{/* lang:事件标识 */}
    									{debugLang.base("eventId")}
    									<em>*</em>：</Col>
    								<Col span={8}>
    									<Input
    										type="text"
    										value={policyDetail[S_DT_VS_EVENTID]}
    										disabled={true}
    									/>
    								</Col>
    							</Row>
    							<Row gutter={CommonConstants.gutterSpan}>
    								<Col span={5} className="basic-info-title">
    									{/* lang:事件发生时间 */}
    									{debugLang.base("eventTime")}
    									<em>*</em>：</Col>
    								<Col span={8}>
    									<Input
    										type="text"
    										value={policyDetail[D_T_VB_EVENTOCCURTIME] || undefined}
    										onChange={this.changeFieldValue.bind(this, D_T_VB_EVENTOCCURTIME, "input")}
    										placeholder={debugLang.base("eventTimePlaceholder")} // lang:请填写事件发生时间
    									/>
    								</Col>
    							</Row>
    							<Row gutter={CommonConstants.gutterSpan}>
    								<Col span={5} className="basic-info-title">
    									{/* lang:自定义字段 */}
    									{debugLang.base("customField")}
                                        ：</Col>
    								<Col span={8} style={{ lineHeight: "32px" }}>
    									<a onClick={this.addSelectFieldItem.bind(this)}>
    										{/* lang:添加 */}
    										{debugLang.base("addField")}
    									</a>
    								</Col>
    							</Row>
    							{
    								policyDetail["selectFieldList"] &&
                                    policyDetail["selectFieldList"].map((item, index) => {
                                    	return (
                                    		<Row gutter={CommonConstants.gutterSpan} key={index}>
                                    			<Col span={5} className="basic-info-title"></Col>
                                    			<Col span={8}>
                                    				<Select
                                    					placeholder={debugLang.base("selectCustomField")} // lang:请选择字段
                                    					value={item.key || undefined}
                                    					onChange={this.changeSelectField.bind(this, "key", index)}
                                    					showSearch
                                    					optionFilterProp="children"
                                    					dropdownMatchSelectWidth={false}
                                    				>
                                    					{
                                    						allMap &&
                                                            allMap["ruleFieldList"] &&
                                                            allMap["ruleFieldList"].map((fieldItem, fieldIndex) => {
                                                            	let obj = policyDetail.selectFieldList && policyDetail.selectFieldList.find(fItem => fItem.key === fieldItem.name);
                                                            	return (
                                                            		<Option
                                                            			value={fieldItem.name}
                                                            			title={fieldItem.dName}
                                                            			key={fieldIndex}
                                                            			disabled={!!obj}
                                                            		>
                                                            			{fieldItem.dName}
                                                            		</Option>
                                                            	);
                                                            })
                                    					}
                                    				</Select>
                                    			</Col>
                                    			<Col span={8}>
                                    				<Input
                                    					value={item.value || undefined}
                                    					placeholder={debugLang.base("enterFieldValuePlaceholder")} // lang:请输入传值
                                    					onChange={this.changeSelectField.bind(this, "value", index)}
                                    				/>
                                    			</Col>
                                    			<Col span={2} className="basic-info-oper">
                                    				{/* lang:删除当前行 */}
                                    				<Tooltip title={debugLang.message("deleteCurrentRow")}
                                    					placement="right">
                                    					<Icon
                                    						className="delete"
                                    						type="delete"
                                    						onClick={this.removeSelectFieldItem.bind(this, index)}
                                    					/>
                                    				</Tooltip>
                                    			</Col>
                                    		</Row>
                                    	);
                                    })
    							}
    							<Row gutter={CommonConstants.gutterSpan}>
    								<Col span={5} className="basic-info-title">
    									{/* lang:发送次数*/}
    									{debugLang.base("sendTimes")}：
    								</Col>
    								<Col span={8}>
    									<InputNumber
    										type="text"
    										style={{ width: "100%" }}
    										min={1}
    										max={10}
    										step={1}
    										value={sendTimes || 1}
    										onChange={(value) => {
    											this.setState({
    												sendTimes: parseInt(value, 10)
    											});
    										}}
    									/>
    								</Col>
    							</Row>
    						</div>
    					</div>
    					<div className="policy-test-footer">
    						<Button
    							onClick={this.policyTest.bind(this)}
    							type="dashed"
    						>
    							{/* lang:点击测试 */}
    							{debugLang.base("clickDebug")}
    						</Button>
    					</div>
    				</div>
    			</Drawer>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(PolicyTestDrawer);
