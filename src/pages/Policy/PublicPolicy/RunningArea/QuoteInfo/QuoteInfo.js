import React from "react";
import { connect } from "dva";
import { routerRedux } from "dva/router";
import { Drawer, Icon } from "antd";
import { publicPolicyListLang } from "@/constants/lang";
import "./QuoteInfo.less";

class QuoteInfo extends React.PureComponent {

	state = {
		totalEventList: []
	}
	constructor(props) {
		super(props);
	}

	// 跳转详情
	goPolicySet = (policySetUuid, policySetName, type) => {
		let { dispatch, location } = this.props;
		let pathname = location.pathname;
		let prefix = pathname.indexOf("/index/") > -1 ? "/index" : "";
		let baseUrl;
		if (type === "running") {
			baseUrl = `/policy/policyList?currentTab=1&policyUuid=${policySetUuid}&policySetName=${policySetName}`;
		} else {
			baseUrl = `/policy/policyList?currentTab=2&policyUuid=${policySetUuid}&policySetName=${policySetName}`;
		}
		dispatch(routerRedux.push(prefix + baseUrl));
	}

	transferRef = (refs) => {
		const newRefs = {};
		refs &&
			refs.length > 0 &&
			refs.map((v) => {
				if (!newRefs[v.appName]) {
					newRefs[v.appName] = [v];
				} else {
					newRefs[v.appName].push(v);
				}
			});
		return newRefs;
	}
	pendTrDom = (refsArr, type) => {
		let { globalStore } = this.props;
		const { appList = []} = globalStore;
		const trArray = [];
		console.log(appList);
		Object.keys(refsArr) && Object.keys(refsArr).forEach((key) => {
			const appDName = (appList.find((v) => { return v.name === key; }) || {}).dName;
			if (appDName) {
				const child = refsArr[key];
				child && child.length > 0 && child.map((item, i) => {
					const { policySetUuid, policySetName } = item;
					trArray.push(
						<tr>
							{
								i === 0 &&
								<td rowspan={child.length}>{appDName}</td>
							}
							<td>
								<a onClick={this.goPolicySet.bind(this, policySetUuid, policySetName, type)}>
									{policySetName}
								</a>
							</td>
						</tr >
					);
				});
			}

		});
		return trArray;
	}
	render() {
		const { policyDetail = {}, visible, onClose } = this.props;
		const {onlineRef, pendRef} = policyDetail || {};
		const onlineTrDom = this.pendTrDom(this.transferRef(onlineRef), "running");
		const pendTrDom = this.pendTrDom(this.transferRef(pendRef), "editor");
		return (
			<Drawer
				title={publicPolicyListLang.quoteDrawer("title")} // "公共策略引用详情"
				width={550}
				className="quote-drawer-wrap"
				placement="right"
				closable={false}
				onClose={onClose}
				visible={visible}
				mask={true}
			>
				<div className="quote-detail">
					<div className="quote-detail-title">
						<span
							className="quote-detail-title-text text-overflow"
							style={{
								maxWidth: "400px",
								display: "inline-block",
								verticalAlign: "bottom"
							}}
						>
							{policyDetail.policyName}
						</span>
						<i className="iconfont icon-close title-close" onClick={onClose}></i>
					</div>
					<div className="quote-detail-base">
						<div className="quote-detail-group">
							<label>
								{/* 修改时间 */}
								{publicPolicyListLang.quoteDrawer("modifyTime")}
							</label>
							<span>
								{policyDetail.gmtModified}
							</span>
						</div>
						<div className="quote-detail-group">
							<label>
								{/* 修改人 */}
								{publicPolicyListLang.quoteDrawer("modifier")}
							</label>
							<span>
								{policyDetail.updatedBy}
							</span>
						</div>
					</div>
					{
						onlineTrDom &&
						onlineTrDom.length > 0 &&
						<div className="quote-detail-rule">
							<h4>
								{/* 运行区引用明细 */}
								{publicPolicyListLang.quoteDrawer("runtimeReferDetail")}
							</h4>
							<table className="effect-scope wid-100-percent">
								<thead>
									<tr>
										<th width="150px">
											{/* lang:应用 */}
											{publicPolicyListLang.quoteDrawer("application")}
										</th>
										<th>
											{/* lang:策略集 */}
											{publicPolicyListLang.quoteDrawer("policySet")}
										</th>
									</tr>
								</thead>
								<tbody>
									{onlineTrDom}
								</tbody>
							</table>
						</div>
					}

					{
						pendTrDom &&
						pendTrDom.length > 0 &&
						<div className="quote-detail-rule">
							<h4>
								{/* 编辑区引用明细 */}
								{publicPolicyListLang.quoteDrawer("editorReferDetail")}
							</h4>
							<table className="effect-scope wid-100-percent no-max-width">
								<thead>
									<tr>
										<th width="150px">
											{/* lang:应用 */}
											{publicPolicyListLang.quoteDrawer("application")}
										</th>
										<th>
											{/* lang:策略集 */}
											{publicPolicyListLang.quoteDrawer("policySet")}
										</th>
									</tr>
								</thead>
								<tbody>
									{pendTrDom}
								</tbody>
							</table>
						</div>
					}
				</div>
				{
					!(
						(onlineRef && onlineRef.length > 0) ||
						(pendRef && pendRef.length > 0)
					) &&
					<div className="quote-detail-rule tc no-quote">
						<Icon type="frown" className="mr10"/>
						{/* 暂无引用 */}
						{publicPolicyListLang.quoteDrawer("noRefer")}
					</div>
				}

				{/* 如果有引用信息，但是两者都没有应用权限 */}
				{
					(
						(
							(onlineRef && onlineRef.length > 0) ||
							(pendRef && pendRef.length > 0)
						) &&
						!(
							(onlineTrDom && onlineTrDom.length > 0) ||
							(pendTrDom && pendTrDom.length > 0)
						)
					) &&
					<div className="quote-detail-rule tc no-quote">
						<Icon type="frown" className="mr10"/>
						{/* 暂无权限查看 */}
						{publicPolicyListLang.quoteDrawer("noRight")}
					</div>
				}
			</Drawer>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(QuoteInfo);
