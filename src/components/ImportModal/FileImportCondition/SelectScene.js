import { PureComponent } from "react";
import { Row, Col, Radio, Icon, Tooltip } from "antd";
import { CommonConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";

export default class SelectScene extends PureComponent {
	render() {
		const { type } = this.props;
		let defaultMsg = "";
		switch (type) {
			case "policySet": defaultMsg = indexListLang.ruleConfig("scenePolicySet"); break; // 默认选择导入策略作为指标场景
			case "rules": defaultMsg = indexListLang.ruleConfig("sceneRules"); break; // 默认选择导入规则作为指标场景
			case "workflow": defaultMsg = indexListLang.ruleConfig("sceneWorkFlow"); break; // 默认选择导入规则流作为指标场景
			case "index": defaultMsg = indexListLang.ruleConfig("sceneIndex"); break; //  忽略异常不处理
		}
		return (
			<Row gutter={CommonConstants.gutterSpan} className="import-mode-wrap select-scene">
				<Col span={5} className="basic-info-title">
					{/* lang:异常处理 */}
					{indexListLang.ruleConfig("exceptHandle")}
				</Col>
				<Col span={19}>
					<Radio checked>
						<span style={{ "whiteSpace": "pre-wrap" }}>
							{defaultMsg}
							{
								type === "index" &&
								// 缺失异常项不展示，可能存在部分下拉框内容为空的情况
								<Tooltip title={indexListLang.ruleConfig("indexMissWarnTip")}>
									<Icon className="ml10" type="question-circle" />
								</Tooltip>
							}
						</span>
					</Radio>
				</Col>
			</Row>
		);
	}
}
