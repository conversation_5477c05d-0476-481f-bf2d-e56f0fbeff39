import { PureComponent } from "react";
import { connect } from "dva";
import { Modal } from "antd";

class ModifySalaxyModal extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { indexEditorStore, dispatch } = this.props;
		let { dialogShow } = indexEditorStore;

		return (
			<Modal
				title="修改指标"
				width={750}
				visible={dialogShow.modifySalaxy || dialogShow.addSalaxy}
				onOk={() => {
				}}
				onCancel={() => {
					dispatch({
						type: "indexEditor/setDialogShow",
						payload: {
							addSalaxy: false,
							modifySalaxy: false
						}
					});
				}}
			>
				<div>
                    修改指标
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor
}))(ModifySalaxyModal);
