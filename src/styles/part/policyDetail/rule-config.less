@import "../../base/variables";

:global {
    .rule-config-wrap {
        & {}

        .basic-info-title {
            text-align: right;
            line-height: 28px; //32px;
        }

        .basic-info-text {
            text-align: center;
            line-height: 28px; //32px;
        }

        .add-new-operation,
        .add-new-filter {
            span {
                &:hover {
                    color: @blue;
                    cursor: pointer;
                }

                i {
                    margin-right: 8px;
                }
            }

            a.disabled,
            a.disabled:hover {
                color: #999;
                cursor: not-allowed;
                text-decoration: none;
            }
        }

        .add-new-filter {
            line-height: 32px;
        }

        .ant-radio-group {
            line-height: 32px;
        }

        .w365 {
            width: 365px;
        }

        .w50 {
            width: 50px;
        }

        .w200 {
            width: 200px;
        }

        .ml-1 {
            margin-left: -1px;
        }

        .rule-detail {
            & {
                position: relative;
                margin: 0 0 16px;
                padding: 20px 0;
                //border: 1px solid #D4D4D4;
                border-bottom: 1px solid #d4d4d4;
            }

            &:last-child {
                margin-bottom: 0;
            }

            h4 {
                & {
                    position: relative;
                    margin: 0;
                    //position: absolute;
                    top: -12px;
                    //left: 20px;
                    font-size: 16px;
                    font-weight: normal;
                    line-height: 20px;
                    //background-color: #f6f6f6;
                    color: #000;
                    padding-left: 12px;
                }

                &:before {
                    position: absolute;
                    left: 0;
                    width: 3px;
                    height: 20px;
                    background: #000;
                    content: '';
                }
            }

            .rule-detail-content {
                & {
                    margin-top: 15px;
                    margin-bottom: 0;
                }

                &.btns {
                    button {
                        margin-right: 12px;
                    }
                }

                .rule-operation-item {
                    & {
                        margin-top: 10px;
                    }
                }

                .ant-checkbox-group {
                    line-height: 32px;
                }
            }
        }

        .rule-btns {
            & {
                margin-left: 16.66667%;
            }

            button {
                margin-right: 12px;
            }
        }

        .inline-span {
            display: inline-block;
            vertical-align: top;
            line-height: 32px;
        }
    }

    .basic-info-oper {
        & {
            font-size: 18px;
            vertical-align: top;
            color: #999;
            margin-left: 10px;
            display: inline-block;
            line-height: 30px !important;
        }

        i {
            margin-right: 10px;
            cursor: pointer;
        }

        i:last-child {
            margin-right: 0;
        }

        i.add:hover {
            color: @blue;
        }

        i.delete:hover {
            color: #f00;
        }

        i.show-more {
            transform: rotate(-90deg);
            -moz-transform: rotate(-90deg);
            -webkit-transform: rotate(-90deg);
        }

        i.show-more.hide {
            transform: rotate(90deg);
            -moz-transform: rotate(90deg);
            -webkit-transform: rotate(90deg);
        }
    }

    .param-tip-icon {
        font-size: 18px;
        color: #999;
        cursor: pointer;
        line-height: 32px;
    }
}
