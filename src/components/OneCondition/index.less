@import "../../styles/base/variables";
.rule-content-common {
	margin-top: -12px;
	&.last-no-line{
		.one-condition.custom-item {
			&:last-of-type {
				border-bottom: none;
			}
		}
	}
	.one-condition.custom-item {
		& {
			border-bottom: 1px dashed #ccc;
			padding: 8px 0 8px 0;
		}

		.one-condition {
			margin-bottom: 0 !important;
		}
	}

	.group-condition.custom-item {
		& {
			border-bottom: 1px dashed #ccc;
			padding: 8px 0 8px 0;
		}
		.one-condition {
			& {
				margin-bottom: 8px;
			}

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	.oper-icon {
		& {
			font-size: 18px;
			vertical-align: top;
			color: #999;
			margin-left: 10px;
			display: inline-block;
			line-height: 30px !important;
		}
		i {
			margin-right: 10px;
			cursor: pointer;
		}
		i.add:hover {
			color: @blue;
		}

		i.delete:hover {
			color: #f00;
		}
	}

	.rule-ctrl {
		& {
			margin-top: 12px;
			margin-bottom: 12px;
		}

		span {
			color: @blue;
			margin-right: 12px;
			cursor: pointer;
		}

		span:hover {
			text-decoration: underline;
		}
	}

	.custom-item .custom-item {
		border-bottom: 0;
		padding: 0;
		margin-bottom: 15px;
	}
}
