import { PureComponent } from "react";
import { connect } from "dva";
import { Input, Icon, Select, Row, Col, Checkbox, Popover } from "antd";
import { CommonConstants, PolicyConstants } from "@/constants";
import { indexListLang } from "@/constants/lang";
import { isJSON } from "@/utils/isJSON";
import { getSimpleCfgList, getHandleType } from "@/utils/salaxy";
import FieldList from "./FieldSet/FieldList";
import FormulaEditor from "./FormulaEditor";
import ComponentGroup from "../../Common/ComponentGroup";
import WrapperCol from "../../Common/WrapperCol/index";

const InputGroup = Input.Group;
const Option = Select.Option;
const TextArea = Input.TextArea;

class IndexCondition extends PureComponent {
	state = {};

	constructor(props) {
		super(props);
	}

	// 详情提示
	popoverTitle = (tipTitle, tipContentArr) => {
		return (
			<Col span={1}>
				<Popover
					popupClassName="rule-param-pop-tip"
					placement="right"
					title={tipTitle}
					content={
						tipContentArr.map((tip, tipIndex) => {
							return (
								<div key={tipIndex}>{tip}</div>
							);
						})
					}
				>
					<Icon
						type="question-circle-o"
						className="param-tip-icon"
					/>
				</Popover>
			</Col>
		);
	}

	changeSelfEvent = (willChangeSelf, simpleCfgList) => {
		let { globalStore } = this.props;
		let { allMap } = globalStore;
		// 获取handle 名称
		let changeHandleName = willChangeSelf && willChangeSelf.name ? willChangeSelf.name : null;
		// 获取handle 实体
		let changeHandleObj = changeHandleName ? simpleCfgList.find(fItem => fItem.name === changeHandleName) : null;
		// 获取handle value
		let changeHandleValue = changeHandleObj ? changeHandleObj.value : null;
		let ruleDisabled = false;
		let ruleHidden = false;
		let ruleMapName = null;
		let ruleMapLocation = null;
		if (willChangeSelf) {
			// console.log(changeHandleValue);
			if (willChangeSelf.changeMode && willChangeSelf.changeMode === "whenSomeValue") {
				// 当为具体值的时候
				willChangeSelf["caseList"] && willChangeSelf["caseList"].map((caseItem) => {
					// 根据子节点组合值改变自身
					let ChangeHandleByChild = null;
					if (caseItem.changeType === "hiddenByChild" && Array.isArray(changeHandleValue)) {
						changeHandleValue.forEach((changeItem) => {
							ChangeHandleByChild = ChangeHandleByChild || changeItem[caseItem.childNodeName];
						});
					}
					if (
						caseItem["modeValueList"] &&
						caseItem["modeValueList"].find(
							mvItem => (
								mvItem === changeHandleValue || mvItem === String(changeHandleValue) || mvItem === ChangeHandleByChild
							)
						)
					) {
						// 如果modeValueList列表中确实有handle value，则进行如下操作
						if (caseItem.changeType && caseItem.changeType === "disabled") {
							ruleDisabled = true;
						} else if (caseItem.changeType && (caseItem.changeType === "hidden" || caseItem.changeType === "hiddenByChild")) {
							ruleHidden = true;
						} else if (caseItem.changeType && caseItem.changeType === "selectMap") {
							// 如果是改变select map
							ruleMapName = caseItem.mapName;
							ruleMapLocation = caseItem.mapLocation;
						}
					}
				});
			} else if (willChangeSelf.changeMode && willChangeSelf.changeMode === "whenSomeType") {
				// 当为具体类型的时候
				let type = getHandleType(changeHandleObj, allMap);
				willChangeSelf["caseList"] && willChangeSelf["caseList"].map((caseItem) => {
					let ChangeHandleByChild = null;
					if (caseItem.changeType === "hiddenByChild" && Array.isArray(changeHandleValue)) {
						changeHandleValue.forEach((changeItem) => {
							ChangeHandleByChild = ChangeHandleByChild || changeItem[caseItem.childNodeName];
						});
					}
					if (caseItem["modeValueList"] && caseItem["modeValueList"].find(mvItem => mvItem.toLowerCase() === type.toLowerCase())) {
						// 如果modeValueList列表中确实有handle value，则进行如下操作
						if (caseItem.changeType && caseItem.changeType === "disabled") {
							ruleDisabled = true;
						} else if (caseItem.changeType && (caseItem.changeType === "hidden" || caseItem.changeType === "hiddenByChild")) {
							ruleHidden = true;
						} else if (caseItem.changeType && caseItem.changeType === "selectMap") {
							// 如果是改变select map
							ruleMapName = caseItem.mapName;
							ruleMapLocation = caseItem.mapLocation;
						}
					}
				});
			}
		}
		return {
			ruleDisabled,
			ruleHidden,
			ruleMapName,
			ruleMapLocation
		};
	}

	getInputAddonAfter = (subItem, indexData) => {
		let { globalStore } = this.props;
		let { allMap, personalMode } = globalStore;
		let { addonAfter = null, addonAfterListName } = subItem;
		if (addonAfterListName && addonAfter) {
			let addonAfterVal = indexData[addonAfter];
			addonAfter = allMap[addonAfterListName].find(v => (v.name === addonAfterVal));
			if (addonAfter) {
				addonAfter = personalMode.lang === "cn" ? addonAfter.dName : addonAfter.enDName;
			}
		}
		return addonAfter;
	}
	render() {
		let { templateStore, globalStore, templateName, indexData } = this.props;
		let { indexTemplateListObj } = templateStore;
		let { allMap, personalMode } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";

		let currentTemplate = indexTemplateListObj && templateName ? indexTemplateListObj[templateName] : null;
		let cfgJson = currentTemplate && currentTemplate.cfgJson && isJSON(currentTemplate.cfgJson) ? JSON.parse(currentTemplate.cfgJson) : null;
		let attachFieldsObj = indexData && indexData.attachFieldsObj ? indexData.attachFieldsObj : null;

		let colSpan = {
			"winType": 3,
			"winCount": 2,
			"winSize": 3,
			"attachFields.calMode": 5,
			"attachFields.operatorValue": 3,
			"attachFields.operatorValueType": 8,
			"attachFields.hasDimReadCheckbox": 4,
			"hasDimReadCheckbox": 4,
			"attachFields.endTime": 3
		};

		let colStyle = {
			"attachFields.hasEndTime": {
				width: "125px"
			},
			"attachFields.includeCurrent": {
				width: "136px"
			}
		};
		if (lang === "en") {
			colSpan["attachFields.hasDimReadCheckbox"] = 8;
			colSpan["hasDimReadCheckbox"] = 8;
			colStyle["attachFields.hasEndTime"] = {
				width: "185px"
			};
			colStyle["attachFields.includeCurrent"] = {
				width: "190px"
			};
		}

		// 判断是否有主属性取值非同一字段、主属性取值字段
		let hasDimReadCheckbox = false;
		let dimReadCheckboxValue = null;
		let hasDimRead1 = null;

		cfgJson && cfgJson.params && cfgJson.params.map((item) => {
			item.children && item.children.map((subItem) => {
				if (subItem.name === "attachFields.hasDimReadCheckbox") {
					hasDimReadCheckbox = true;
				}
				if (subItem.name === "dimRead1") {
					hasDimRead1 = true;
				}
			});
		});
		if (hasDimReadCheckbox && hasDimRead1) {
			dimReadCheckboxValue = attachFieldsObj && attachFieldsObj["hasDimReadCheckbox"] ? attachFieldsObj["hasDimReadCheckbox"] : undefined;
		}

		// 这里处理规则
		let simpleCfgList = getSimpleCfgList(cfgJson, indexData);

		return (
			<div className="ml-1">
				{
					// 循环处理每一行
					cfgJson && cfgJson.params && cfgJson.params.map((item, index) => {
						let tipTitle = lang === "cn" ? item["tipTitle"] : item["enTipTitle"];
						let tipContent = lang === "cn" ? item["tipContent"] : item["enTipContent"];
						let tipContentArr = [];
						if (tipContent) {
							tipContentArr = tipContent.split(/\n/);
						}
						const PopoverTitle = this.popoverTitle(tipTitle, tipContentArr);
						let RowDom = (
							<Row className="mb10" gutter={CommonConstants.gutterSpan} key={index}>
								<Col span={4} className="basic-info-title">
									{lang === "cn" ? item.labelText : item["enLabelText"]}
								</Col>
								{
									item.children && item.children.map((subItem, subIndex, arr) => {
										let span = 8;
										let styleSet = {};
										if (arr.length > 1) {
											span = colSpan[subItem.name] ? colSpan[subItem.name] : 4;
											styleSet = colStyle[subItem.name] ? colStyle[subItem.name] : {};
										}
										let value = "";
										let operatorValueType = "input";
										let isAttachField = false;
										let field = null;
										if (subItem.name.indexOf("attachFields.") > -1) {
											field = subItem.name.split(".")[1];
											value = attachFieldsObj && attachFieldsObj[field] ? attachFieldsObj[field] : undefined;
											operatorValueType = attachFieldsObj && attachFieldsObj["operatorValueType"] ? attachFieldsObj["operatorValueType"] : operatorValueType;
											isAttachField = true;
										} else {
											field = subItem.name;
											value = indexData && indexData[subItem.name] ? indexData[subItem.name] : undefined;
										}

										// 处理像ruleFieldList.DOUBLE这样类型的字段
										let selectName = "";
										let filterType = null;
										if (subItem.componentType === "select" && subItem.selectType === "service" || subItem.selectType === "local") {
											if (subItem["selectName"].indexOf("ruleFieldList.") > -1) {
												selectName = subItem["selectName"].split(".")[0];
												filterType = [subItem["selectName"].split(".")[1]];
											} else {
												selectName = subItem["selectName"];
												filterType = subItem["filterType"] && subItem["filterType"].length ? subItem["filterType"] : null;
											}
										}

										let disabled = false;
										if (indexData && !indexData["unSave"]) {
											if (subItem.canEditSecondTimes === undefined) {
												disabled = false;
											} else {
												disabled = !subItem.canEditSecondTimes;
											}
										}

										/*
										* 从这里处理changeRuleForOther规则
										* handle：指的是改变当前参数的那个值
										* */
										let currentSimpleObj = simpleCfgList.find(fItem => fItem.name === subItem.name);
										// 得到willChangeSelf
										let willChangeSelf = currentSimpleObj && currentSimpleObj.willChangeSelf ? currentSimpleObj.willChangeSelf : null;
										let {
											ruleHidden,
											ruleMapName,
											ruleMapLocation
										} = this.changeSelfEvent(willChangeSelf, simpleCfgList);

										// 最小改动，在原来的基础上新增更多改变自身事件列表。
										let willChangeMoreSelf = currentSimpleObj && currentSimpleObj.willChangeMoreSelf ? currentSimpleObj.willChangeMoreSelf : null;
										let ruleHiddenMore = false;
										willChangeMoreSelf && willChangeMoreSelf.length > 0 && willChangeMoreSelf.map((chillChangeMore) => {
											const {
												ruleHidden: curRuleHidden
											} = this.changeSelfEvent(chillChangeMore, simpleCfgList);
											ruleHiddenMore = ruleHiddenMore || curRuleHidden;
										});

										if (ruleHidden) {
											return;
										}

										if (ruleHiddenMore) {
											return;
										}

										if (subItem.componentType === "fieldSet" && indexData && indexData.calcType !== "blackList") {
											return;
										}

										if (subItem.componentType === "fieldSet") {
											span = 20;
										}
										if (subItem.componentType === "formula") {
											span = 8;
										}

										return (
											<WrapperCol span={span} componentType={subItem.componentType} styleSet={styleSet}>
												{
													subItem.componentType === "fieldSet" &&
													<FieldList indexData={indexData} />
												}
												{
													subItem.componentType === "formula" &&
													<FormulaEditor indexData={indexData} />
												}
												{
													subItem.componentType === "group" &&
													<ComponentGroup
														currentIndexObj={indexData}
														groupList={value || []}
														subItem={subItem}
														disabled={true}
														isRunning={true}
													>
														{PopoverTitle}
													</ComponentGroup>
												}
												{
													subItem.componentType === "input" &&
													subItem.name !== "attachFields.expression" &&
													<Input
														value={value}
														disabled={true}
														addonBefore={subItem.addonBefore}
														addonAfter={this.getInputAddonAfter(subItem, indexData)}
													/>
												}
												{
													subItem.componentType === "input" &&
													subItem.name === "attachFields.expression" &&
													<TextArea
														value={value}
														placeholder="输入业务链表达式" // 业务链表达式
														rows={4}
														disabled={true}
													/>
												}
												{
													subItem.componentType === "select" &&
													<Select
														placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
														value={value}
														showSearch
														optionFilterProp="children"
														disabled={true}
													>
														{
															subItem.selectType === "local" &&
															selectName &&
															!ruleMapLocation &&
															PolicyConstants[selectName] &&
															PolicyConstants[selectName].map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.name}
																		key={optionIndex}
																	>
																		{optionItem.dName}
																	</Option>
																);
															})
														}
														{
															ruleMapName &&
															ruleMapLocation &&
															ruleMapLocation === "local" &&
															PolicyConstants[ruleMapName] &&
															PolicyConstants[ruleMapName].map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.name}
																		key={optionIndex}
																	>
																		{optionItem.dName}
																	</Option>
																);
															})
														}
														{
															subItem.selectType === "self" && subItem.selectOption &&
															subItem.selectOption.map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.value}
																		key={optionIndex}
																	>
																		{optionItem.name}
																	</Option>
																);
															})
														}
														{
															subItem.selectType === "service" &&
															selectName &&
															!ruleMapLocation &&
															allMap[selectName] &&
															allMap[selectName].filter(fItem => filterType ? filterType.indexOf(fItem.type.toLowerCase()) > -1 : true).map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.name}
																		key={optionIndex}
																	>
																		{lang === "en" ? optionItem.endName ? optionItem.endName : optionItem.dName : optionItem.dName}
																	</Option>
																);
															})
														}
														{
															subItem.selectType === "service" &&
															ruleMapLocation &&
															ruleMapLocation === "service" &&
															ruleMapName &&
															allMap[ruleMapName] &&
															allMap[ruleMapName].filter(fItem => filterType ? filterType.indexOf(fItem.type.toLowerCase()) > -1 : true).map((optionItem, optionIndex) => {
																return (
																	<Option
																		value={optionItem.name}
																		key={optionIndex}
																	>
																		{lang === "en" ? optionItem.endName ? optionItem.endName : optionItem.dName : optionItem.dName}
																	</Option>
																);
															})
														}
													</Select>
												}
												{
													subItem.componentType === "checkbox" &&
													subItem.name !== "attachFields.hasDimReadCheckbox" &&
													<Checkbox.Group
														value={value ? value.toString().split(",") : undefined}
														disabled={true}
													>
														{
															subItem.selectType === "self" && subItem.selectOption &&
															subItem.selectOption.map((optionItem, optionIndex) => {
																return (
																	<Checkbox
																		value={optionItem.value}
																		key={optionIndex}
																	>
																		{lang === "cn" ? optionItem.name : optionItem.enName}
																	</Checkbox>
																);
															})
														}
														{
															subItem.selectType === "service" && subItem.selectName &&
															allMap[subItem.selectName] && allMap[subItem.selectName].map((optionItem, optionIndex) => {
																return (
																	<Checkbox
																		value={optionItem.name}
																		key={optionIndex}
																	>
																		{lang === "cn" ? optionItem.name : optionItem.enName}
																	</Checkbox>
																);
															})
														}
													</Checkbox.Group>
												}
												{
													subItem.componentType === "checkbox" &&
													subItem.name === "attachFields.hasDimReadCheckbox" &&
													<Checkbox.Group
														value={attachFieldsObj && attachFieldsObj["hasDimReadCheckbox"] ? attachFieldsObj["hasDimReadCheckbox"] : undefined}
														disabled={true}
													>
														{
															subItem.selectType === "self" && subItem.selectOption &&
															subItem.selectOption.map((optionItem, optionIndex) => {
																return (
																	<Checkbox
																		value={optionItem.value}
																		key={optionIndex}
																	>
																		{lang === "cn" ? optionItem.name : optionItem.enName}
																	</Checkbox>
																);
															})
														}
													</Checkbox.Group>
												}
												{
													subItem.componentType === "variable" &&
													<InputGroup compact>
														<Select
															value={value}
															style={{ width: "30%" }}
															disabled={true}
														>
															{/* lang:常量变量*/}
															<Option value="input">
																{indexListLang.ruleAttr("constant")}
															</Option>
															<Option
																value="context">
																{indexListLang.ruleAttr("variable")}
															</Option>
														</Select>
														{
															operatorValueType === "input" &&
															<Input
																style={{ width: "70%" }}
																value={attachFieldsObj && attachFieldsObj["operatorValue"] ? attachFieldsObj["operatorValue"] : undefined}
																placeholder={indexListLang.ruleAttr("pleaseEnter")} // lang:请输入
																disabled={true}
																addonBefore={subItem.addonBefore}
																addonAfter={this.getInputAddonAfter(subItem, indexData)}
															/>
														}
														{
															operatorValueType === "context" &&
															<Select
																style={{ width: "70%" }}
																value={attachFieldsObj && attachFieldsObj["operatorValue"] ? attachFieldsObj["operatorValue"] : undefined}
																placeholder={indexListLang.ruleAttr("pleaseSelect")} // lang:请选择
																disabled={true}
															>
																{
																	allMap && allMap["ruleFieldList"] && allMap["ruleFieldList"].map((item, index) => {
																		return (
																			<Option value={item.name} key={index}>
																				{/* lang:当前*/}
																				{indexListLang.ruleAttr("current") + item.dName}
																			</Option>
																		);
																	})
																}
															</Select>
														}
													</InputGroup>
												}
											</WrapperCol>
										);
									})
								}
								{
									tipTitle &&
									tipContent &&
									item.labelText !== "从属性" &&
									PopoverTitle
								}
							</Row>
						);

						// console.log(item);
						// 在这里处理行规则willChangeLine
						let showThisLine = false;
						let willChangeLine = JSON.stringify(item["willChangeLine"]) !== "{}" ? item["willChangeLine"] : undefined;
						if (willChangeLine) {
							let { changeValue } = willChangeLine;
							let currentSimpleObj = simpleCfgList.find(fItem => fItem.name === willChangeLine.name) || {};
							// 是否隐藏当前行
							changeValue = changeValue.split("||"); // 改成或
							showThisLine = changeValue && changeValue.filter(value => (value === currentSimpleObj.value));
							showThisLine = showThisLine && showThisLine.length > 0;
							// showThisLine = currentSimpleObj.value === willChangeLine.changeValue;
						}
						if (willChangeLine && showThisLine) {
							return RowDom;
						}
						if (!willChangeLine) {
							if (item.labelText === "主属性取值字段") { // 主属性取值字段
								if (hasDimReadCheckbox && hasDimRead1 && dimReadCheckboxValue === "1") {
									return RowDom;
								}
							} else {
								return RowDom;
							}
						}
					})
				}
			</div>
		);
	}
}

export default connect(state => ({
	indexRunningStore: state.indexRunning,
	globalStore: state.global,
	templateStore: state.template
}))(IndexCondition);

