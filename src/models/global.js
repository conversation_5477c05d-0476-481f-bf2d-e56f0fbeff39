import { messageError } from "@/utils/utils";
import Store from "store";
import { message } from "antd";
import { baseAPI, userAPI } from "@/services";
import cloneDeep from "lodash.clonedeep";
import { topLang } from "@/constants/lang";
import { compare, multipleToNull } from "@/utils/modelOperator";
import { isJSON } from "@/utils/isJSON";

export default {
	// model 的命名空间，同时也是他在全局 state 上的属性，只能用字符串，不支持通过 . 的方式创建多层命名空间
	namespace: "global",
	state: {
		// 初始值，优先级低于传给 dva() 的 opts.initialState。
		messageStatus: false,
		appList: [],
		currentApp: {},
		hiddenApp: false,
		sideMenu: {
			collapsed: false,
			activeGroupCode: false,
			openKeys: [],
			beforeOpenKeys: []
		},
		customMenuList: [],
		allMap: {},
		customTree: {},
		ruleField: {},
		policyRuleCopyList: [],
		personalMode: {
			showModal: false,
			lang: "cn",
			theme: "themeS1",
			layout: "default",
			simplified: true
		},
		userInfoMode: {
			avatar: null,
			userName: null,
			account: null
		},
		policyModel: [
			{
				dName: "首次匹配",
				name: "FirstMatch"
			},
			{
				dName: "最坏匹配",
				name: "WorstMatch"
			},
			{
				dName: "权重模式",
				name: "Weighted"
			}
		],
		policyDealLevel: [
			{
				dName: "规则级",
				name: "rule"
			},
			{
				dName: "策略级",
				name: "policy"
			}
		],
		menuTreeReady: false,
		configList: [],
		multiUserModal: false // 多用户弹窗
	},
	effects: {
		*changeMessage({ payload }, { call, put }) {
			yield put({
				type: "changeMessageStatus",
				payload: true
			});
			const response = yield call(messageError, payload);
			yield put({
				type: "changeMessageStatus",
				payload: response
			});
		},
		*getAllMap({ payload }, { all, call, put, select }) {
			const searchParams = yield select(state => state.blackList.searchParams);
			// const response = yield call(baseAPI.getAllMap, payload);
			const [response1, response2] = yield all([
				call(baseAPI.getAllMap1, payload),
				call(baseAPI.getAllMap2, payload)
			]);
			if (!response1 && !response2) {
				return;
			}
			if (!response1.success || !response2.success) {
				message.error(response1.message || response2.message);
				return;
			}
			const response = {};
			response.data = {...response1.data, ...response2.data};
			if (response && response.data && response.data.blackListSelect && response.data.blackListSelect.length) {
				searchParams["listName"] = response.data.blackListSelect[0]["name"];
			}
			yield put({
				type: "blackList/setAttrValue",
				payload: {
					searchParams: searchParams
				}
			});
			yield put({
				type: "initAllMap",
				payload: response
			});
		},
		*getUserMenuTree({ payload }, { call, put }) {
			let response = yield call(userAPI.getUserMenuTree, payload);
			if (!(response && response.data)) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initUserMenuTree",
				payload: response
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 用于在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = multipleToNull(stateChange[key]);
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},

		setMultipleAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				for (let [key, value] of Object.entries(stateChange)) {
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined) {
							stateChange[key] = newState[key];
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},

		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},

		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},

		changeLayoutCollapsed(state, { payload }) {
			Store.set("collapsed", !!payload);
			return {
				...state,
				collapsed: payload
			};
		},

		changeMessageStatus(state, { payload }) {
			return {
				...state,
				messageStatus: payload
			};
		},

		initAllMap(state, { payload }) {
			let { allMap, appList, currentApp, ruleField } = state;
			allMap = payload.data || {};
			appList = allMap["appNames"] && allMap["appNames"].length ? allMap["appNames"] : [];

			let allTempObj = {
				dName: topLang.common("allApplication"),
				name: "all"
			};

			let defaultAppObj = {
				dName: topLang.common("allApplication"),
				name: "all"
			};

			appList.splice(0, 0, allTempObj);

			// 这里判断localStorage在缓存中的多种复杂情况
			if (localStorage.hasOwnProperty("currentApp")) {
				// 缓存中是否存在currentApp
				let currentAppObjStr = localStorage.getItem("currentApp");
				if (currentAppObjStr && isJSON(currentAppObjStr)) {
					// 存在的currentApp是否是标准JSON
					let currentAppJson = JSON.parse(currentAppObjStr);
					if (currentAppJson.name && currentAppJson.dName) {
						// 判断currentApp是否是标准格式
						currentApp = currentAppJson;
					} else {
						currentApp = defaultAppObj;
						localStorage.setItem("currentApp", JSON.stringify(currentApp));
					}
				} else {
					currentApp = defaultAppObj;
					localStorage.setItem("currentApp", JSON.stringify(currentApp));
				}
			} else {
				currentApp = defaultAppObj;
				localStorage.setItem("currentApp", JSON.stringify(currentApp));
			}

			if (JSON.stringify(currentApp) === "{}") {
				currentApp = appList.length ? appList[0] : {};
			}

			allMap["ruleAndIndexFieldList"] = [];
			allMap["ruleFieldList"] && allMap["ruleFieldList"].map((item, index) => {
				allMap["ruleAndIndexFieldList"].push(
					{
						dName: item.dName,
						name: item.name,
						type: item.type,
						sourceName: "field"
					}
				);
				ruleField[item.name] = item.dName;
			});
			allMap["salaxyFieldList"] && allMap["salaxyFieldList"].map((item, index) => {
				item["sourceName"] = "realtime";
				allMap["ruleAndIndexFieldList"].push(
					{
						dName: item.dName,
						name: item.name,
						type: item.type,
						calcType: item.calcType,
						sourceName: "realtime",
						enumField:item.enumField || null,
						apps: item.apps || []
					}
				);
			});
			allMap["offlineZbSelect"] && allMap["offlineZbSelect"].map((item, index) => {
				allMap["ruleAndIndexFieldList"].push(
					{
						dName: item.dName,
						name: item.name,
						type: item.type,
						sourceName: "offline"
					}
				);
			});

			// 针对场景先前端添加全局字段
			if (allMap["sceneTypeSelect"]) {
				allMap["sceneTypeSelect"].splice(0, 0, {
					name: "ALL",
					dName: "默认场景类型"
				});
			}
			if (allMap["sceneSelect"]) {
				allMap["sceneSelect"].ALL = [{
					name: "ALL",
					dName: "全局场景"
				}];
			}

			return {
				...state,
				allMap: allMap,
				appList: appList,
				currentApp: currentApp,
				ruleField: ruleField
			};
		},

		initUserMenuTree(state, { payload }) {
			let name = payload.name;
			let { customTree } = state;
			customTree = payload.data || {};
			window.localStorage.setItem("customTree", JSON.stringify(customTree));
			return {
				...state,
				customTree: customTree,
				menuTreeReady: true
			};
		}
	},

	subscriptions: {
		// 监听地址，如果地址含有app则跳转到登陆页
		setup({ dispatch, history }) {
			history.listen(location => {
				let list = [
					"/policyDeal/dealType",
					"/policy/policyDetail",
					"/policy/versionPolicyDetail",
					"/system/ruleTemplate",
					"/policyDeal/triggerManage",
					"/fieldSetManagement",
					"/policy/publicPolicyList",
					"/policy/publicPolicyDetail",
					"/policy/versionPublicPolicyDetail"
				];
				let noNeedApp = list.find(item => location.pathname.includes(item) === true);
				dispatch({
					type: "setAttrValue",
					payload: {
						hiddenApp: !!noNeedApp
					}
				});
			});
		}
	}
};
