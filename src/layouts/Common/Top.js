import React, { PureComponent } from "react";
import { connect } from "dva";
import { Link } from "dva/router";
import { Layout, Menu, Icon, Dropdown, Popover, Button, Input, message } from "antd";
import { themeImages } from "@/constants/images";
import { topLang } from "@/constants/lang";
import { isJSON } from "@/utils/isJSON";
import { userAPI } from "@/services";
import NoOperate from "@/components/NoOperate";
import MultiUser from "@/components/MultiUser";
import Cookies from "universal-cookie";

const DevelopmentLoginModal = React.lazy(() => import("./Modal/DevelopmentLoginModal"));

const { Header } = Layout;

class Top extends PureComponent {
    state = {
    	appSearchValue: null,
    	rowsSelectReportName: {},
    	showDevelopmentLoginModal: false
    };

    constructor(props) {
    	super(props);
    	this.switchApp = this.switchApp.bind(this);
    	this.changePersonalMode = this.changePersonalMode.bind(this);
    }

    componentWillMount() {
    	let { globalStore, dispatch } = this.props;
    	let { personalMode, userInfoMode, sideMenu } = globalStore;
    	let lang = (localStorage.getItem("lang") && localStorage.getItem("lang") === "en") ? "en" : "cn";
    	let theme = (localStorage.getItem("theme") && localStorage.getItem("theme") === "themeS1") ? "themeS1" : localStorage.getItem("theme");
    	let layout = localStorage.getItem("layout") ? localStorage.getItem("layout") : "default";
    	let sideMenuFromLocal = localStorage.getItem("sideMenu") && isJSON(localStorage.getItem("sideMenu")) ? JSON.parse(localStorage.getItem("sideMenu")) : sideMenu;

    	let baseSideMenuObj = this.getInitKey();
    	sideMenuFromLocal["openKeys"] = sideMenuFromLocal["collapsed"] ? [] : baseSideMenuObj["openKeys"];
    	sideMenuFromLocal["beforeOpenKeys"] = baseSideMenuObj["openKeys"];
    	sideMenuFromLocal["activeGroupCode"] = baseSideMenuObj["activeGroupCode"];

    	if (localStorage.hasOwnProperty("currentApp")) {
    		let currentApp = localStorage.getItem("currentApp");
    		let allApp = {
    			"dName": topLang.common("allApplication"),
    			"name": "all"
    		};
    		let currentAppObj = currentApp && isJSON(currentApp) ? JSON.parse(currentApp) : allApp;
    		dispatch({
    			type: "global/setAttrValue",
    			payload: {
    				currentApp: currentAppObj
    			}
    		});
    	}
    	personalMode["lang"] = lang;
    	personalMode["theme"] = theme;
    	personalMode["layout"] = layout;
    	dispatch({
    		type: "global/setAttrValue",
    		payload: {
    			personalMode: personalMode,
    			sideMenu: sideMenuFromLocal
    		}
    	});

    	// 初始化cookie，与local一致
    	let cookies = new Cookies();
    	cookies.set("lang", lang, { path: "/" });

    	// 获取用户信息
    	userAPI.getUserInfo().then(response => {
    		if (response && response.success && response.data) {
    			userInfoMode["avatar"] = response.data.avatar;
    			userInfoMode["userName"] = response.data.userName;
    			userInfoMode["account"] = response.data.account;
    			dispatch({
    				type: "global/setAttrValue",
    				payload: {
    					userInfoMode: userInfoMode
    				}
    			});
    		}
    	});
    	dispatch({
    		type: "global/getAllMap",
    		payload: {}
    	});
    	// 获取规则模板
    	dispatch({
    		type: "template/getTemplateList",
    		payload: {
    			type: 1
    		}
    	});
    	// 获取指标模板
    	dispatch({
    		type: "template/getTemplateList",
    		payload: {
    			type: 2
    		}
    	});

    	// 为了防止sideMenu中途改动，将最新的放到localStorage
    	localStorage.setItem("sideMenu", JSON.stringify(sideMenuFromLocal));
    }

    getInitKey = () => {
    	const { globalStore, location } = this.props;
    	const { pathname } = location;
    	const { customTree } = globalStore;

    	let obj = {
    		openKeys: [],
    		activeGroupCode: null
    	};

    	customTree["menuTree"] && customTree["menuTree"].forEach((item, index) => {
    		item.children && item.children.length > 0 && item.children.forEach((subItem) => {
    			if (subItem.path.indexOf(pathname) > -1) {
    				obj.openKeys = [(index + 1).toString()];
    				obj.activeGroupCode = item.code;
    			}
    		});
    	});
    	return obj;
    };

    onMenuClick = ({ key }) => {
    	if (key === "logout") {
    		this.props.dispatch({
    			type: "login/logout"
    		});
    	} else if (key === "user") {
    		window.location = "/bridge/userCenter";
    	}
    };

    switchApp(item) {
    	let { dispatch, globalStore } = this.props;
    	let { appList } = globalStore;

    	let currentApp = appList.find(fItem => fItem.name === item.name);
    	if (currentApp && currentApp.name === "all") {
    		currentApp.dName = topLang.common("allApplication");
    	}
    	localStorage.setItem("currentApp", JSON.stringify(currentApp));

    	dispatch({
    		type: "global/setMultipleAttrValue",
    		payload: {
    			currentApp: currentApp
    		}
    	});

    	// 清空应用搜索结果
    	this.setState({
    		appSearchValue: null
    	});
    }

    changePersonalMode(field, value) {
    	let { dispatch, globalStore } = this.props;
    	let { personalMode } = globalStore;
    	let cookies = new Cookies();

    	personalMode[field] = value;
    	if (field === "lang" && value === "cn") {
    		personalMode["simplified"] = true;
    	}
    	dispatch({
    		type: "global/setAttrValue",
    		payload: {
    			personalMode: personalMode
    		}
    	});
    	localStorage.setItem(field, value);

    	cookies.set(field, value, { path: "/" });
    }

    handleTrigger = () => {
    	let { dispatch, globalStore } = this.props;
    	let { sideMenu } = globalStore;
    	let { collapsed, openKeys, beforeOpenKeys } = sideMenu;

    	sideMenu["collapsed"] = !collapsed;
    	if (!collapsed) {
    		sideMenu["openKeys"] = [];
    		sideMenu["beforeOpenKeys"] = openKeys;
    	} else {
    		sideMenu["openKeys"] = beforeOpenKeys;
    	}

    	dispatch({
    		type: "global/setAttrValue",
    		payload: {
    			sideMenu: sideMenu
    		}
    	});
    	// 将结果备份到localStorage
    	localStorage.setItem("sideMenu", JSON.stringify(sideMenu));
    };

    render() {
    	let { appSearchValue } = this.state;
    	let { globalStore, isPublishLayout } = this.props;
    	let { appList, hiddenApp, currentApp, personalMode, userInfoMode, sideMenu } = globalStore;
    	let { collapsed } = sideMenu;

    	if (appList && appList[0] && appList[0]["name"] === "all") {
    		appList[0]["dName"] = topLang.common("allApplication");
    	}

    	if (currentApp && currentApp.name === "all") {
    		currentApp.dName = topLang.common("allApplication");
    	}

    	let userInfoDom = (
    		<div className="user-info-setting-wrap">
    			<div className="user-info-body">
    				<p className="user-info-body-username">
    					{userInfoMode.userName ? userInfoMode.userName : "暂无昵称"}
    				</p>
    				<p className="user-info-body-account">
    					{userInfoMode.account}
    				</p>
    				{
    					process.env.SYS_ENV === "development"
    						? <Link
    							to="/userCenter?currentTab=1">
    							{/* 个人设置 */}
    							{topLang.common("personalSet")}
    						</Link>
    						: <a onClick={() => {
    							window.location = "/bridge/userCenter?currentTab=1";
    						}}>
    							{/* 个人设置 */}
    							{topLang.common("personalSet")}
    						</a>
    				}
    				{
    					process.env.SYS_ENV === "development"
    						? <Link
    							to="/userCenter?currentTab=2">
    							{/* 修改密码 */}
    							{topLang.common("changePwd")}
    						</Link>
    						: <a onClick={() => {
    							window.location = "/bridge/userCenter?currentTab=2";
    						}}>
    							{/* 修改密码 */}
    							{topLang.common("changePwd")}
    						</a>
    				}
    			</div>
    			<div className="user-info-footer">
    				<Button
    					className="user-info-footer-signout"
    					onClick={(e) => {
    						this.props.dispatch({
    							type: "login/logout"
    						});
    					}}>
    					{/* 退出 */}
    					{topLang.common("exit")}
    				</Button>
    			</div>
    		</div>
    	);
    	let appListDom = (
    		<Menu className="app-menu-list" selectedKeys={[]}>
    			<Menu.Item
    				className="app-search-warp"
    				key="search"
    				disabled
    			>
    				<Input
    					allowClear
    					placeholder="输入关键词搜索"
    					value={appSearchValue || undefined}
    					onChange={(e) => {
    						this.setState({
    							appSearchValue: e.target.value
    						});
    					}}
    				/>
    			</Menu.Item>
    			{
    				appList.filter(fItem => {
    					let result = true;
    					if (fItem.dName !== topLang.common("allApplication")) {
    						if (appSearchValue && appSearchValue.trim()) {
    							result = fItem.dName.indexOf(appSearchValue) > -1;
    						}
    					}
    					return result;
    				}).map((item, index) => {
    					return (
    						<Menu.Item
    							key={index}
    							onClick={this.switchApp.bind(this, item)}
    						>
    							{item.dName}
    						</Menu.Item>
    					);
    				})
    			}
    		</Menu>
    	);
    	let personalModeDom = (
    		<div className="personal-mode">
    			<div className="theme-select-handle">
    				<div
    					className="theme-select-item"
    					onClick={this.changePersonalMode.bind(this, "theme", "themeS1")}
    				>
    					<img src={themeImages.themeSymbol1} />
    					{
    						personalMode.theme &&
                            personalMode.theme === "themeS1" &&
                            <div className="select-icon">
                            	<Icon type="check" />
                            </div>
    					}
    				</div>
    				<div
    					className="theme-select-item"
    					onClick={this.changePersonalMode.bind(this, "theme", "themeS2")}
    				>
    					<img src={themeImages.themeSymbol2} />
    					{
    						personalMode.theme &&
                            personalMode.theme === "themeS2" &&
                            <div className="select-icon">
                            	<Icon type="check" />
                            </div>
    					}
    				</div>
    			</div>
    			<span className="line"></span>
    			<div className="switch font-family-group">
    				<a className={personalMode.lang === "cn" ? "switch-btn active" : "switch-btn"}
    					onClick={this.changePersonalMode.bind(this, "lang", "cn")}>中文</a>
    				<a className={personalMode.lang === "en" ? "switch-btn active" : "switch-btn"}
    					onClick={this.changePersonalMode.bind(this, "lang", "en")}>EN</a>
    			</div>
    			{/* <div className="switch layout-style-group">*/}
    			{/* <a className={personalMode.layout === "default" ? "switch-btn active" : "switch-btn"}*/}
    			{/* onClick={this.changePersonalMode.bind(this, "layout", "default")}>宽松</a>*/}
    			{/* <a className={personalMode.layout === "compact" ? "switch-btn active" : "switch-btn"}*/}
    			{/* onClick={this.changePersonalMode.bind(this, "layout", "compact")}>紧凑</a>*/}
    			{/* </div>*/}
    		</div>
    	);
    	return (
    		<Header className="basic-header">
    			<div
    				className="sidebar-collapsed-controller"
    				onClick={() => {
    					if (isPublishLayout) {
    						this.handleTrigger();
    					} else {
    						message.info("当前为开发环境，想看去发布环境！");
    					}
    				}}
    			>
    				<Icon
    					className="trigger"
    					type={collapsed ? "menu-unfold" : "menu-fold"}
    					onClick={this.toggle}
    				/>
    			</div>
    			{
    				!hiddenApp &&
                    <div className="switch-app">
                    	<Dropdown overlay={appListDom} trigger={["click"]}>
                    		<a className="switch-app-menu">
                    			<span>
                    				{currentApp && currentApp.dName ? currentApp.dName : topLang.common("allApplication")}
                    			</span>
                    			<Icon type="caret-down" />
                    		</a>
                    	</Dropdown>
                    </div>
    			}
    			<div className="navbar-content clearfix">
    				<ul className="nav navbar-top-links pull-right">
    					{
    						process.env.SYS_ENV === "development" &&
                            <li>
                            	<a
                            		onClick={() => {
                            			this.setState({
                            				showDevelopmentLoginModal: true
                            			});
                            		}}
                            		className="development-login-modal"
                            	>
                                    开发模拟登陆
                            	</a>
                            </li>
    					}
    					<li className="login-info">
    						<Popover popupClassName="personal-mode-wrap" placement="bottomRight" title={null}
    							content={personalModeDom} trigger="click">
    							<a className="ant-dropdown-link">
    								<i className="iconfont icon-selfhood"></i>
    							</a>
    						</Popover>
    					</li>
    					<li className="login-info">
    						<Popover popupClassName="personal-mode-wrap" placement="bottomRight" title={null}
    							content={userInfoDom} trigger="click">
    							<a className="ant-dropdown-link">
    								<span className="user-avatar-wrap">
    									{
    										userInfoMode["avatar"]
    											? <img src={`/salaxy-resource/avatar/${userInfoMode["avatar"]}.png`}
    												onError={(e) => {
    													e.target.onerror = null;
    													e.target.src = "/salaxy-resource/avatar/empty.png";
    												}}
    											/>
    											: <img src="/salaxy-resource/avatar/empty.png" />
    									}
    								</span>
    							</a>
    						</Popover>
    					</li>
    				</ul>
    			</div>
    			{/* 长时间登陆无任何操作，弹框提示并跳转登陆页 */}
    			<NoOperate />
    			{/* 被其他用户顶 */}
    			<MultiUser/>
    			{
    				this.state.showDevelopmentLoginModal &&
                    <React.Suspense fallback={<div>正在加载中</div>}>
                    	<DevelopmentLoginModal
                    		showDevelopmentLoginModal={this.state.showDevelopmentLoginModal}
                    		onCancel={() => {
                    			this.setState({
                    				showDevelopmentLoginModal: false
                    			});
                    		}}
                    	/>
                    </React.Suspense>
    			}
    		</Header>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global
}))(Top);
