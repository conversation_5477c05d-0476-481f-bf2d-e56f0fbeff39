import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Select, Form, Row, Col, message } from "antd";
import { policyEditorAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { policyListLang } from "@/constants/lang";

const TextArea = Input.TextArea;
const Option = Select.Option;

class ModifyPolicySetModal extends PureComponent {
	constructor(props) {
		super(props);
		this.changeDialogDataHandle = this.changeDialogDataHandle.bind(this);
		this.modifyPolicySet = this.modifyPolicySet.bind(this);
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyEditorStore } = this.props;
		let { modifyPolicySetData } = policyEditorStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		}
		modifyPolicySetData[field] = value;
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				modifyPolicySetData: modifyPolicySetData
			}
		});
	}

	modifyPolicySet() {
		let { dispatch, policyEditorStore, globalStore } = this.props;
		let { curPage, pageSize, dialogData } = policyEditorStore;
		let { modifyPolicySetData } = dialogData;
		let { currentApp } = globalStore;

		let params = {
			uuid: modifyPolicySetData.uuid,
			partnerCode: modifyPolicySetData.partnerCode,
			appName: modifyPolicySetData.appName,
			name: modifyPolicySetData.name,
			description: modifyPolicySetData.description
		};

		if (!params.name) {
			// lang:策略集名称不能为空
			message.error(policyListLang.policySetModal("policySetNameIsEmptyTip"));
			return;
		}

		if (params.name.length >= 50 || params.name.length <= 1) {
			// lang:策略集名称长度应大于1,小于50
			message.error(policyListLang.policySetModal("policySetLenTip"));
			return;
		}

		if (!/^[A-Za-z0-9\u4E00-\u9FA5\_]{2,50}$/.test(params.name)) {
			// lang:策略集名称：请不要输入全角字符、中划线等非法字符,建议输入 中文、英文、数字和下划线的组合
			message.error(policyListLang.policySetModal("policySetRegTip"));
			return;
		}

		policyEditorAPI.modifyPolicySet(params).then(res => {
			if (res.success) {
				dispatch({
					type: "policyEditor/setDialogShow",
					payload: {
						modifyPolicySet: false
					}
				});
				// lang:修改策略集成功
				message.success(policyListLang.policySetModal("modifyPolicySetSuccessTip"));
				dispatch({
					type: "policyEditor/getPolicySets",
					payload: {
						appName: currentApp && currentApp.name ? currentApp.name : null,
						curPage: curPage,
						pageSize: pageSize
					}
				});

				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
			} else {
				console.log(res.message);
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { policyEditorStore, globalStore, dispatch } = this.props;
		let { dialogShow, dialogData } = policyEditorStore;
		let { modifyPolicySetData } = dialogData;
		let { allMap } = globalStore;

		return (
			<Modal
				title={policyListLang.policySetModal("modifyPolicySet")} // lang:修改策略集
				visible={dialogShow.modifyPolicySet}
				maskClosable={false}
				onOk={this.modifyPolicySet.bind(this)}
				onCancel={() => {
					dispatch({
						type: "policyEditor/setDialogShow",
						payload: {
							modifyPolicySet: false
						}
					});
					dispatch({
						type: "policyEditor/setAttrValue",
						payload: {
							modalType: null
						}
					});
					dispatch({
						type: "policyEditor/setDialogData",
						payload: {
							modifyPolicySetData: {
								uuid: null,
								partnerCode: null,
								appName: null,
								name: null,
								type: "normal",
								description: null
							}
						}
					});
				}}
			>
				<Form className="modal-form">
					<Row gutter={CommonConstants.gutterSpan}>
						{/* lang:策略集名称 */}
						<Col span={6}
							className="basic-info-title">{policyListLang.policySetModal("policySetName")}：</Col>
						<Col span={18}>
							<Input
								type="text"
								placeholder={policyListLang.policySetModal("enterPolicyName")} // lang:请输入策略名称
								value={modifyPolicySetData.name}
								onChange={this.changeDialogDataHandle.bind(this, "name", "input")}
							/>
							<span className="tip-text">
								{/* lang:建议输入：中文、英文、数字和下划线的组合 */}
								{policyListLang.policySetModal("inputSuggest")}
							</span>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* lang:应用名 */}
							{policyListLang.policySetModal("appName")}：
						</Col>
						<Col span={18}>
							<Select
								value={modifyPolicySetData.appDisplayName}
								disabled={true}
							>
								{
									allMap && allMap["appNames"] && allMap["appNames"].map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* lang:事件类型*/}
							{policyListLang.policySetModal("eventType")}：
						</Col>
						<Col span={18}>
							<Select
								value={modifyPolicySetData.eventType}
								disabled={true}
							>
								{
									allMap && allMap["EventType"] && allMap["EventType"].map((item, index) => {
										return (
											<Option value={item.name} key={index}>
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* lang:事件标识 */}
							{policyListLang.policySetModal("eventId")}：
						</Col>
						<Col span={18}>
							<Input
								type="text"
								value={modifyPolicySetData.eventId || undefined}
								disabled={true}
							/>
						</Col>
					</Row>
					<Row gutter={CommonConstants.gutterSpan}>
						<Col span={6} className="basic-info-title">
							{/* lang:描述 */}
							{policyListLang.policySetModal("description")}：
						</Col>
						<Col span={18}>
							<TextArea
								value={modifyPolicySetData.description}
								rows={4}
								placeholder={policyListLang.policySetModal("enterPolicyDescription")} // lang:请输入策略描述
								onChange={this.changeDialogDataHandle.bind(this, "description", "input")}
							></TextArea>
						</Col>
					</Row>
				</Form>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(ModifyPolicySetModal);
