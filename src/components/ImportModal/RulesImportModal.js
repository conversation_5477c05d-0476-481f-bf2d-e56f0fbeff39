import { PureComponent } from "react";
import { connect } from "dva";
import ImportCommon from "./ImportCommon";
import { imExportModeLang } from "@/constants/lang";
import { policyEditorAPI } from "@/services";

class RulesImportModal extends PureComponent {
	changeImport = (e, fileName, type = "input") => {
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		let { policyEditorStore, dispatch } = this.props;
		let { dialogData } = policyEditorStore;
		let { rulesImportData } = dialogData;
		const newRulesImportData = { ...rulesImportData };

		newRulesImportData[fileName] = value;
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				rulesImportData: newRulesImportData
			}
		});
	}
	closeModal = () => {
		const { dispatch } = this.props;
		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				RulesImport: false
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				rulesImportData: {
					uuid: null,
					importMode: null,
					file: null,
					replaceScene: "",
					zbMode: "",
					ruleMode: ""
				}
			}
		});
	}
	importCallback = () => {
		let { policyEditorStore, globalStore, dispatch } = this.props;
		let { curPage, pageSize } = policyEditorStore;
		// 导入成功后
		// 1. 重新刷新编辑区策略列表
		let { currentApp } = globalStore;
		if (currentApp.name) {
			dispatch({
				type: "policyEditor/getPolicySets",
				payload: {
					appName: currentApp.name ? currentApp.name : null,
					curPage: curPage,
					pageSize: pageSize
				}
			});
		}
	}
	render() {
		const { policyEditorStore } = this.props;
		const { dialogShow, dialogData } = policyEditorStore;
		const { rulesImportData } = dialogData;
		return (
			<ImportCommon
				title={imExportModeLang.importPolicyModal("ruleImport")} // lang:规则导入
				visible={dialogShow.RulesImport}
				modalData={rulesImportData}
				importType="rules"
				changeField={this.changeImport.bind(this)}
				onCancel={this.closeModal.bind(this)}
				importCallback={this.importCallback.bind(this)}
				fetchMethod={policyEditorAPI.importRules}
			/>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(RulesImportModal);
