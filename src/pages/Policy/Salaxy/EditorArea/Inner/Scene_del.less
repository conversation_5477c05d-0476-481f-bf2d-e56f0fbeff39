:global {
    .scene-wrap {
        & {
            position: relative;
            display: flex;
            height: 213px;
            border: 1px solid #e6e6e6;
            margin-bottom: 10px;
            overflow: hidden;
        }

        .scene-search-wrap {
            & {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }

            .ant-input-affix-wrapper {
                & {
                    z-index: 10;
                }

                input {
                    border-radius: 0;
                }
            }
        }

        .scene-content-wrap {
            padding: 0 0 10px;

            .scene-content-header {
                & {
                    position: relative;
                    height: 32px;
                    line-height: 32px;
                    padding: 0 8px;
                    border-bottom: 1px dashed #e6e6e6;
                }

                h3 {
                    font-size: 14px;
                    font-weight: normal;
                    margin-bottom: 0;
                }

                .ant-switch {
                    position: absolute;
                    right: 0;
                    top: 5px;
                }

                .search-handle {
                    & {
                        float: right;
                        position: absolute;
                        right: 8px;
                        top: 0;
                    }
                }
            }

            .scene-content-body {
                & {
                    clear: both;
                    height: 181px;
                    overflow-x: hidden;
                    overflow-y: auto;
                }
            }
        }

        .left {
            & {
                width: 150px;
                height: 212px;
                overflow-x: hidden;
                overflow-y: auto;
                border-right: 1px solid #e6e6e6;
                background: #fff;
                overflow: hidden;
            }

            .scene-app-list {
                & {
                    padding: 0;
                    list-style: none;
                    margin-bottom: 0;
                }

                .scene-app-item {
                    & {
                        position: relative;
                        height: 36px;
                        line-height: 36px;
                        border-bottom: 1px solid #e6e6e6;
                        padding-left: 6px;
                        padding-right: 6px;
                        cursor: pointer;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        -o-text-overflow: ellipsis;
                        -webkit-text-overflow: ellipsis;
                        -moz-text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    &.active::after {
                        position: absolute;
                        right: 0;
                        top: 0;
                        width: 2px;
                        height: 36px;
                        content: "";
                        display: inline-block;
                        background: #4496ff;
                    }

                    &:hover {
                        background: #f0f0f0;
                    }

                    &.active {
                        background: #f0f0f0;
                        font-weight: bold;
                    }

                    i {
                        width: 14px;
                        height: 14px;
                        color: #999;
                        opacity: 0;
                    }

                    i.anticon-check-square {
                        opacity: 1;
                    }
                }
            }
        }

        .right {
            & {
                flex: 1;
                background-color: #fff;
                overflow: hidden;
            }
            .scene-content-body{
                overflow-y: hidden;
            }
            .scene-event-list {
                & {
                    list-style: none;
                    padding: 0;
                    height: 173px;
                    overflow: auto;
                    padding: 0 8px;
                    margin-top: 6px;
                }

                .scene-event-item {
                    height: 28px;
                    line-height: 28px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                    -webkit-text-overflow: ellipsis;
                    -moz-text-overflow: ellipsis;
                    white-space: nowrap;

                    .ant-checkbox+span {
                        width: 160px;
                        display: inline-block;
                        overflow: hidden;
                        vertical-align: middle;
                        text-overflow: ellipsis;
                        -o-text-overflow: ellipsis;
                        -webkit-text-overflow: ellipsis;
                        -moz-text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }
}
