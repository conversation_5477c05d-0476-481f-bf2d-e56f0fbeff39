import { getApiDetailHeader, getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request, { PostForm, downloadFileHandle } from "../utils/request";

const getBlackListHeader = async(params) => {
	return request(getUrl("/admin/blackList/header", params), {
		method: "GET",
		headers: getApiDetailHeader()
	});
};
const getBlackListData = async(params) => {
	return request(getUrl("/admin/blackList/data", params), {
		method: "GET",
		headers: getApiDetailHeader()
	});
};

const changeBlackListHeader = async(params) => {
	return request("/admin/blackList/header", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const addBlackListData = async(params) => {
	return request("/admin/blackList/data", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};
const modifyBlackListData = async(params) => {
	return request("/admin/blackList/data", {
		method: "PUT",
		headers: getHeader(),
		body: { ...params }
	});
};
const deleteBlackListData = async(params) => {
	return request(getUrl("/admin/blackList/data", params), {
		method: "DELETE",
		headers: getApiDetailHeader()
	});
};

const importBlackListData = async(params) => {
	const res = await PostForm("/admin/blackList/import", "post", { ...params });
	return res;
};

export default {
	getBlackListHeader,
	getBlackListData,
	changeBlackListHeader,
	addBlackListData,
	modifyBlackListData,
	deleteBlackListData,
	importBlackListData
};
