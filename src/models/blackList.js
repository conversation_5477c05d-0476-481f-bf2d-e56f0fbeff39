import { message } from "antd";
import { blackListAPI } from "@/services";

export default {
	namespace: "blackList",
	state: {
		tableHeaderList: [],
		dataList: [],
		searchParams: {
			curPage: 1,
			pageSize: 10,
			total: 0,
			pages: 0,
			listName: null,
			columnName: null,
			columnValue: null
		},
		dialogShow: {
			tableHeadModal: false,
			addBlackListModal: false,
			modifyBlackListModal: false,
			blackListImportModal: false
		},
		dialogData: {
			tableHeadData: {
				tableHeaderList: []
			},
			addBlackListData: {
				listName: null,
				expireTime: null,
				modifyObj: null,
				tableHeaderList: []
			},
			blackListImportData: {
				listName: null,
				file: null
			}
		}
	},
	effects: {
		*getBlackListHeader({ payload }, { call, put, select }) {
			let searchParams = yield select(state => state.blackList.searchParams);
			let response = yield call(blackListAPI.getBlackListHeader, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}

			yield put({
				type: "setAttrValue",
				payload: {
					tableHeaderList: response.data || [],
					searchParams: searchParams
				}
			});
		},
		*getBlackListData({ payload }, { call, put, select }) {
			let searchParams = yield select(state => state.blackList.searchParams);
			let response = yield call(blackListAPI.getBlackListData, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let data = response.data;
			searchParams["curPage"] = data.curPage;
			searchParams["pageSize"] = data.pageSize;
			searchParams["total"] = data.total;
			searchParams["pages"] = data.pages;

			yield put({
				type: "setAttrValue",
				payload: {
					searchParams: searchParams,
					dataList: data.dataList
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		}
	}
};
