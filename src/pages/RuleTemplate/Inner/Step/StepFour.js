import { PureComponent } from "react";
import { connect } from "dva";
import { Input, message } from "antd";
import { templateAPI } from "@/services";

const { TextArea } = Input;

class StepFour extends PureComponent {
	constructor(props) {
		super(props);
		this.changeHandle = this.changeHandle.bind(this);
		this.modifyTemplate = this.modifyTemplate.bind(this);
	}

	changeHandle(field, type, e) {
		let { dispatch, templateStore } = this.props;
		let { modifyTemplateData } = templateStore.dialogData;
		let value = "";
		if (type === "input" || type === "textarea") {
			value = e.target.value;
		}
		modifyTemplateData[field] = value;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	modifyTemplate() {
		let { templateStore, dispatch } = this.props;
		let { currentType, dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;
		templateAPI.modifyTemplate(modifyTemplateData).then((res) => {
			if (res.success) {
				dispatch({
					type: "template/getTemplateList",
					payload: {
						type: currentType
					}
				});
				message.success("更新模板成功");
				dispatch({
					type: "template/setDialogShow",
					payload: {
						modifyTemplate: false
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { templateStore } = this.props;
		let { dialogData } = templateStore;
		let { modifyTemplateData } = dialogData;

		return (
			<div>
				<TextArea
					style={{ height: "calc(100vh - 220px)" }}
					placeholder="请输入JSON配置"
					value={JSON.stringify(modifyTemplateData.cfgJson, null, 4)}
					onChange={this.changeHandle.bind(this, "cfgJson", "textarea")}
					disabled={true}
				/>
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(StepFour);
