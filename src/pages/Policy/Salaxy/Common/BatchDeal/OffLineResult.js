import { PureComponent } from "react";
import { Table, Modal, Button, Tag } from "antd";
import { indexListLang, commonLang } from "@/constants/lang";

class OffLineResult extends PureComponent {
	constructor(props) {
		super(props);
	}
	render() {
		const { visible, data = [], onCancel, title } = this.props;
		const columns = [
			{
				width: 300,
				title: indexListLang.batchRes("name"), // 指标名称
				dataIndex: "name",
				key: "name"
			},
			{
				width: 100,
				title: indexListLang.batchRes("result"), // 结果
				dataIndex: "result",
				key: "result",
				render: (text) => {
					const color = text ? "green" : "red";
					return <Tag color={color}>{text ? indexListLang.batchRes("success") : indexListLang.batchRes("fail")}</Tag>; // "成功" : "失败"
				}
			}, {
				title: indexListLang.batchRes("failDescription"), // 描述
				dataIndex: "description",
				key: "description"
			}];
		return (
			<Modal
				title={title}// 批量下线结果
				visible={visible}
				onCancel={onCancel}
				width={650}
				footer={
					// 确定
					<Button onClick={onCancel}>{commonLang.base("ok")}</Button>
				}
			>
				<Table
					dataSource={data}
					columns={columns}
					pagination={false}
					rowKey="id"
				/>
			</Modal>
		);
	}
}

export default OffLineResult;
