import { PureComponent, Fragment } from "react";
import { connect } from "dva";
import { Input } from "antd";
import InputTemplate from "../Template/InputTemplate";
import SelectTemplate from "../Template/SelectTemplate";
import VariableTemplate from "../Template/VariableTemplate";
import ChangeRuleForLine from "../Template/ChangeRuleForLine";
import FieldSetTemplate from "../Template/FieldSetTemplate";
import FormulaTemplate from "../Template/FormulaTemplate";
import GroupTemplate from "../Template/GroupTemplate";
import ScriptTemplate from "../Template/ScriptTemplate";

const { TextArea } = Input;

class RightContent extends PureComponent {
	constructor(props) {
		super(props);
		this.changeParamValue = this.changeParamValue.bind(this);
		this.addSingleParam = this.addSingleParam.bind(this);
		this.modifyItemBase = this.modifyItemBase.bind(this);
	}

	changeParamValue(type, field, actionType, e) {
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		if (actionType === "onBlur") {
			// 正则表达式去除首尾空格
			value = value.replace(/(^\s*)|(\s*$)/g, "");
		}
		let { algoLibStore } = this.props;
		let { applyStore } = algoLibStore;
		let { operatorConfigIndex, currentTabIndex } = applyStore;
		let operatorConfigParam = JSON.parse(JSON.stringify(applyStore.operatorConfigParam));
		if (operatorConfigParam.isTab) {
			operatorConfigParam.configDetail[currentTabIndex]["paramsConfig"][operatorConfigIndex][field] = value;
		} else {
			operatorConfigParam.configDetail[operatorConfigIndex][field] = value;
		}

		algoLibStore.applyStore.setOperatorConfigParam(operatorConfigParam);
	}

	addSingleParam(type) {
		let { templateStore, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;
		let needTemplate = {};
		let variableObj = {
			componentType: "variable",
			defaultValue: "",
			name: "",
			type: "context"
		};
		let inputObj = {
			componentType: "input",
			defaultValue: "",
			name: "",
			selectSourceName: "systemList",
			addonBefore: "",
			addonAfter: "",
			placeholder: "",
			canEditSecondTimes: true,
			type: "string"
		};
		let selectObj = {
			componentType: "select",
			defaultValue: "",
			name: "",
			selectType: null,
			selectName: null,
			selectOption: [],
			mapType: "static",
			placeholder: "",
			canEditSecondTimes: true,
			type: "string"
		};
		let checkboxObj = {
			componentType: "checkbox",
			defaultValue: "",
			name: "",
			selectType: null,
			selectName: null,
			selectOption: [],
			canEditSecondTimes: true,
			type: "string"
		};
		let radioObj = {
			componentType: "radio",
			defaultValue: "",
			name: "",
			selectType: null,
			selectName: null,
			selectOption: [],
			canEditSecondTimes: true,
			type: "string"
		};
		let fieldSetObj = {
			componentType: "fieldSet",
			type: "string",
			name: "fieldSet"
		};
		let formulaObj = {
			componentType: "formula",
			type: "string",
			name: "formula"
		};
		let groupObj = {
			componentType: "group",
			name: "",
			children: []
		};
		let scriptObj = {
			componentType: "script",
			type: "string",
			name: "script"
		};
		if (type === "input") {
			needTemplate = inputObj;
		} else if (type === "select") {
			needTemplate = selectObj;
		} else if (type === "checkbox") {
			needTemplate = checkboxObj;
		} else if (type === "radio") {
			needTemplate = radioObj;
		} else if (type === "variable") {
			needTemplate = variableObj;
		} else if (type === "fieldSet") {
			needTemplate = fieldSetObj;
		} else if (type === "formula") {
			needTemplate = formulaObj;
		} else if (type === "group") {
			needTemplate = groupObj;
		} else if (type === "script") {
			needTemplate = scriptObj;
		}
		cfgJson["params"][operatorTemplateItemIndex]["children"].push(needTemplate);
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	modifyItemBase(field, e) {
		let { templateStore, dispatch } = this.props;
		let { dialogData, operatorTemplateItemIndex } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson || null;

		cfgJson["params"][operatorTemplateItemIndex][field] = e.target.value || "";
		modifyTemplateData.cfgJson = cfgJson;
		dispatch({
			type: "template/setDialogData",
			payload: {
				modifyTemplateData: modifyTemplateData
			}
		});
	}

	render() {
		let { templateStore } = this.props;
		let { operatorTemplateItemIndex, dialogData, currentType } = templateStore;
		let { modifyTemplateData } = dialogData;
		let cfgJson = modifyTemplateData.cfgJson;
		let lineItem = cfgJson["params"] && cfgJson["params"][operatorTemplateItemIndex] && cfgJson["params"][operatorTemplateItemIndex] ? cfgJson["params"][operatorTemplateItemIndex] : {};

		// currentType:1是规则，2是指标
		// console.log(templateStore);
		return (
			<div className="param-config-list">
				{
					cfgJson["params"] &&
					cfgJson["params"].length > 0 &&
					<Fragment>
						<div className="param-config-item">
							<div className="param-title">
								<span>标签名：</span>
							</div>
							<div className="param-field">
								<Input
									size="default"
									style={{ width: "100%", marginBottom: "10px" }}
									addonBefore="cn"
									value={lineItem && lineItem["labelText"] ? lineItem["labelText"] : undefined}
									onChange={this.modifyItemBase.bind(this, "labelText")}
									placeholder="请输入标签中文名"
								/>
								<Input
									addonBefore="en"
									value={lineItem && lineItem["enLabelText"] ? lineItem["enLabelText"] : undefined}
									onChange={this.modifyItemBase.bind(this, "enLabelText")}
									placeholder="请输入标签英文名"
								/>
							</div>
						</div>
						<div className="param-config-item">
							<div className="param-title">
								<span>提示标题：</span>
							</div>
							<div className="param-field">
								<Input
									style={{ width: "100%", marginBottom: "10px" }}
									addonBefore="cn"
									value={lineItem && lineItem["tipTitle"] ? lineItem["tipTitle"] : undefined}
									onChange={this.modifyItemBase.bind(this, "tipTitle")}
									placeholder="请输入中文提示标题"
								/>
								<Input
									style={{ width: "100%" }}
									addonBefore="en"
									value={lineItem && lineItem["enTipTitle"] ? lineItem["enTipTitle"] : undefined}
									onChange={this.modifyItemBase.bind(this, "enTipTitle")}
									placeholder="请输入英文提示标题" />
							</div>
						</div>
						<div className="param-config-item">
							<div className="param-title">
								<span>提示详情-cn：</span>
							</div>
							<div className="param-field">
								<TextArea
									value={lineItem && lineItem["tipContent"] || undefined}
									rows={2}
									onChange={this.modifyItemBase.bind(this, "tipContent")}
									onBlur={this.modifyItemBase.bind(this, "tipContent")}
									placeholder="请输入中文提示详情"
								/>
							</div>
						</div>
						<div className="param-config-item">
							<div className="param-title">
								<span>提示详情-en：</span>
							</div>
							<div className="param-field">
								<TextArea
									value={lineItem && lineItem["enTipContent"] || undefined}
									rows={2}
									onChange={this.modifyItemBase.bind(this, "enTipContent")}
									onBlur={this.modifyItemBase.bind(this, "enTipContent")}
									placeholder="请输入英文提示详情"
								/>
							</div>
						</div>
						<div className="param-config-item">

						</div>
						<div className="single-param">
							<ChangeRuleForLine />
						</div>

						{
							lineItem && lineItem.children && lineItem.children.map((item, index) => {
								let obj = null;
								if (item["componentType"] === "input") {
									obj = <InputTemplate childIndex={index} />;
								} else if (item["componentType"] === "select") {
									obj = <SelectTemplate childIndex={index} componentType="select" />;
								} else if (item["componentType"] === "checkbox") {
									obj = <SelectTemplate childIndex={index} componentType="checkbox" />;
								} else if (item["componentType"] === "radio") {
									obj = <SelectTemplate childIndex={index} componentType="radio" />;
								} else if (item["componentType"] === "variable") {
									obj = <VariableTemplate childIndex={index} />;
								} else if (item["componentType"] === "fieldSet") {
									obj = <FieldSetTemplate childIndex={index} />;
								} else if (item["componentType"] === "formula") {
									obj = <FormulaTemplate childIndex={index} />;
								} else if (item["componentType"] === "group") {
									obj = <GroupTemplate childIndex={index} />;
								} else if (item["componentType"] === "script") {
									obj = <ScriptTemplate childIndex={index} />;
								}
								return obj;
							})
						}
						<div className="add-single-param-handle">
							<span onClick={this.addSingleParam.bind(this, "variable")}>常量/变量</span>
							<span onClick={this.addSingleParam.bind(this, "input")}>Input</span>
							<span onClick={this.addSingleParam.bind(this, "select")}>Select</span>
							<span onClick={this.addSingleParam.bind(this, "radio")}>Radio</span>
							<span onClick={this.addSingleParam.bind(this, "checkbox")}>Checkbox</span>
							{
								currentType === 2 &&
								<Fragment>
									<span onClick={this.addSingleParam.bind(this, "fieldSet")}>fieldSet</span>
									<span onClick={this.addSingleParam.bind(this, "formula")}>计算公式</span>
									<span onClick={this.addSingleParam.bind(this, "group")}>群组</span>
								</Fragment>
							}
							{
								currentType !== 2 &&
								<Fragment>
									<span onClick={this.addSingleParam.bind(this, "script")}>脚本规则</span>
								</Fragment>
							}
						</div>
					</Fragment>
				}
			</div>
		);
	}
}

export default connect(state => ({
	templateStore: state.template
}))(RightContent);
