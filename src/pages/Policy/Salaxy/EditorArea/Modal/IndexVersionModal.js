import { PureComponent } from "react";
import { connect } from "dva";
import { Modal } from "antd";
import RunningArea from "../../RunningArea";
import { indexListLang } from "@/constants/lang";

class IndexVersionModal extends PureComponent {
	constructor(props) {
		super(props);
	}

	render() {
		let { indexRunningStore, dispatch } = this.props;
		let { dialogShow } = indexRunningStore;

		return (
			<Modal
				title={indexListLang.common("indicatorVersionList")} // lang:指标版本列表
				width={1100}
				footer={null}
				visible={dialogShow.indexVersion}
				className="index-version-modal-wrap"
				onCancel={() => {
					dispatch({
						type: "indexRunning/setDialogShow",
						payload: {
							indexVersion: false
						}
					});
				}}
			>
				<RunningArea isOtherPageCite={true} citePageName="versionModal" />
			</Modal>
		);
	}
}

export default connect(state => ({
	indexEditorStore: state.indexEditor,
	indexRunningStore: state.indexRunning
}))(IndexVersionModal);
