import { message } from "antd";
import { approvalTaskAPI } from "@/services";
import cloneDeep from "lodash.clonedeep";

export default {
	namespace: "approvalTask",
	state: {
		policyPage: {
			taskList: [],
			total: 0,
			pages: 0,
			curPage: 1,
			pageSize: 10,
			searchParams: {
				targetName: null,
				startTs: null,
				endTs: null,
				isPublicPolicy: null
			}
		},
		salaxyPage: {
			taskList: [],
			total: 0,
			pages: 0,
			curPage: 1,
			pageSize: 10,
			searchParams: {
				targetName: null,
				startTs: null,
				endTs: null
			}
		},
		workflowPage: {
			taskList: [],
			total: 0,
			pages: 0,
			curPage: 1,
			pageSize: 10,
			searchParams: {
				targetName: null,
				startTs: null,
				endTs: null
			}
		},
		dialogShow: {
			policyApproval: false,
			salaxyApproval: false,
			workflowApproval: false
		},
		dialogData: {
			policyApprovalData: {
				uuid: null,
				status: "APPROVED",
				desc: null,
				leftNavIndex: 0,
				tabIndex: 0,
				diffTime: "online",
				diffDetail: {
					onlineVersion: null,
					pendVersion: null
				}
			},
			salaxyApprovalData: {
				id: null,
				uuid: null,
				status: "APPROVED",
				desc: null,
				leftNavIndex: 0,
				tabIndex: 0,
				diffDetail: {
					onlineVersion: null,
					pendVersion: null
				}
			},
			workflowApprovalData: {
				id: null,
				uuid: null,
				status: "APPROVED",
				desc: null,
				leftNavIndex: 0,
				diffTime: "online",
				diffDetail: {
					onlineVersion: null,
					pendVersion: null
				}
			}
		}
	},
	effects: {
		*getPolicyApprovalTaskList({ payload }, { call, put }) {
			// 如果没有选择公共策略类型不传参数
			const newSearchParams = { ...payload };
			const { isPublicPolicy } = newSearchParams;
			if (!(String(isPublicPolicy) === "0" || String(isPublicPolicy) === "1")) {
				delete newSearchParams.isPublicPolicy;
			}
			let response = yield call(approvalTaskAPI.getPolicyApprovalTaskList, newSearchParams);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let data = response.data || null;
			yield put({
				type: "setAttrValue",
				payload: {
					policyPage: {
						taskList: data && data.dataList ? data.dataList : [],
						total: data && data.total ? data.total : 0,
						pages: data && data.pages ? data.pages : 0,
						curPage: data && data.curPage ? data.curPage : 1,
						pageSize: data && data.pageSize ? data.pageSize : 10
					}
				}
			});
		},
		*getSalaxyApprovalTaskList({ payload }, { call, put }) {
			let response = yield call(approvalTaskAPI.getSalaxyApprovalTaskList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			console.log(response);
			let data = response.data || null;
			yield put({
				type: "setAttrValue",
				payload: {
					salaxyPage: {
						taskList: data && data.dataList ? data.dataList : [],
						total: data && data.total ? data.total : 0,
						pages: data && data.pages ? data.pages : 0,
						curPage: data && data.curPage ? data.curPage : 1,
						pageSize: data && data.pageSize ? data.pageSize : 10
					}
				}
			});
		},
		*getWorkflowApprovalTaskList({ payload }, { call, put }) {
			let response = yield call(approvalTaskAPI.getWorkflowApprovalTaskList, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			let data = response.data || null;
			yield put({
				type: "setAttrValue",
				payload: {
					workflowPage: {
						taskList: data && data.dataList ? data.dataList : [],
						total: data && data.total ? data.total : 0,
						pages: data && data.pages ? data.pages : 0,
						curPage: data && data.curPage ? data.curPage : 1,
						pageSize: data && data.pageSize ? data.pageSize : 10
					}
				}
			});
		}
	},
	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined) {
							stateChange[key] = newState[key];
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		}
	}
};

