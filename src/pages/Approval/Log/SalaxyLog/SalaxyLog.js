import { PureComponent } from "react";
import { connect } from "dva";
import { approvalLogLang, commonLang } from "@/constants/lang";
import { Button, Input, Table, Pagination, DatePicker } from "antd";
import { approvalTaskConstants } from "@/constants";
import moment from "moment";

const { RangePicker } = DatePicker;
const { operationTypeMap } = approvalTaskConstants;

class SalaxyLog extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.startSearch = this.startSearch.bind(this);
    	this.changePagination = this.changePagination.bind(this);
    	this.changeSearchParamField = this.changeSearchParamField.bind(this);
    }

    componentDidMount() {
    	// 页面初始化数据
    	this.startSearch();
    }

    startSearch() {
    	let { approvalLogStore, globalStore, dispatch } = this.props;
    	let { salaxyPage } = approvalLogStore;
    	let { pageSize, searchParams } = salaxyPage;

    	let { currentApp } = globalStore;
    	if (currentApp.name !== "all") {
    		searchParams["appName"] = currentApp.name;
    	} else {
    		searchParams["appName"] = "";
    	}

    	dispatch({
    		type: "approvalLog/getSalaxyApprovalLogList",
    		payload: {
    			curPage: 1,
    			pageSize: pageSize,
    			...searchParams
    		}
    	});
    }

    componentWillReceiveProps(nextProps) {
    	let { approvalLogStore, dispatch, globalStore } = this.props;
    	let { menuTreeReady } = globalStore;
    	let { salaxyPage } = approvalLogStore;
    	let { curPage, pageSize, searchParams } = salaxyPage;
    	let preCurrentApp = this.props.globalStore.currentApp;
    	let nextCurrentApp = nextProps.globalStore.currentApp;
    	if (!menuTreeReady || !true) {
    		// 这里放权限
    		return;
    	}
    	if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
    		if (nextCurrentApp.name !== "all") {
    			searchParams["appName"] = nextCurrentApp.name;
    		} else {
    			searchParams["appName"] = "";
    		}

    		dispatch({
    			type: "approvalLog/getSalaxyApprovalLogList",
    			payload: {
    				curPage: curPage,
    				pageSize: pageSize,
    				...searchParams
    			}
    		});
    	}
    }

    changePagination(curPage, pageSize) {
    	let { approvalLogStore, dispatch } = this.props;
    	let { salaxyPage } = approvalLogStore;
    	let { searchParams } = salaxyPage;

    	dispatch({
    		type: "approvalLog/getSalaxyApprovalLogList",
    		payload: {
    			curPage: curPage,
    			pageSize: pageSize,
    			...searchParams
    		}
    	});
    }

    changeSearchParamField(field, type, e) {
    	let { approvalLogStore, dispatch } = this.props;
    	let { salaxyPage } = approvalLogStore;
    	let { searchParams } = salaxyPage;

    	let value;
    	if (type === "input") {
    		value = e.target.value;
    	} else if (type === "date") {
    		value = e;
    	}
    	if (field === "targetName") {
    		searchParams[field] = value;
    	} else if (field === "selectTs") {
			let startStr = moment(value[0]).format("YYYY-MM-DD") + " 00:00:00";
			let endStr = moment(value[1]).format("YYYY-MM-DD") + " 23:59:59";
    		searchParams["startTs"] = value.length === 2 ? Date.parse(startStr) : null;
    		searchParams["endTs"] = value.length === 2 ? Date.parse(endStr) : null;
    	}
    	dispatch({
    		type: "approvalLog/setAttrValue",
    		payload: {
    			salaxyPage: salaxyPage
    		}
    	});
    }

    render() {
    	let { approvalLogStore } = this.props;
    	let { salaxyPage } = approvalLogStore;
    	let { taskList, total, curPage, searchParams } = salaxyPage;
    	let { targetName, startTs, endTs } = searchParams;
    	let columns = [
    		{
    			title: approvalLogLang.table("operationType"),		// lang:变更类型
    			render: (record) => {
    				return (
    					<div>
    						{
    							record["operationType"] &&
								operationTypeMap[record["operationType"]] &&
								operationTypeMap[record["operationType"]]()["text"]
    						}
    					</div>
    				);
    			}
    		},
    		{
    			title: approvalLogLang.table("taskName"),		// lang:任务名称
    			render: (record) => {
    				return (
    					<div>{record.targetName}</div>
    				);
    			}
    		},
    		{
    			title: approvalLogLang.table("publishDesc"),		// lang:发版描述
    			render: (record) => {
    				return (
    					<div>{record.description ? record.description : "-"}</div>
    				);
    			}
    		},
    		{
    			title: approvalLogLang.table("approvalResult"),		// lang:审核结果
    			render: (record) => {
    				return (
    					<div>
    						{/* lang:通过or拒绝 */}
    						{
    							record.status && record.status === "APPROVED" ? approvalLogLang.table("pass") : approvalLogLang.table("reject")
    						}
    					</div>
    				);
    			}
    		},
    		{
    			title: approvalLogLang.table("approvalDesc"),				// lang:审批理由
    			render: (record) => {
    				return (
    					<div>{record.approveDesc ? record.approveDesc : "暂无审批理由"}</div>
    				);
    			}
    		},
    		{
    			title: approvalLogLang.table("approvalTime"),				// lang:审批时间
    			render: (record) => {
    				return (
    					<div>{record.approveTime}</div>
    				);
    			}
    		},
    		{
    			title: approvalLogLang.table("createdBy"),				// lang:提交人
    			dataIndex: "createdBy"
    		},
    		{
    			title: approvalLogLang.table("submitTime"),				// lang:提交时间
    			dataIndex: "gmtCreate"
    		}
    	];
    	return (
    		<div>
    			<div className="page-global-body">
    				<div className="page-global-body-search">
    					<div className="left-info">
    						<h2>
    							{/* lang:指标审批日志列表 */}
    							{approvalLogLang.common("indicatorTitle2")}
    						</h2>
    					</div>
    					<div className="right-info">
    						<div className="right-info-item">
    							<RangePicker
    								onChange={this.changeSearchParamField.bind(this, "selectTs", "date")}
    								style={{ width: "270px" }}
    								value={startTs && endTs ? [moment(startTs), moment(endTs)] : []}
    								format="YYYY-MM-DD"
    							/>
    						</div>
    						<div className="right-info-item">
    							<Input
    								placeholder={approvalLogLang.searchParams("taskName")} // lang:任务名称
    								value={targetName || undefined}
    								onChange={this.changeSearchParamField.bind(this, "targetName", "input")}
    								onPressEnter={this.startSearch.bind(this)}
    							/>
    						</div>
    						<div className="right-info-item">
    							<Button
    								type="primary"
    								className="search-button"
    								onClick={this.startSearch.bind(this)}
    							>
    								{/* lang:搜索 */}
    								{approvalLogLang.searchParams("search")}
    							</Button>
    						</div>
    					</div>
    				</div>
    				<div className="page-global-body-main">
    					<Table
    						className="table-card-body"
    						columns={columns}
    						dataSource={taskList}
    						pagination={false}
    						rowKey="id"
    					/>
    					<div className="page-global-body-pagination">
    						<span className="count">
    							{commonLang.getRecords(total)}
    						</span>
    						<Pagination
    							showQuickJumper
    							showSizeChanger
    							onChange={this.changePagination.bind(this)}
    							onShowSizeChange={this.changePagination.bind(this)}
    							current={curPage}
    							total={total}
    						/>
    					</div>
    				</div>
    			</div>
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	approvalLogStore: state.approvalLog
}))(SalaxyLog);
