import { isPublicPolicy } from "./utils";

// 授权过滤ruleAndIndexFieldList
const filterAvailableFieldList = ({allMap = {}, appName}) => {
	console.log(appName);
	if (!allMap || !allMap.ruleAndIndexFieldList) {
		return [];
	}
	const isPublic = isPublicPolicy(); // 公共策略
	const { ruleAndIndexFieldList = [] } = allMap || {};

	let newFieldList = [];
	newFieldList = ruleAndIndexFieldList.filter(item=>{
		let showOption = !item.apps;
		if (!showOption && item.apps) {
			if (isPublic) {
				showOption = appName === "GLOBAL_APP";
				if (!showOption) {
					let exist = false;
					appName.split(",").forEach(name=>{
						if (!exist && item.apps.indexOf(name) > -1) {
							exist = true;
						}
					});
					showOption = exist;
				}

			} else {
				showOption = item.apps.indexOf(appName) > -1;
			}
		}
		if (showOption) {
			return item;
		}
	});
	return newFieldList;
};

export {
	filterAvailableFieldList
}
;
