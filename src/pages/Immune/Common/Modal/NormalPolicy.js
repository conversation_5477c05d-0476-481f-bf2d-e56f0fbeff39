import { Fragment, PureComponent } from "react";
import { connect } from "dva";
import { Form, Col, Select } from "antd";
import { immuneConfigLang } from "@/constants/lang";

const { Option } = Select;
class NormalPolicy extends PureComponent {
	constructor(props) {
		super(props);
		this.changeField = props.changeField;
	}

	// 修改应用
	changeApp = (e) => {
		const { dispatch } = this.props;
		this.changeField("appName", e, "select");
		// 清空相关列表数据
		dispatch({
			type: "immuneConfig/setAttrValue",
			payload: {
				policySetListLoad: false,
				policySetList: [],
				ruleList: [],
				policyList: []
			}
		});
		// 清空相关联动数据值
		dispatch({
			type: "immuneConfig/setAddModifyImmuneData",
			payload: {
				"policySetUuid": "", // 策略集
				"policyUuid": "", // 策略
				"ruleName": "", // 规则名
				"ruleUuid": "" // 规则
			}
		});
		// 清空联动的select数据
		this.props.form.resetFields(["policySetUuid", "policyUuid", "ruleUuid"]);
		// 联动策略集 此为接口请求
		dispatch({
			type: "immuneConfig/fetchPolicySet"
		});
	}

	// 策略集联动策略
	changePolicySet = (value, ind) => {
		// 如果是全选应用则回填真实在的appname
		const { immuneConfigStore } = this.props;
		const { policySetList } = immuneConfigStore || {};
		const policySetItem = policySetList[ind] || {};
		const { policyList = [] } = policySetItem;

		if (policySetItem.appName) {
			this.changeField("appName", policySetItem.appName, "select");
		}

		this.changeField("policySetUuid", value || "", "select");
		const { dispatch } = this.props;
		// 清空相关联动数据
		dispatch({
			type: "immuneConfig/setAttrValue",
			payload: {
				policyList,
				ruleList: []
			}
		});
		// 清空相关联动数据值
		dispatch({
			type: "immuneConfig/setAddModifyImmuneData",
			payload: {
				"policyUuid": "", // 策略
				"ruleName": "", // 规则名
				"ruleUuid": "" // 规则
			}
		});
		this.props.form.resetFields(["policyUuid", "ruleUuid"]);
	}

	// 策略联动规则
	changePolicy = (value, policy) => {
		this.changeField("policyUuid", value, "select");
		const { dispatch } = this.props;

		// 重置联动数据值
		dispatch({
			type: "immuneConfig/setAddModifyImmuneData",
			payload: {
				"ruleName": "", // 规则名
				"ruleUuid": "" // 规则
			}
		});
		this.props.form.resetFields(["ruleUuid"]);

		// 请求相应的规则 此为接口请求
		dispatch({
			type: "immuneConfig/getPolicyRules",
			payload: {
				isFirst: true,
				policyUuid: policy.uuid,
				policyVersion: policy.policyVersion
			}
		});
	}

	render() {
		const { form, globalStore, immuneConfigStore } = this.props;
		const { getFieldDecorator } = form;
	 	const { addModifyImmuneData, policySetList = [], policyList = [], ruleList = [], openType } = immuneConfigStore || {};
	 	const {
	 		appName,
	 		policySetUuid,
	 		policyUuid,
	 		ruleUuid
	 	} = addModifyImmuneData || {};
		let { appList } = globalStore;
		let disabled = this.props.disabled || openType === "edit";
		return (
			<Fragment>
				<Col span="24">
					{/* 免疫应用 */}
					<Form.Item label={immuneConfigLang.immuneConfigModal("immuneApp")}>
						{
							getFieldDecorator("appName", {
								initialValue: appName || undefined,
								rules: [
									{
										required: true,
										message: immuneConfigLang.immuneConfigModal("immuneAppMsg") // "请选择应用"
									}
								]
							})(
								<Select
									disabled={disabled}
									value={appName || undefined}
									placeholder={immuneConfigLang.immuneConfigModal("immuneAppMsg")} // "请选择应用"
									allowClear
									showSearch
									onChange={(e)=>{
										this.changeApp(e);
									}}
									filterOption={ (input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 }
								>
									{
										appList &&
										appList.length > 0 &&
										appList.map(v=>{
											return (
												<Option value={v.name} key={v.name}>{v.dName}</Option>
											);
										})
									}
								</Select>
							)
						}
					</Form.Item>
				</Col>
				<Col span="24">
					{/* 选择策略集 */}
					<Form.Item label={immuneConfigLang.immuneConfigModal("selectPolicySet")}>
						{
							getFieldDecorator("policySetUuid", {
								initialValue: policySetUuid || undefined,
								rules: [
									{
										required: true,
										message: immuneConfigLang.immuneConfigModal("selectPolicySetMsg")// "请选择策略集"
									}
								]
							})(
								<Select
									disabled={disabled}
									value={policySetUuid || undefined}
									placeholder={immuneConfigLang.immuneConfigModal("selectPolicySetMsg")} // "请选择策略集"
									filterOption={ (input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 }
									showSearch
									allowClear
									onChange={(e, _this)=>{
										const { ind } = (_this && _this.props) ? _this.props : {} ;
										this.changePolicySet(e, ind);
									}}
								>
									{
										policySetList &&
										policySetList.length > 0 &&
										policySetList.map((v, i)=>{
											return (
												<Option value={v.id} ind={i} key={v.id}>{v.name}</Option>
											);
										})
									}
								</Select>
							)
						}
					</Form.Item>
				</Col>
				<Col span="24">
					{/* 选择策略 */}
					<Form.Item label={immuneConfigLang.immuneConfigModal("selectPolicy")}>
						{
							getFieldDecorator("policyUuid", {
								initialValue: policyUuid || undefined,
								rules: [
									{
										required: true,
										message: immuneConfigLang.immuneConfigModal("selectPolicyMsg") // 请选择策略
									}
								]
							})(
								<Select
									disabled={disabled}
									value={policyUuid || undefined}
									placeholder={immuneConfigLang.immuneConfigModal("selectPolicyMsg")} // 请选择策略
									allowClear
									showSearch
									filterOption={ (input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 }
									onChange={(e, _this)=>{
										const { policy } = (_this && _this.props) ? _this.props : {} ;
										this.changePolicy(e, policy);
									}}
								>
									{
										policyList &&
										policyList.length > 0 &&
										policyList.map(v=>{
											return (
												<Option value={v.uuid} key={v.uuid} policy={v}>{v.name}</Option>
											);
										})
									}
								</Select>
							)
						}
					</Form.Item>
				</Col>
				<Col span="24">
					{/* 选择规则 */}
					<Form.Item label={immuneConfigLang.immuneConfigModal("selectRule")}>
						{
							getFieldDecorator("ruleUuid", {
								initialValue: ruleUuid || undefined,
								rules: [
									{
										required: true,
										message: immuneConfigLang.immuneConfigModal("selectRuleMsg") // 请选择规则
									}
								]
							})(
								<Select
									disabled={disabled}
									value={ruleUuid || undefined}
									placeholder={immuneConfigLang.immuneConfigModal("selectRuleMsg") } // 请选择规则
									allowClear
									showSearch
									filterOption={ (input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 }
									onChange={(e, _this)=>{
										const { name } = (_this && _this.props) ? _this.props : {} ;
										this.changeField("ruleUuid", e, "select");
										this.changeField("ruleName", name, "select");
									}}
								>
									{
										ruleList &&
										ruleList.length > 0 &&
										ruleList.map((v)=>{
											return (
												<Option value={v.uuid} key={v.uuid} name={v.name}>{v.name}</Option>
											);
										})
									}
								</Select>
							)
						}
					</Form.Item>
				</Col>
			</Fragment>
		);
	}
};
export default connect(state => ({
	globalStore: state.global,
	immuneConfigStore: state.immuneConfig
}))(NormalPolicy);
