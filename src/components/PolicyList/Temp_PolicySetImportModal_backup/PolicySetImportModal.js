import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Button, Row, Col, message, Tooltip } from "antd";
import "./PolicySetImportModal.less";
import { policyEditorAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { imExportModeLang, commonLang } from "@/constants/lang";
import ImportMode from "@/components/ImportModal/FileImportCondition/ImportMode";
import SelectScene from "@/components/ImportModal/FileImportCondition/SelectScene";
import FileImportResult from "@/components/ImportModal/FileImportResult";
import { cloneDeep } from "lodash";

class PolicySetImportModal extends PureComponent {
	state = {
		hasSuccess: false,
		successData: {},
		uploadLoading: false
	};

	constructor(props) {
		super(props);
	}

	changeImport = (e, { type, valueType = "input" }) => {
		let value = "";
		if (valueType === "input") {
			value = e.target.value;
		} else {
			value = e;
		}
		let { policyEditorStore, dispatch } = this.props;
		let { dialogData } = policyEditorStore;
		let { policySetImportData } = dialogData;
		const newPolicySetImportData = cloneDeep(policySetImportData);

		newPolicySetImportData[type] = value;
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				policySetImportData: newPolicySetImportData
			}
		});
	}

	importPolicySets = async() => {
		let { policyEditorStore, globalStore, dispatch } = this.props;
		let { dialogData, curPage, pageSize } = policyEditorStore;
		let { policySetImportData } = dialogData;

		if (!policySetImportData["policyMode"]) {
			// lang:请选择策略导入模式
			message.warning(imExportModeLang.importPolicyModal("policyModeRequired"));
			return;
		}

		if (!policySetImportData["zbMode"]) {
			// lang:请选择指标导入模式
			message.warning(imExportModeLang.importPolicyModal("zbModeRequired"));
			return;
		}

		if (!policySetImportData["file"]) {
			// lang:请选择要上传的文件
			message.warning(imExportModeLang.importPolicyModal("selectFileFirst"));
			return;
		}

		const newPolicySetImportData = cloneDeep(policySetImportData);
		if (!newPolicySetImportData.replaceScene) {
			delete newPolicySetImportData.replaceScene;
		} else {
			newPolicySetImportData.replaceScene = JSON.stringify(newPolicySetImportData.replaceScene);
		}
		await this.setState({
			uploadLoading: true
		});
		await policyEditorAPI.importPolicySets(newPolicySetImportData).then(res => {
			this.setState({
				uploadLoading: false
			});
			if (res.success) {
				this.refs.policySetImportFile.value = "";
				this.setState({
					hasSuccess: true,
					successData: res.data || {}
				});
				// 导入成功后
				// 1. 重新刷新编辑区策略列表
				let { currentApp } = globalStore;
				if (currentApp.name) {
					dispatch({
						type: "policyEditor/getPolicySets",
						payload: {
							appName: currentApp.name ? currentApp.name : null,
							curPage: curPage,
							pageSize: pageSize
						}
					});
				}
				// 2.刷新allMap
				dispatch({
					type: "global/getAllMap",
					payload: {}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			this.setState({
				uploadLoading: false
			});
			console.log(err);
		});
	}

	closeModal = () => {
		let { dispatch } = this.props;

		dispatch({
			type: "policyEditor/setDialogShow",
			payload: {
				policySetImport: false
			}
		});
		dispatch({
			type: "policyEditor/setDialogData",
			payload: {
				policySetImportData: {
					uuid: null,
					importMode: null,
					file: null,
					replaceScene: "",
					zbMode: "",
					policyMode: ""
				}
			}
		});
		if (this.refs.policySetImportFile && this.refs.policySetImportFile.value) {
			this.refs.policySetImportFile.value = "";
		}
		this.setState({
			hasSuccess: false,
			successData: {},
			uploadLoading: false
		});
	}

	render() {
		let { policyEditorStore, dispatch } = this.props;
		let { successData = {}, hasSuccess, uploadLoading } = this.state;
		let { dialogShow, dialogData } = policyEditorStore;
		let { policySetImportData } = dialogData;
		const { replaceScene } = policySetImportData || {};
		let footerButton = [];
		if (hasSuccess) {
			footerButton = [
				<Button type="primary" onClick={this.closeModal.bind(this)}>
					{/* lang:确定 */}
					{commonLang.base("ok")}
				</Button>
			];
		} else {
			footerButton = [
				<Button onClick={this.closeModal.bind(this)}>
					{/* lang:取消 */}
					{commonLang.base("cancel")}
				</Button>,
				<Button
					type="primary"
					onClick={this.importPolicySets.bind(this)}
					loading={uploadLoading}
					disabled={uploadLoading}
				>
					{/* lang:上传 */}
					{commonLang.base("upload")}
				</Button>
			];
		}

		return (
			<Modal
				title={imExportModeLang.importPolicyModal("policyImport")} // 策略导入
				visible={dialogShow.policySetImport}
				maskClosable={false}
				onCancel={this.closeModal.bind(this)}
				className="rules-import-modal-wrap"
				footer={footerButton}
				width={700}
			>
				<div className="basic-form s2">
					{
						!hasSuccess &&
						<div>
							<Row gutter={CommonConstants.gutterSpan} style={{ height: "auto" }}>
								<Col span={5} className="basic-info-title">
									{/* lang:策略集*/}
									{imExportModeLang.importPolicyModal("policySet")}：
                        		</Col>
								<Col span={10}>
									{policySetImportData.policySetName}
								</Col>
							</Row>
							{/* 导入场景 */}
							<ImportMode
								changeImport={this.changeImport}
								data={policySetImportData}
								showMode={{ "zbMode": true, "policyMode": true }}
							/>

							<Row gutter={CommonConstants.gutterSpan}>
								<Col span={5} className="basic-info-title line-height-32">
									{/* lang:选择文件 */}
									{imExportModeLang.importPolicyModal("selectFile")}：
                        		</Col>
								<Col span={19}>
									{/* lang:文件大小在100M以内 */}
									<Tooltip title={imExportModeLang.importPolicyModal("fileSizeTip")}>
										<a className="file line-height-32">
											{/* lang:选择文件 */}
											{imExportModeLang.importPolicyModal("selectFile")}
											<input
												type="file"
												ref="policySetImportFile"
												onChange={(e) => {
													let file = e.target && e.target.files && e.target.files[0] ? e.target.files[0] : undefined;
													if (policySetImportData["file"]) {
														if (!file) {
															return;
														}
													} else {
														if (!file) {
															// lang:请先选择文件
															message.warning(imExportModeLang.importPolicyModal("selectFileFirst"));
															return;
														}
													}
													let filePath = file.name;
													let fileSize = file.size / 1024;
													let reg = new RegExp(".(pls)$", "i");
													if (!reg.test(filePath)) { // 校验不通过
														// lang:只允许上传pls格式的文件
														message.warning(imExportModeLang.importPolicyModal("fileAllowedPlsTip"));
														return;
													}
													if (fileSize > 100000) {
														// lang:文件大小请100M内
														message.warning(imExportModeLang.importPolicyModal("fileSizeTip"));
														return;
													}

													policySetImportData["file"] = file;
													dispatch({
														type: "policyEditor/setDialogData",
														payload: {
															policySetImportData: policySetImportData
														}
													});
												}}
											/>
										</a>
									</Tooltip>
									<div className="mb10">
										{policySetImportData && policySetImportData["file"] ? policySetImportData["file"]["name"] : undefined}
									</div>
								</Col>
							</Row>

							{/* 选择场景 */}
							<SelectScene
								changeImport={this.changeImport}
								data={policySetImportData}
								type="policySet"
							/>
						</div>
					}
					{
						hasSuccess &&
						<FileImportResult
							type="policySet"
							showMode={{ "zbMode": true, "policyMode": true, "ruleMode": true }}
							successData={successData}
							replaceScene={replaceScene}
						/>
					}
				</div>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyEditorStore: state.policyEditor
}))(PolicySetImportModal);
