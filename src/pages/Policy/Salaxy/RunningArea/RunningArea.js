import React, { PureComponent } from "react";
import { connect } from "dva";
import { indexRunningAPI } from "@/services";
import { Input, Icon, message, Tooltip, Select, Modal, Collapse, Pagination, Spin, Checkbox, Button, Row, Col } from "antd";
import { cloneDeep } from "lodash";
import { indexListLang, commonLang } from "@/constants/lang";
import { checkFunctionHasPermission } from "@/utils/permission";
import NoPermission from "@/components/NoPermission";
import IndexConfig from "./Inner/IndexConfig";
import ExportIndex from "../Common/ExportIndex";
import "../Salaxy.less";

const IndexVersionModal = React.lazy(() => import("../EditorArea/Modal/IndexVersionModal"));
const OffLineResult = React.lazy(() => import("../Common/BatchDeal/OffLineResult"));
const AuthListModal = React.lazy(() => import("../Common/AuthListModal"));

const InputGroup = Input.Group;
const {Option, OptGroup} = Select;
const confirm = Modal.confirm;
const Panel = Collapse.Panel;

class RunningArea extends PureComponent {
	state = {
		indexActiveKey: [],
		currentIndexId: null,
		ruleNameWidth: 330
	};

	constructor(props) {
		super(props);
	}

	componentDidMount() {
		this.initPage();
		window.addEventListener("resize", this.initPage);
		let { isOtherPageCite } = this.props;
		if (!isOtherPageCite) {
			this.timer = setInterval(() => {
				const { globalStore } = this.props;
				const { menuTreeReady } = globalStore;
				if (menuTreeReady) {
					clearInterval(this.timer);
					if (checkFunctionHasPermission("ZB0102", "runningSearch")) {
						this.search();
					}
				}
			}, 100);
		}
	}

	componentDidUpdate(preProps) {
		const { dispatch, isOtherPageCite } = this.props;
		let { indexActiveKey, currentIndexId } = preProps.indexRunningStore;
		if (indexActiveKey && indexActiveKey.length > 0 && currentIndexId && !isOtherPageCite) {
			console.log("receive message setState");
			this.setState({
				indexActiveKey: indexActiveKey,
				currentIndexId: currentIndexId
			});
			dispatch({
				type: "indexRunning/setAttrValue",
				payload: {
					indexActiveKey: [],
					currentIndexId: ""
				}
			});
		}
	}
	initPage = () => {
		let clientWidth = document.querySelector("body").offsetWidth;

		let ruleNameWidth = clientWidth - 850;
		if (ruleNameWidth < 330) {
			ruleNameWidth = 330;
		}
		this.setState({
			ruleNameWidth: ruleNameWidth
		});
	}

	search = () => {
		let { indexRunningStore, dispatch, globalStore } = this.props;
		let { currentApp } = globalStore;
		let { curPage, pageSize, searchParams } = indexRunningStore;
		let appName;

		if (currentApp.name) {
			appName = currentApp.name;
		}
		searchParams["app"] = appName;
		dispatch({
			type: "indexRunning/getSalaxyList",
			payload: {
				curPage: curPage,
				pageSize: pageSize,
				...searchParams
			}
		});
	}

	componentWillReceiveProps(nextProps) {
		let { indexRunningStore, dispatch, globalStore } = this.props;
		const { menuTreeReady } = globalStore;
		let { pageSize, searchParams } = indexRunningStore;
		let preCurrentApp = this.props.globalStore.currentApp;
		let nextCurrentApp = nextProps.globalStore.currentApp;

		if (!menuTreeReady || !checkFunctionHasPermission("ZB0101", "runningSearch")) {
			return;
		}
		if (JSON.stringify(preCurrentApp) !== JSON.stringify(nextCurrentApp)) {
			if (nextCurrentApp.name !== "all") {
				searchParams["app"] = nextCurrentApp.name;
			} else {
				searchParams["app"] = "";
			}

			searchParams["sceneType"] = null;
			searchParams["event"] = null;
			searchParams["calcType"] = null;

			dispatch({
				type: "indexRunning/setAttrValue",
				payload: {
					curPage: 1,
					searchParams: searchParams
				}
			});

			dispatch({
				type: "indexRunning/getSalaxyList",
				payload: {
					curPage: 1,
					pageSize,
					...searchParams
				}
			});
		}
	}

	paginationOnChange = (current, pageSize) => {
		let { indexRunningStore, policyDetailStore, dispatch, citePageName } = this.props;
		let { searchParams } = indexRunningStore;

		// 判断是policyDetail
		if (citePageName === "policyDetail") {
			const { policyDetail } = policyDetailStore;
			dispatch({
				type: "indexRunning/getPolicyCiteIndexList",
				payload: {
					curPage: current,
					pageSize: pageSize,
					...searchParams,
					policyUuid: policyDetail.uuid
				}
			});
			return;
		}

		dispatch({
			type: "indexRunning/setAttrValue",
			payload: {
				curPage: current,
				pageSize: pageSize
			}
		});

		// 判断是policyDetail
    	if (citePageName === "policyDetail") {
    		const { policyDetail } = policyDetailStore;
    		dispatch({
    			type: "indexRunning/getPolicyCiteIndexList",
    			payload: {
    				curPage: current,
    				pageSize: pageSize,
    				...searchParams,
    				policyUuid: policyDetail.uuid
    			}
    		});
    		return;
		}

		dispatch({
			type: "indexRunning/getSalaxyList",
			payload: {
				curPage: current,
				pageSize,
				...searchParams
			}
		});
	}

	onSearch = () => {
		let { indexRunningStore, dispatch, globalStore } = this.props;
		let { pageSize, searchParams } = indexRunningStore;
		let { currentApp } = globalStore;
		let appName = currentApp.name;

		if (appName !== "all") {
			searchParams["app"] = appName;
		} else {
			searchParams["app"] = "";
		}
		dispatch({
			type: "indexRunning/getSalaxyList",
			payload: {
				curPage: 1,
				pageSize: pageSize,
				...searchParams
			}
		});
	}

	changeSearchParams = (field, type, e, toSearch) => {
		let { indexRunningStore, dispatch } = this.props;
		let { searchParams } = indexRunningStore;
		let value = e || "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e === "all" ? null : e;
		}
		searchParams[field] = value;
		dispatch({
			type: "indexRunning/setAttrValue",
			payload: {
				searchParams: searchParams
			}
		});

		if (toSearch) {
			this.onSearch();
		}
	}

	changeCollapse = (value) => {
		let { indexRunningStore, dispatch } = this.props;
		let { indexList, dialogShow, dialogData } = indexRunningStore;
		let { indexCiteDrawer } = dialogShow;
		let { indexCiteDrawerData } = dialogData;
		let idList = value.length > 1 ? [value[value.length - 1]] : value;

		if (indexCiteDrawer) {
			if (idList.length) {
				// 判断查看指标引用的抽屉有没有打开，如果打开，则点击请求指标引用接口
				if (indexCiteDrawerData.indexId && indexCiteDrawerData.indexId.toString() === idList[0]) {
					dispatch({
						type: "indexRunning/setDialogShow",
						payload: {
							indexCiteDrawer: false
						}
					});
				} else {
					let currentIndexObj = indexList.find(item => item.id.toString() === idList[0]);
					indexCiteDrawerData["item"] = currentIndexObj;
					indexCiteDrawerData["indexId"] = idList[0];

					dispatch({
						type: "indexRunning/setDialogData",
						payload: {
							indexCiteDrawerData: indexCiteDrawerData
						}
					});
					this.getIndexCite(idList[0]);
				}
			} else {
				this.setState({
					indexActiveKey: []
				});
			}
		} else {
			// 如果抽屉没有打开，判断当前指标是打开还是关闭
			if (idList.length) {
				this.setState({
					indexActiveKey: idList,
					currentIndexId: idList[0]
				});
			} else {
				this.setState({
					indexActiveKey: []
				});
			}
		}
	}

	// 切换版本后刷新编辑区列表页面
	refreshEditorArea = () => {
		const { dispatch, globalStore, indexEditorStore, indexRunningStore } = this.props;
		const { dialogData = {} } = indexRunningStore;
		const { indexVersion: {switchType, id} } = dialogData || {};
		// 1.清空每个策略详情
		dispatch({
			type: "indexEditor/initSalaxyExpandDetail"
		});
		// 如果是运行区则刷新编辑区列表
		if (switchType === "Running") {
			// 重新获取编辑区列表数据
			let { currentApp } = globalStore;
			let { curPage, pageSize, searchParams } = indexEditorStore;
			let appName;
			if (currentApp.name) {
				appName = currentApp.name;
			}
			searchParams["app"] = appName;
			dispatch({
				type: "indexEditor/getSalaxyList",
				payload: {
					curPage: curPage,
					pageSize: pageSize,
					...searchParams
				}
			});
		} else {
			// 如果是编辑区则刷新当前指标的更换后的版本详情
			// 刷新当前item的数据
			dispatch({
				type: "indexEditor/switchVersionDetail",
				payload: {
					id
				}
			});
		}
	}
	// 切换版本
	indexVersionSwitch = (item, e) => {
		e.stopPropagation();
		let params = {
			id: item.id
		};
		confirm({
			title: indexListLang.operator("switchVersion"),		// lang:切换版本到编辑区
			content: indexListLang.operator("switchVersionContentText1") + item.zbVersion + indexListLang.operator("switchVersionContentText2"),	// lang:"确认将当前指标V" + item.zbVersion + "覆盖到编辑区吗？"
			onOk: ()=> {
				indexRunningAPI.indexVersionSwitch(params).then(res => {
					if (res.success) {
						// lang:切换版本到编辑区成功
						message.success(res.message || indexListLang.operator("switchVersionSuccessTip"));
						// 刷新编辑区详情
						this.refreshEditorArea();
					} else {
						message.error(res.message);
					}
				}).catch(err => {
					console.log(err);
				});
			},
			onCancel: ()=> {
				console.log("Cancel");
			}
		});
	}

	// 批量下线
	batchOffline = (checkedList) => {
		const { dispatch } = this.props;
		confirm({
			title: indexListLang.common("batchOffLine"),
			content: indexListLang.common("batchOffWarn"),
			onOk() {
				const ids = checkedList.map((checked) => {
					return checked.id;
				}).join(",");
				dispatch({
					type: "indexRunning/closeOnlineZb",
					payload: {
						ids
					}
				});
			},
			onCancel() { }
		});
	}
	cancelZbRes = () => {
		const { dispatch, globalStore, indexEditorStore } = this.props;
		dispatch({
			type: "indexRunning/setAttrValue",
			payload: {
				closeZbDialogShow: false
			}
		});
		// 重新获取列表数据
		let { currentApp } = globalStore;
		let { curPage: editorCurPage, pageSize: editorPageSize, searchParams: editorSearchParams } = indexEditorStore;
		let appName;
		if (currentApp.name) {
			appName = currentApp.name;
		}
		editorSearchParams["app"] = appName;
		dispatch({
			type: "indexEditor/getSalaxyList",
			payload: {
				curPage: editorCurPage,
				pageSize: editorPageSize,
				...editorSearchParams
			}
		});
		this.onSearch();
	}

	// 打开授权列表
	openAuthListModal = (item) => {
		const { dispatch } = this.props;
		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				runningAuthList: true
			}
		});
		dispatch({
			type: "indexEditor/fetchLicenseApps",
			payload: {
				zbUuid: item.uuid,
				isRunning: true
			}
		});
	}

	render() {
		let { indexActiveKey, currentIndexId, ruleNameWidth } = this.state;
		let { indexRunningStore, globalStore, templateStore, isOtherPageCite, citePageName, dispatch } = this.props;
		let { allMap, personalMode, menuTreeReady } = globalStore;
		let lang = personalMode.lang === "cn" ? "cn" : "en";
		const pageName = citePageName ? citePageName : "runningArea";
		let { indexPages, searchParams, listLoad, closeZbRes, closeZbDialogShow, dialogShow } = indexRunningStore;
		let { indexList, indexListReady, curPage, pageSize, total } = indexPages[pageName];

		// 获取筛选参数
		const { sceneType, event } = searchParams;

		// 获取场景条件
		const { sceneTypeSelect, sceneSelect } = allMap;
		const sceneSelectList = sceneSelect ? sceneSelect[sceneType || "ALL"] : [];

		// 准备checkbox的状态数据
		let indexListBackup = cloneDeep(indexList);
		let checkedList = indexList.filter(item => item.checked);
		let indeterminate = !!checkedList.length && (checkedList.length < indexList.length);
		let checkAll = checkedList.length === indexList.length && indexList.length !== 0;

		let collapseList = indexList.map((item, index) => {
			let title = (obj) => {
				// 转指标类型为中文
				let currentCalcTypeObj = templateStore.indexOperateTypeMap && templateStore.indexOperateTypeMap.find((v) => {
					if (v.name === obj.calcType) {
						return v;
					}
				}) || {};
				return (
					<div
						className="rule-manage-header collapse-header"
						key={index}
					>
						{
							!isOtherPageCite &&
							<div className="checkbox">
								<Checkbox
									checked={obj.checked}
									onClick={(e) => {
										e.stopPropagation();
										const status = e.target.checked;
										indexListBackup.find(fItem => fItem.id === obj.id)["checked"] = status;
										dispatch({
											type: "indexRunning/initCheckedSalaxyList",
											payload: {
												pageName,
												indexList: indexListBackup
											}
										});
									}}
								></Checkbox>
							</div>
						}
						<div
							className="rule-name"
							style={{ maxWidth: ruleNameWidth + "px" }}
						>
							<Icon
								className={indexActiveKey.length && indexActiveKey[0].toString() === obj.id.toString() ? "row-collapsed active" : "row-collapsed"}
								type={indexActiveKey.length && indexActiveKey[0].toString() === obj.id.toString() ? "minus-square" : "plus-square"}
							/>
							{
								obj ? obj.name : ""
							}
						</div>
						<div className="rule-st">
							{
								isOtherPageCite &&
								citePageName === "versionModal" &&
								<span className="need-refresh">{indexListLang.table("version")}：V{obj.zbVersion}</span>	// lang:版本号
							}
							{
								isOtherPageCite &&
								citePageName === "approvalTask" &&
								<span className="need-refresh">{obj.approvalStatus}</span>
							}
						</div>
						{
							isOtherPageCite &&
							citePageName === "versionModal" &&
							<div className="rule-action table-action">
								<a
									className={checkFunctionHasPermission("ZB0102", "switchVersion") ? null : "a-disabled"}
									onClick={(e) => {
										if (checkFunctionHasPermission("ZB0102", "switchVersion")) {
											this.indexVersionSwitch(obj, e);
										} else {
											// lang:无权限操作
											message.warning(commonLang.messageInfo("noPermission"));
										}
									}}
								>
									{/* lang：切换版本 */}
									{indexListLang.table("switchVersion")}
								</a>
							</div>
						}
						{
							!isOtherPageCite &&
							<div className="rule-action">
								{
									checkFunctionHasPermission("ZB0102", "editorViewHistoryVersion")
										// lang:查看历史版本
										? <Tooltip title={indexListLang.tooltip("viewHistoryVersion")}>
											<i
												className="iconfont icon-version"
												onClick={(e) => {
													e.stopPropagation();
													dispatch({
														type: "indexRunning/setDialogShow",
														payload: {
															indexVersion: true
														}
													});
													dispatch({
														type: "indexRunning/setDialogData",
														payload: {
															indexVersion: {
																switchType: "Running",
																id: ""
															}
														}
													});
													dispatch({
														type: "indexRunning/getIndexVersionList",
														payload: {
															uuid: obj.uuid
														}
													});
												}}
											></i>
										</Tooltip>
										: <i className="iconfont icon-version icon-disabled" onClick={() => {
											// lang:无权限操作
											message.warning(commonLang.messageInfo("noPermission"));
										}}></i>
								}
								{
									checkFunctionHasPermission("ZB0102", "queryLicenseApplication")
										// lang:授权
										? <Tooltip title={indexListLang.tooltip("authorization")}>
											<i className="salaxy-iconfont salaxy-user-follow-line"
												onClick={(e) => {
													e.stopPropagation();
													this.openAuthListModal(obj, e);
												}}></i>
										</Tooltip>
										: <i className="salaxy-iconfont salaxy-user-follow-line icon-disabled" onClick={() => {
											// lang:无权限操作
											message.warning(commonLang.messageInfo("noPermission"));
										}}></i>
								}
							</div>
						}
						{/* lang:开启/关闭 */}
						{/*
                            <div className="rule-status2" onClick={(e) => {
    						    e.stopPropagation();
    					    }}>
                                <Switch
                                    checkedChildren={indexListLang.table("on")}
                                    unCheckedChildren={indexListLang.table("off")}
                                    checked={obj.status === "1"}
                                    disabled={true}
                                />
                            </div>
                        */}
						<div className="rule-number">
							{currentCalcTypeObj && currentCalcTypeObj.dName}
						</div>
					</div>
				);
			};
			let renderDom = [];
			let itemObj = (
				<Panel
					header={title(item, [index])}
					key={item.id}
					showArrow={false}
					forceRender={true}
				>
					{
						currentIndexId &&
						currentIndexId.toString() === item.id.toString() &&
						<IndexConfig
							templateName={item.calcType}
							indexData={item}
							changeActiveKey={(indexActiveKey) => {
								this.setState({
									indexActiveKey: indexActiveKey
								});
							}}
						/>
					}
				</Panel>
			);
			renderDom.push(itemObj);
			return renderDom;
		});

		return (
			<div className={isOtherPageCite ? "page-global-body no-padding" : "page-global-body"}>
				{
					!isOtherPageCite &&
					<div className="page-global-body-search">
						{
							menuTreeReady &&
							checkFunctionHasPermission("ZB0102", "runningSearch") &&
							<Row gutter={10}>
    							<Col span={8}>
									<InputGroup compact>
										<Select
											style={{"width": "35%"}}
											placeholder={indexListLang.searchParams("selectSceneType")} // lang:选择场景类型
											value={sceneType || "ALL" }
											onChange={(e) => {
												this.changeSearchParams("event", "select", null); // 联动场景
												this.changeSearchParams("sceneType", "select", e === "ALL" ? null : e, true);
											}}
										>
											{
												sceneTypeSelect &&
												sceneTypeSelect.length > 0 &&
												sceneTypeSelect.map(v=>{
													return (
														<Option value={v.name} key={v.name}>{v.dName}</Option>
													);
												})
											}
										</Select>
										<Select
											placeholder={indexListLang.searchParams("selectScene")} // lang:选择场景
											value={event || (!sceneType ? "ALL" : "")}
											style={{"width": "65%"}}
											showSearch
											optionFilterProp="children"
											dropdownMatchSelectWidth={false}
											onChange={(e) => {
												this.changeSearchParams("event", "select", !sceneType ? null : e, true);
											}}
										>
											{
												sceneType &&
												sceneType !== "ALL" &&
												<Option value="">
													{/* 全部 */}
													{indexListLang.searchParams("all")}
												</Option>
											}
											{
												sceneSelectList &&
													sceneSelectList.length > 0 &&
													sceneSelectList.map((v)=>{
														if (sceneType !== "EVENT") {
															return (
																<Option key={v.name} value={v.name}>{v.dName}</Option>
															);
														} else {
															const { eventList = [], dName, name } = v;
															return (
																<OptGroup label={dName} key={name}>
																	{
																		eventList &&
																		eventList.length > 0 &&
																		eventList.map(eventItem=>{
																			return <Option key={eventItem.name} value={eventItem.name}>{eventItem.dName}</Option>;
																		})
																	}
																</OptGroup>
															);
														}
													})
											}
										</Select>
									</InputGroup>
								</Col>
								<Col span={4}>
									<Select
										className="wid-100-percent"
										placeholder={indexListLang.searchParams("selectIndexTypes")} // lang：选择指标类型
										value={searchParams.calcType ? searchParams.calcType : "all"}
										onChange={(e) => {
											this.changeSearchParams("calcType", "select", e, true);
										}}
										showSearch
										optionFilterProp="children"
										dropdownMatchSelectWidth={false}
									>
										{/* lang：全部指标类型*/}
										<Option value="all">{indexListLang.searchParams("allIndexTypes")}</Option>
										{
											templateStore.indexOperateTypeMap &&
											templateStore.indexOperateTypeMap.map((item, index) => {
												return (
													<Option value={item.name} key={index}>
														{lang === "en" ? item.enDName : item.dName}
													</Option>
												);
											})
										}
									</Select>
								</Col>
								<Col span={5}>
									<Input
										className="wid-100-percent"
										placeholder={indexListLang.searchParams("indexName")} // lang：指标名称
										value={searchParams["name"] || undefined}
										onPressEnter={this.onSearch}
										onChange={(e) => {
											this.changeSearchParams("name", "input", e);
										}}
									/>
								</Col>
								<Col span={7}>
									<Row type="flex" justify="space-between">
										<Button
											style={{"marginRight": "10px"}}
											type="primary"
											onClick={() => {
												this.onSearch();
											}}
										>
											{/* lang：搜索 */}
											{indexListLang.searchParams("search")}
										</Button>
										<Button.Group>
											{/* 导出指标 */}
											<ExportIndex
												disabled={!(checkedList && checkedList.length > 0)}
												checkedList={checkedList}
												isEditorArea={false}
											/>
											<Button
												disabled={!(checkedList && checkedList.length > 0)}
												onClick={() => {
													if (checkFunctionHasPermission("ZB0102", "indicesBatchOffline")) {
														this.batchOffline(checkedList);
													} else {
													// lang:无权限操作
														message.warning(commonLang.messageInfo("noPermission"));
													}
												}}
											>
												{indexListLang.common("batchOffLine")}
											</Button>
										</Button.Group>
									</Row>
								</Col>
							</Row>
						}
					</div>
				}
				{
					menuTreeReady &&
					<div className="policy-detail-wrap zb-detail-wrap">
						{
							checkFunctionHasPermission("ZB0102", "runningSearch")
								? <Spin spinning={!isOtherPageCite && listLoad}>
									<div
										className="page-global-body-main has-table-border"
										style={{
											paddingBottom: indexList.length > 0 ? "20px" : 0,
											minHeight: indexList.length < 1 ? "100px" : 0
										}}
									>
										<div className="card-section-body">
											<div className="rule-manage">
												{
													indexList &&
													indexList.length > 0 &&
													<div className="rule-manage-header rule-th">
														{
															!isOtherPageCite &&
															<div className="checkbox pad-left-6">
																<Checkbox
																	onClick={(e) => {
																		e.stopPropagation();
																		let status = e.target.checked;
																		indexListBackup.map((item) => {
																			item.checked = status;
																		});
																		dispatch({
																			type: "indexRunning/initCheckedSalaxyList",
																			payload: {
																				pageName,
																				indexList: indexListBackup
																			}
																		});
																	}}
																	checked={checkAll}
																	indeterminate={indeterminate}
																></Checkbox>
															</div>
														}
														<div className="rule-name">
															{/* lang:指标名称 */}
															{indexListLang.table("indexName")}
														</div>
														{
															citePageName !== "approvalTask" &&
															citePageName !== "policyDetail" &&
															<div className="rule-operator">
																{/* lang:操作 */}
																{indexListLang.table("operator")}
															</div>
														}
														{/* lang:指标状态 */}
														{/*
                                                            <div className="rule-status2">
                                                                {indexListLang.table("status")}
                                                            </div>
                                                        */}
														<div className="rule-number">
															{/* lang:指标类型 */}
															{indexListLang.table("calcType")}
														</div>
													</div>
												}
												<div className="rule-manage-body">
													<Collapse
														className="rule-manage-collapse"
														activeKey={indexActiveKey}
														onChange={this.changeCollapse}
													>
														{collapseList}
													</Collapse>
													{
														indexListReady && indexList.length === 0 &&
														<div className="no-rule-tip"></div>
													}
												</div>
											</div>
										</div>
										{
											isOtherPageCite &&
											total > 10 &&
											<div className="page-global-body-pagination">
												{/* lang:共x条记录 */}
												<span className="count">{commonLang.getRecords(total)}</span>
												<Pagination
													showSizeChanger
													onChange={this.paginationOnChange}
													onShowSizeChange={this.paginationOnChange}
													defaultCurrent={1}
													total={total}
													current={curPage}
													pageSize={pageSize}
												/>
											</div>
										}
										{
											!isOtherPageCite &&
											total !== 0 &&
											<div className="page-global-body-pagination">
												{/* lang:共x条记录 */}
												<span className="count">{commonLang.getRecords(total)}</span>
												<Pagination
													showSizeChanger
													onChange={this.paginationOnChange}
													onShowSizeChange={this.paginationOnChange}
													defaultCurrent={1}
													total={total}
													current={curPage}
													pageSize={pageSize}
												/>
											</div>
										}
										{
											!isOtherPageCite &&
											total === 0 &&
											indexListReady &&
											<div class="none-data index-none-data">
												<i class="iconfont icon-empty"></i>
												<p>
													{/* lang:暂无指标信息 */}
													{indexListLang.table("noIndexInfo")}
												</p>
											</div>
										}
										{
											isOtherPageCite &&
											total === 0 &&
											indexListReady &&
											<div className="none-data">
												<i className="iconfont icon-empty"></i>
												{
													citePageName === "versionModal" &&
													<p>
														{/* lang:当前指标暂无版本记录 */}
														{indexListLang.tooltip("indexHasNoVersionRecord")}
													</p>
												}
												{
													citePageName === "policyDetail" &&
													<p>
														{/* lang:当前策略暂无引用指标 */}
														{indexListLang.tooltip("policyHasNoCiteIndex")}
													</p>
												}
											</div>
										}
										{
											!isOtherPageCite &&
											<React.Suspense fallback={null}>
												<IndexVersionModal />
											</React.Suspense>
										}

										{
											!isOtherPageCite &&
											<React.Suspense fallback={null}>
												<AuthListModal
													disabled={true}
												/>
											</React.Suspense>
										}

									</div>
								</Spin> : <NoPermission />
						}
					</div>
				}
				<React.Suspense fallback={null}>
					<OffLineResult
						title={indexListLang.batchRes("batchOfflineTitle")}
						visible={closeZbDialogShow}
						data={closeZbRes}
						onCancel={this.cancelZbRes}
					/>
				</React.Suspense>

			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	indexEditorStore: state.indexEditor,
	indexRunningStore: state.indexRunning,
	templateStore: state.template,
	policyDetailStore: state.policyDetail
}))(RunningArea);

