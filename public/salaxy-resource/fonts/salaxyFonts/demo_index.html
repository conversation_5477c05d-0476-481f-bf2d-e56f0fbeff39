<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://gtms04.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1573863" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe783;</span>
                <div class="name">出参</div>
                <div class="code-name">&amp;#xe783;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe624;</span>
                <div class="name">过程回溯-发布</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe62f;</span>
                <div class="name">任务调度回溯</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe63f;</span>
                <div class="name">icon - 回溯</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe60a;</span>
                <div class="name">repayAdd</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe609;</span>
                <div class="name">repayEdit</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe6af;</span>
                <div class="name">授权管理</div>
                <div class="code-name">&amp;#xe6af;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe607;</span>
                <div class="name">免打扰</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe602;</span>
                <div class="name">view</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe606;</span>
                <div class="name">disturb</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe605;</span>
                <div class="name">view</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe604;</span>
                <div class="name">publicPolicy</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe601;</span>
                <div class="name">addDebug</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe612;</span>
                <div class="name">免疫算法</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe7b0;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe7b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe658;</span>
                <div class="name">页面样例</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe72f;</span>
                <div class="name">020-公共策略</div>
                <div class="code-name">&amp;#xe72f;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe643;</span>
                <div class="name">样例</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon salaxy-iconfont">&#xe611;</span>
                <div class="name">下线</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 salaxy-iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'salaxy-iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont salaxy-chucan"></span>
            <div class="name">
              出参
            </div>
            <div class="code-name">.salaxy-chucan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-recall-edit"></span>
            <div class="name">
              过程回溯-发布
            </div>
            <div class="code-name">.salaxy-recall-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-recall"></span>
            <div class="name">
              任务调度回溯
            </div>
            <div class="code-name">.salaxy-recall
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-view-recall"></span>
            <div class="name">
              icon - 回溯
            </div>
            <div class="code-name">.salaxy-view-recall
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-repayAdd"></span>
            <div class="name">
              repayAdd
            </div>
            <div class="code-name">.salaxy-repayAdd
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-repayEdit"></span>
            <div class="name">
              repayEdit
            </div>
            <div class="code-name">.salaxy-repayEdit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-user-follow-line"></span>
            <div class="name">
              授权管理
            </div>
            <div class="code-name">.salaxy-user-follow-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-miandarao"></span>
            <div class="name">
              免打扰
            </div>
            <div class="code-name">.salaxy-miandarao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-view1"></span>
            <div class="name">
              view
            </div>
            <div class="code-name">.salaxy-view1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-disturb"></span>
            <div class="name">
              disturb
            </div>
            <div class="code-name">.salaxy-disturb
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-view"></span>
            <div class="name">
              view
            </div>
            <div class="code-name">.salaxy-view
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-publicPolicy"></span>
            <div class="name">
              publicPolicy
            </div>
            <div class="code-name">.salaxy-publicPolicy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-addDebug"></span>
            <div class="name">
              addDebug
            </div>
            <div class="code-name">.salaxy-addDebug
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-mianyi"></span>
            <div class="name">
              免疫算法
            </div>
            <div class="code-name">.salaxy-mianyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-preview"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.salaxy-preview
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-bk_sample"></span>
            <div class="name">
              页面样例
            </div>
            <div class="code-name">.salaxy-bk_sample
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-public_policy"></span>
            <div class="name">
              020-公共策略
            </div>
            <div class="code-name">.salaxy-public_policy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-yangli"></span>
            <div class="name">
              样例
            </div>
            <div class="code-name">.salaxy-yangli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont salaxy-offline"></span>
            <div class="name">
              下线
            </div>
            <div class="code-name">.salaxy-offline
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont salaxy-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-chucan"></use>
                </svg>
                <div class="name">出参</div>
                <div class="code-name">#salaxy-chucan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-recall-edit"></use>
                </svg>
                <div class="name">过程回溯-发布</div>
                <div class="code-name">#salaxy-recall-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-recall"></use>
                </svg>
                <div class="name">任务调度回溯</div>
                <div class="code-name">#salaxy-recall</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-view-recall"></use>
                </svg>
                <div class="name">icon - 回溯</div>
                <div class="code-name">#salaxy-view-recall</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-repayAdd"></use>
                </svg>
                <div class="name">repayAdd</div>
                <div class="code-name">#salaxy-repayAdd</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-repayEdit"></use>
                </svg>
                <div class="name">repayEdit</div>
                <div class="code-name">#salaxy-repayEdit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-user-follow-line"></use>
                </svg>
                <div class="name">授权管理</div>
                <div class="code-name">#salaxy-user-follow-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-miandarao"></use>
                </svg>
                <div class="name">免打扰</div>
                <div class="code-name">#salaxy-miandarao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-view1"></use>
                </svg>
                <div class="name">view</div>
                <div class="code-name">#salaxy-view1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-disturb"></use>
                </svg>
                <div class="name">disturb</div>
                <div class="code-name">#salaxy-disturb</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-view"></use>
                </svg>
                <div class="name">view</div>
                <div class="code-name">#salaxy-view</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-publicPolicy"></use>
                </svg>
                <div class="name">publicPolicy</div>
                <div class="code-name">#salaxy-publicPolicy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-addDebug"></use>
                </svg>
                <div class="name">addDebug</div>
                <div class="code-name">#salaxy-addDebug</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-mianyi"></use>
                </svg>
                <div class="name">免疫算法</div>
                <div class="code-name">#salaxy-mianyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-preview"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#salaxy-preview</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-bk_sample"></use>
                </svg>
                <div class="name">页面样例</div>
                <div class="code-name">#salaxy-bk_sample</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-public_policy"></use>
                </svg>
                <div class="name">020-公共策略</div>
                <div class="code-name">#salaxy-public_policy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-yangli"></use>
                </svg>
                <div class="name">样例</div>
                <div class="code-name">#salaxy-yangli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#salaxy-offline"></use>
                </svg>
                <div class="name">下线</div>
                <div class="code-name">#salaxy-offline</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
