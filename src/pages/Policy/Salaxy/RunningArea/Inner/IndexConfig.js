import { PureComponent } from "react";
import { connect } from "dva";
import { Form, Button } from "antd";
import IndexBase from "./IndexBase";
import IndexCondition from "./IndexCondition";
import IndexMore from "./IndexMore";
import { indexListLang } from "@/constants/lang";

class IndexConfig extends PureComponent {

	constructor(props) {
		super(props);
	}

	render() {
		let { templateName, indexData } = this.props;

		return (
			<div className="rule-config-wrap">
				<Form>
					<div className="rule-detail rule-attr">
						<h4>
							{/* lang:基本设置 */}
							{indexListLang.ruleConfig("basicSetting")}
						</h4>
						<div className="rule-detail-content">
							<IndexBase templateName={templateName} indexData={indexData} />
						</div>
					</div>
					<div className="rule-detail rule-condition">
						<h4>
							{/* lang:指标配置 */}
							{indexListLang.ruleConfig("indexConfig")}
						</h4>
						<div className="rule-detail-content">
							<IndexCondition templateName={templateName} indexData={indexData} />
						</div>
					</div>
					{
						indexData &&
						<div className="rule-detail rule-condition">
							<h4>
								{/* lang:更新配置 */}
								{indexListLang.ruleConfig("updateConfig")}
							</h4>
							<div className="rule-detail-content">
								{
									<IndexMore indexData={indexData} templateName={templateName} />
								}
							</div>
						</div>
					}

					<div className="rule-btns">
						<Button
							type="default"
							onClick={() => {
								let { changeActiveKey } = this.props;
								changeActiveKey([]);
							}}
						>
							{/* lang:收起 */}
							{indexListLang.ruleConfig("packUp")}
						</Button>
					</div>
				</Form>
			</div>
		);
	}
}

export default connect(state => ({
	indexRunningStore: state.indexRunning,
	templateStore: state.template
}))(IndexConfig);

