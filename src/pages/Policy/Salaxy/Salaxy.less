:global {
    .index-none-data {
        & {
            position: relative;
            margin-top: 100px;
            text-align: center;
            padding-bottom: 100px;
        }

        i {
            font-size: 48px;
        }

        p {
            font-size: 14px;
        }
    }

    .index-version-modal-wrap {
        .page-global-body.no-padding {
            padding: 0;
        }

        .ant-modal-body {
            .none-data {
                & {
                    text-align: center;
                    position: relative;
                    top: 50px;
                    min-height: 150px;
                }

                i {
                    font-size: 64px;
                }

                p {
                    font-size: 16px;
                }
            }

            .ant-card.fix-card {
                min-height: 400px;
            }
        }
    }

    // 指标导入
    .index-import-modal-wrap {
        .file {
            & {
                position: relative;
                display: inline-block;
                overflow: hidden;
                color: #3399FC;
                font-weight: 500;
                text-decoration: none;
                text-indent: 0;
            }

            input {
                position: absolute;
                font-size: 100px;
                right: 0;
                top: 0;
                opacity: 0;
            }
        }
    }
    .batch-deal-drop-down{
        .ant-dropdown-menu-item{
            padding: 0 12px;
        }
    }
}
