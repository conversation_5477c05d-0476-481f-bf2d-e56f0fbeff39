import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		reset: {
			"cn": "重置",
			"en": "Reset"
		},
		search: {
			"cn": "查询",
			"en": "Search"
		},
		add: {
			"cn": "新增",
			"en": "Add"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		ruleName: {
			"cn": "规则名称",
			"en": "Rule name"
		},
		policyType: {
			"cn": "策略类型",
			"en": "Policy type"
		},
		policyOrSetName: {
			"cn": "策略(集)名称",
			"en": "Policy(set) name"
		},
		createType: {
			"cn": "创建类型",
			"en": "Create type"
		},
		effectTime: {
			"cn": "生效时间",
			"en": "Effect time"
		},
		failureTime: {
			"cn": "失效时间",
			"en": "Failure time"
		},
		createPerson: {
			"cn": "创建人",
			"en": "Create person"
		},
		updateTime: {
			"cn": "更新时间",
			"en": "Update time"
		},
		remarks: {
			"cn": "备注",
			"en": "Remarks"
		},
		operation: {
			"cn": "操作",
			"en": "Operation"
		},
		delMsgTitle: {
			"cn": "删除免疫配置提醒",
			"en": "Delete immune configuration reminder"
		},
		delMsgContent: {
			"cn": "您真的要删除免疫配置吗？",
			"en": "Do you really want to delete the immune configuration?"
		}
	};
	return params[field][lang];
};

const tooltip = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		editImmune: {
			"cn": "编辑免疫配置",
			"en": "Edit immune config"
		},
		viewImmune: {
			"cn": "查看免疫配置",
			"en": "View immune config"
		},
		delImmune: {
			"cn": "删除免疫配置",
			"en": "Delete immune config"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		ruleName: {
			"cn": "请输入规则名称",
			"en": "Please enter a rule name"
		},
		createType: {
			"cn": "请选择创建类型",
			"en": "Please select an create type"
		},
		effectStartTime: {
			"cn": "生效起始时间",
			"en": "Effective start time"
		},
		effectEndTime: {
			"cn": "生效结束时间",
			"en": "Effective end time"
		},
		expirationStartTime: {
			"cn": "失效起始时间",
			"en": "Expiration start time"
		},
		expirationEndTime: {
			"cn": "失效结束时间",
			"en": "Expiration end time"
		}
	};
	return params[field][lang];
};

const immuneConfigModal = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		addTitle: {
			"cn": "新增规则免疫",
			"en": "New rule immunity"
		},
		editTitle: {
			"cn": "编辑规则免疫",
			"en": "Edit rule immunity"
		},
		viewTitle: {
			"cn": "查看规则免疫",
			"en": "View rule immunity"
		},
		policyType: {
			"cn": "策略类型",
			"en": "Policy type"
		},
		generalRule: {
			"cn": "普通策略规则",
			"en": "General policy rules"
		},
		publicRule: {
			"cn": "公共策略规则",
			"en": "Public policy rules"
		},
		immuneApp: {
			"cn": "免疫应用",
			"en": "Immune app"
		},
		selectPolicySet: {
			"cn": "选择策略集",
			"en": "Policy set"
		},
		selectPolicy: {
			"cn": "选择策略",
			"en": "Policy"
		},
		selectRule: {
			"cn": "选择规则",
			"en": "Rule"
		},
		immuneEffectTime: {
			"cn": "免疫生效时间",
			"en": "Effective time"
		},
		immuneUnEffectTime: {
			"cn": "免疫失效时间",
			"en": "Failure time"
		},
		remarks: {
			"cn": "备注",
			"en": "Remarks"
		},
		immuneCondition: {
			"cn": "免疫条件",
			"en": "Condition"
		},
		policyTypeMsg: {
			"cn": "请选择策略类型",
			"en": "Please select a policy type"
		},
		immuneAppMsg: {
			"cn": "请选择免疫应用",
			"en": "Please select immune application"
		},
		selectPolicySetMsg: {
			"cn": "请选择策略集",
			"en": "Please select a policy set"
		},
		selectPolicyMsg: {
			"cn": "请选择策略",
			"en": "Please select a policy"
		},
		selectRuleMsg: {
			"cn": "请选择规则",
			"en": "Please select a rule"
		},
		effectTimeMsg: {
			"cn": "请选择生效时间",
			"en": "Please select the effective time"
		},
		unEffectTimeMsg: {
			"cn": "请选择失效时间",
			"en": "Please select the expiration time"
		},
		immuneConMsg: {
			"cn": "必须要配置一条免疫条件",
			"en": "An immune condition must be configured"
		},
		max24: {
			"cn": "不能超过24个字符",
			"en": "Cannot exceed 24 characters"
		},
		remarksMsg: {
			"cn": "请输入备注",
			"en": "Please enter comments"
		},
		editSuccess: {
			"cn": "编辑免疫配置成功",
			"en": "Edit immune configuration succeeded"
		},
		createSuccess: {
			"cn": "新增免疫配置成功",
			"en": "New immune configuration succeeded"
		}
	};
	return params[field][lang];
};
export default {
	common,
	table,
	tooltip,
	searchParams,
	immuneConfigModal
};
