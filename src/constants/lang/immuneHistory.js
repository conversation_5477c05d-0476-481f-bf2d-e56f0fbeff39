import store from "../../app";

const common = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		reset: {
			"cn": "重置",
			"en": "Reset"
		},
		search: {
			"cn": "查询",
			"en": "Search"
		}
	};
	return params[field][lang];
};

const table = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		ruleName: {
			"cn": "规则名称",
			"en": "Rule name"
		},
		updateType: {
			"cn": "更新类型",
			"en": "Update type"
		},
		effectTime: {
			"cn": "生效时间",
			"en": "Effect time"
		},
		failureTime: {
			"cn": "失效时间",
			"en": "Failure time"
		},
		operaPerson: {
			"cn": "操作人",
			"en": "Operation person"
		},
		operaTime: {
			"cn": "操作时间",
			"en": "Operation time"
		},
		operaType: {
			"cn": "操作类型",
			"en": "Operation type"
		},
		remarks: {
			"cn": "备注",
			"en": "Remarks"
		},
		operation: {
			"cn": "操作",
			"en": "Operation"
		}
	};
	return params[field][lang];
};

const tooltip = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		viewHistoryImmune: {
			"cn": "查看历史详情",
			"en": "View history details"
		},
		viewDeleteImmune: {
			"cn": "无需查看删除详情",
			"en": "No need to view deletion details"
		}
	};
	return params[field][lang];
};

const searchParams = (field) => {
	let globalStore = store.getState().global;
	let { personalMode } = globalStore;
	let lang = personalMode.lang === "cn" ? "cn" : "en";
	let params = {
		ruleName: {
			"cn": "请输入规则名称",
			"en": "Please enter a rule name"
		},
		updateType: {
			"cn": "请选择更新类型",
			"en": "Please select an update type"
		},
		operaStartTime: {
			"cn": "操作起始时间",
			"en": "Operation start time"
		},
		operaEndTime: {
			"cn": "操作结束时间",
			"en": "Operation end time"
		},
		operaType: {
			"cn": "操作类型",
			"en": "Please select the operation type"
		}
	};
	return params[field][lang];
};
export default {
	common,
	table,
	tooltip,
	searchParams
};
