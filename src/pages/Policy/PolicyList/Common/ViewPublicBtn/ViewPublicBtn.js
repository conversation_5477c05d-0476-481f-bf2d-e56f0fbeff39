import { PureComponent, Fragment } from "react";
import { message, Tooltip } from "antd";
import { checkFunctionHasPermission } from "@/utils/permission";
import { policyListLang, commonLang } from "@/constants/lang";

class ViewPublicBtn extends PureComponent {
	render() {
		const { viewPublicPolicy, record } = this.props;
		const { hasPublicPolicy } = record;
		return (
			<Fragment>
				{
					hasPublicPolicy
						? checkFunctionHasPermission("ZB0101", "EffectPublicPolicy")
							// lang:公共策略
							? <Tooltip title={policyListLang.tooltip("publicPolicy")}>
								<a onClick={(e) => {
									e.stopPropagation();
									viewPublicPolicy(record);
								}}>
									<i className="salaxy-iconfont salaxy-public_policy"></i>
								</a>
							</Tooltip>
							: <a className="a-disabled" onClick={(e) => {
								// lang:无权限操作
								e.stopPropagation();
								message.warning(commonLang.messageInfo("noPermission"));
							}}>
								<i className="salaxy-iconfont salaxy-public_policy icon-disabled"></i>
							</a>
						: null
				}
			</Fragment>
		);
	}
}

export default ViewPublicBtn;

