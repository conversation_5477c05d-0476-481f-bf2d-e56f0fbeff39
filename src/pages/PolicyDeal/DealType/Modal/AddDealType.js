import { PureComponent } from "react";
import { connect } from "dva";
import { Modal, Input, Form, Row, Col, message } from "antd";
import { policyDealAPI } from "@/services";
import { CommonConstants } from "@/constants";
import { dealTypeLang } from "@/constants/lang";
import ColorPick from "../ColorPick";

class AddDeal extends PureComponent {
	constructor(props) {
		super(props);
		this.changeDialogDataHandle = this.changeDialogDataHandle.bind(this);
	}

	changeDialogDataHandle(field, type, e) {
		let { dispatch, policyDealStore } = this.props;
		let { addDealTypeData } = policyDealStore.dialogData;
		let value = "";
		if (type === "input") {
			value = e.target.value;
		} else if (type === "select") {
			value = e;
		} else if (type === "color") {
			value = e.hex;
		}
		addDealTypeData[field] = value;
		dispatch({
			type: "policyDeal/setDialogData",
			payload: {
				addDealTypeData: addDealTypeData
			}
		});
	}

    addDealType = () => {
    	let { dispatch, policyDealStore } = this.props;
    	let { addDealTypeData } = policyDealStore.dialogData;
    	let { dealType } = policyDealStore;
    	let { curPage, pageSize } = dealType;
    	const reg = /^[1-9]\d*$/;
    	if (!addDealTypeData.dealName) {
    		// lang:风险决策名称不能为空
    		message.warning(dealTypeLang.modal("nameCannotEmpty"));
    		return;
    	}
    	if (addDealTypeData.dealName.length > 24) {
    		// lang:风险决策名称长度不能超过24个字符
    		message.warning(dealTypeLang.modal("nameLengthTip"));
    		return;
    	}
    	if (!addDealTypeData.dealType) {
    		// lang:风险决策标识不能为空
    		message.warning(dealTypeLang.modal("identifierCannotEmpty"));
    		return;
    	}
    	if (addDealTypeData.dealType.length > 32) {
    		// lang:风险决策标识长度不能超过32个字符
    		message.warning(dealTypeLang.modal("identifierLengthTip"));
    		return;
    	}
    	if (!addDealTypeData.grade) {
    		// lang:风险决策等级不能为空
    		message.warning(dealTypeLang.modal("levelCannotEmpty"));
    		return;
    	}
    	if (!reg.test(addDealTypeData.grade)) {
    		// lang:风险决策等级只能输入正整数
    		message.warning(dealTypeLang.modal("intType"));
    		return;
    	}
    	if (!addDealTypeData.color) {
    		// lang:颜色值不能为空
    		message.warning(dealTypeLang.modal("colorCannotEmpty"));
    		return;
    	}

    	policyDealAPI.addDealType(addDealTypeData).then(res => {
    		if (res.success) {
    			dispatch({
    				type: "policyDeal/setDialogShow",
    				payload: {
    					addDealType: false
    				}
    			});
    			// lang:新增风险决策成功
    			message.success(dealTypeLang.modal("addSuccessTip"));
    			dispatch({
    				type: "policyDeal/getDealTypes",
    				payload: {
    					curPage: curPage,
    					pageSize: pageSize
    				}
    			});
    			dispatch({
    				type: "global/getAllMap"
    			});
    			// 关闭弹窗
    			this.closeModal();
    		} else {
    			console.log(res.message);
    			message.error(res.message);
    		}
    	}).catch(err => {
    		console.log(err);
    	});
    }

    closeModal = () => {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "policyDeal/setDialogShow",
    		payload: {
    			addDealType: false
    		}
    	});
    	dispatch({
    		type: "policyDeal/setDialogData",
    		payload: {
    			addDealTypeData: {
    				dealType: null,
    				dealName: null,
    				grade: null
    			}
    		}
    	});
    }
    render() {
    	let { policyDealStore } = this.props;
    	let { dialogShow, dialogData } = policyDealStore;
    	let { addDealTypeData } = dialogData;
    	return (
    		<Modal
    			title={dealTypeLang.modal("addDealType")} // lang:新增风险决策
    			visible={dialogShow.addDealType}
    			maskClosable={false}
    			onOk={this.addDealType}
    			onCancel={this.closeModal}
    		>
    			<Form className="modal-form">
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={6} className="basic-info-title">
    						{/* lang:风险决策名称：*/}
    						{dealTypeLang.modal("name")}
    					</Col>
    					<Col span={18}>
    						<Input
    							value={addDealTypeData.dealName || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "dealName", "input")}
    							placeholder={dealTypeLang.modal("namePlaceholder")} // lang:请输入风险决策名称
    						/>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={6} className="basic-info-title">
    						{/* lang:风险决策标识： */}
    						{dealTypeLang.modal("identifier")}
    					</Col>
    					<Col span={18}>
    						<Input
    							value={addDealTypeData.dealType || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "dealType", "input")}
    							placeholder={dealTypeLang.modal("identifierPlaceholder")} // lang:请输入风险决策标识
    						/>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={6} className="basic-info-title">
    						{/* lang:风险决策等级： */}
    						{dealTypeLang.modal("level")}
    					</Col>
    					<Col span={18}>
    						<Input
    							value={addDealTypeData.grade || undefined}
    							onChange={this.changeDialogDataHandle.bind(this, "grade", "input")}
    							placeholder={dealTypeLang.modal("levelPlaceholder")} // lang:请输入风险决策等级
    						/>
    						<span className="tip-text">
    							{/* lang:注：风险决策等级数字越大等级越高 */}
    							{dealTypeLang.modal("levelTip")}
    						</span>
    					</Col>
    				</Row>
    				<Row gutter={CommonConstants.gutterSpan}>
    					<Col span={6} className="basic-info-title">
    						{/* lang:颜色值 */}
    						{dealTypeLang.modal("color")}
    					</Col>
    					<Col span={18}>
    						{
    							dialogShow.addDealType &&
								<ColorPick
									changeDialogDataHandle={this.changeDialogDataHandle.bind(this)}
									color={addDealTypeData.color}
								/>
    						}
    					</Col>
    				</Row>
    			</Form>
    		</Modal>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(AddDeal);
