import React, { PureComponent } from "react";
import { connect } from "dva";
import { message } from "antd";
import RuleList from "./RuleList";

class DragRule extends PureComponent {

    state = {
    	items: [1, 2, 3, 4, 5, 6]
    };

    constructor(props) {
    	super(props);
    }

    render() {
    	return (
    		<RuleList
    			items={this.state.items}
    			onChange={(items) => {
    				this.setState({ items });
    			}}
    		>
    		</RuleList>
    	);
    }
}

export default connect(state => ({
	policyEditorStore: state.policyEditor
}))(DragRule);
