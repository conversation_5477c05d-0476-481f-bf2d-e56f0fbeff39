import { PureComponent, Fragment } from "react";
import { Collapse, <PERSON>, Icon, But<PERSON> } from "antd";
import ImportPanelItem from "./ImportPanelItem";
import HandleResult from "./HandleResult";
import { imExportModeLang } from "@/constants/lang";
import { ImportConstants } from "@/constants";
import "./index.less";

const { showNumType } = ImportConstants;
const { Panel } = Collapse;
// 一部分title前端写死，infoTitle因为后面场景为动态，所以由后端返回参数拼接
const exportResNameMap = () => ({
	systemField: imExportModeLang.importInfo("systemField"),
	offlineZb: imExportModeLang.importInfo("offlineZb"),
	nameList: imExportModeLang.importInfo("nameList"),
	multiNameList: imExportModeLang.importInfo("multiNameList"),
	thirdName: imExportModeLang.importInfo("thirdName"),
	holmesName: imExportModeLang.importInfo("holmesName"),
	zbSceneMiss: imExportModeLang.importInfo("zbSceneMiss"),
	zbSceneAllMiss: imExportModeLang.importInfo("zbSceneAllMiss"),
	appMissInfo: imExportModeLang.importInfo("appMissInfo"),
	eventMissInfo: imExportModeLang.importInfo("eventMissInfo")
});

const exceptionNameMap = () => ({
	eventTypeUnMatch: imExportModeLang.importInfo("eventTypeUnMatch"),
	policyModeUnMatch: imExportModeLang.importInfo("policyModeUnMatch")
});

class FileExportResult extends PureComponent {
	constructor(props) {
		super(props);
	}
	exportErrDetail = ({ missField, exceptionField, result }) => {
		const { infoTitle } = this.props;
		const exportResNameMapObj = {...exportResNameMap(), ...infoTitle};
		const timestamp = Date.parse(new Date());
		const fileName = imExportModeLang.importInfo("importDetail") + "_" + timestamp;
		const fileType = "csv";
		var dataType = "\uFEFF"; // 解决乱码问题

		// 处理结果
		const { policyResults = [], ruleResults = [], zbResults = [] } = result;
		if (policyResults && policyResults.length > 0) {
			dataType += (["" + imExportModeLang.importInfo("policyNo"), imExportModeLang.importInfo("policyName"), imExportModeLang.importInfo("mode")].join(",")); // 添加表格的头
			dataType += "\n";
			policyResults.forEach((item) => {
				dataType += (["" + item.id, item.name, item.msg].join(","));
				dataType += "\n";
			});
			dataType += "\n\n";
		}
		if (ruleResults && ruleResults.length > 0) {
			dataType += (["" + imExportModeLang.importInfo("ruleNo"), imExportModeLang.importInfo("ruleName"), imExportModeLang.importInfo("mode")].join(",")); // 添加表格的头
			dataType += "\n";
			ruleResults.forEach((item) => {
				dataType += (["" + item.id, item.name, item.msg].join(","));
				dataType += "\n";
			});
			dataType += "\n\n";
		}
		if (zbResults && zbResults.length > 0) {
			dataType += (["" + imExportModeLang.importInfo("zbNo"), imExportModeLang.importInfo("zbName"), imExportModeLang.importInfo("mode")].join(",")); // 添加表格的头
			dataType += "\n";
			zbResults.forEach((item) => {
				dataType += (["" + item.id, item.name, item.msg].join(","));
				dataType += "\n";
			});
			dataType += "\n\n";
		}

		// 异常项
		let exceptFieldLen = 0;
		Object.keys(exceptionField).forEach((v) => {
			if (exceptionField[v] && exceptionField[v].length > 0) {
				exceptFieldLen += exceptionField[v].length;
			}
		});
		if (exceptFieldLen > 0) {
			dataType += (["" + imExportModeLang.importInfo("exceptType"), imExportModeLang.importInfo("exportScene"), imExportModeLang.importInfo("importScene")].join(",")); // 添加表格的头
			dataType += "\n";
			Object.keys(exceptionField).forEach((v) => {
				const details = exceptionField[v];
				details.forEach((item) => {
					dataType += ([
						"" + exceptionNameMap()[v],
						item.exportScene,
						item.importScene
					].join(","));
					dataType += "\n";
				});
			});
			dataType += "\n\n";
		}

		// 缺失字段内容
		// 非应用与策略集场景
		let missNormalFieldLen = 0;
		Object.keys(missField).forEach((v) => {
			if (v !== "appMissInfo" && v !== "eventMissInfo") {
				if (missField[v] && missField[v].length > 0) {
					missNormalFieldLen += missField[v].length;
				}
			}
		});
		if (missNormalFieldLen) {
			dataType += (["" + imExportModeLang.importInfo("missType"), imExportModeLang.importInfo("misId"), imExportModeLang.importInfo("misName"), imExportModeLang.importInfo("misVersion")].join(",")); // 添加表格的头
			dataType += "\n";
			Object.keys(missField).forEach((v) => {
				if (v !== "appMissInfo" && v !== "eventMissInfo") {
					const details = missField[v];
					details.forEach((item) => {
						dataType += (["" + exportResNameMapObj[v], item.name, item.dName, item.version].join(","));
						dataType += "\n";
					});
				}
			});
			dataType += "\n\n";
		}
		// 缺应用
		if (missField["appMissInfo"] && missField["appMissInfo"].length > 0) {
			dataType += (["" + imExportModeLang.importInfo("exceptType"), imExportModeLang.importInfo("app"), imExportModeLang.importInfo("appName")].join(",")); // 添加表格的头
			dataType += "\n";
			missField["appMissInfo"].forEach((item) => {
				dataType += (["" + exportResNameMapObj["appMissInfo"], item.app, item.appName].join(","));
				dataType += "\n";
			});
			dataType += "\n\n";
		}
		// 缺策略
		if (missField["eventMissInfo"] && missField["eventMissInfo"].length > 0) {
			dataType += (["" + imExportModeLang.importInfo("exceptType"), imExportModeLang.importInfo("app"), imExportModeLang.importInfo("appName"), imExportModeLang.importInfo("eventId"), imExportModeLang.importInfo("policySetName")].join(",")); // 添加表格的头
			dataType += "\n";
			missField["eventMissInfo"].forEach((item) => {
				dataType += (["" + exportResNameMapObj["eventMissInfo"], item.app, item.appName, item.eventId, item.policySetName].join(","));
				dataType += "\n";
			});
			dataType += "\n\n";
		}
		var csvData = new Blob([dataType], {
			type: "text/csv"
		});
		const url = URL.createObjectURL(csvData);
		let a = document.createElement("a");
		a.download = `${fileName}.${fileType || "pdf"}`;
		a.href = url;
		document.body.appendChild(a);
		a.click();
		a.remove();
	}

	panelDomRender = (nameMap, title) => {
		const { successData} = this.props;
		// totalItems当前项目下总共有多少条记录 activeKey默认展开
		let [totalItems, activeKey, fieldList, missField] = [0, [], [], {}];
		successData && Object.keys(successData).forEach((v) => {
			if (nameMap[v]) {
				const fieldLen = successData[v] && successData[v].length;
				totalItems += Number(fieldLen);
				if (fieldLen > 0) {
					activeKey.push(v);
				}
				missField[v] = successData[v];
				fieldList.push(v);
			}
		});
		if (totalItems > 10) {
			activeKey = activeKey[0];
		}
		return {
			totalItems,
			activeKey,
			fieldList,
			missField,
			title
		};
	}
	render() {
		const { successData, importType = {}, checkFileResult, infoTitle } = this.props;

		const exportResNameMapObj = {...exportResNameMap(), ...infoTitle};
		// 缺失项+异常项
		const exportTotalNameMap = {...exportResNameMapObj, ...exceptionNameMap()};
		const dataList = [];
		const disMissContent = this.panelDomRender(exportResNameMapObj, imExportModeLang.importInfo("missField"));
		const exceptionContent = this.panelDomRender(exceptionNameMap(), imExportModeLang.importInfo("exceptionField"));
		if (disMissContent.totalItems > 0) {
			dataList.push(disMissContent);
		}
		if (exceptionContent.totalItems > 0) {
			dataList.push(exceptionContent);
		}

		const { policyResults = [], ruleResults = [], zbResults = [] } = successData;
		const resultLen = policyResults.length + ruleResults.length + zbResults.length;
		const showMode = showNumType[importType]; // 获取显示导入数目
		return (
			<Fragment>
				{
					!checkFileResult &&
					<Row className="total-import-data" type="flex" justify="center" align="middle">
						<Icon type="check-circle" />
						<div>
							{/* 共导入 */}
							{imExportModeLang.importInfo("totalImport")}
							{/* 个策略 */}
							{
								showMode.policyMode &&
								<Fragment>
									<b>{successData.policyCount || 0}</b>{imExportModeLang.importInfo("policySet")}
									{
										(showMode.zbMode || showMode.ruleMode) ? "，" : ""
									}
								</Fragment>
							}
							{/* 个规则 */}
							{
								showMode.ruleMode &&
								<Fragment>
									<b>{successData.ruleCount || 0}</b>{imExportModeLang.importInfo("rules")}
									{
										showMode.zbMode ? "，" : ""
									}
								</Fragment>
							}
							{/* 个指标 */}
							{
								showMode.zbMode &&
								<Fragment>
									<b>{successData.zbCount || 0}</b>{imExportModeLang.importInfo("indicator")}
								</Fragment>
							}
						</div>
					</Row>
				}
				{
					(resultLen > 0 || dataList.length > 0) &&
					<Fragment>
						<div className="down-csv">
							<Button
								icon="download"
								size="small"
								onClick={this.exportErrDetail.bind(this, {
									missField: disMissContent.missField,
									exceptionField: exceptionContent.missField,
									result: {
										policyResults, ruleResults, zbResults
									}
								})}
							>
								{/* 下载导入明细  */}
								{imExportModeLang.importInfo("importDetail")}
							</Button>
						</div>
						{
							resultLen > 0 &&
							<div className="mb10">
								{/* 处理结果 */}
								<h4 className="result-title">{imExportModeLang.importInfo("resultTitle")}</h4>
								<HandleResult
									policyResults={policyResults}
									ruleResults={ruleResults}
									zbResults={zbResults}
								/>
							</div>
						}
						{
							dataList &&
							dataList.length > 0 &&
							dataList.map(v=>{
								if (v.totalItems > 0) {
									return (
										<div style={{"marginBottom": "5px"}}>
											<h4 className="result-title">{v.title}</h4>
											<Collapse defaultActiveKey={v.activeKey} size="small" className="policy-import-collapse">
												{
													v.fieldList &&
													v.fieldList.length > 0 &&
													v.fieldList.map((fieldKey) => {
														const fieldLen = successData[fieldKey] && successData[fieldKey].length;
														if (fieldLen > 0) {
															return (
																<Panel
																	header={exportTotalNameMap[fieldKey]}
																	key={fieldKey}
																	extra={
																		<span className="filed-num">
																			{/* 共<label>{fieldLen}</label>条 */}
																			{imExportModeLang.importInfo("eachTotal")(fieldLen)}
																		</span>
																	}>
																	<ImportPanelItem data={successData[fieldKey]} />
																</Panel>
															);
														}
													})
												}
											</Collapse>
										</div>
									);
								}
							})
						}
					</Fragment>
				}
			</Fragment>
		);
	}
}

export default FileExportResult;
