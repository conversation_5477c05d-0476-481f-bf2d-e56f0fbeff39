import { message } from "antd";
import { policyDealAPI } from "@/services";

export default {
	namespace: "policyDeal",
	state: {
		trigger: {
			triggerList: [],
			total: 0,
			pages: 0,
			searchParams: {
				manageType: "untrigger",
				dealType: null,
				controlType: null,
				curPage: 1,
				pageSize: 10
			}
		},
		dealType: {
			dealTypesList: [],
			curPage: 1,
			pageSize: 10,
			total: 0,
			pages: 0
		},
		dialogShow: {
			addDealType: false,
			modifyDealType: false,
			addTriggerBoot: false,
			modifyTriggerByTime: false,
			modifyTriggerByField: false,
			modifyImmuno: false,
			modifyTimeEvent: false,
			quickAddTrigger: false
		},
		dialogData: {
			addTriggerBoot: {
				manageType: null,
				dealType: null,
				controlType: null
			},
			modifyTriggerFieldData: {
				matchField: null,
				matchValue: null,
				dealType: null,
				isUpdate: false
			},
			modifyTriggerTimeData: {
				isUpdate: false,
				dealType: null,
				quickType: null,
				calendarValue: null,
				calendarDataList: []
			},
			modifyImmuno: {
				eventId: null,
				immunoField: null,
				immunoTime: null,
				isUpdate: false,
				app: "", // app应用
				description: "", // 描述
				// 执行条件
				immunoConditions: {
					logicOperator: "&&",
					priority: "1",
					defaultNode: false,
					children: []
				}
			},
			addDealTypeData: {
				dealType: null,
				dealName: null,
				grade: null
			},
			modifyDealTypeData: {
				uuid: null,
				dealType: null,
				dealName: null,
				grade: null
			},
			modifyTimeEvent: {
				startDay: null,
				startHour: null,
				endHour: null,
				id: null,
				all: null,
				isUpdate: false
			},
			quickAddTrigger: {
				startHour: null,
				endHour: null,		// 每日
				startWeek: null,
				endWeek: null,		// 每周
				startDay: null,
				endDay: null,		// 每月
				quickType: null,	// 快捷设置类型
				timeSlot: false		// 时间片
			}
		}
	},
	effects: {
		*getTriggers({ payload }, { call, put }) {
			let { manageType } = payload;
			let response;
			if (manageType === "untrigger") {
				response = yield call(policyDealAPI.getTriggers, payload);
			} else {
				response = yield call(policyDealAPI.getImmunos, payload);
			}
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initTriggers",
				payload: response
			});
		},
		*getDealTypes({ payload }, { call, put }) {
			let response = yield call(policyDealAPI.getDealTypes, payload);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "initDealTypes",
				payload: response
			});
		},
		*loadCalendarData({ payload }, { call, put }) {
			let params = {
				dealType: payload.dealType,
				start: payload.calendarValue.startOf("month").valueOf(),
				end: payload.calendarValue.endOf("month").valueOf() + 1
			};

			let response = yield call(policyDealAPI.getCalendarDetail, params);
			if (!response) {
				return;
			}
			if (!response.success) {
				message.error(response.message);
				return;
			}
			yield put({
				type: "setTimeEvents",
				payload: response.data ? JSON.parse(response.data) : []
			});
		}
	},
	reducers: {
		setAttrValue(state, action) {
			let { payload } = action;
			for (let key in state) {
				if (payload[key] !== undefined) {
					state[key] = payload[key];
				}
			}

			return {
				...state
			};
		},
		setDialogShow(state, action) {
			let { dialogShow } = state;
			let { payload } = action;
			for (let key in dialogShow) {
				if (payload[key] !== undefined) {
					dialogShow[key] = payload[key];
				}
			}

			return {
				...state,
				dialogShow: dialogShow
			};
		},
		setDialogData(state, action) {
			let { dialogData } = state;
			let { payload } = action;
			for (let key in dialogData) {
				if (payload[key] !== undefined || payload[key] === null) {
					dialogData[key] = payload[key];
				}
			}

			return {
				...state,
				dialogData: dialogData
			};
		},
		setTimeEvents(state, action) {
			let { dialogData } = state;
			let { modifyTriggerTimeData } = dialogData;
			let { payload } = action;
			modifyTriggerTimeData["calendarDataList"] = payload;

			return {
				...state,
				dialogData: dialogData
			};
		},
		setTrigger(state, action) {
			let { trigger } = state;
			let { payload } = action;
			for (let key in trigger) {
				if (payload[key] !== undefined || payload[key] === null) {
					trigger[key] = payload[key];
				}
			}

			return {
				...state,
				trigger: trigger
			};
		},
		initTriggers(state, action) {
			let { trigger } = state;
			let data = action.payload.data || null;
			let list = data["dataList"] || [];
			trigger.searchParams.curPage = data.curPage;
			trigger.searchParams.pageSize = data.pageSize;
			trigger.total = data.total;
			trigger.pages = data.pages;
			trigger.triggerList = list || [];

			return {
				...state,
				trigger: trigger
			};
		},
		initDealTypes(state, action) {
			let { dealType } = state;
			let data = action.payload.data || null;
			let list = data["dataList"] || [];
			if (list.length) {
				dealType.curPage = data.curPage;
				dealType.pageSize = data.pageSize;
				dealType.total = data.total;
				dealType.pages = data.pages;
			}
			dealType.dealTypesList = list || [];

			return {
				...state,
				dealType: dealType
			};
		}
	}
};

