import { PureComponent } from "react";
import { connect } from "dva";
import { indexEditorAPI } from "@/services";
import { Form, Button, message } from "antd";
import moment from "moment";
import IndexMore from "./IndexMore";
import IndexBase from "./IndexBase";
import IndexCondition from "./IndexCondition";
import { checkFunctionHasPermission } from "@/utils/permission";
import { cloneDeep } from "lodash";
import { indexListLang, commonLang } from "@/constants/lang";
import { isJSON } from "@/utils/isJSON";
import IndexVersionSubmitModal from "../Modal/IndexVersionSubmitModal";

class IndexConfig extends PureComponent {
	state = {
		submitIndexLoad: false
	};

	constructor(props) {
		super(props);
		this.addSalaxy = this.addSalaxy.bind(this);
		this.modifySalaxy = this.modifySalaxy.bind(this);
		this.indexVersionSubmitHandle = this.indexVersionSubmitHandle.bind(this);
	}

	// 函数校验左右两边值是否一样
	testIsEqualLeftRight = (item) => {
		let access = true;
		if (item.rightVarType === "context") {
			if (item.leftVar === item.rightVar) {
				access = false;
			}
		}
		return access;
	}
	// 提交校验
	testHandlePost = () => {
		let { indexEditorStore } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		const { name, attachFieldsObj = {} } = currentIndexObj || {};
		const { focusType, focusUnit, ranges, threshold } = attachFieldsObj || {};
		if (!name) {
			// 请输入指标名称
			message.error(indexListLang.ruleAttr("inputIndexName"));
			return false;
		}
		if (name && name.length > 80) {
			// 指标名称长度不大于80
			message.error(indexListLang.ruleAttr("indexNameMaxLen"));
			return false;
		}

		// 判断空时间段 当特征度／集中度天数 中的时间类型为 「指定」 时进行以下逻辑判断
		const needCheckTime = ["FEATURE_RANGE", "TIME_RANGE"].indexOf(focusType) > -1;
		if (needCheckTime) {
			if (ranges && ranges.length > 0) {
				// 是否有至少一条时间记录
				let hasTime = false;
				ranges.map(v => {
					if (v.from && v.to && v.from !== "Invalid date" && v.to !== "Invalid date" && !hasTime) {
						hasTime = true;
					};
				});
				if (!hasTime) {
					// 输入时间段区间
					message.error(indexListLang.ruleAttr("periodWarn"));
					return false;
				}

				// 判断是否只输入了其中一个时间
				let hasOneTime = false;
				ranges.map(v => {
					if ((v.from || v.to) && !(v.from && v.to && v.from !== "Invalid date" && v.to !== "Invalid date") && !hasOneTime) {
						hasOneTime = true;
					}
				});
				if (hasOneTime) {
					// 输入时间段区间
					message.error(indexListLang.ruleAttr("periodWarn"));
					return false;
				}

				// 时间格式判断时间段是否正确  to>from
				if (focusUnit === "DAYTIME" || !focusUnit) {
					let pass = true;
					ranges.map(v => {
						if (moment(v.to, "HH:mm:ss").diff(moment(v.from, "HH:mm:ss"), "seconds") < 0 && pass) {
							pass = false;
						};
					});
					if (!pass) {
						// 请输入正确的时间区间
						message.error(indexListLang.ruleAttr("periodWarn1"));
						return false;
					}
				}

				// 针对日期、星期等的时间区间判断 to>from
				if (focusUnit !== "DAYTIME") {
					let pass = true;
					ranges.map(v => {
						if (v.to - v.from < 0 && pass) {
							pass = false;
						};
					});
					if (!pass) {
						// 请输入正确的时间区间
						message.error(indexListLang.ruleAttr("periodWarn1"));
						return false;
					}
				}
			} else {
				// 输入时间段区间
				message.error(indexListLang.ruleAttr("periodWarn"));
				return false;
			}
		}
		if (threshold && threshold > 100) {
			// 集中度阀值小于100
			message.error(indexListLang.ruleAttr("thresholdLess100"));
			return false;
		}
		if (threshold && threshold < 0) {
			// 集中度阀值大于0
			message.error(indexListLang.ruleAttr("thresholdUp0"));
			return false;
		}

		// 从属性值校验
		const { slaveDimsForWeb } = currentIndexObj || {};
		let slaveSuccess = true;
		let groupSlaveSuccess = true;
		let newSlaveParams = [];
		slaveDimsForWeb &&
			slaveDimsForWeb.length > 0 &&
			slaveDimsForWeb.map((v) => {
				if (v.slaveField === v.slaveValueField && slaveSuccess) {
					slaveSuccess = false;
				}
				if (groupSlaveSuccess) {
					let slaveValueFieldTemp = v.slaveField;
					if (newSlaveParams.indexOf(slaveValueFieldTemp) > -1) {
						groupSlaveSuccess = false;
					}
					newSlaveParams.push(slaveValueFieldTemp);
				}

			});
		if (!slaveSuccess) {
			message.error(indexListLang.ruleAttr("slaveWarn"));
			return false;
		}

		if (!groupSlaveSuccess) {
			message.error(indexListLang.ruleAttr("slaveGroupWarn"));
			return false;
		}
		// 判断过滤条件 指标变量过滤条件右边字段不能与左边字段相同
		const { filterList } = currentIndexObj || {};
		let access = true; // 默认通过过滤条件判断
		filterList &&
			filterList.children &&
			filterList.children.length > 0 &&
			filterList.children.map((v) => {
				const { children } = v;
				if (access) {
					access = this.testIsEqualLeftRight(v);
				}
				if (children && children.length > 0) {
					children.map((child) => {
						if (access) {
							access = this.testIsEqualLeftRight(child);
						}
					});
				}
			});
		if (!access) {
			message.error(indexListLang.ruleAttr("filterWarn"));
			return false;
		}

		return true;
	}

	addSalaxy() {
		let { indexEditorStore, dispatch } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		currentIndexObj["filterFieldInfoList"] = JSON.stringify(currentIndexObj["filterList"], null, 4);
		currentIndexObj["attachFields"] = JSON.stringify(currentIndexObj["attachFieldsObj"], null, 4);

		const testHandleRes = this.testHandlePost();
		if (!testHandleRes) {
			return;
		}

		// 移除空的场景项
		let newSceneList = [];
		if (currentIndexObj.sceneType === "EVENT") {
			currentIndexObj["sceneList"] &&
				currentIndexObj["sceneList"].length > 0 &&
				currentIndexObj["sceneList"].forEach(scene => {
					if (scene.eventList && scene.eventList.length > 0) {
						newSceneList.push(scene);
					}
				});
		} else {
			newSceneList = currentIndexObj.sceneList || [];
		}
		currentIndexObj["scene"] = JSON.stringify(newSceneList, null, 4);
		let newCurrentIndexObj = cloneDeep(currentIndexObj);
		// 对值为数组格式的进行stringify
		Object.keys(newCurrentIndexObj).forEach((key) => {
			if (Array.isArray(newCurrentIndexObj[key])) {
				newCurrentIndexObj[key] = JSON.stringify(newCurrentIndexObj[key], null, 4);
			}
		});

		this.setState({
			submitIndexLoad: true
		}, () => {
			indexEditorAPI.addSalaxy(newCurrentIndexObj).then(res => {
				this.setState({
					submitIndexLoad: false
				});
				if (res.success) {
					let data = res.data;
					// lang:新增指标成功
					message.success(indexListLang.ruleConfig("indexAddedSuccessTip"));
					currentIndexObj["hasModify"] = false;
					currentIndexObj["unSave"] = false;
					data["needRefresh"] = true;
					data["filterList"] = data["filterStr"] && isJSON(data["filterStr"]) ? JSON.parse(data["filterStr"]) : [];
					data["attachFieldsObj"] = data["attachFields"] && isJSON(data["attachFields"]) ? JSON.parse(data["attachFields"]) : {};
					data["sceneList"] = data["scene"] && isJSON(data["scene"]) ? JSON.parse(data["scene"]) : [];

					editIndexMap[data.id] = data;

					dispatch({
						type: "indexEditor/setAttrValue",
						payload: {
							indexActiveKey: [data.id.toString()],
							currentIndexId: data.id.toString()
						}
					});

					delete editIndexMap[currentIndexId];
					dispatch({
						type: "indexEditor/setAttrValue",
						payload: {
							editIndexMap: editIndexMap
						}
					});
					dispatch({
						type: "indexEditor/changeIndexStatus",
						payload: {
							id: data.id.toString()
						}
					});
					dispatch({
						type: "global/getAllMap",
						payload: {}
					});
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
				this.setState({
					submitIndexLoad: false
				});
			});
		});

	}

	modifySalaxy() {
		let { indexEditorStore, dispatch } = this.props;
		let { currentIndexId, editIndexMap } = indexEditorStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;
		currentIndexObj["filterFieldInfoList"] = JSON.stringify(currentIndexObj["filterList"], null, 4);
		currentIndexObj["attachFields"] = JSON.stringify(currentIndexObj["attachFieldsObj"], null, 4);

		// 提交前校验
		const testHandleRes = this.testHandlePost();
		if (!testHandleRes) {
			return;
		}

		// 移除空的场景项
		let newSceneList = [];
		if (currentIndexObj.sceneType === "EVENT") {
			currentIndexObj["sceneList"] &&
				currentIndexObj["sceneList"].length > 0 &&
				currentIndexObj["sceneList"].forEach(scene => {
					if (scene.eventList && scene.eventList.length > 0) {
						newSceneList.push(scene);
					}
				});
		} else {
			newSceneList = currentIndexObj.sceneList;
		}

		currentIndexObj["scene"] = JSON.stringify(newSceneList, null, 4);

		let newCurrentIndexObj = cloneDeep(currentIndexObj);
		// 对值为数组格式的进行stringify
		Object.keys(newCurrentIndexObj).forEach((key) => {
			if (Array.isArray(newCurrentIndexObj[key])) {
				newCurrentIndexObj[key] = JSON.stringify(newCurrentIndexObj[key], null, 4);
			}
		});
		this.setState({
			submitIndexLoad: true
		}, () => {
			indexEditorAPI.modifySalaxy(newCurrentIndexObj).then(res => {
				this.setState({
					submitIndexLoad: false
				});
				if (res.success) {
					// lang:修改指标成功
					message.success(indexListLang.ruleConfig("indexUpdateSuccessTip"));
					currentIndexObj["hasModify"] = false;
					currentIndexObj["unSave"] = false;
					dispatch({
						type: "indexEditor/setAttrValue",
						payload: {
							editIndexMap: editIndexMap
						}
					});
					dispatch({
						type: "indexEditor/changeIndexStatus",
						payload: {
							id: currentIndexId
						}
					});
					dispatch({
						type: "global/getAllMap",
						payload: {}
					});
				} else {
					message.error(res.message);
				}
			}).catch(err => {
				console.log(err);
				this.setState({
					submitIndexLoad: false
				});
			});
		});
	}

	indexVersionSubmitHandle(id, currentIndexObj) {
		let { indexEditorStore, dispatch } = this.props;
		let { dialogData } = indexEditorStore;
		let { indexVersionSubmitData } = dialogData;
		indexVersionSubmitData["id"] = id;

		// 移除空的场景项
		let newSceneList = [];
		if (currentIndexObj) {
			if (currentIndexObj.sceneType === "EVENT") {
				currentIndexObj["sceneList"] &&
					currentIndexObj["sceneList"].length > 0 &&
					currentIndexObj["sceneList"].forEach(scene => {
						if (scene.eventList && scene.eventList.length > 0) {
							newSceneList = newSceneList.concat(scene.eventList);
						}
					});
			} else {
				newSceneList = currentIndexObj.sceneList || [];
			}
		}
		console.log(newSceneList);
		if (!(newSceneList && newSceneList.length > 0)) {
			message.error(indexListLang.ruleAttr("selectScene")); // 请选择场景
			return;
		}

		dispatch({
			type: "indexEditor/setDialogShow",
			payload: {
				indexVersionSubmit: true
			}
		});

		// 离线计算判断
		indexEditorAPI.judgeNeedOffline({ id }).then(res => {
			if (res.success) {
				indexVersionSubmitData["needOffline"] = res.data.need;
				indexVersionSubmitData["needOfflineMessage"] = res.message;

				dispatch({
					type: "indexEditor/setDialogData",
					payload: {
						indexVersionSubmitData: indexVersionSubmitData
					}
				});
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			console.log(err);
		});
	}

	render() {
		let { indexEditorStore, templateName, dispatch } = this.props;
		let { editIndexMap, currentIndexId } = indexEditorStore;
		let currentIndexObj = editIndexMap[currentIndexId] ? editIndexMap[currentIndexId] : null;

		return (
			<div className="rule-config-wrap">
				<Form>
					<div className="rule-detail rule-attr">
						<h4>
							{/* lang:基本设置 */}
							{indexListLang.ruleConfig("basicSetting")}
						</h4>
						<div className="rule-detail-content">
							<IndexBase templateName={templateName} />
						</div>
					</div>
					<div className="rule-detail rule-condition">
						<h4>
							{/* lang:指标配置 */}
							{indexListLang.ruleConfig("indexConfig")}
						</h4>
						<div className="rule-detail-content">
							<IndexCondition templateName={templateName} />
						</div>
					</div>
					{
						currentIndexObj &&
						<div className="rule-detail rule-condition">
							<h4>
								{/* lang:更新配置 */}
								{indexListLang.ruleConfig("updateConfig")}
							</h4>
							<div className="rule-detail-content">
								<IndexMore templateName={templateName} />
							</div>
						</div>
					}
					<div className="rule-btns">
						<Button
							type="default"
							onClick={() => {
								dispatch({
									type: "indexEditor/setAttrValue",
									payload: {
										indexActiveKey: [],
										currentIndexId: null
									}
								});
							}}
						>
							{/* lang:收起 */}
							{indexListLang.ruleConfig("packUp")}
						</Button>
						{
							currentIndexObj &&
							!currentIndexObj.unSave &&
							!currentIndexObj.hasModify &&
							<Button
								onClick={() => {
									if (checkFunctionHasPermission("ZB0102", "editorSubmitVersion")) {
										this.indexVersionSubmitHandle(currentIndexId, currentIndexObj);
									} else {
										// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}}
								disabled={!checkFunctionHasPermission("ZB0102", "editorSubmitVersion")}
							>
								{/* lang:提交版本 */}
								{indexListLang.ruleConfig("submitVersion")}
							</Button>
						}
						<Button
							type="primary"
							loading={this.state.submitIndexLoad}
							onClick={() => {
								if (editIndexMap[currentIndexId] && editIndexMap[currentIndexId]["unSave"]) {
									if (checkFunctionHasPermission("ZB0102", "editorAddIndex")) {
										this.addSalaxy();
									} else {
										// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								} else {
									if (checkFunctionHasPermission("ZB0102", "editorModifyIndex")) {
										this.modifySalaxy();
									} else {
										// lang:无权限操作
										message.warning(commonLang.messageInfo("noPermission"));
									}
								}
							}}
						>
							{/* lang:更新 */}
							{indexListLang.ruleConfig("update")}
						</Button>
					</div>
				</Form>
				<IndexVersionSubmitModal />
			</div>
		);
	}
}

export default connect(state => ({
	indexEditorStore: state.indexEditor,
	templateStore: state.template
}))(IndexConfig);
