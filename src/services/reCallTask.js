import { getHeader, getUrl, deleteEmptyObjItem } from "./common";
import request from "../utils/request";

// 回测任务列表
const getReCallList = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/zb/fillback/list", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 添加指标回溯
const fillbackAdd = async(params) => {
	return request("/admin/zb/fillback/add", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

// 修改指标回溯
const fillbackModify = async(params) => {
	return request("/admin/zb/fillback/modify", {
		method: "POST",
		headers: getHeader(),
		body: { ...params }
	});
};

// 终止回测任务
const terminateReCall = async(params) => {
	params = deleteEmptyObjItem(params);
	return request("/admin/zb/fillback/terminate", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};

// 废弃/生效回测任务
const dealProcessResult = async(params) => {
	params = deleteEmptyObjItem(params);
	return request("/admin/zb/fillback/processResult", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};

// 指标回溯详情
const backDetail = async(params) => {
	params = deleteEmptyObjItem(params);
	return request(getUrl("/admin/zb/fillback/detailByZbUuid", params), {
		method: "GET",
		headers: getHeader()
	});
};

// 查询回溯结果
const queryResult = async(params) => {
	return request("/admin/zb/fillback/queryResult", {
		method: "POST",
		headers: getHeader(),
		body: params
	});
};
export default {
	getReCallList,
	terminateReCall,
	dealProcessResult,
	fillbackAdd,
	fillbackModify,
	backDetail,
	queryResult
};
