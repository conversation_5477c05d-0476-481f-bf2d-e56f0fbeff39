const reCallStatusMap = {
	"PEND_RUN": {
		value: "PEND_RUN",
		dName: "待运行",
		enDName: "Pending",
		color: "blue"
	},
	"RUNNING": {
		value: "RUNNING",
		dName: "运行中",
		enDName: "Running",
		color: "geekblue"
	},
	"TERMINATED": {
		value: "TERMINATED",
		dName: "已终止",
		enDName: "Terminated",
		color: "red"
	},
	"FAILED": {
		value: "FAILED",
		dName: "失败",
		enDName: "Failed",
		color: "volcano"
	},
	"COMPLETED": {
		value: "COMPLETED",
		dName: "完成",
		enDName: "Completed",
		color: "cyan"
	},
	"SUBMITTED": {
		value: "SUBMITTED",
		dName: "已提交使用",
		enDName: "Submitted",
		color: "green"
	}
};

const offlineStatusMap = {
	"1": {
		value: "1",
		dName: "等待运行",
		enDName: "Wait_Run"
	},
	"2": {
		value: "2",
		dName: "等待资源",
		enDName: "Wait_Resource"
	},
	"3": {
		value: "3",
		dName: "提交中",
		enDName: "Submitting"
	},
	"5": {
		value: "5",
		dName: "运行中",
		enDName: "Running"
	},
	"7": {
		value: "7",
		dName: "运行失败",
		enDName: "Run_Fail"
	},
	"9": {
		value: "9",
		dName: "运行成功",
		enDName: "Run_Suc"
	},
	"11": {
		value: "11",
		dName: "暂停",
		enDName: "Pause"
	},
	"13": {
		value: "13",
		dName: "运行终止",
		enDName: "Stop"
	},
	"14": {
		value: "14",
		dName: "任务不存在",
		enDName: "Not_Exists"
	}
};
const resMap = {
	"PEND": {
		value: "PEND",
		dName: "待生效",
		enDName: "Pend",
		color: "#2db7f5"
	},
	"WAIT_REVIEW": {
		value: "WAIT_REVIEW",
		dName: "审核中",
		enDName: "WAIT_REVIEW",
		color: "#f39f46"
	},
	"APPLIED": {
		value: "APPLIED",
		dName: "运用",
		enDName: "Applied",
		color: "#87d068"
	},
	"DISCARD": {
		value: "DISCARD",
		dName: "丢弃",
		enDName: "Discard",
		color: "#f50"
	}
};
export default {
	reCallStatusMap,
	offlineStatusMap,
	resMap
};
