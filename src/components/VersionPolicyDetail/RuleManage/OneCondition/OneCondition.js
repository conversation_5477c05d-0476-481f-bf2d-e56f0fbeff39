import React from "react";
import { connect } from "dva";
import { Input, Select, Row, Col } from "antd";
import { PolicyConstants, CommonConstants } from "@/constants";
import { commonLang, policyDetailLang } from "@/constants/lang";
import TooltipWrapper from "@/components/TooltipWrapper";

const Option = Select.Option;
const InputGroup = Input.Group;

class OneCondition extends React.PureComponent {

	constructor(props) {
		super(props);
	}

	componentWillMount() {
		let { policyDetailStore, globalStore, dispatch, conditionSingleData, ruleIndexArr, conditionArr } = this.props;
		let { allMap } = globalStore;
		let { policyRules } = policyDetailStore;

		// propertyObj的作用是判断选择的系统字段或指标是不是还存在，继而做下一步操作
		let propertyObj = conditionSingleData && conditionSingleData["property"] && allMap && allMap["ruleAndIndexFieldList"] && allMap["ruleAndIndexFieldList"].find(item => item.name === conditionSingleData["property"]);
		if (!propertyObj) {
			let currentLine = null;
			if (ruleIndexArr.length === 1) {
				// 如果不是IF规则
				if (conditionArr.length === 1) {
					currentLine = policyRules[ruleIndexArr[0]]["conditions"]["children"][conditionArr[0]];
				} else if (conditionArr.length === 2) {
					// 如果是条件组
					let groupList = policyRules[ruleIndexArr[0]]["conditions"]["children"];
					currentLine = groupList[conditionArr[0]]["children"][conditionArr[1]];
				}
			} else if (ruleIndexArr.length === 2) {
				// 如果是IF规则
				if (conditionArr.length === 1) {
					currentLine = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"][conditionArr[0]];
				} else if (conditionArr.length === 2) {
					let groupList = policyRules[ruleIndexArr[0]]["children"][ruleIndexArr[1]]["conditions"]["children"];
					currentLine = groupList[conditionArr[0]]["children"][conditionArr[1]];
				}
			}

			currentLine["operator"] = "==";
			currentLine["property"] = null;
			currentLine["type"] = "context";
			currentLine["rightValueType"] = "context";

			dispatch({
				type: "policyDetail/setPolicyRule",
				payload: {
					policyRules: policyRules
				}
			});
		}
	}

	render() {
		let { globalStore, conditionData, conditionSingleData, conditionType, conditionArr } = this.props;
		let { allMap, personalMode } = globalStore;
		let { fieldParamListSelect = [] } = allMap;
		const { lang } = personalMode;
		const operaTypeBlong = conditionSingleData["operator"] === "belong" || conditionSingleData["operator"] === "notbelong";
		const belongNames = fieldParamListSelect.reduce((pre, v) => {
			pre.push(v.name);
			return pre;
		}, []);

		let propertyName = "";
		let enumField = "";
		if (conditionSingleData && conditionSingleData["property"] && allMap && allMap["ruleAndIndexFieldList"]) {
			propertyName = (allMap["ruleAndIndexFieldList"].find(v=>{
				return v.name === conditionSingleData["property"];
			}) || {});
			propertyName = `[${commonLang.sourceName(propertyName.sourceName)}]${propertyName.dName}`;

			// 枚举值
			enumField = (
				(allMap["ruleAndIndexFieldList"].find(v=>(
					v.name === conditionSingleData.property
				)) || {}).enumField
			) || conditionSingleData.property;
		}
		return (
			<div className={conditionType === "group" ? "group-item" : ""}>
				<Row gutter={CommonConstants.gutterSpan}>
					{
						conditionType === "group" && conditionArr[1] === 0 &&
                        <Col span={2} offset={2} className="basic-info-title">
                        	<Select
                        		value={conditionData.logicOperator || undefined}
                        		disabled={true}
                        	>
                        		<Option value="&&">
                        			{/* lang:与 */}
                        			{policyDetailLang.oneCondition("and")}
                        		</Option>
                        		<Option value="||">
                        			{/* lang:或 */}
                        			{policyDetailLang.oneCondition("or")}
                        		</Option>
                        	</Select>
                        </Col>
					}
					{
						conditionType === "group" && conditionArr[1] !== 0 &&
                        <Col span={4} className="basic-info-title"></Col>
					}
					{
						conditionType === "single" &&
                        <Col span={4} className="basic-info-title"></Col>
					}
					<Col span={5}>
						<TooltipWrapper>
							<Select
								value={conditionSingleData && conditionSingleData["property"] ? conditionSingleData["property"] : undefined}
								placeholder={policyDetailLang.oneCondition("select")} // 请选择
								disabled={true}
							>
								{
									allMap && allMap["ruleAndIndexFieldList"] && allMap["ruleAndIndexFieldList"].map((item, index) => {
										return (
											<Option value={item.name} key={index}>
                                            [{commonLang.sourceName(item.sourceName)}]&nbsp;
												{item.dName}
											</Option>
										);
									})
								}
							</Select>
						</TooltipWrapper>
					</Col>
					<Col className="gutter-row" span={3}>
						<div className="gutter-box">
							<TooltipWrapper>
								<Select
									value={conditionSingleData && conditionSingleData["operator"] ? conditionSingleData["operator"] : undefined}
									placeholder={policyDetailLang.oneCondition("select")} // 请选择
									disabled={true}
								>
									{
										PolicyConstants.conditionOperator[conditionSingleData.propertyDataType ? conditionSingleData.propertyDataType : "STRING"].map((item, index) => {
											return (
												<Option value={item.name} key={index}>
													{lang === "en" ? item.enDName : item.dName}
												</Option>
											);
										})
									}
								</Select>
							</TooltipWrapper>
						</div>
					</Col>
					{
						conditionSingleData &&
                        conditionSingleData["operator"] &&
                        conditionSingleData["operator"] !== "isnull" &&
                        conditionSingleData["operator"] !== "notnull" &&
                        <Col className="gutter-row" span={8}>
                        	<div className="gutter-box">
                        		{
                        			conditionSingleData["rightValueType"] === "input" &&
                                    conditionSingleData["propertyDataType"] !== "ENUM" &&
                                    !operaTypeBlong &&
                                    <InputGroup compact>
                                    	<TooltipWrapper>
                                    	<Select
                                    		value={conditionSingleData && conditionSingleData["rightValueType"] ? conditionSingleData["rightValueType"] : undefined}
                                    		style={{ width: "30%" }}
                                    		disabled={true}
                                    	>
                                    		<Option value="input">
                                    			{/* 常量 */}
                                    			{policyDetailLang.oneCondition("constant")}
                                    		</Option>
                                    		<Option value="context">
                                    			{/* 变量 */}
                                    			{policyDetailLang.oneCondition("variable")}
                                    		</Option>
                                    	</Select>
                                    	</TooltipWrapper>
                                    	<TooltipWrapper>
                                    		<Input
                                    			style={{ width: "70%" }}
                                    			value={conditionSingleData && conditionSingleData["value"] ? conditionSingleData["value"] : undefined}
                                    			placeholder={policyDetailLang.oneCondition("inputConstant")} // 请输入常量内容
                                    			disabled={true}
                                    		/>
                                    	</TooltipWrapper>
                                    </InputGroup>
                        		}
                        		{
                        			conditionSingleData &&
                                    conditionSingleData["rightValueType"] === "context" &&
                                    conditionSingleData["propertyDataType"] !== "ENUM" &&
                                    !operaTypeBlong &&
                                    <InputGroup compact>
                                    	<TooltipWrapper>
                                    		<Select
                                    			value={conditionSingleData && conditionSingleData["rightValueType"] ? conditionSingleData["rightValueType"] : undefined}
                                    			style={{ width: "30%" }}
                                    			disabled={true}
                                    		>
                                    			<Option value="input">
                                    				{/* 常量 */}
                                    				{policyDetailLang.oneCondition("constant")}
                                    			</Option>
                                    			<Option value="context">
                                    				{/* 变量 */}
                                    				{policyDetailLang.oneCondition("variable")}
                                    			</Option>
                                    		</Select>
                                    	</TooltipWrapper>
                                    	<TooltipWrapper>
                                    		<Select
                                    			style={{ width: "70%" }}
                                    			value={conditionSingleData && conditionSingleData["value"] ? conditionSingleData["value"] : undefined}
                                    			placeholder={policyDetailLang.oneCondition("select")} // 请选择
                                    			disabled={true}
                                    		>
                                    			{
                                    				allMap &&
													allMap["ruleAndIndexFieldList"] &&
													conditionSingleData &&
													conditionSingleData["propertyDataType"] &&
													allMap["ruleAndIndexFieldList"].filter(fItem => {
														let leftOptionDataType = conditionSingleData["propertyDataType"];
														return (
															fItem.type === leftOptionDataType ||
															(["DOUBLE", "INT"].indexOf(fItem.type) > -1 && ["DOUBLE", "INT"].indexOf(leftOptionDataType) > -1)
														);
													}).map((item, index) => {
														return (
															<Option value={item.name} key={index}>
																[{commonLang.sourceName(item.sourceName)}]&nbsp;
																{item.dName}
															</Option>
														);
													})
                                    			}
                                    		</Select>
                                    	</TooltipWrapper>
                                    </InputGroup>
                        		}
                        		{
                        			conditionSingleData["propertyDataType"] === "ENUM" &&
									!operaTypeBlong &&
									<TooltipWrapper>
										<Select
											value={conditionSingleData["value"] || undefined}
											placeholder={policyDetailLang.oneCondition("select")} // lang:请选择
											disabled={true}
										>
											{
												allMap &&
												allMap["fieldEnumObj"] &&
												allMap["fieldEnumObj"][enumField] &&
												allMap["fieldEnumObj"][enumField].map((item, index) => {
													return (
														<Option
															value={item.value}
															key={index}
															title={item.value}
														>
															{item.description}  [{item.value}]
														</Option>
													);
												})
											}
										</Select>
									</TooltipWrapper>
                        		}
                        		{
                        			operaTypeBlong &&
									<TooltipWrapper>
										<Select
											value={
												(conditionSingleData["value"] && belongNames.indexOf(conditionSingleData["value"]) > -1)
													? conditionSingleData["value"]
													: undefined
											}
											placeholder={policyDetailLang.oneCondition("select")} // lang:请选择
											disabled={true}
										>
											{
												allMap &&
												allMap["fieldParamListSelect"] &&
												allMap["fieldParamListSelect"].map((item, index) => {
													return (
														<Option
															value={item.name}
															key={index}
														>
															[参数列表]{lang === "en" ? item.enDName : item.dName}
														</Option>
													);
												})
											}
										</Select>
									</TooltipWrapper>
                        		}
                        	</div>
                        </Col>
					}
				</Row>
			</div>
		);
	}
}

export default connect(state => ({
	globalStore: state.global,
	policyDetailStore: state.policyDetail
}))(OneCondition);
