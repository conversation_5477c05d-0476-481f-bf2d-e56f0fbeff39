import { PureComponent } from "react";
import { connect } from "dva";
import { policyDealAPI } from "@/services";
import { Table, Button, Pagination, message, Modal, Tooltip, Select } from "antd";
import AddTriggerModal from "./Modal/AddTriggerBoot";
import ModifyTriggerByTime from "./Modal/ModifyTriggerByTime";
import ModifyTriggerByField from "./Modal/ModifyTriggerByField";
import { checkFunctionHasPermission } from "@/utils/permission";
import NoPermission from "@/components/NoPermission";
import ModifyImmuno from "./Modal/ModifyImmuno";

const Option = Select.Option;
const confirm = Modal.confirm;

class TriggerManage extends PureComponent {
    state = {};

    constructor(props) {
    	super(props);
    	this.paginationOnChange = this.paginationOnChange.bind(this);
    	this.addDealTypeHandle = this.addDealTypeHandle.bind(this);
    	this.modifyDealTypeTriggerHandle = this.modifyDealTypeTriggerHandle.bind(this);
    	this.deleteDealTypeTriggerHandle = this.deleteDealTypeTriggerHandle.bind(this);
    	this.startSearch = this.startSearch.bind(this);
    	this.resetSearch = this.resetSearch.bind(this);
    }

    componentDidMount() {
    	this.timer = setInterval(() => {
    		const { globalStore } = this.props;
    		const { menuTreeReady } = globalStore;
    		if (menuTreeReady) {
    			clearInterval(this.timer);
    			if (checkFunctionHasPermission("ZB0301", "listUnTrigger")) {
    				this.search();
    			}
    		}
    	}, 100);
    }

    search() {
    	let { policyDealStore, dispatch } = this.props;
    	let { trigger } = policyDealStore;
    	let { searchParams } = trigger;
    	dispatch({
    		type: "policyDeal/getTriggers",
    		payload: searchParams
    	});
    }

    addDealTypeHandle() {
    	let { dispatch } = this.props;
    	dispatch({
    		type: "policyDeal/setDialogShow",
    		payload: {
    			addTriggerBoot: true
    		}
    	});
    }

    modifyDealTypeTriggerHandle(item) {
    	let { dispatch } = this.props;
    	let controlType = item.controlType;
    	if (controlType === "byTime") {
    		dispatch({
    			type: "policyDeal/setDialogShow",
    			payload: {
    				modifyTriggerByTime: true
    			}
    		});

    		let modifyTriggerTimeData = {
    			isUpdate: true,
    			dealType: item.dealType,
    			calendarDataList: []
    		};

    		dispatch({
    			type: "policyDeal/setDialogData",
    			payload: {
    				modifyTriggerTimeData: modifyTriggerTimeData
    			}
    		});

    	} else if (controlType === "byField") {

    		let params = {
    			dealType: item.dealType
    		};

    		policyDealAPI.getTriggerFieldDetail(params).then(res => {
    			if (res.success) {
    				dispatch({
    					type: "policyDeal/setDialogShow",
    					payload: {
    						addTriggerBoot: false,
    						modifyTriggerByField: true
    					}
    				});

    				let fieldConfig = JSON.parse(res.data.fieldConfig);

    				let modifyTriggerFieldData = {
    					matchField: fieldConfig.matchField,
    					matchValue: fieldConfig.matchValue,
    					dealType: item.dealType,
    					isUpdate: true
    				};

    				dispatch({
    					type: "policyDeal/setDialogData",
    					payload: {
    						modifyTriggerFieldData: modifyTriggerFieldData
    					}
    				});
    			} else {
    				console.log(res.message);
    				message.error(res.message);
    			}
    		}).catch(err => {
    			console.log(err);
    		});

    	}
    }

    deleteDealTypeTriggerHandle(item, e) {
    	let { policyDealStore, dispatch } = this.props;
    	let { trigger } = policyDealStore;
    	let { searchParams } = trigger;

    	e.stopPropagation();

    	let params = {
    		dealType: item.dealType,
    		controlType: item.controlType
    	};
    	confirm({
    		title: "删除决策触发提醒",
    		content: "您真的要删除《" + item.dealTypeName + "》吗？",
    		onOk() {
    			policyDealAPI.deleteTrigger(params).then(res => {
    				if (res.success) {
    					message.success("删除成功");
    					dispatch({
    						type: "policyDeal/getTriggers",
    						payload: searchParams
    					});
    				} else {
    					console.log(res.message);
    					message.error(res.message);
    				}
    			}).catch(err => {
    				console.log(err);
    			});
    		},
    		onCancel() {
    			console.log("Cancel");
    		}
    	});
    }

    modifyDealTypeImmunoHandle(item) {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "policyDeal/setDialogShow",
    		payload: {
    			modifyImmuno: true
    		}
    	});

    	let immunoConditions = {
    		logicOperator: "&&",
    		priority: "1",
    		defaultNode: false,
    		children: []
    	};
    	if (item.immunoConditions && JSON.parse(item.immunoConditions)) {
    		const itemImmunoConditions = JSON.parse(item.immunoConditions);
    		if (itemImmunoConditions.logicOperator && itemImmunoConditions.children) {
    			immunoConditions = itemImmunoConditions;
    		}
    	}
    	let modifyImmuno = {
    		eventId: item.eventId,
    		immunoField: item.immunoField,
    		immunoTime: item.immunoTime,
    		app: item.app,
    		description: item.description,
    		immunoConditions,
    		isUpdate: true
    	};

    	dispatch({
    		type: "policyDeal/setDialogData",
    		payload: {
    			modifyImmuno: modifyImmuno
    		}
    	});
    }

    deleteDealTypeImmunoHandle(item, e) {
    	let { policyDealStore, globalStore, dispatch } = this.props;
    	let { trigger } = policyDealStore;
    	let { eventIdSelect } = globalStore.allMap;
    	let { searchParams } = trigger;

    	e.stopPropagation();

    	let params = {
    		eventId: item.eventId
    	};

    	confirm({
    		title: "删除决策触发提醒",
    		content: "您真的要删除《" + eventIdSelect[item.eventId] + "》吗？",
    		onOk() {
    			policyDealAPI.deleteImmuno(params).then(res => {
    				if (res.success) {
    					message.success("删除成功");
    					dispatch({
    						type: "policyDeal/getTriggers",
    						payload: searchParams
    					});
    				} else {
    					message.error(res.message);
    				}
    			}).catch(err => {
    				console.log(err);
    			});
    		},
    		onCancel() {
    			console.log("Cancel");
    		}
    	});
    }

    paginationOnChange(current, pageSize) {
    	let { policyDealStore, dispatch } = this.props;
    	let { trigger } = policyDealStore;
    	let { searchParams } = trigger;
    	console.log(current);
    	dispatch({
    		type: "policyDeal/setAttrValue",
    		payload: {
    			curPage: current,
    			pageSize: pageSize
    		}
    	});
    	dispatch({
    		type: "policyDeal/getTriggers",
    		payload: {
    			...searchParams,
    			curPage: current,
    			pageSize: pageSize
    		}
    	});
    }

    startSearch(field, e) {
    	let { policyDealStore, dispatch } = this.props;
    	let { trigger } = policyDealStore;
    	let { searchParams } = trigger;
    	searchParams[field] = e !== "all" ? e : null;
    	dispatch({
    		type: "policyDeal/setTrigger",
    		payload: {
    			searchParams: searchParams
    		}
    	});
    	dispatch({
    		type: "policyDeal/getTriggers",
    		payload: searchParams
    	});
    }

    resetSearch() {
    	let { dispatch } = this.props;

    	dispatch({
    		type: "policyDeal/setTrigger",
    		payload: {
    			searchParams: {
    				manageType: "untrigger",
    				dealType: null,
    				controlType: null
    			}
    		}
    	});
    }

    render() {
    	let { policyDealStore, globalStore } = this.props;
    	let { personalMode, allMap, ruleField, menuTreeReady } = globalStore;
    	let { trigger, dialogShow } = policyDealStore;
    	let { eventIdSelect } = allMap;
    	let { triggerList, searchParams, total } = trigger;
    	let columns;

    	if (searchParams.manageType === "untrigger") {
    		columns = [
    			{
    				title: "风险决策",
    				dataIndex: "dealTypeName"
    			},
    			{
    				title: "类型管理",
    				dataIndex: "manageTypeName"
    			},
    			{
    				title: "控制方式",
    				dataIndex: "controlTypeName"
    			},
    			{
    				title: "操作",
    				width: 200,
    				render: (record) => {
    					return (
    						<div className="table-action">
    							{
    								(record.controlType === "byTime" && checkFunctionHasPermission("ZB0301", "getUnTriggerByTime")) ||
                                        (record.controlType === "byField" && checkFunctionHasPermission("ZB0301", "getUnTriggerByField"))
    									? <Tooltip title="编辑决策触发">
    										<a onClick={(e) => {
    											this.modifyDealTypeTriggerHandle(record, e);
    										}}>
                                                编辑
    										</a>
    									</Tooltip>
    									: <a className="a-disabled" onClick={() => {
    										message.warning("无权限操作");
    									}}>编辑</a>
    							}
    							{
    								checkFunctionHasPermission("ZB0301", "deleteUnTrigger")
    									? <Tooltip title="删除决策触发">
    										<a onClick={(e) => {
    											this.deleteDealTypeTriggerHandle(record, e);
    										}}>
                                                删除
    										</a>
    									</Tooltip>
    									: <a className="a-disabled" onClick={() => {
    										message.warning("无权限操作");
    									}}>删除</a>
    							}
    						</div>
    					);
    				}
    			}
    		];
    	} else {
    		columns = [
    			{
    				title: "策略集",
    				dataIndex: "eventId",
    				render: (value) => eventIdSelect[value]
    			},
    			{
    				title: "类型管理",
    				dataIndex: "manageTypeName",
    				render: (value, row) => "免打扰配置"
    			},
    			{
    				title: "维度",
    				dataIndex: "immunoField",
    				render: (value) => ruleField[value]
    			},
    			{
    				title: "免打扰时间(小时)",
    				dataIndex: "immunoTime"
    			},
    			{
    				title: "操作",
    				width: 200,
    				render: (record) => {
    					return (
    						<div className="table-action">
    							{
    								checkFunctionHasPermission("ZB0301", "updateDealTypeImmuno")
    									? <Tooltip title="编辑决策触发">
    										<a onClick={(e) => {
    											this.modifyDealTypeImmunoHandle(record, e);
    										}}>
                                                编辑
    										</a>
    									</Tooltip>
    									: <a className="a-disabled" onClick={() => {
    										message.warning("无权限操作");
    									}}>编辑</a>
    							}
    							{
    								checkFunctionHasPermission("ZB0301", "deleteDealTypeImmuno")
    									? <Tooltip title="删除决策触发">
    										<a onClick={(e) => {
    											this.deleteDealTypeImmunoHandle(record, e);
    										}}>
                                                删除
    										</a>
    									</Tooltip>
    									: <a className="a-disabled" onClick={() => {
    										message.warning("无权限操作");
    									}}>删除</a>
    							}
    						</div>
    					);
    				}
    			}
    		];
    	}

    	return (
    		<div>
    			<div className="page-global-header">
    				<div className="left-info">
    					<h2>决策触发列表</h2>
    				</div>
    				{
    					menuTreeReady && checkFunctionHasPermission("ZB0301", "listUnTrigger") &&
                        <div className="right-info">
                        	<div className="right-info-item">
                        		<Select
                        			style={{ width: "150px" }}
                        			value={searchParams.manageType || undefined}
                        			onChange={this.startSearch.bind(this, "manageType")}
                        		>
                        			<Option value="untrigger">不触发配置</Option>
                        			<Option value="immuno">免打扰配置</Option>
                        		</Select>
                        	</div>
                        	<div className="right-info-item">
                        		<Select
                        			style={{ width: "150px" }}
                        			value={searchParams.dealType ? searchParams.dealType : "all"}
                        			onChange={this.startSearch.bind(this, "dealType")}
                        			disabled={searchParams.manageType === "immuno"}
                        		>
                        			<Option value="all">全部风险决策</Option>
                        			{
                        				allMap && allMap["dealTypeList"] && allMap["dealTypeList"].map((item, index) => {

                        					return (<Option value={item.name} key={index}>{item.dName}</Option>);

                        				})
                        			}
                        		</Select>
                        	</div>
                        	<div className="right-info-item">
                        		<Select
                        			style={{ width: "150px" }}
                        			value={searchParams.controlType ? searchParams.controlType : "all"}
                        			onChange={this.startSearch.bind(this, "controlType")}
                        			disabled={searchParams.manageType === "immuno"}
                        		>
                        			<Option value="all">全部控制方式</Option>
                        			<Option value="byTime">按时间</Option>
                        			<Option value="byField">按字段</Option>
                        		</Select>
                        	</div>
                        	<div className="right-info-item">
                        		<Button
                        			type="primary"
                        			onClick={this.addDealTypeHandle.bind(this)}
                        		>
                                    新增触发方式
                        		</Button>
                        	</div>
                        </div>
    				}
    			</div>
    			{
    				menuTreeReady &&
                    <div className="page-global-body">
                    	{
                    		checkFunctionHasPermission("ZB0301", "listUnTrigger")
                    			? <div>
                    				<div className="page-global-body-main has-table-border">
                    					<Table
                    						className="table-out-border"
                    						columns={columns}
                    						pagination={false}
                    						dataSource={triggerList}
                    						rowKey={(record, index) => index}
                    						size={personalMode.layout === "default" ? "default" : "middle"}
                    					/>
                    					<div className="page-global-body-pagination">
                    						<span className="count">共{total}条记录</span>
                    						<Pagination
                    							showSizeChanger
                    							onChange={this.paginationOnChange.bind(this)}
                    							onShowSizeChange={this.paginationOnChange.bind(this)}
                    							defaultCurrent={1}
                    							total={total}
                    							current={searchParams.curPage || 1}
                    						/>
                    					</div>
                    				</div>
                    			</div>
                    			: <NoPermission />
                    	}
                    </div>
    			}
    			<AddTriggerModal />
    			{
    				dialogShow.modifyTriggerByTime && <ModifyTriggerByTime />
    			}
    			{
    				dialogShow.modifyTriggerByField && <ModifyTriggerByField />
    			}
    			{
    				dialogShow.modifyImmuno && <ModifyImmuno />
    			}
    		</div>
    	);
    }
}

export default connect(state => ({
	globalStore: state.global,
	policyDealStore: state.policyDeal
}))(TriggerManage);

