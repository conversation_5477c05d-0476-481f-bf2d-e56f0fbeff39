@import "../../base/variables";

:global {
	.policy-detail-header {
		.rule-tools-bar {
			& {
				position: absolute;
				right: 0;
				top: 0;
				height: 32px;
				line-height: 32px;
			}
			.tool-item {
				& {
					position: relative;
					display: inline-block;
					margin-left: 16px;
				}
				i {
					font-size: 24px;
					color: #b4b4b4;
					cursor: pointer;
				}
				i:hover {
					color: #3498db;
				}
				&.rule-storage {
					position: relative;
					//top: -5px;
					width: 24px;
					height: 24px;
				}
				.badge {
					position: absolute;
					-webkit-transform: translate(50%, -50%);
					-ms-transform: translate(50%, -50%);
					transform: translate(50%, -50%);
					top: 0;
					right: 0;
					height: 20px;
					border-radius: 10px;
					min-width: 20px;
					background: #f5222d;
					color: #fff;
					line-height: 20px;
					text-align: center;
					padding: 0 6px;
					font-size: 12px;
					font-weight: normal;
					white-space: nowrap;
					-webkit-transform-origin: 0 center;
					-ms-transform-origin: 0 center;
					transform-origin: 0 center;
					-webkit-box-shadow: 0 0 0 1px #fff;
					box-shadow: 0 0 0 1px #fff;
				}
				&.rule-search {
					i {
						position: absolute;
						right: 12px;
						top: 0;
						height: 32px;
						line-height: 32px;
						font-size: 14px;
					}
				}
			}
			.ant-badge {
				position: relative;
				display: inline-block;
				width: 40px;
				height: 26px;
				vertical-align: initial;
			}
		}
		.policy-detail-rule-search {
			& {
				position: relative;
			}
			input {
				text-align: center;
				//border-radius: 20px;
			}
			.rule-search-tip {
				& {
					position: absolute;
					right: 12px;
					top: 0;
					height: 40px;
					line-height: 40px;
					color: #dcdcdc;
				}
				i {
					margin-right: 10px;
				}
				span {

				}
			}
		}
	}
}

