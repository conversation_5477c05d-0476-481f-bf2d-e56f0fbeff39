import { PureComponent } from "react";
import { indexListLang } from "@/constants/lang";
import "./ShowScene.less";

export default class ShowSingleScene extends PureComponent {
	constructor(props) {
		super(props);
	}
	render() {
		let { sceneListSource, scene, className } = this.props;
		if (scene && typeof scene === "string") {
			scene = JSON.parse(scene);
		}
		if (sceneListSource && typeof sceneListSource === "string") {
			sceneListSource = JSON.parse(sceneListSource);
		}
		scene = scene || [];
		sceneListSource = sceneListSource || [];

		const sceneNameList = [];
		scene &&
		scene.length > 0 &&
		scene.forEach(sceneItem=>{
			sceneListSource.forEach(v=>{
				if (v.name === sceneItem) {
					sceneNameList.push(v.dName);
				} ;
			});
		});

		return (
			<div className={`show-scene-table-wrap ${className}`}>
				<table>
					<thead>
						<tr>
							<th>
								{/* lang:具体场景 */}
								{indexListLang.ruleConfig("sceneDetail")}
							</th>
						</tr>
					</thead>
					<tbody>
						{
							sceneNameList &&
							sceneNameList.length > 0 &&
							<tr>
								<td>
									{sceneNameList.join(" | ")}
								</td>
							</tr>

						}
					</tbody>
				</table>
			</div>
		);
	}
}
